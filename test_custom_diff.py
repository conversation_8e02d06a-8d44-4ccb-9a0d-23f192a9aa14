#!/usr/bin/env python3
"""
测试自定义 diff 格式支持
"""

import subprocess
import tempfile
import os

def test_custom_diff_format():
    """测试自定义 diff 格式解析"""
    print("🧪 测试自定义 diff 格式解析")
    print("=" * 60)
    
    # 创建一个简单的测试 Java 文件
    test_java_content = """package com.example;

public class TestService {
    
    private String name;
    
    /**
     * 获取名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 设置名称
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * 测试方法
     */
    public void testMethod() {
        System.out.println("Hello World");
    }
}
"""
    
    # 创建测试目录和文件
    test_dir = "test_java_files"
    os.makedirs(test_dir, exist_ok=True)
    
    test_file_path = os.path.join(test_dir, "TestService.java")
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_java_content)
    
    # 创建自定义格式的 diff
    custom_diff = f"""文件{test_file_path}的变更内容为: @@ -20,6 +20,8 @@ public class TestService {{\\n \\n     /**\\n      * 测试方法\\n      */\\n     public void testMethod() {{\\n+        // 新增注释\\n+        System.out.println("New line");\\n         System.out.println("Hello World");\\n     }}\\n }}"""
    
    try:
        print("\n1. 测试自定义 diff 格式:")
        print(f"   测试文件: {test_file_path}")
        print(f"   Diff 格式: 文件{test_file_path}的变更内容为: @@ -20,6 +20,8 @@...")
        
        cmd = [
            'python3', 'extractor_final.py',
            '--diff', custom_diff,
            '--debug'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 执行成功")
            
            # 检查输出内容
            output = result.stdout
            
            # 检查是否正确解析了文件
            if "找到变更文件" in output and test_file_path in output:
                print("   ✅ 正确解析了文件路径")
            else:
                print("   ❌ 未能正确解析文件路径")
            
            # 检查是否找到了方法
            if "找到被修改的方法" in output:
                print("   ✅ 找到了被修改的方法")
            else:
                print("   ❌ 未找到被修改的方法")
            
            # 检查是否生成了详细上下文
            if "详细代码上下文" in output:
                print("   ✅ 生成了详细代码上下文")
            else:
                print("   ❌ 未生成详细代码上下文")
            
            # 显示部分输出
            print(f"\n   📄 输出预览 (前15行):")
            lines = output.split('\n')[:15]
            for i, line in enumerate(lines, 1):
                if line.strip():
                    print(f"   {i:2d}: {line}")
        else:
            print(f"   ❌ 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
    
    except Exception as e:
        print(f"   ❌ 测试错误: {e}")
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
            os.rmdir(test_dir)
        except:
            pass

def test_multiple_files_diff():
    """测试多文件 diff 格式"""
    print("\n🔄 测试多文件 diff 格式")
    print("=" * 60)
    
    # 创建多个测试文件
    test_dir = "test_multi_files"
    os.makedirs(test_dir, exist_ok=True)
    
    # 文件1
    file1_content = """public class Service1 {
    public void method1() {
        System.out.println("Service1");
    }
}"""
    
    # 文件2
    file2_content = """public class Service2 {
    public void method2() {
        System.out.println("Service2");
    }
}"""
    
    file1_path = os.path.join(test_dir, "Service1.java")
    file2_path = os.path.join(test_dir, "Service2.java")
    
    with open(file1_path, 'w') as f:
        f.write(file1_content)
    with open(file2_path, 'w') as f:
        f.write(file2_content)
    
    # 创建多文件 diff
    multi_diff = f"""文件{file1_path}的变更内容为: @@ -2,6 +2,7 @@ public class Service1 {{\\n     public void method1() {{\\n+        // 新增注释\\n         System.out.println("Service1");\\n     }}\\n }}\\n 文件{file2_path}的变更内容为: @@ -2,6 +2,7 @@ public class Service2 {{\\n     public void method2() {{\\n+        // 新增注释\\n         System.out.println("Service2");\\n     }}\\n }}"""
    
    try:
        print(f"\n1. 测试多文件 diff:")
        print(f"   文件1: {file1_path}")
        print(f"   文件2: {file2_path}")
        
        cmd = [
            'python3', 'extractor_final.py',
            '--diff', multi_diff,
            '--debug'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 执行成功")
            
            output = result.stdout
            
            # 检查是否处理了多个文件
            file1_count = output.count(os.path.basename(file1_path))
            file2_count = output.count(os.path.basename(file2_path))
            
            print(f"   📊 文件1 ({os.path.basename(file1_path)}) 出现次数: {file1_count}")
            print(f"   📊 文件2 ({os.path.basename(file2_path)}) 出现次数: {file2_count}")
            
            if file1_count > 0 and file2_count > 0:
                print("   ✅ 成功处理了多个文件")
            else:
                print("   ❌ 未能正确处理多个文件")
        else:
            print(f"   ❌ 执行失败 (返回码: {result.returncode})")
    
    except Exception as e:
        print(f"   ❌ 测试错误: {e}")
    finally:
        # 清理测试文件
        try:
            os.unlink(file1_path)
            os.unlink(file2_path)
            os.rmdir(test_dir)
        except:
            pass

def test_original_diff_file():
    """测试原始 diff.txt 文件"""
    print("\n📁 测试原始 diff.txt 文件")
    print("=" * 60)
    
    try:
        cmd = [
            'python3', 'extractor_final.py',
            '--file', 'diff.txt',
            '--debug'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 执行成功")
            
            output = result.stdout
            
            # 统计解析的文件数量
            file_count = output.count("找到变更文件")
            method_count = output.count("找到被修改的方法")
            
            print(f"   📊 解析的文件数量: {file_count}")
            print(f"   📊 找到的方法数量: {method_count}")
            
            # 检查是否有详细上下文
            if "详细代码上下文" in output:
                print("   ✅ 生成了详细代码上下文")
            elif "未找到相关上下文" in output:
                print("   ⚠️  未找到相关上下文 (文件可能不存在)")
            else:
                print("   ❓ 输出格式未知")
            
            # 显示调试信息
            debug_lines = [line for line in output.split('\n') if '[DEBUG]' in line]
            print(f"\n   🔍 调试信息 (前10行):")
            for i, line in enumerate(debug_lines[:10], 1):
                print(f"   {i:2d}: {line}")
        else:
            print(f"   ❌ 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
    
    except Exception as e:
        print(f"   ❌ 测试错误: {e}")

def main():
    """主函数"""
    print("🚀 测试自定义 diff 格式支持")
    print("=" * 80)
    
    # 检查 extractor_final.py 是否存在
    if not os.path.exists('extractor_final.py'):
        print("❌ 错误: 找不到 extractor_final.py 文件")
        return
    
    try:
        test_custom_diff_format()
        test_multiple_files_diff()
        test_original_diff_file()
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成!")
        print("\n📋 功能总结:")
        print("- ✅ 支持自定义 diff 格式解析")
        print("- ✅ 支持多文件 diff 处理")
        print("- ✅ 智能跳过不存在的文件")
        print("- ✅ 保持向后兼容性")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
