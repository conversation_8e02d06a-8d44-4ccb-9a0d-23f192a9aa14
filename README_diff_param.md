# Java 代码上下文提取器 - diff 参数功能

## 🆕 新功能概述

Java 代码上下文提取器现在支持通过 `--diff` 参数直接传入 diff 内容，无需创建临时文件。这个功能特别适合：

- 🔄 **CI/CD 流水线集成**: 直接处理 Git 命令输出
- 🤖 **自动化脚本**: 避免临时文件管理
- 🔗 **API 集成**: 直接处理字符串形式的 diff 数据
- ⚡ **快速分析**: 无需文件 I/O 操作

## 📋 命令行参数

### 输入参数 (互斥)

| 参数 | 说明 | 示例 |
|------|------|------|
| `--file` | 从文件读取 diff 内容 | `--file changes.diff` |
| `--diff` | 直接传入 diff 内容 | `--diff "diff --git a/..."` |

> ⚠️ **注意**: `--file` 和 `--diff` 参数是互斥的，只能使用其中一个。

### 其他参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--repo-root` | 仓库根目录 | 当前目录 (`.`) |
| `--output` | 输出文件路径 | 标准输出 |
| `--debug` | 启用调试输出 | 关闭 |

## 🚀 使用方法

### 基本用法

```bash
# 方式1: 从文件读取 (原有功能)
python3 extractor_final.py --file changes.diff

# 方式2: 直接传入 diff 内容 (新功能)
python3 extractor_final.py --diff "diff --git a/src/main/java/Service.java..."
```

### 实际使用场景

#### 场景1: Git 命令集成

```bash
# 获取最新提交的 diff 并直接分析
git show HEAD --format="" | python3 extractor_final.py --diff "$(cat)"

# 比较两个分支的差异并分析
git diff main feature-branch | python3 extractor_final.py --diff "$(cat)"

# 分析暂存区的变更
git diff --cached | python3 extractor_final.py --diff "$(cat)"
```

#### 场景2: CI/CD 流水线

```yaml
# GitLab CI 示例
analyze_changes:
  script:
    - DIFF_CONTENT=$(git diff $CI_MERGE_REQUEST_TARGET_BRANCH_NAME..HEAD)
    - python3 extractor_final.py --diff "$DIFF_CONTENT" --output analysis.md
    - cat analysis.md
```

```yaml
# GitHub Actions 示例
- name: Analyze Code Changes
  run: |
    DIFF_CONTENT=$(git diff origin/main..HEAD)
    python3 extractor_final.py --diff "$DIFF_CONTENT" --output analysis.md
```

#### 场景3: 脚本自动化

```bash
#!/bin/bash
# 自动分析脚本

# 获取 diff 内容
DIFF_CONTENT=$(git log --oneline -1 --format="" --name-only | head -1 | xargs git show)

# 分析代码上下文
python3 extractor_final.py \
  --diff "$DIFF_CONTENT" \
  --repo-root /path/to/project \
  --output "analysis_$(date +%Y%m%d_%H%M%S).md" \
  --debug
```

#### 场景4: 编程方式使用

```python
#!/usr/bin/env python3
import subprocess
from extractor_final import JavaContextExtractor

# 获取 Git diff
result = subprocess.run(['git', 'diff', 'HEAD~1..HEAD'], 
                       capture_output=True, text=True)
diff_content = result.stdout

# 创建提取器并分析
extractor = JavaContextExtractor(debug=True, repo_root='/path/to/repo')
context = extractor.extract_all_contexts(diff_content)

# 处理结果
print(context)
```

## 🔧 高级用法

### 多仓库分析

```bash
# 分析多个仓库的变更
for repo in repo1 repo2 repo3; do
  echo "分析 $repo..."
  cd $repo
  git diff HEAD~1..HEAD | python3 ../extractor_final.py \
    --diff "$(cat)" \
    --repo-root . \
    --output "../analysis_$repo.md"
  cd ..
done
```

### 批量提交分析

```bash
# 分析最近5个提交
for i in {1..5}; do
  echo "分析提交 HEAD~$i"
  git show HEAD~$i --format="" | python3 extractor_final.py \
    --diff "$(cat)" \
    --output "commit_analysis_$i.md"
done
```

### 合并请求分析

```bash
# 分析合并请求的所有变更
git diff origin/main...feature-branch | python3 extractor_final.py \
  --diff "$(cat)" \
  --repo-root . \
  --output mr_analysis.md \
  --debug
```

## ⚡ 性能优势

| 方式 | 文件 I/O | 临时文件 | 内存使用 | 适用场景 |
|------|----------|----------|----------|----------|
| `--file` | 需要 | 需要管理 | 较低 | 本地开发、手动分析 |
| `--diff` | 不需要 | 无需管理 | 较高 | 自动化、CI/CD、脚本 |

## 🛡️ 错误处理

工具提供完善的错误处理：

```bash
# 互斥参数检查
$ python3 extractor_final.py --file diff.txt --diff "content"
❌ error: argument --diff: not allowed with argument --file

# 必需参数检查
$ python3 extractor_final.py
❌ error: one of the arguments --file --diff is required

# 空内容检查
$ python3 extractor_final.py --diff ""
❌ 错误: diff 内容为空

# 空白内容检查
$ python3 extractor_final.py --diff "   "
❌ 错误: diff 内容为空
```

## 📊 使用建议

### 何时使用 `--file`
- ✅ 本地开发和调试
- ✅ 需要重复分析同一个 diff
- ✅ diff 内容很大 (>1MB)
- ✅ 需要手动编辑 diff 内容

### 何时使用 `--diff`
- ✅ CI/CD 流水线集成
- ✅ 自动化脚本
- ✅ 实时 Git 命令输出处理
- ✅ API 或服务集成
- ✅ 避免临时文件管理

## 🔍 调试技巧

```bash
# 查看 diff 内容是否正确传递
python3 extractor_final.py --diff "$DIFF_CONTENT" --debug | head -20

# 验证 diff 格式
echo "$DIFF_CONTENT" | head -5

# 检查仓库根目录
python3 extractor_final.py --diff "$DIFF_CONTENT" --repo-root . --debug | grep "仓库根目录"
```

## 🚨 注意事项

1. **Shell 转义**: 在 shell 中使用时注意特殊字符转义
2. **内容大小**: 非常大的 diff 内容可能导致命令行参数过长
3. **编码问题**: 确保 diff 内容使用 UTF-8 编码
4. **换行符**: 不同系统的换行符可能影响解析

## 📈 版本更新

### v2.1.0 (当前版本)
- ✅ 新增 `--diff` 参数支持
- ✅ 添加互斥参数验证
- ✅ 增强空内容检查
- ✅ 完善错误处理机制
- ✅ 保持向后兼容性

### v2.0.0
- ✅ 新增仓库根目录支持
- ✅ 改进路径处理机制

### v1.0.0
- ✅ 基础代码上下文提取功能

## 🤝 最佳实践

1. **CI/CD 集成**: 使用 `--diff` 参数避免临时文件
2. **本地开发**: 使用 `--file` 参数便于调试
3. **错误处理**: 始终检查命令返回码
4. **日志记录**: 在生产环境中启用 `--debug`
5. **资源管理**: 大型 diff 考虑使用文件方式
