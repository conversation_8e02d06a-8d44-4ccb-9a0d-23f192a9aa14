package com.yeepay.g3.app.account.pay.mboss.controller.middleground;


import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.ContactQueryParam;
import com.yeepay.g3.app.account.pay.mboss.service.AccountManageInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.ContactsService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.ContactsDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.SubMerchantContactsDownloadService;
import com.yeepay.g3.app.account.pay.mboss.vo.RemitContactRequestVO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.bac.dto.enumtype.BasePageResDto;
import com.yeepay.g3.facade.bac.dto.enumtype.NewContactResDto;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("/contacts")
public class ContactsController extends BaseController {

    @Autowired
    private ContactsService contactsService;
    @Autowired
    private AccountManageInfoService accountManageInfoService;

    private static final Logger logger = LoggerFactory.getLogger(ContactsController.class);

    /**
     * 查询联系人的信息
     *
     * @param page
     * @param size
     * @param name          收款名称
     * @param bankAccountNo 收款账号
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO query(@RequestParam(value = "page") Integer page, @RequestParam(value = "size") Integer size,
                             @RequestParam(value = "name", required = false) String name, @RequestParam(value = "bankAccountNo", required = false) String bankAccountNo) {
        try {
            logger.info("查询联系人的信息，name：{},accountNumber:{}", name, bankAccountNo);
            ShiroUser user = super.getCurrentUser();
            String customerMember = null;
            String operator = null;
            if (user != null) {
                customerMember = user.getCustomerNumber();
                if (StringUtils.isBlank(customerMember)) {
                    return null;
                }
                operator = user.getLoginName();
                if (StringUtils.isBlank(operator)) {
                    return null;
                }
            }
            BasePageResDto<NewContactResDto> bankPageSearchResDTO = contactsService.selectUserPageAndCondition(page, size, name, bankAccountNo, customerMember, operator);
            return BaseRespDTO.success(bankPageSearchResDTO);
        } catch (Throwable e) {
            logger.error("查询联系人信息接口异常", e);
            return BaseRespDTO.fail("查询联系人信息接口异常");
        }
    }

    /**
     * 删除联系人
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/deleteContact/{id}", method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO deleteContact(@PathVariable(value = "id") Long id) {
        try {
            contactsService.deleteContact(id);
            return BaseRespDTO.success(id);
        } catch (Throwable e) {
            logger.error("删除联系人信息接口异常", e);
            return BaseRespDTO.fail("删除联系人信息接口异常");
        }
    }


    /**
     * 新增联系人
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO saveContact(@RequestBody RemitContactRequestVO remitContactRequest) {
        logger.info("新增联系人信息请求remitContactRequest = {}", new Gson().toJson(remitContactRequest));
        remitContactRequest.validateParam();
        try {
            ShiroUser user = super.getCurrentUser();
            String customerMember = null;
            String operator = null;
            if (user != null) {
                customerMember = user.getCustomerNumber();
                if (StringUtils.isBlank(customerMember)) {
                    return BaseRespDTO.fail("获取用户登陆信息异常");
                }
                operator = user.getLoginName();
                if (StringUtils.isBlank(operator)) {
                    return BaseRespDTO.fail("获取用户登陆信息异常");
                }
            }
            remitContactRequest.setCustomerNumber(customerMember);
            remitContactRequest.setUserNo(operator);
            contactsService.saveContact(remitContactRequest);
            return BaseRespDTO.success();
        } catch (Throwable e) {
            logger.error("新增联系人信息接口异常", e);
            return BaseRespDTO.fail("新增联系人信息接口异常");
        }
    }

    /**
     * 查询下级常用联系人的信息
     *
     * @param page
     * @param size
     * @param name          收款名称
     * @param bankAccountNo 收款账号
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/subMerchant/query", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO subMerchantContractsQuery(@RequestParam(value = "page") Integer page,
                                                 @RequestParam(value = "size") Integer size,
                                                 @RequestParam(value = "name", required = false) String name,
                                                 @RequestParam(value = "bankAccountNo", required = false) String bankAccountNo,
                                                 @RequestParam(value = "merchantNo", required = false) String merchantNo) {
        try {
            logger.info("查询下级常用联系人的信息, name：{}, bankAccountNo:{}, merchantNo = {} ", name, bankAccountNo, merchantNo);
            BasePageResDto<NewContactResDto> contactsList;
            ShiroUser user = super.getCurrentUser();
            String operator = null;
            String customerMember = null;
            if (user != null) {
                customerMember = user.getCustomerNumber();
                if (StringUtils.isBlank(customerMember)) {
                    return BaseRespDTO.fail("获取用户登陆信息异常");
                }
                operator = user.getLoginName();
                if (StringUtils.isBlank(operator)) {
                    return BaseRespDTO.fail("获取用户登陆信息异常");
                }
            }
            contactsList = contactsService.getContactsListByMerchantNo(customerMember, page, size, name, bankAccountNo, merchantNo);
            return BaseRespDTO.success(contactsList);
        } catch (Exception e) {
            logger.error("查询下级联系人信息接口异常", e);
            return BaseRespDTO.fail("查询下级联系人信息接口异常");
        }
    }

    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadContactRecord(ContactQueryParam contactQueryParam, HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("开始下载常用收款人管理数据，请求参数{}", new Gson().toJson(contactQueryParam));
        try {
            StringBuilder desc = new StringBuilder().append("常用收款人查询数据");
            if ("appointSync".equals(contactQueryParam.getSyncType())) {
                new ContactsDownloadService(getCurrentUser(), contactQueryParam, desc.toString(), "常用收款人查询-", contactsService).syncDownload(request, response);
            } else {
                new ContactsDownloadService(getCurrentUser(), contactQueryParam, desc.toString(), "常用收款人查询-", contactsService).download(request, response);
            }
            logger.info("常用收款人下载excel已完成");
        } catch (Exception ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }

    }

    @RequestMapping(value = "/subMerchant/download")
    @ResponseBody
    public void downloadSubMerchantContactRecord(ContactQueryParam contactQueryParam, HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("开始下载下级常用收款人管理数据，请求参数{}", new Gson().toJson(contactQueryParam));
        try {
            StringBuilder desc = new StringBuilder().append("下级常用收款人查询数据");
            contactQueryParam.setCustomerNumber(getCurrentCustomerNumber());
            new SubMerchantContactsDownloadService(getCurrentUser(), contactQueryParam, desc.toString(), "下级常用收款人查询-", contactsService).download(request, response);
            logger.info("下级常用收款人下载excel已完成");
        } catch (Exception ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }

    }
}
