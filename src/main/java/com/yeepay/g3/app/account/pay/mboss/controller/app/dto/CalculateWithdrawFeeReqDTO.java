package com.yeepay.g3.app.account.pay.mboss.controller.app.dto;

import com.yeepay.g3.app.account.pay.mboss.dto.BaseDto;
import com.yeepay.g3.facade.unionaccount.recharge.annotation.verify.NotEmpty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;

/**
 * @author: Mr.yin
 * @date: 2024/7/25  14:59
 */
@ApiModel(description = "计算手续费请求参数")

public class CalculateWithdrawFeeReqDTO extends BaseDto {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "订单金额")
    @DecimalMin(value = "0", message = "订单金额需大于等于0")
    @NotEmpty
    private String tradeAmount;

    /**
     * {@link com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum}
     */
    @ApiModelProperty(value = "账户类型")
    @NotEmpty
    private String accountType;

    /**
     * {@link com.yeepay.g3.facade.unionaccount.trade.enumtype.WithdrawTypeEnum}
     */
    @ApiModelProperty(value = "到账时效 code REAL_TIME、TWO_HOUR、NEXT_DAY")
    @NotEmpty
    private String arriveType;

    @ApiModelProperty(value = "当前余额")
    @NotEmpty
    private String balance;

    @ApiModelProperty(value = "交易类型 withdraw")
    @NotEmpty
    private String tradeType;

    public String getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(String tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getArriveType() {
        return arriveType;
    }

    public void setArriveType(String arriveType) {
        this.arriveType = arriveType;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }
}
