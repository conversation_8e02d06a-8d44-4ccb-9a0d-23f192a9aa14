package com.yeepay.g3.app.account.pay.mboss.dto.app.response;

import com.yeepay.g3.facade.unionaccount.manage.enumtype.AccountStatusEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class BankAccountInfo implements Serializable {
    /**
     * 账户编号
     */
    private String accountNo;

    /**
     * 账户类型
     *  SETTLE_ACCOUNT("待结算账户"),
     * 	FUND_ACCOUNT("商户支付账户"),
     * 	MARKET_ACCOUNT("营销账户"),
     * 	DIVIDE_ACCOUNT("待分账账户"),
     * 	MEMBER_ACCOUNT("个人支付账户");
     */
    private String accountType;

    private String accountTypeStr;

    /**
     * 账户开立时间
     */
    private Date createTime;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 账户状态
     *  AVAILABLE("可用"),
     *  FROZEN("冻结"),
     *  FROZEN_CREDIT("冻结止收"),
     *  FROZEN_DEBIT("冻结止付"),
     *  CANCELLATION("销户");
     */
    private AccountStatusEnum accountStatus;

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public AccountStatusEnum getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(AccountStatusEnum accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getAccountTypeStr() {
        return accountTypeStr;
    }

    public void setAccountTypeStr(String accountTypeStr) {
        this.accountTypeStr = accountTypeStr;
    }
}
