package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.BankCardOcrResultModel;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.CommonService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.ProvinceCityService;
import com.yeepay.g3.app.account.pay.mboss.utils.BacRsaKeysHolder;
import com.yeepay.g3.app.account.pay.mboss.utils.KJFileUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.req.ChangeCardInfoRequestDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.req.OpenAccountReRemitRequestDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.req.OpenAccountRemitAuthRequestDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.ChangeCardInfoResponseDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.OpenAccountReRemitResponseDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.OpenAccountRemitAuthResponseDTO;
import com.yeepay.g3.facade.auth2.ocr.dto.BankCardOCRResponse;
import com.yeepay.g3.facade.auth2.ocr.dto.BizLicenceOCRResponse;
import com.yeepay.g3.facade.auth2.ocr.dto.IdCardOCRResponse;
import com.yeepay.g3.facade.bank.management.facade.dto.CardBinConvertResultDTO;
import com.yeepay.g3.facade.bank.management.facade.dto.DistrictInfoDTO;
import com.yeepay.g3.facade.mer.dto.response.file.MerFileUploadHomeRespDto;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.utils.lock.Lock;
import com.yeepay.utils.lock.impl.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/13
 */
@Controller
@RequestMapping("/common")
@Api(tags = "公共管理-API")
public class CommonController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private CommonService commonService;

    /**
     * 分页和关键字查询银行信息
     *
     * @param page
     * @param pageSize
     * @param searchKeyWords 关键字
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/headBanks", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "分页和关键字查询银行信息")
    public BaseRespDTO<BankPageSearchResDTO> headBanks(@RequestParam(value = "page") Integer page, @RequestParam(value = "pageSize") Integer pageSize,
                                 @RequestParam(value = "searchKeyWords", required = false) String searchKeyWords) {
        try {
            logger.info("[/headBanks 查询总行] 接收到的请求参数 searchKeyWords={},page={},pageSize={}", searchKeyWords, page, pageSize);
            BankPageSearchResDTO bankPageSearchResDTO = ProvinceCityService.getAllBankByPage(page, pageSize, searchKeyWords);
            return BaseRespDTO.success(bankPageSearchResDTO);
        } catch (Throwable e) {
            logger.error("查询银行信息接口异常", e);
            return BaseRespDTO.fail("查询银行信息接口异常");
        }
    }

    /**
     * 分页和关键字查询支行信息
     *
     * @param page
     * @param pageSize
     * @param headBankCode
     * @param provinceCode
     * @param cityCode
     * @param searchKeyWords
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/queryBranchBanks", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("分页和关键字查询支行信息")
    public BaseRespDTO<BankPageSearchResDTO> queryBranchBanks(@RequestParam(value = "page", required = false) Integer page, @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                        @RequestParam(value = "headBankCode", required = false) String headBankCode, @RequestParam(value = "provinceCode", required = false) String provinceCode,
                                        @RequestParam(value = "cityCode", required = false) String cityCode, @RequestParam(value = "searchKeyWords", required = false) String searchKeyWords) {
        try {
            logger.info("[/queryBranchBanks 查询支行信息] 接收到的请求参数 searchKeyWords={},page={},pageSize={},headBankCode={},provinceCode={},cityCode={}", searchKeyWords, page, pageSize, headBankCode, provinceCode, cityCode);
            BankPageSearchResDTO bankPageSearchResDTO = ProvinceCityService.getAllBranchBankByPage(page, pageSize, headBankCode, provinceCode,
                    cityCode, searchKeyWords);
            return BaseRespDTO.success(bankPageSearchResDTO);
        } catch (Throwable e) {
            logger.error("查询银行信息接口异常", e);
            return BaseRespDTO.fail("查询银行信息接口异常");
        }
    }


    /**
     * 分页和关键字查询银行信息
     *
     * @return
     * @throws Exception
     * @describe (headBanks方法采用的是kmp模糊)
     */
    @RequestMapping(value = "/headBanks/vague/query", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "分页和关键字查询银行信息")
    public BaseRespDTO<BankPageSearchResDTO> vagueQueryHeadBanks(@RequestParam(value = "page") Integer page, @RequestParam(value = "pageSize") Integer pageSize,
                                                                 @RequestParam(value = "searchKeyWords", required = false) String searchKeyWords) {
        try {
            logger.info("[/headBanks/vague/query 查询总行] 接收到的请求参数 searchKeyWords={},page={},pageSize={}", searchKeyWords, page, pageSize);
            BankPageSearchResDTO bankPageSearchResDTO = ProvinceCityService.getVagueBankByPage(page, pageSize, searchKeyWords);
            return BaseRespDTO.success(bankPageSearchResDTO);
        } catch (Throwable e) {
            logger.error("查询银行信息接口异常", e);
            return BaseRespDTO.success();
        }
    }

    /**
     * 分页和关键字查询支行信息
     *
     * @return
     * @throws Exception
     * @describe (queryBranchBanks方法采用的是kmp模糊)
     */
    @RequestMapping(value = "/branchBanks/vague/query", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("分页和关键字查询支行信息")
    public BaseRespDTO<BankPageSearchResDTO> vagueQueryBranchBanks(@RequestParam(value = "page", required = false) Integer page, @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                   @RequestParam(value = "headBankCode", required = false) String headBankCode, @RequestParam(value = "provinceCode", required = false) String provinceCode,
                                                                   @RequestParam(value = "cityCode", required = false) String cityCode, @RequestParam(value = "searchKeyWords", required = false) String searchKeyWords) {
        try {
            if (StringUtils.isBlank(headBankCode)) {
                return BaseRespDTO.fail("请选择总行");
            }
            logger.info("[/branchBanks/vague/query 查询支行信息] 接收到的请求参数 searchKeyWords={},page={},pageSize={},headBankCode={},provinceCode={},cityCode={}", searchKeyWords, page, pageSize, headBankCode, provinceCode, cityCode);
            BankPageSearchResDTO bankPageSearchResDTO = ProvinceCityService.getVagueBranchByPage(page, pageSize, headBankCode, provinceCode,
                    cityCode, searchKeyWords);
            return BaseRespDTO.success(bankPageSearchResDTO);
        } catch (Throwable e) {
            logger.error("查询银行信息接口异常", e);
            return BaseRespDTO.success(e.getMessage());
        }
    }


    /**
     * 页面异步查询省对应的区
     *
     * @param request
     * @param parentCode
     * @return
     */
    @RequestMapping(value = "/queryAreas")
    @ResponseBody
    @ApiOperation("查询省对应的区")
    public BaseRespDTO<List<DistrictInfoDTO>> queryAreas(HttpServletRequest request,
                                  @RequestParam(value = "parentCode", required = false) String parentCode) {
        try {
            logger.info("[/queryAreas 查询省对应的区] 接收到的请求参数 parentCode={}", parentCode);

            List<DistrictInfoDTO> areaBaseDTOList = ProvinceCityService.getAreasV2(parentCode);
            return BaseRespDTO.success(areaBaseDTOList);
        } catch (Throwable e) {
            logger.error("查询省对应的区异常", e);
            return BaseRespDTO.fail("查询省对应的区异常");
        }
    }

    /**
     * 查询商户基本信息及资金账户余额
     *
     * @return
     */
    @RequestMapping("/merchant/infoAndFundBalance")
    @ResponseBody
    @ApiOperation(hidden = true,value="/merchant/infoAndFundBalance")
    public BaseRespDTO merchantInfo() {
        try {
            MerchantRespDTO merchantRespDTO = getCurrentMerchant();
            if (merchantRespDTO == null) {
                return BaseRespDTO.fail("未登陆/无法获取商户信息");
            }
            JSONObject jo = new JSONObject();
            jo.put("merchantNo", merchantRespDTO.getMerchantNo());
            jo.put("merchantName", merchantRespDTO.getSignName());
            jo.put("passWordPublicKey", BacRsaKeysHolder.getPasswordRsaPublicKey());
            jo.put("balance", BigDecimal.ZERO);
            AccountInfoRespDTO resp = businessCheckRemoteService.accountStatusAndBalance(merchantRespDTO.getMerchantNo(), AccountTypeEnum.FUND_ACCOUNT);
            if (resp != null) {
                jo.put("balance", resp.getBalance());
                jo.put("accountStatus", resp.getAccountStatus());
            }
            return BaseRespDTO.success(jo);
        } catch (Exception ex) {
            return BaseRespDTO.fail(ex.getMessage());
        }
    }


    /**
     * 证件上传到跨境的云储存
     *
     * @throws Exception
     */
    @RequestMapping(value = "/uploadCrossFile", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage uploadCrossFile(@RequestParam("file") MultipartFile req) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        String customerId = getCurrentCustomerNumber();
        InputStream in = null;
        FileOutputStream out = null;
        String filePath = this.getClass().getResource("/static/跨境审核文件.xlsx").getPath();
        String cephFilePath = "cbp/tradeDetails/" + customerId + "/" +
                com.yeepay.g3.utils.event.utils.DateUtils.format(new Date(), "yyyyMM") + "/" +
                "DETAIL/" + req.getOriginalFilename();
        File file = new File(filePath);
        try {
            in = req.getInputStream();
            out = new FileOutputStream(file);
            int len = 0;
            byte[] buf = new byte[1024];
            while ((len = in.read(buf)) != -1) {
                out.write(buf, 0, len);
            }
            out.flush();
            String encryptFile = KJFileUtils.upload(file, cephFilePath);
            resMsg.put("fileUrl", encryptFile);
            resMsg.put("fileName", req.getOriginalFilename());
        } catch (IOException e) {
            logger.error("上传图片格式有误,商编为={},异常信息为={}", customerId, e);
            resMsg.setErrCode("UA0001");
            resMsg.setErrMsg("上传文件有误");
            return resMsg;
        } finally {
            try {
                in.close();
                out.close();
            } catch (Exception e) {
                logger.error("上传图片关闭io流异常,商编为={},异常信息为={}", customerId, e);
            }
        }
        return resMsg;
    }

    /**
     * 证件上传云存储
     *
     * @throws Exception
     */
    @RequestMapping(value = "/upload",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("证件上传云储存")
    public ResponseMessage upload(@RequestParam("file") MultipartFile file) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        String customerId = getCurrentCustomerNumber();
        logger.info("证件上传云存储，请求商编为={}",customerId);
        byte[] data = null;
        InputStream in = null;
        try {
            in = file.getInputStream();
            data = new byte[in.available()];
            in.read(data);
        } catch (IOException e) {
            logger.error("上传图片格式有误,商编为={},异常信息为={}", customerId, e);
            resMsg.setErrCode("UA0001");
            resMsg.setErrMsg("上传文件有误");
            return resMsg;
        } finally {
            try {
                in.close();
            } catch (Exception e) {
                logger.error("上传图片关闭io流异常,商编为={},异常信息为={}", customerId, e);
            }
        }
        if (data.length >= 2 * 1024 * 1024) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("UA0001");
            resMsg.setErrMsg("传的图片大小不能超过2M");
            return resMsg;
        }
        String uploadFile = commonService.uploadBankAccountFile(data, file.getOriginalFilename());
        if (StringUtils.isNotBlank(uploadFile)) {
            resMsg.put("fileName", uploadFile);
        } else {
            logger.info("证件上传云存储失败，请求商编为={}", customerId);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("UA0002");
            resMsg.setErrMsg("上传资质文件失败");
        }
        return resMsg;
    }

    /**
     * ocr识别能力
     *
     * @param file
     */
    @RequestMapping(value = "/ocrRecogize",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("ocr识别能力")
    public ResponseMessage ocrRecogize(@RequestParam("file") MultipartFile file) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        String customerId = getCurrentCustomerNumber();
        logger.info("ocr识别日志，请求商编为={}",customerId);
        byte[] data = null;
        InputStream in = null;
        try {
            in = file.getInputStream();
            data = new byte[in.available()];
            in.read(data);
        } catch (IOException e) {
            logger.info("商户后台供应商图片格式有误", e);
            resMsg.setErrCode("UA0001");
            resMsg.setErrMsg("图片格式有误");
            return resMsg;
        } finally {
            try {
                in.close();
            } catch (Exception e) {
                logger.info("商户后台供应商ocr关闭io流异常={}", e);
            }
        }
        if(data.length >= 2 * 1048576){
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("UA0001");
            resMsg.setErrMsg("传的图片大小不能超过2M");
            return resMsg;
        }
        MerFileUploadHomeRespDto uploadFile = remoteService.uploadMerchantUrl(data, file.getOriginalFilename());
        if (uploadFile != null && StringUtils.isNotBlank(uploadFile.getMerQualUrl())) {
            resMsg.put("fileName", uploadFile.getMerQualUrl().replace("http","https"));
        }else {
            logger.info("证件上传云存储失败，请求商编为={}",customerId);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("UA0002");
            resMsg.setErrMsg("上传资质文件失败");
            return resMsg;
        }
        IdCardOCRResponse idCardOCRResponse = remoteService.recognize(data, customerId);
        if (idCardOCRResponse != null && "SUCCESS".equals(idCardOCRResponse.getStatus())) {
            resMsg.put("idCard", idCardOCRResponse.getIdCardNumber());
            resMsg.put("name", idCardOCRResponse.getName());
            resMsg.put("issueDate", convertTime(idCardOCRResponse.getIssueDate()));
            resMsg.put("validDate", convertTime(idCardOCRResponse.getValidDate()));
        } else {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("UA0003");
            resMsg.setErrMsg("请上传正确的身份证照");
        }
        return resMsg;
    }

    /**
     *
      * @param file
     * @return
     */
    @RequestMapping(value = "/enterpriseOcrAuth",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("营业执照ocr识别")
    public ResponseMessage enterpriseOcrAuth(@RequestParam("file") MultipartFile file) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        String customerId = getCurrentCustomerNumber();
        logger.info("营业执照ocr识别，请求商编为={}", customerId);
        InputStream in = null;
        String uploadFile = null;
        try {
            in = file.getInputStream();
            byte[] data = new byte[in.available()];
            in.read(data);
            if (data.length >= 2 * 1048576) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("图片大小不能超过2M");
            }
            uploadFile = commonService.uploadBankAccountFile(data, file.getOriginalFilename());
            if (StringUtils.isNotBlank(uploadFile)) {
                resMsg.put("fileName", uploadFile);
            } else {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("上传营业执照失败");
            }
            BizLicenceOCRResponse bizLicenceOCRResponse = remoteService.enterpriseOcrAuth(data, customerId);
            if (bizLicenceOCRResponse != null && "SUCCESS".equals(bizLicenceOCRResponse.getStatus())) {
                String creditCode = bizLicenceOCRResponse.getCreditCode();
                if (StringUtils.isEmpty(creditCode)) {
                    throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("未识别出营业执照代码，请上传正确的营业执照");
                }
                resMsg.put("certificateNo", creditCode);
            } else {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("识别失败，请上传正确的营业执照");
            }
        } catch (YeepayBizException e) {
            logger.error("营业执照识别业务异常, Cause by={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (IOException e) {
            logger.info("营业执照OCR,文件格式有误, Cause by=", e);
            resMsg.setErrCode("UA0001");
            resMsg.setErrMsg("文件格式有误");
            return resMsg;
        } catch (Exception e) {
            logger.error("营业执照识别异常, Cause by=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } finally {
            try {
                in.close();
            } catch (Exception e) {
                logger.error("营业执照OCR关闭io流异常=", e);
            }
            if (StringUtils.isNotBlank(uploadFile)) {
                resMsg.put("fileName", uploadFile);
            }
        }
        return resMsg;
    }

    /**
     *
     * @param file
     * @return
     */
    @RequestMapping(value = "/bankcardOcrAuth",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("银行卡ocr识别")
    public BaseRespDTO<BankCardOcrResultModel> bankcardOcrAuth(@RequestParam("file") MultipartFile file) {
        String customerId = getCurrentCustomerNumber();
        logger.info("银行卡ocr识别，请求商编为={}", customerId);
        BankCardOcrResultModel result = new BankCardOcrResultModel();
        InputStream in = null;
        try {
            in = file.getInputStream();
            byte[] data = new byte[in.available()];
            in.read(data);
            if (data.length >= 5 * 1048576) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("图片大小不能超过5M");
            }
            BankCardOCRResponse cardOCRResponse = remoteService.bankCardOcrAuth(data, customerId);
            logger.info("银行卡ocr识别 响应结果:{}", JSONUtils.toJsonString(cardOCRResponse));
            if (!"SUCCESS".equals(cardOCRResponse.getStatus()) || StringUtils.isEmpty(cardOCRResponse.getBankCardNumber())) {
                return BaseRespDTO.fail("识别失败，请手动填写内容");
            }
            result.setCardNo(cardOCRResponse.getBankCardNumber());
            CardBinConvertResultDTO convertCardBinInfo = remoteService.getConvertCardBinInfo(cardOCRResponse.getBankCardNumber());
            logger.info("银行卡bin转换结果:{}", JSONUtils.toJsonString(convertCardBinInfo));
            if (!CheckUtils.isEmpty(convertCardBinInfo)) {
                result.setBankCode(convertCardBinInfo.getBankId());
                result.setBankName(convertCardBinInfo.getBankName());
            }
        } catch (YeepayBizException e) {
            logger.error("银行卡ocr识别 业务异常, Cause by={}", e.getMessage());
            return BaseRespDTO.fail(e.getMessage());
        } catch (IOException e) {
            logger.info("银行卡ocr识别 文件异常, Cause by=", e);
            return BaseRespDTO.fail("图片文件格式有误");
        } catch (Exception e) {
            logger.error("银行卡ocr识别 系统异常, Cause by=", e);
            return BaseRespDTO.fail("系统异常");
        } finally {
            try {
                in.close();
            } catch (Exception e) {
                logger.error("银行卡ocr识别 io流关闭异常=", e);
            }
        }
        return BaseRespDTO.success(result);
    }

    private String convertTime(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }
        try {
            if ("长期".equals(param)) {
                return "forever";
            } else {
                SimpleDateFormat smf = new SimpleDateFormat("yyyyMMdd");
                return smf.format(DateUtils.parseDate(param, DateUtils.DATE_FORMAT_DATEONLY));
            }
        } catch (Throwable e) {
            logger.error("ocr识别处理时间格式错误,请求参数为={},异常为={}", param, e);
        }
        return null;
    }

    /**
     * 打款验证
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation("打款验证")
    @RequestMapping(value = "/remitAuth", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage remitAuth(@RequestBody OpenAccountRemitAuthParam reqDTO) {
        logger.info("打款验证请求reqDTO={}", JSONUtils.toJsonString(reqDTO));
        reqDTO.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            OpenAccountRemitAuthRequestDTO remitAuthRequestDTO = new OpenAccountRemitAuthRequestDTO();
            remitAuthRequestDTO.setRequestNo(reqDTO.getRequestNo());
            remitAuthRequestDTO.setAuthAmount(reqDTO.getAuthAmount());
            remitAuthRequestDTO.setMerchantNo(reqDTO.getMerchantNo());
            OpenAccountRemitAuthResponseDTO resp = remoteService.remitAuth(remitAuthRequestDTO);
            if (!"AM00000".equals(resp.getReturnCode())) {
                resMsg.setErrCode(resp.getReturnCode());
                resMsg.setErrMsg(resp.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            } else {
                resMsg.put("data", resp);
            }
        } catch (YeepayBizException e) {
            logger.warn("打款验证业务异常为={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("打款验证异常, Cause by=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 重新打款
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation("重新打款")
    @RequestMapping(value = "/openReRemit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage openReRemit(@RequestBody OpenAccountReRemitParam reqDTO) {
        logger.info("重新打款reqDTO={}", JSONUtils.toJsonString(reqDTO));
        reqDTO.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Assert.isTrue(!StringUtils.isEmpty(reqDTO.getMerchantNo()), "商户编号不能为空");
            Assert.isTrue(!StringUtils.isEmpty(reqDTO.getRequestNo()), "请求号不能为空");
        } catch (IllegalArgumentException e) {
            logger.warn("打款验证参数校验失败, Cause by={}", e.getMessage());
            resMsg.setErrCode(ErrorCode.PARAM_VALIDATE_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        }

        Lock lock = new RedisLock("OPEN_RE_REMIT" + "_" + reqDTO.getMerchantNo(), 4);
        try {
            if (lock.tryLock(3)) {
                OpenAccountReRemitRequestDTO reRemitRequestDTO = new OpenAccountReRemitRequestDTO();
                reRemitRequestDTO.setMerchantNo(reqDTO.getMerchantNo());
                reRemitRequestDTO.setRequestNo(reqDTO.getRequestNo());
                OpenAccountReRemitResponseDTO resp = remoteService.openAccountReRemit(reRemitRequestDTO);
                if (!"AM00000".equals(resp.getReturnCode())) {
                    resMsg.setErrCode(resp.getReturnCode());
                    resMsg.setErrMsg(resp.getReturnMsg());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                } else {
                    resMsg.put("data", resp);
                }
            } else {
                logger.warn("重新打款没有获取到锁资源，请求参数{}", JSONUtils.toJsonString(reqDTO));
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求过于频繁，请稍候重试");
            }
        } catch (YeepayBizException e) {
            logger.warn("打款验证业务失败, Cause by={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("打款验证异常, Cause by=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                logger.error("释放分布式锁异常为=", e);
            }
        }
        return resMsg;
    }


    /**
     * 变更卡信息
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation("变更卡信息")
    @RequestMapping(value = "/changeCardInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage changeCardInfo(@RequestBody ChangeCardInfoParam reqDTO) {
        logger.info("变更卡信息reqDTO={}", JSONUtils.toJsonString(reqDTO));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Assert.isTrue(!StringUtils.isEmpty(reqDTO.getMerchantNo()), "商户编号不能为空");
            Assert.isTrue(!StringUtils.isEmpty(reqDTO.getRequestNo()), "请求号不能为空");
            Assert.isTrue(!StringUtils.isEmpty(reqDTO.getBindCardNo()), "绑定银行卡号不能为空");
            Assert.isTrue(!StringUtils.isEmpty(reqDTO.getBindBankCode()), "绑定银行编码不能为空");
            Assert.isTrue(!StringUtils.isEmpty(reqDTO.getBranchBankNo()), "支行行号不能为空");
        } catch (IllegalArgumentException e) {
            logger.warn("变更卡信息校验失败, Cause by={}", e.getMessage());
            resMsg.setErrCode(ErrorCode.PARAM_VALIDATE_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        }

        Lock lock = new RedisLock("CHANGE_CARD_INFO" + "_" + reqDTO.getMerchantNo(), 4);
        try {
            if (lock.tryLock(3)) {
                ChangeCardInfoRequestDTO cardInfoRequestDTO = new ChangeCardInfoRequestDTO();
                cardInfoRequestDTO.setRequestNo(reqDTO.getRequestNo());
                cardInfoRequestDTO.setBindCardNo(reqDTO.getBindCardNo());
                cardInfoRequestDTO.setBindBankCode(reqDTO.getBindBankCode());
                cardInfoRequestDTO.setBranchBankNo(reqDTO.getBranchBankNo());
                cardInfoRequestDTO.setMerchantNo(reqDTO.getMerchantNo());

                ChangeCardInfoResponseDTO resp = remoteService.changeCardInfo(cardInfoRequestDTO);
                if (!"AM00000".equals(resp.getReturnCode())) {
                    resMsg.setErrCode(resp.getReturnCode());
                    resMsg.setErrMsg(resp.getReturnMsg());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                } else {
                    resMsg.put("data", resp);
                }
            } else {
                logger.warn("变更卡信息没有获取到锁资源，请求参数{}", JSONUtils.toJsonString(reqDTO));
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求过于频繁，请稍候重试");
            }
        } catch (YeepayBizException e) {
            logger.warn("变更卡信息失败, Cause by={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("变更卡信息异常, Cause by=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                logger.error("释放分布式锁异常为=", e);
            }
        }
        return resMsg;
    }

}
