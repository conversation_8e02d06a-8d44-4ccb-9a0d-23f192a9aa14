package com.yeepay.g3.app.account.pay.mboss.dto.app.response;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 15:51
 */
public class PaymentPageDetailResponseDTO implements Serializable {

    /**
     * 收款方账户名称
     */
    private String receiverAccountName;

    /**
     * 状态
     */
    private String status;

    /**
     * 到账金额
     */
    private String orderAmount;

    /**
     * 下单时间
     */
    private String createTime;

    /**
     * 订单编号
     */
    private String orderId;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReceiverAccountName() {
        return receiverAccountName;
    }

    public void setReceiverAccountName(String receiverAccountName) {
        this.receiverAccountName = receiverAccountName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
