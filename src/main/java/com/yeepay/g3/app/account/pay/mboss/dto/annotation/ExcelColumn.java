package com.yeepay.g3.app.account.pay.mboss.dto.annotation;

import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelColumn {

    /**
     * 描述
     * @return
     */
    String value() default "";

    /**
     * 列号
     * @return
     */
    int col() default 0;

    /**
     * 不能为空
     * @return
     */
    boolean notEmpty() default false;

    /**
     * 长度 0 不验证
     * @return
     */
    int length() default 0;
}
