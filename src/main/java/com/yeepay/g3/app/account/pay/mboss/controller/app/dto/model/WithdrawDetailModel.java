package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;

import com.yeepay.g3.facade.unionaccount.trade.enumtype.*;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Mr.yin
 * @date: 2024/7/25  16:51
 */
@ApiModel(description = "提现订单详情信息")

public class WithdrawDetailModel implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "易宝订单号")
    private String orderNo;

    @ApiModelProperty(value = "提现商户主体商编")
    private String fromCustomerNo;

    @ApiModelProperty(value = "提现商户名称")
    private String fromCustomerName;

    /**
     * {@link AccountTypeEnum}
     */
    @ApiModelProperty(value = "提现账户类型")
    private String accountType;

    /**
     * {@link AccountTypeEnum}
     */
    @ApiModelProperty(value = "提现账户类型 中文描述")
    private String accountTypeDesc;

    /**
     * {@link WithdrawTypeEnum}
     */
    @ApiModelProperty(value = "到账时效类型 REAL_TIME 实时;TWO_HOUR 2小时;NEXT_DAY隔日")
    private String arriveType;

    @ApiModelProperty(value = "到账银行及后4位卡号")
    private String bankNameAndCardNo;

    @ApiModelProperty(value = "操作员")
    private String operator;

    @ApiModelProperty(value = "提现附言 可能为空")
    private String remark;

    @ApiModelProperty(value = "提现创建时间")
    private String createTime;

    @ApiModelProperty(value = "提现金额")
    private String withdrawAmount;

    @ApiModelProperty(value = "扣款金额  可能为空")
    private String deductAmount;

    @ApiModelProperty(value = "到账金额 可能为空")
    private String receivedAmount;

    @ApiModelProperty(value = "手续费金额  可能为空")
    private String fee;

    @ApiModelProperty(value = "收费模式 REAL_TIME、UN_REAL_TIME、PREPAID_REAL（扣手续费账户）")
    private String feeChargeType;

    @ApiModelProperty(value = "提现状态 (REQUEST_RECEIVE 已接收;REQUEST_ACCEPT 已受理;;REMITING 处理中) 统一合并成处理中PROCESS,SUCCESS已到账;FAIL失败;REVERSED银行撤销;")
    private String status;

    @ApiModelProperty(value = "到账时间  可能为空")
    private String finishTime;

    @ApiModelProperty(value = "错误码  可能为空")
    private String returnCode;

    @ApiModelProperty(value = "失败原因  可能为空")
    private String returnMessage;

    public String getFeeChargeType() {
        return feeChargeType;
    }

    public void setFeeChargeType(String feeChargeType) {
        this.feeChargeType = feeChargeType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getFromCustomerNo() {
        return fromCustomerNo;
    }

    public void setFromCustomerNo(String fromCustomerNo) {
        this.fromCustomerNo = fromCustomerNo;
    }

    public String getFromCustomerName() {
        return fromCustomerName;
    }

    public void setFromCustomerName(String fromCustomerName) {
        this.fromCustomerName = fromCustomerName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountTypeDesc() {
        return accountTypeDesc;
    }

    public void setAccountTypeDesc(String accountTypeDesc) {
        this.accountTypeDesc = accountTypeDesc;
    }

    public String getArriveType() {
        return arriveType;
    }

    public void setArriveType(String arriveType) {
        this.arriveType = arriveType;
    }

    public String getBankNameAndCardNo() {
        return bankNameAndCardNo;
    }

    public void setBankNameAndCardNo(String bankNameAndCardNo) {
        this.bankNameAndCardNo = bankNameAndCardNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getWithdrawAmount() {
        return withdrawAmount;
    }

    public void setWithdrawAmount(String withdrawAmount) {
        this.withdrawAmount = withdrawAmount;
    }

    public String getDeductAmount() {
        return deductAmount;
    }

    public void setDeductAmount(String deductAmount) {
        this.deductAmount = deductAmount;
    }

    public String getReceivedAmount() {
        return receivedAmount;
    }

    public void setReceivedAmount(String receivedAmount) {
        this.receivedAmount = receivedAmount;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }
}
