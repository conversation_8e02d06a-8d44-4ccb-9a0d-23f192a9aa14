package com.yeepay.g3.app.account.pay.mboss.cache;

/**
 * @ClassName: MemoryCacheConstant
 * @Description: 内存缓存常量
 * <AUTHOR>
 * @Date 2022/11/9
 * @Version 1.0
 */
public class MemoryCacheConstant {
    /**
     * 内存过期时间10分钟
     */
    public static final long EXPIRE_TIME_MINUTES_10 = 10L;
    /**
     * 最大缓存数5000
     */
    public static final int MAX_KEY_SIZE_5000 = 5000;
    /**
     * 允许同时并发更新操作数 10
     */
    public static final int CONCURRENCY_LEVEL_10 = 10;

    /**
     * 缓存初始化条数-128
     */
    public static final int INITIAL_CAPACITY_128 = 128;

}
