package com.yeepay.g3.app.account.pay.mboss.dao;

import java.util.List;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorCreditNoRelaEntity;
import com.yeepay.g3.utils.persistence.GenericDao;

public interface DirectTransferOperatorCreditNoRelaDao extends GenericDao<DirectTransferOperatorCreditNoRelaEntity>  {
    
    
    List<DirectTransferOperatorCreditNoRelaEntity> queryByOperatorId(Long operatorId);

    
    void deleteByOperatorId(Long operatorId);
    
    
    List<DirectTransferOperatorCreditNoRelaEntity> queryByDebitCustomerAndLoginName(String debitCustomerNo,String loginName);
}

