package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 支付宝单笔代发
 */
@ApiModel(description = "支付宝单笔代发")
public class AliPaySingleProxyTransferDTO implements Serializable {
    /**
     * 记账本id
     */
    private String channelBookId;


    /**
     * 金额
     */
    private String totalAmount;


    /**
     * 收款账号
     */
    private String receiveNo;
    /**
     * 收款名称
     */
    private String receiveName;

    /**
     * 备注
     */
    private String remark;
    /**
     * 标题
     */
    private String title;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getReceiveNo() {
        return receiveNo;
    }

    public void setReceiveNo(String receiveNo) {
        this.receiveNo = receiveNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
