package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import com.yeepay.g3.facade.unionaccount.manage.annotation.verify.NotEmpty;

public class AliChannelBookOpenReqDTO {
    /**
     * 记账本名称
     */
    @NotEmpty(message = "记账本名称不能为空")
    private String channelBookName;

    private String merchantNo;

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getChannelBookName() {
        return channelBookName;
    }

    public void setChannelBookName(String channelBookName) {
        this.channelBookName = channelBookName;
    }
}
