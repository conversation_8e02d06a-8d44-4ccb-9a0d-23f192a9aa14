package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.constant.RemitConstant;
import com.yeepay.g3.app.account.pay.mboss.dto.BatchRemitAnalyResult;
import com.yeepay.g3.app.account.pay.mboss.dto.OrderParam;
import com.yeepay.g3.app.account.pay.mboss.dto.RemitQueryParam;
import com.yeepay.g3.app.account.pay.mboss.dto.RemitResponseParam;
import com.yeepay.g3.app.account.pay.mboss.entity.MerchantAccountInfoEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.RemitOrder;
import com.yeepay.g3.app.account.pay.mboss.enumtype.RemitStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.FileStorageService;
import com.yeepay.g3.app.account.pay.mboss.service.AccountManageInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.RemitOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RemitElectronicReceiptDownloader;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RemitOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.utils.DateUtil;
import com.yeepay.g3.app.account.pay.mboss.validate.CheckBlank;
import com.yeepay.g3.app.account.pay.mboss.validate.StrategyContext;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.facade.bac.dto.enumtype.NewRemitSendType;
import com.yeepay.g3.facade.bank.management.facade.SearchBankInfoFacade;
import com.yeepay.g3.facade.bank.management.facade.dto.HeadBankDTO;
import com.yeepay.g3.facade.bankinfo.service.BankInfoQueryFacade;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantinfoRespDTO;
import com.yeepay.g3.facade.mp.exception.ExceptionWrapper;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.mp.utils.StringUtil;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.TradeTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.*;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitBatchCheckRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitBatchDetailRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitBatchRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitOrderQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitOrderStatusEnum;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.TaskStatus;
import com.yeepay.g3.facade.unionaccount.trade.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.facade.unionaccount.trade.facade.RemitFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.yeepay.g3.app.account.pay.mboss.constant.NewRemitConstant.PAGE_NO_DEFAULT_VAL;
import static com.yeepay.g3.app.account.pay.mboss.constant.NewRemitConstant.PAGE_SIZE_DEFAULT_VAL;

/**
 * @ClassName: SupplierRemitController
 * @Description: 下级账户管理的批量付款
 * <AUTHOR>
 * @Date 2022/3/10
 * @Version 1.0
 */
@Controller
@RequestMapping("/subMerchant")
public class SubMerchantController extends BaseController {

    final static String[] headStr = {"订单号（非必填）", "下级商户编号（必填）", "收款方银行账号（必填）", "收款方开户名（必填）", "开户银行（对公付款必填）", "银行账户类型（非必填）", "付款金额（必填）", "支行名称（非必填）", "银行附言（非必填）","订单备注（非必填）"};


    private static final Logger logger = LoggerFactory.getLogger(SubMerchantController.class);


    private FileStorageService fileStorageService = new FileStorageService();


    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    private BankInfoQueryFacade bankInfoQueryFacade = RemoteServiceFactory.getService(BankInfoQueryFacade.class);

    @Autowired
    private RemitOrderService remitOrderService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    @Autowired
    private AccountManageInfoService accountManageInfoService;

    //企业付款
    private RemitFacade remitFacade = RemoteServiceFactory.getService(RemitFacade.class);

    private SearchBankInfoFacade searchBankInfoFacade = RemoteServiceFactory.getService(SearchBankInfoFacade.class);


    private BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();

    @RequestMapping(value = "/init/WTJS")
    public ModelAndView initWTJSSubmitPage(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String type = request.getParameter("tradeType");
        mav.addObject("hasAvailableStatus",true);
        List<String> secondProductList = new ArrayList<String>();
        secondProductList.add("URGENCY");
        secondProductList.add("COMMON");
        secondProductList.add("NEXT_DAY");
        mav.addObject("sendType", secondProductList);
        mav.setViewName("subMerchant/batchSendSubmit");
        mav.addObject("hasRemitProduct",true);
        mav.addObject("hasRJTProduct",false);
        logger.info("init wtjs batchRemit 返回值：" + JSONUtils.toJsonString(mav));
        return mav;
    }



    /**
     * 跳转充值记录页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/batchRemitQuery", method = RequestMethod.GET)
    public ModelAndView queryView(HttpServletRequest request) {

        ModelAndView mav = new ModelAndView();
        Map<String, String> bankMap = queryAllHeadBank();
        mav.addObject("bankMap", bankMap);
        mav.setViewName("subMerchant/batchRemitQuery");
        return mav;
    }

    /**
     * 下级联系人的页面
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/contact", method = RequestMethod.GET)
    public ModelAndView subMerchantContact(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("singleRemit/subMerchantContact");
        logger.info("下级常用联系人的页面菜单-{}", request.getAttribute("tabMenu"));
        return mv;
    }

    private AccountInfoRespDTO getRemitBalance(String currentCustomerNumber){
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        //查询余额
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(currentCustomerNumber, AccountTypeEnum.FUND_ACCOUNT);
        return queryAccountResponseDto;
    }

    /**
     * 下级管理中的企业付款
     * @return
     */
    @RequestMapping("/batchRemit")
    public ModelAndView batchRemit(){
        ModelAndView mav = new ModelAndView();
        mav.addObject("hasAvailableStatus",true);
        mav.setViewName("subMerchant/batchSendSubmit");
        List<String> secondProductList = new ArrayList<String>();
        secondProductList.add("URGENCY");
        secondProductList.add("COMMON");
        secondProductList.add("NEXT_DAY");
        mav.addObject("sendType", secondProductList);
        mav.addObject("hasRemitProduct",true);
        mav.addObject("productType", "WTJS");
        return mav;
    }



    public Map<String, String> queryAllHeadBank(){
        Map<String, String> returnMap = null;
        try {
            /*付款复核的查询列表 暂时先不改*/
            Map<String, String> bankMap = bankInfoQueryFacade.queryAllHeadBank();
            logger.debug("银行列表返回 bankMap={}", JSON.toJSONString(bankMap));
            // 根据Value值排序
            returnMap = sortMap(bankMap);
            logger.debug("银行列表排序返回 returnMap={}",JSON.toJSONString(returnMap));
        }catch (Exception e){
            logger.error("query BankInfoQueryFacade error ",e);
        }
        return returnMap;
    }

    /**
     * @param map
     * @return
     */
    private Map<String, String> sortMap(Map<String, String> map) {

        List<Map.Entry<String, String>> entries = new ArrayList<Map.Entry<String, String>>(map.entrySet());
        java.util.Collections.sort(entries, new Comparator<Map.Entry<String, String>>() {

            @Override
            public int compare(Map.Entry<String, String> obj1, Map.Entry<String, String> obj2) {
                int o1 = 0, o2 = 0;
                try {
                    o1 = getCnAscii(obj1.getValue().toCharArray()[0]);
                    o2 = getCnAscii(obj2.getValue().toCharArray()[0]);
                } catch (UnsupportedEncodingException e) {
                    o1 = 0;
                    o2 = 0;
                }
                if (o1 == o2) {
                    return 0;
                } else {
                    return o1 > o2 ? 1 : -1;
                }
            }
        });

        Map<String, String> map_link = new LinkedHashMap<String, String>();
        for (Map.Entry<String, String> entity : entries) {
            map_link.put(entity.getKey(), entity.getValue());
        }
        return map_link;

    }



    /**
     * @param cn
     * @throws UnsupportedEncodingException
     */
    private int getCnAscii(char cn) throws UnsupportedEncodingException {
        byte[] bytes = (String.valueOf(cn)).getBytes("GBK");
        if (bytes == null || bytes.length > 2 || bytes.length <= 0) {
            return 0;
        }
        if (bytes.length == 1) {
            return bytes[0];
        }
        if (bytes.length == 2) {
            int hightByte = 256 + bytes[0];
            int lowByte = 256 + bytes[1];

            return (256 * hightByte + lowByte) - 256 * 256;
        }

        return 0;
    }


    @RequestMapping(value = "/ajaxBatchSendConfirm")
    @ResponseBody
    public String ajaxBatchSendConfirm(HttpServletResponse response, HttpServletRequest request, @RequestParam("file") MultipartFile file,
                                       @RequestParam("sendType") NewRemitSendType remitSendType,
                                       @RequestParam("batchNo") String batchNo,
                                       @RequestParam("totalCount") Integer totalCount,
                                       @RequestParam("productType") String productType,
                                       @RequestParam("totalAmount") String totalAmount, @RequestParam("tradeType") String tradeType){

        logger.info("预提交接口入参： sendType：" + remitSendType + "， batchNo：" + batchNo + "， totalCount：" + totalCount + ", totalAmount: " + totalAmount + "，productType：" + productType+",tradeType:"+tradeType);

        BacNewBatchRemitController.ResponseMsg responseMsg = new BacNewBatchRemitController.ResponseMsg();
        try {
            String rediectUrl = "/subMerchant/batchSendConfirm?token=";

            //ip地址获取
            String ipAddress = NetUtils.getRemoteIP(request);
            logger.info("ip地址，ip={}",ipAddress);
            ShiroUser shiroUser = super.getCurrentUser();
            String customerNumber = super.getCurrentCustomerNumber();
            //生成全局唯一标识
            String token = customerNumber + System.currentTimeMillis();
            logger.info("预提交 生成的唯一标识：" + token);

            BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
            //获取营销产品码
            String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
            Map<String, Object> stringMap = preBatchSendConfirm(file, batchNo, totalCount, totalAmount, shiroUser, token, remitSendType.name(), productType.toUpperCase(),markProductCode,ipAddress,tradeType);
            if (stringMap.size() > 0) {
                responseMsg.setStatus(BacNewBatchRemitController.ResponseMsg.Status.FAILED);
                responseMsg.setRetMsg(stringMap);
            } else {
                responseMsg.setStatus(BacNewBatchRemitController.ResponseMsg.Status.SUCCESS);
                responseMsg.setToken(token);
                responseMsg.setRedirectUrl(rediectUrl + token);
                responseMsg.setRetMsg(stringMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("系统异常，", e);
            responseMsg.setStatus(BacNewBatchRemitController.ResponseMsg.Status.FAILED);
            responseMsg.setSystemCode(BacNewBatchRemitController.ResponseMsg.SystemCode.SYS_ERROR.name());
        }
        logger.info("ajax异步批量提交 返回信息： " + JSONUtils.toJsonString(responseMsg));
        return JSONUtils.toJsonString(responseMsg);
    }

    @RequestMapping(value = "/getFileResultPageByBatch")
    public ModelAndView getFileResultPage(HttpServletRequest request, @RequestParam("batchNo") String batchNo) {
        ModelAndView mav = new ModelAndView();
        Map<String, String> bankMap = queryAllHeadBank();
        mav.addObject("bankMap", bankMap);
        mav.addObject("batchNo", batchNo);
        mav.addObject("validResult", "");
        mav.setViewName("subMerchant/batchRemitQuery");
        return mav;
    }


    /**
     * @param remitQueryParam:
     * @Description: 查询付款订单列表（批量)
     */
    @RequestMapping(value = "/getFailResultPage")
    @ResponseBody
    public ResponseMessage queryOrderList(RemitQueryParam remitQueryParam,
                                          @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                          @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("查询付款订单，请求参数{}", JSON.toJSONString(remitQueryParam));

        if (remitQueryParam.isEmptyCheck()) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询参数为空");
            return resMsg;
        }

        String status = remitQueryParam.getStatus();
        if (status.equals(RemitStatusEnum.REQUEST_ACCEPT.name())) {
            status = RemitStatusEnum.REQUEST_ACCEPT.name() + "," + RemitStatusEnum.REQUEST_RECEIVE.name();
            remitQueryParam.setStatus(status);
        }
        QueryResult queryResult = null;
        try {
            //查询列表
            //处理收款账号
            String bankAccountNo = remitQueryParam.getBankAccountNo();
            if(StringUtils.isNotBlank(bankAccountNo)){
                remitQueryParam.setBankAccountNo(AESUtils.encryptDigest(bankAccountNo));
            }
            //处理收款方名称
            String receiverAccountName = remitQueryParam.getReceiverAccountName();
            if(StringUtils.isNotBlank(receiverAccountName)){
                remitQueryParam.setReceiverAccountName(AESUtils.encryptDigest(receiverAccountName));
            }
            queryResult = this.queryRemitOrderList(remitQueryParam, pageNo, pageSize);

        } catch (Exception e) {
            logger.error("queryRechargeOrderList,查询异常,e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }

        List<MerchantAccountInfoEntity> merchantAccountInfoEntityList = accountManageInfoService.queryMerchantList(getCurrentCustomerNumber());
        Map<String, String> merchantNameMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(merchantAccountInfoEntityList)) {
            merchantNameMap = merchantAccountInfoEntityList.stream().collect(Collectors.toMap(MerchantAccountInfoEntity::getMerchantNo,
                    MerchantAccountInfoEntity::getSignName, (key1, key2) -> key2));

        }
        MerchantRespDTO merchantRespDTO = getCurrentMerchant();
        merchantNameMap.put(merchantRespDTO.getMerchantNo(),merchantRespDTO.getSignName());

        if (queryResult != null) {
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                for (Map<String, Object> map : queryResult.getData()) {
                    //处理返回参数
                    adaptReturnResult(map,merchantNameMap);
                    String orderStatus = map.get("status").toString();
                    if(orderStatus.equals(RemitStatusEnum.REQUEST_ACCEPT.name()) || orderStatus.equals(RemitStatusEnum.REQUEST_RECEIVE.name())){
                        map.put("status",RemitStatusEnum.REQUEST_ACCEPT.getDesc());
                    }else{
                        String reversed = map.get("reversed").toString();
                        if(orderStatus.equals(RemitStatusEnum.SUCCESS.name()) && reversed.equals("1")){
                            map.put("status","银行冲退");
                        }else{
                            map.put("status",RemitStatusEnum.valueOf(orderStatus).getDesc());
                        }

                    }
                    if(map.get("trade_type")==null){
                        map.put("trade_type",Costants.ENTERPRISE_PAYMENT);
                    }
                }
            }
            resMsg.put("dataList", queryResult.getData());
        }
        //查询汇总信息
        resMsg = this.queryRemitOrderListSum(remitQueryParam, resMsg);
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        logger.info("查询充值订单列表返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }



    /**
     * 查询付款汇总
     *
     * @param remitQueryParam
     * @param resMsg
     */
    private ResponseMessage queryRemitOrderListSum(RemitQueryParam remitQueryParam, ResponseMessage resMsg) {

        Map<String, Object> queryMap = com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils.toMapWithResourceType(remitQueryParam);
        if(StringUtils.isNotBlank(remitQueryParam.getSubMerchant())) {
            queryMap.put("customerNumber", remitQueryParam.getSubMerchant());
        }
        queryMap.put("initMerchantNo", getCurrentCustomerNumber());
        queryMap.put("remark",remitQueryParam.getRemark());
        queryMap.put("notFirstProductCode","PAYMENT_SUPPLIER");
        if(!CheckUtils.isEmpty(remitQueryParam.getStatus())) {
            if("REVERSED".equals(remitQueryParam.getStatus())) {
                queryMap.put("status","SUCCESS");
                queryMap.put("reversed","1");
            }else {
                queryMap.put("reversednull","0");
            }
        }

        List<Map<String, Object>> withOrderListSum = null;
        try {
            withOrderListSum = QueryServiceUtil.query("accountTradeService", "queryRemitOrderListSum", queryMap);
        } catch (Exception e) {
            logger.error("queryRemitOrderListSum-参数异常", e);
            // 直接把异常信息返回
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }

        // 如果查询结果不为空的话
        if (withOrderListSum != null && !withOrderListSum.isEmpty()) {
            Map<String, Object> sumResult = withOrderListSum.get(0);
            String sum_amount = sumResult.get("sum_amount").toString();
            String sum_fee = sumResult.get("sum_fee").toString();
            if(StringUtils.isEmpty(sum_fee)){
                sum_fee = "0";
            }
            if(StringUtils.isEmpty(sum_amount)){
                sum_amount="0";
            }
            resMsg.getData().put("sum_count", sumResult.get("sum_count").toString());// 总笔数
            resMsg.getData().put("sum_amount", new BigDecimal(sum_amount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总金额
            resMsg.getData().put("sum_fee", new BigDecimal(sum_fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总手续费
            resMsg.getData().put("totalCount", sumResult.get("sum_count").toString());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", "0.00");// 总金额
            resMsg.getData().put("sum_fee", "0.00");// 总手续费
        }
        return resMsg;
    }



    private Map<String, Object> adaptReturnResult(Map<String, Object> detail,Map<String, String> merchantNameMap) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (null != detail.get("merchant_no")) {
                detail.put("merchant_no", detail.get("merchant_no").toString());
            }
            if(null != merchantNameMap && null != detail.get("merchant_no")){
                detail.put("merchant_name", merchantNameMap.get(detail.get("merchant_no").toString()));
            }
            if (null != detail.get("order_amount")) {
                detail.put("order_amount", nf.format(new BigDecimal(detail.get("order_amount").toString())));
            }
            if(null != detail.get("receive_amount")){
                detail.put("receive_amount", nf.format(new BigDecimal(detail.get("receive_amount").toString())));
            }
            if (null != detail.get("fee")) {
                if (null != detail.get("fee_undertaker_merchant_no") && !detail.get("merchant_no").toString().equals(detail.get("fee_undertaker_merchant_no").toString())) {
                    detail.put("fee", "");
                }else{
                    detail.put("fee", nf.format(new BigDecimal(detail.get("fee").toString())));
                }
            }
            //下单时间
            Object obj = detail.get("create_time");
            if (null != obj) {
                if (obj instanceof String) {
                    String str = String.valueOf(obj);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("create_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("create_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (obj instanceof Timestamp) {
                    detail.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

            //完成时间
            Object finishTime = detail.get("finish_time");
            if (null != finishTime) {
                if (finishTime instanceof String) {
                    String str = String.valueOf(finishTime);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("finish_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("finish_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (finishTime instanceof Timestamp) {
                    detail.put("finish_time", DateUtils.toSqlTimestampString((Timestamp) finishTime, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

            //付款时间
            Object reverseTime = detail.get("reverse_time");
            if (null != reverseTime) {
                if (reverseTime instanceof String) {
                    String str = String.valueOf(reverseTime);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("reverse_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("reverse_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (reverseTime instanceof Timestamp) {
                    detail.put("reverse_time", DateUtils.toSqlTimestampString((Timestamp) reverseTime, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

            String receiver_account_no = (String)detail.get("receiver_account_no");

            receiver_account_no = AESUtils.decryptWithBase64(receiver_account_no);

            String receiver_account_name = detail.get("receiver_account_name").toString();
            receiver_account_name= AESUtils.decryptWithBase64(receiver_account_name);
            /* 付款记录查询 结果转化银行名称 */
            HeadBankDTO headBankDTO = searchBankInfoFacade.searchHeadBankInfoByCode(detail.get("receiver_bank_code").toString());
            StringBuffer buffer = new StringBuffer();
            if(headBankDTO != null){
                String bankName = headBankDTO.getBankName();
                if (StringUtils.isNotBlank(bankName)) {
                    buffer.append(NumberUtils.subStringWithMaxLength(bankName, 45));
                    buffer.append("—");
                }
            }
            if(StringUtil.isNotEmpty(receiver_account_name)){
                buffer.append(NumberUtils.subStringWithMaxLength(receiver_account_name, 24));
            }
            if(StringUtil.isNotEmpty(receiver_account_no)){
                buffer.append("(" + receiver_account_no.substring(receiver_account_no.length() - 4, receiver_account_no.length()) + ")");
            }
            detail.put("receiver_account_name",buffer);
            if(detail.get("batch_no") == null){
                detail.put("batch_no","");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return detail;
    }

    /**
     * @Description: 查询付款订单
     * @param remitQueryParam
     * orderNoList:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     */
    private QueryResult queryRemitOrderList(RemitQueryParam remitQueryParam,int pageNo, int pageSize) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("batchNo",remitQueryParam.getBatchNo());
        queryMap.put("bankCode",remitQueryParam.getBankCode());
        queryMap.put("bankAccountNo",remitQueryParam.getBankAccountNo());
        queryMap.put("receiverAccountName",remitQueryParam.getReceiverAccountName());
        queryMap.put("requestNo",remitQueryParam.getRequestNo());
        queryMap.put("remitType",remitQueryParam.getRemitType());
        queryMap.put("status",remitQueryParam.getStatus());
        if(StringUtils.isNotBlank(remitQueryParam.getSubMerchant())) {
            queryMap.put("customerNumber", remitQueryParam.getSubMerchant());
        }
        queryMap.put("initMerchantNo", getCurrentCustomerNumber());
        queryMap.put("createStartDate",remitQueryParam.getCreateStartDate());
        queryMap.put("createEndDate",remitQueryParam.getCreateEndDate());
        queryMap.put("remark",remitQueryParam.getRemark());
        queryMap.put("notFirstProductCode","PAYMENT_SUPPLIER");
        if(StringUtils.isNotBlank(remitQueryParam.getTradeType())) {
            queryMap.put("tradeType", remitQueryParam.getTradeType());
        }

        if(!CheckUtils.isEmpty(remitQueryParam.getStatus())) {
            if("REVERSED".equals(remitQueryParam.getStatus())) {
                queryMap.put("status","SUCCESS");
                queryMap.put("reversed","1");
            }else {
                queryMap.put("reversednull","0");
            }
        }
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("queryRemitOrderList", queryParam);

    }

    public Map<String, Object> preBatchSendConfirm(MultipartFile file,
                                                   String batchNo,
                                                   Integer totalCount,
                                                   String totalAmount,
                                                   ShiroUser shiroUser,
                                                   String token,
                                                   String remitSendType,
                                                   String productType,String markProductCode,String ipAddress,String tradeType) throws Exception {


        Map<String, Object> map = new HashMap<>();

        //1, 校验文件类型
        String filename = file.getOriginalFilename();
        if (filename == null
//                || !(ExcelUtil.isExcel2003(filename))
        ) {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.FILETYPE_ERROR.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.FILETYPE_ERROR.getValue());
            map.put("file", hashMap);
        }

        //2,批次号校验
        if (StringUtils.isNotBlank(batchNo)) {
            String batchNoRegx = "[a-z|A-Z|0-9]{1,15}";
            boolean matches = batchNo.matches(batchNoRegx);
            if (!matches) {
                Map<String, String> hashMap = new HashMap<>();
                hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.BATCHNO_FORMAT_ERROR.getName());
                hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.BATCHNO_FORMAT_ERROR.getValue());
                map.put("batchNo", hashMap);
            }

            //查询批次信息
            RemitBatchInfoReqDTO dto = new RemitBatchInfoReqDTO();
            dto.setBatchNo(batchNo);
            dto.setMerchantNo(shiroUser.getCustomerNumber());
            RemitBatchRespDTO remitBatchRespDTO = remitFacade.queryBatchInfo(dto);
            String batch_no = remitBatchRespDTO.getBatchNo();
            if(StringUtils.isNotEmpty(batch_no)){
                Map<String, String> hashMap = new HashMap<>();
                hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.BATCHNO_REPEAT_ERROR.getName());
                hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.BATCHNO_REPEAT_ERROR.getValue());
                hashMap.put("url", "/subMerchant/getFileResultPageByBatch?batchNo=" + batchNo);
                map.put("batchNo", hashMap);
                return map;
            }

        } else {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.BATCHNO_FORMAT_ERROR.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.BATCHNO_FORMAT_ERROR.getValue());
            map.put("batchNo", hashMap);
        }

        //3,总笔数，总金额
        if (totalCount > 1000) {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_OVER.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_OVER.getValue());
            map.put("totalCount", hashMap);
        }
        if (totalCount <= 0) {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_LESS.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_LESS.getValue());
            map.put("totalCount", hashMap);
        }

        String regx = "^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
        boolean matches = totalAmount.matches(regx);
        if (!matches || totalAmount.length() > 13) {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.TOTAL_AMOUNT_ERROR.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.TOTAL_AMOUNT_ERROR.getValue());
            map.put("totalAmount", hashMap);
        }
        //解析excel文件
        analyseFile(file, token, map, batchNo, remitSendType, productType, shiroUser,markProductCode,ipAddress,tradeType);

        if (map.containsKey("file") || map.containsKey("batchNo")) {
            return map;
        }
        Map<String, String> hmget = null;
        try {
            hmget = RedisUtils.hgetall(token);
        } catch (Exception e) {
            logger.error("调用redis hgetall异常", e);
            e.printStackTrace();
        }
        if (null == hmget || hmget.size() < 1) {
            logger.error("redis取值失败，系统异常");
            throw new Exception("系统异常：读取redis失败");
        }

        //校验是否大于1000笔
        Integer after_totalCount = new Integer(hmget.get("totalCount"));
        if (after_totalCount > 1000) {
            Map<String, String> map1 = new HashMap<>();
            map1.put("errorCode", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_OVER.getName());
            map1.put("description", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_OVER.getValue());
            map.put("totalCount", map1);
        }
        if (after_totalCount <= 0) {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_LESS.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_LESS.getValue());
            map.put("file", hashMap);
        }

        BigDecimal decimal_totalamount = new BigDecimal(totalAmount);
        if (decimal_totalamount.compareTo(new BigDecimal(hmget.get("totalAmount"))) != 0) {
            Map<String, String> map1 = new HashMap<>();
            map1.put("errorCode", BacNewBatchRemitController.ERROR_CODE.TOTAL_AMOUNT_MATCH_ERROR.getName());
            map1.put("description", BacNewBatchRemitController.ERROR_CODE.TOTAL_AMOUNT_MATCH_ERROR.getValue());
            map.put("totalAmount", map1);
        }
        if (totalCount.compareTo(after_totalCount) != 0) {
            Map<String, String> map2 = new HashMap<>();
            map2.put("errorCode", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_MATCH_ERROR.getName());
            map2.put("description", BacNewBatchRemitController.ERROR_CODE.TOTAL_COUNT_MATCH_ERROR.getValue());
            map.put("totalCount", map2);
        }
        return map;
    }

    private void analyseFile(MultipartFile file,
                             String token,
                             Map<String, Object> map,
                             String batchNo,
                             String remitSendType,
                             String productType,
                             ShiroUser shiroUser,String markProductCode,String ipAddress,String tradeType) {

        Workbook wb = null;
        try {
            wb = WorkbookFactory.create(file.getInputStream());
        } catch (Exception e) {
            logger.error("生成异常", e);
            // 空指针会被转化为出款批次重复有点吓人，还是具体的写清楚吧
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.FILEPARSE_ERROR.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.FILEPARSE_ERROR.getValue());
            map.put("file", hashMap);
            return;
        }
        Sheet sheet = wb.getSheetAt(0);
        Row head = sheet.getRow(0);
        // 校验文件头
        if (!checkHead(head)) {
            logger.error("文件头不准确");
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.FILEHEAD_ERROR.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.FILEHEAD_ERROR.getValue());
            map.put("file", hashMap);
            return;
        }
        try {
            //校验文件体,并且保存基本信息，写新文件，异常返回"文件解析"
            processBody(map,FileUtil.multipartFileToFile(file), token, batchNo, remitSendType, productType, shiroUser,markProductCode,ipAddress,tradeType);

        } catch (Exception e) {
            logger.error("token: " + token + " 文件解析异常", e);
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.FILEPARSE_ERROR.getName());
            hashMap.put("description", BacNewBatchRemitController.ERROR_CODE.FILEPARSE_ERROR.getValue());
            map.put("file", hashMap);
            return;
        }


    }
    private boolean checkHead(Row head) {
        if(head==null){
            return false;
        }
        for (int i1 = 0; i1 < headStr.length; i1++) {
            String strValue = POIUtil.getStrValue(head.getCell(i1));
            // 兼容历史模版
            if(i1 == 4 && Objects.equals(strValue, "开户银行（必填）")){
                continue;
            }
            if (StringUtils.equals(strValue, headStr[i1])) {
                continue;
            } else {
                return false;
            }
        }
        return true;
    }



    private void processBody(Map<String, Object> hashMap, File file, String token, String batchNo, String remitSendType, String productType, ShiroUser shiroUser, String markProductCode, String ipAddress, String tradeType) throws Exception {

        //文件复制
        File destFile = new File(System.getProperty("java.io.tmpdir") + token + ".xls");
        FileUtil.copyFile(file, destFile);
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(destFile);
        } catch (Exception e) {
            logger.error("复制后的文件读取失败");
            throw new Exception("复制后的文件读取失败");
        }
        //处理单条记录
        Workbook wb = WorkbookFactory.create(inputStream);
        List<RemitBatchDetailReqDTO> remitBatchDetailList = buildBatchDetailData(wb, remitSendType, productType, shiroUser.getCustomerNumber(), batchNo,ipAddress,tradeType);
        if(CollectionUtils.isNotEmpty(remitBatchDetailList)){
            RemitBatchCheckReqDTO batchCheckReqDTO = new RemitBatchCheckReqDTO();
            batchCheckReqDTO.setBatchNo(batchNo);
            batchCheckReqDTO.setOperatorName(shiroUser.getLoginName());
            batchCheckReqDTO.setMerchantNo(shiroUser.getCustomerNumber());
            if (NewRemitSendType.NEXT_DAY.name().equals(remitSendType)) {
                batchCheckReqDTO.setRemitType(RemitTypeEnum.NEXT_DAY.name());
            } else if (NewRemitSendType.COMMON.name().equals(remitSendType)) {
                batchCheckReqDTO.setRemitType(RemitTypeEnum.TWO_HOUR.name());
            } else if (NewRemitSendType.URGENCY.name().equals(remitSendType)) {
                batchCheckReqDTO.setRemitType(RemitTypeEnum.REAL_TIME.name());
            }
            batchCheckReqDTO.setRemitBatchDetailList(remitBatchDetailList);
            batchCheckReqDTO.setSaleProductCode(markProductCode);
            batchCheckReqDTO.setFileName(file.getName());
            batchCheckReqDTO.setStorageFileName(token);
            batchCheckReqDTO.setCheckSubMerchant(true);
            //返回
            RemitBatchCheckRespDTO remitBatchCheckRespDTO = remitFacade.batchCheckRemitParam(batchCheckReqDTO);
            logger.info("批量校验参数返回"+JSON.toJSONString(remitBatchCheckRespDTO));
            String returnCode = remitBatchCheckRespDTO.getReturnCode();
            Integer validExceptionCount = remitBatchCheckRespDTO.getValidExceptionCount();
            if("UA00000".equals(returnCode)){
                List<RemitBatchDetailRespDTO> batchDetailList = remitBatchCheckRespDTO.getBatchDetailList();
                if(CollectionUtils.isNotEmpty(batchDetailList)){
                    writeBackExcel(wb,batchDetailList);
                }
            }else if("UA30013".equals(returnCode)){
                Map<String, String> hashMap1 = new HashMap<>();
                hashMap1.put("errorCode", BacNewBatchRemitController.ERROR_CODE.ORDER_REPETE.getName());
                hashMap1.put("description", BacNewBatchRemitController.ERROR_CODE.ORDER_REPETE.getValue());
                hashMap.put("totalAmount", hashMap1);
            }
            //发往云存储
            FileOutputStream outputStream = null;
            try {
                //如果全部校验通过，则上传原文件； 有校验失败，则上传新文件
                if (validExceptionCount > 0) {
                    outputStream = new FileOutputStream(destFile);
                } else {
                    outputStream = new FileOutputStream(file);
                }
                wb.write(outputStream);
            } catch (Exception e) {
                logger.error("系统处理异常", e);
                throw new Exception("系统处理异常");
            } finally {
                outputStream.close();
                logger.info("开始发往云存储");
                fileStorageService.uploadFileCloud(token + ".xls", new FileInputStream(destFile));
            }

            Map<String, String> map = new HashMap<>();
            Integer validSuccessCount = remitBatchCheckRespDTO.getValidSuccessCount();
            map.put("totalCount", remitBatchCheckRespDTO.getTotalCount().toString());
            map.put("totalAmount", remitBatchCheckRespDTO.getTotalAmount().toString());
            map.put("validExceptionCount", (validExceptionCount == null)? "":validExceptionCount.toString());
            map.put("validExceptionAmount", (remitBatchCheckRespDTO.getValidExceptionAmount()==null)?"":remitBatchCheckRespDTO.getValidExceptionAmount().toString());
            map.put("validSuccessCount", (validSuccessCount== null)? "":validSuccessCount.toString());
            map.put("validSuccessAmount", (remitBatchCheckRespDTO.getValidSuccessAmount() == null)?"":remitBatchCheckRespDTO.getValidSuccessAmount().toString());
            map.put("batchNo", batchNo);
            map.put("remitSendType", remitSendType);
            map.put("productType", "WTJS");
            map.put("loginName", shiroUser.getLoginName());
            map.put("fileName", file.getName());
            // 是否疑似重复
            Map<String, String> md5Map = remitBatchCheckRespDTO.getMd5Map();
            List<BatchRemitAnalyResult> batchRemitAnalyResults = null;
            if (batchRemitAnalyResults != null && batchRemitAnalyResults.size() > 0 && validExceptionCount == 0 && validSuccessCount > 0) {
                logger.info("batch:{}, 存在疑似重复订单");
                map.put("isRepeated", "true");
                map.put(getAnalyseRedisKey(shiroUser.getCustomerNumber(), batchNo), JSONUtils.toJsonString(batchRemitAnalyResults));
            } else {
                map.put("isRepeated", "false");
            }
            //保存解析结果
            if (remitBatchCheckRespDTO.getTaskId() != null) {
                map.put("taskId", remitBatchCheckRespDTO.getTaskId().toString());
            }
            logger.info("发往redis的值： " + JSONUtils.toJsonString(map));
            /*md5保存在redis中*/
            try {
                String detailRedisKey = getDetailRedisKey(shiroUser.getCustomerNumber(), batchNo);
                if (RedisUtils.exists(detailRedisKey)) {
                    RedisUtils.delete(detailRedisKey);
                }
                RedisUtils.hmset(token, map, ConfigUtils.getBatchSendExpire());
                //保存md5明细
            } catch (Exception e) {
                logger.error("保存明细md5 到redis失败", e);
            }
        }else{
            Map<String, String> hashMap1 = new HashMap<>();
            hashMap1.put("errorCode", BacNewBatchRemitController.ERROR_CODE.FILEEMPTY_ERROR.getName());
            hashMap1.put("description", BacNewBatchRemitController.ERROR_CODE.FILEEMPTY_ERROR.getValue());
            hashMap.put("file", hashMap1);
        }


    }



    private List<RemitBatchDetailReqDTO> buildBatchDetailData(Workbook wb, String remitSendType, String productType, String customerNumber, String batchNo,String ipAddress,String tradeType) {


        Sheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        //填充单元格
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        //填黄色
        cellStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
        //新文件追加一列
        Row headRow = sheet.getRow(0);
        Cell cell1 = headRow.createCell(10);
        cell1.setCellValue("异常原因");


        List<RemitBatchDetailReqDTO> batchDetailList = new ArrayList<>();

        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            RemitBatchDetailReqDTO reqDto = new RemitBatchDetailReqDTO();
            Row row = sheet.getRow(i);
            // 去除row 无值的情况
            if (row == null) {
                continue;
            } else {
                //账户名称 银行账号 开户银行 金额 有为空项 认为是无效数据
                StrategyContext strategyContext = new StrategyContext(new CheckBlank(cellStyle, wb), row);
                String context = strategyContext.context();
                if (StringUtils.equals("true", context)) {
                    logger.info("第" + i + "行， 数据无效");
                    continue;
                }
            }
            //序号
//            String strValue0 = POIUtil.getStrValue(row.getCell(0));
//            reqDto.setSerialNumber(strValue0);
            //订单号
            String strValue1 = POIUtil.getStrValue(row.getCell(0));
            logger.info("商户订单号：requestNo={}",strValue1);
            reqDto.setRequestNo(strValue1);

            //下级商户号
            String subMerchantNo = POIUtil.getStrValue(row.getCell(1));
            reqDto.setSubMerchantNo(subMerchantNo);

            //银行账号
            String strValue2 = POIUtil.getStrValue(row.getCell(2));
            reqDto.setReceiverAccountNo(strValue2);
            //开户名
            String strValue3 = POIUtil.getStrValue(row.getCell(3));
            reqDto.setReceiverAccountName(strValue3);
            //开户银行名称
            String strValue4 = POIUtil.getStrValue(row.getCell(4));
            reqDto.setBankName(strValue4);
            //账户类型
            String strValue5 = POIUtil.getStrValue(row.getCell(5));
            reqDto.setBankAccountType(strValue5);
            //付款金额
            String strValue6 = POIUtil.getStrValue(row.getCell(6));
            reqDto.setOrderAmount(strValue6);
            //支行名称
            String strValue7 = POIUtil.getStrValue(row.getCell(7));
            reqDto.setBankBranchName(strValue7);
            //银行附言
            String strValue8 = POIUtil.getStrValue(row.getCell(8));
            reqDto.setComments(strValue8);
            //订单描述
            String strValue9 = POIUtil.getStrValue(row.getCell(9));
            reqDto.setTradeType(tradeType);
            reqDto.setOrderDesc(strValue9);
            //行号
            reqDto.setLineNumber(i);
            reqDto.setClientIp(ipAddress);
            batchDetailList.add(reqDto);

        }
        return batchDetailList;
    }



    /**
     * 获取存放在redis中的明细key
     *
     * @param customerNumber
     * @param batchNo
     * @return
     */
    private String getDetailRedisKey(String customerNumber, String batchNo) {
        logger.info("开始组装 detail 的Rediskey， customerNumber:{}, batchNO:{}", customerNumber, batchNo);
        return customerNumber + "_" + batchNo + "_detail";
    }


    /**
     * 组装存放分析结果的Rediskey
     *
     * @param customerNumber
     * @param batchNo
     * @return
     */
    public static String getAnalyseRedisKey(String customerNumber, String batchNo) {
        logger.info("开始组装 analyseResult 的Rediskey， customerNumber:{}, batchNO:{}", customerNumber, batchNo);
        return customerNumber + "_" + batchNo + "_analyseResult";
    }


    /**
     * 付款确认页面
     * @param token
     * @return
     */
    @RequestMapping(value = "/batchSendConfirm")
    public ModelAndView batchSendConfirm(@RequestParam("token") String token) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("subMerchant/batchSendConfirm");

        Map<String, String> hgetall = RedisUtils.hgetall(token);
        String totalCount = hgetall.get("totalCount");
        String totalAmount = hgetall.get("totalAmount");
        String validExceptionCount = hgetall.get("validExceptionCount");
        String validExceptionAmount = hgetall.get("validExceptionAmount");
        String validSuccessCount = hgetall.get("validSuccessCount");
        String validSuccessAmount = hgetall.get("validSuccessAmount");
        String batchNo = hgetall.get("batchNo");
        String isRepeated = hgetall.get("isRepeated");

        logger.info("校验数量："+JSON.toJSONString(hgetall));
        if(StringUtils.isEmpty(validSuccessAmount)){
            validSuccessAmount ="0";
        }
        if(StringUtils.isEmpty(validSuccessCount)){
            validSuccessCount="0";
        }
        if(StringUtils.isEmpty(validExceptionAmount)){
            validExceptionAmount="0";
        }
        if(StringUtils.isEmpty(validExceptionCount)){
            validExceptionCount="0";
        }
        Integer totalCount_ = Integer.parseInt(totalCount);
        Integer validExceptionCount_ = Integer.parseInt(validExceptionCount);

        modelAndView.addObject("totalCount", Integer.parseInt(totalCount==""?"0":totalCount));
        modelAndView.addObject("totalAmount", NumberUtils.formateNum(null, new BigDecimal(totalAmount)));
        modelAndView.addObject("validExceptionCount", Integer.parseInt(validExceptionCount));
        modelAndView.addObject("validExceptionAmount", NumberUtils.formateNum(null, new BigDecimal(validExceptionAmount)));
        modelAndView.addObject("validSuccessCount", Integer.parseInt(validSuccessCount));
        modelAndView.addObject("validSuccessAmount", NumberUtils.formateNum(null, new BigDecimal(validSuccessAmount)));

        modelAndView.addObject("batchNo", batchNo);
        modelAndView.addObject("isRepeated", isRepeated);
        modelAndView.addObject("token", token);
        String result = "";

        if (validExceptionCount_ == 0) {
            result = "全部校验成功";
        } else if (totalCount_.equals(validExceptionCount_)) {
            result = "全部校验异常";
        } else {
            result = "部分校验异常";
        }
        modelAndView.addObject("result", result);

        return modelAndView;

    }

    @RequestMapping(value = "/downBatchDetail")
    public void downBatchDetail(@RequestParam("token") String token, HttpServletRequest request, HttpServletResponse response) {

        logger.info("token：{}, 开始下载异常文件...", token);
        //根据token 下载云存储的文件
        if (StringUtils.isBlank(token)) {
            logger.error("下载接口 token 为空");
            return;
        }
        //下载
        String cloudName = token + ".xls";
        List<String> lists = RedisUtils.hmget(token, new String[]{"fileName"});
        String fileName = null;
        if (null != lists && lists.size() > 0) {
            String list = lists.get(0);
            String substring = list.substring(0, list.lastIndexOf("."));
            fileName = substring + "(异常原因)" + list.substring(list.lastIndexOf("."));
        }
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            inputStream = fileStorageService.downloadFileCloud(cloudName);
            response.setHeader("Content-disposition", "attachment; filename=" + new String(URLEncoder.encode(fileName, "utf-8")));
            response.setHeader("Content-Type", "application/octet-stream");
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[2048];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            inputStream.close();
            outputStream.flush();
            logger.info("下载成功， token：" + token);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("下载失败", e);
        }

    }


    /**
     * 安全验证
     * @param token
     * @return
     */
    @RequestMapping(value = "/batchSendSafetyVerify")
    public ModelAndView batchSendSafetyVerify(@RequestParam("token") String token) {
        ModelAndView view = new ModelAndView();
        view.setViewName("subMerchant/batchSendSafetyVerify");
        view.addObject("token", token);
        return view;

    }

    /**
     * 查看结果页面
     * @param request
     * @param taskId
     * @return
     */
    @RequestMapping(value = "/batchPayResult")
    public ModelAndView batchPayResultPage(HttpServletRequest request, @RequestParam("taskId") String taskId) {
        ModelAndView mav = new ModelAndView();
        mav.addObject("taskId", taskId);
        mav.setViewName("subMerchant/batchPayResult");
        return mav;
    }


    /**
     * 跳转到查询批量结果的页面
     * @param request
     * @param taskId
     * @param validResult
     * @return
     */
    @RequestMapping(value = "/getFileResultPage")
    public ModelAndView getFileResultPage(HttpServletRequest request, @RequestParam("taskId") String taskId, @RequestParam("validResult") String validResult) {
        ModelAndView mav = new ModelAndView();
        //获取银行列表
        Map<String, String> bankMap = queryAllHeadBank();
        mav.addObject("bankMap", bankMap);
        RemitBatchInfoReqDTO reqDTO  = new RemitBatchInfoReqDTO();
        reqDTO.setTaskId(Long.valueOf(taskId));
        reqDTO.setMerchantNo(getCurrentCustomerNumber());
        RemitBatchRespDTO remitBatchRespDTO = remitFacade.queryBatchInfo(reqDTO);
        mav.addObject("batchNo", remitBatchRespDTO.getBatchNo());
        mav.addObject("validResult", validResult);
        mav.setViewName("subMerchant/batchRemitQuery");
        return mav;
    }

    private void writeBackExcel(Workbook wb,List<RemitBatchDetailRespDTO> batchDetailList) {

        Sheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        //填充单元格
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        //填黄色
        cellStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
        //新文件追加一列
        Row headRow = sheet.getRow(0);
        Cell cell1 = headRow.createCell(10);
        cell1.setCellValue("异常原因");

        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            for(RemitBatchDetailRespDTO dto:batchDetailList){
                Integer lineNumber = dto.getLineNumber();

                if(lineNumber == i){
                    Row row = sheet.getRow(i);

                    String requestNo = dto.getRequestNo();
                    if(!(requestNo == null)){
                        Cell cell = row.getCell(0);
                        cell.setCellStyle(cellStyle);
                    }

                    //下级商编
                    String subMerchantNo = dto.getSubMerchantNo();
                    if(!(subMerchantNo == null)){
                        Cell cell = row.getCell(1);
                        cell.setCellStyle(cellStyle);
                    }
                    //账号
                    String bankAccountNo = dto.getReceiverAccountNo();
                    if(!(bankAccountNo == null)){
                        Cell cell = row.getCell(2);
                        cell.setCellStyle(cellStyle);
                    }
                    //开户名
                    String bankAccountName = dto.getReceiverAccountName();
                    if(!(bankAccountName == null)){
                        Cell cell = row.getCell(3);
                        cell.setCellStyle(cellStyle);
                    }
                    //银行名称
                    String bankName = dto.getBankName();
                    if(!(bankName == null)){
                        Cell cell = row.getCell(4);
                        cell.setCellStyle(cellStyle);
                    }
                    //卡类型
                    String bankAccountType = dto.getBankAccountType();
                    if(!(bankAccountType == null)){
                        Cell cell = row.getCell(5);
                        cell.setCellStyle(cellStyle);
                    }
                    //金额
                    String amountStr = dto.getOrderAmount();
                    if(!(amountStr == null)){
                        Cell cell = row.getCell(6);
                        cell.setCellStyle(cellStyle);
                    }
                    //支行名称
                    String  bankBranchName = dto.getBankBranchName();
                    if(!(bankBranchName == null)){
                        Cell cell = row.getCell(7);
                        cell.setCellStyle(cellStyle);
                    }
                    //银行附言
                    String comments = dto.getComments();
                    if(!(comments == null)){
                        Cell cell = row.getCell(8);
                        cell.setCellStyle(cellStyle);
                    }
                    //订单描述
                    String orderDesc = dto.getOrderDesc();
                    if(!(orderDesc == null)){
                        Cell cell = row.getCell(9);
                        cell.setCellStyle(cellStyle);
                    }
                    //异常信息
                    String errorMsg = dto.getErrorMsg();
                    if (StringUtils.isNotBlank(errorMsg)) {
                        Cell cell = row.getCell(10);
                        if (null == cell) {
                            cell = row.createCell(10);
                        }
                        cell.setCellValue(errorMsg);
                    }
                }
            }
        }
    }



    @RequestMapping(value = "/downElectronicReceipt")
    @ResponseBody
    public void downElectronicReceipt(HttpServletRequest request, HttpServletResponse response) throws Exception{
        String orderNo = request.getParameter("orderNo");
        String customerNumber = super.getCurrentCustomerNumber();
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-type", "application/pdf;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;fileName="+orderNo+".pdf");
        Map<String,Object> map = new HashMap();
        map.put("initMerchantNo",customerNumber);
        map.put("orderNo",orderNo);
        RemitOrder remitOrder = remitOrderService.queryRemitOrderInfo(map);
        PdfUtils pdfUtils = new PdfUtils();
        long start = System.currentTimeMillis();
        OrderParam orderParam = handleResult(remitOrder);
        String data = pdfUtils.generateReceipt(orderParam, getCurrentCustomerNumber());
        BASE64Decoder decoder = new BASE64Decoder();
        //Base64解码
        byte[] len = decoder.decodeBuffer(data);
        for (int i = 0; i < len.length; ++i) {
            //调整异常数据
            if (len[i] < 0) {
                len[i] += 256;
            }
        }
        OutputStream out = response.getOutputStream();
        out.write(len);
        out.flush();
        out.close();
        long end = System.currentTimeMillis();
        logger.info("电子回单耗时{}ms",(end-start));

    }

    @RequestMapping(value = "/receipt", method = RequestMethod.GET)
    public ModelAndView receiptView(HttpServletRequest request) {
        String orderNo = request.getParameter("orderNo");
        String customerNumber = super.getCurrentCustomerNumber();
        ModelAndView mav = new ModelAndView();
        mav.addObject("orderNo",orderNo);
        mav.addObject("merchantNo",customerNumber);
        mav.setViewName("subMerchant/receipt");
        return mav;
    }


    /**
     * 处理数据
     * @param remitOrder
     * @return
     */
    private OrderParam handleResult(RemitOrder remitOrder) {
        OrderParam orderParam = new OrderParam();
        orderParam.setParentMerchantNo(remitOrder.getParentMerchantNo());
        orderParam.setOrderNo(remitOrder.getOrderNo());
        orderParam.setInitiateMerchantNo(remitOrder.getInitiateMerchantNo());
        orderParam.setTradeType(TradeTypeEnum.PAY);
        return orderParam;
    }


    @RequestMapping(value = "/queryRemitDetail")
    public ModelAndView queryRemitDetail(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        String batchNo = request.getParameter("batchNo");
        String requestNo = request.getParameter("requestNo");
        String merchantNo = request.getParameter("merchantNo");
        logger.info("查询订单明细 ：batchNo={},orderNo={}",batchNo,orderNo);
        RemitOrderQueryReqDTO dto =new RemitOrderQueryReqDTO();
        dto.setInitiateMerchantNo(getCurrentCustomerNumber());
        dto.setMerchantNo(merchantNo);
        dto.setOrderNo(orderNo);
        dto.setBatchNo(batchNo);
        dto.setRequestNo(requestNo);
        RemitOrderQueryRespDTO remitOrderQueryRespDTO = remitFacade.queryRemitOrderBySystem(dto);
        RemitResponseParam param = new RemitResponseParam();
        if(remitOrderQueryRespDTO != null){
            String returnCode = remitOrderQueryRespDTO.getReturnCode();
            if("UA00000".equals(returnCode)){
                org.springframework.beans.BeanUtils.copyProperties(remitOrderQueryRespDTO,param);
                RemitOrderStatusEnum status = remitOrderQueryRespDTO.getStatus();
                if(status == RemitOrderStatusEnum.SUCCESS){
                    param.setStatus(RemitStatusEnum.SUCCESS.getDesc());
                }else if(status == RemitOrderStatusEnum.FAIL){
                    param.setStatus(RemitStatusEnum.FAIL.getDesc());
                }else if(status == RemitOrderStatusEnum.CANCELED){
                    param.setStatus(RemitStatusEnum.CANCELED.getDesc());
                }else if(status == RemitOrderStatusEnum.REMITING){
                    param.setStatus(RemitStatusEnum.REMITING.getDesc());
                }else{
                    param.setStatus(RemitStatusEnum.REQUEST_ACCEPT.getDesc());
                }
                if(!CheckUtils.isEmpty(remitOrderQueryRespDTO.getFinishTime())){
                    param.setFinishTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getFinishTime()));
                }
                param.setOrderAmount(remitOrderQueryRespDTO.getOrderAmount().toString());
                if(null != remitOrderQueryRespDTO.getFee()){
                    param.setFee(remitOrderQueryRespDTO.getFee().toString()+"元");
                }else{
                    param.setFee("--");
                }

                String receiverAccountNo = remitOrderQueryRespDTO.getReceiverAccountNo();
                /* 付款记录 跳转 查询明细*/
                HeadBankDTO headBankDTO = searchBankInfoFacade.searchHeadBankInfoByCode(remitOrderQueryRespDTO.getReceiverBankCode());
                if(headBankDTO != null){
                    String bankName = headBankDTO.getBankName();
                    if (StringUtils.isNotBlank(bankName) && StringUtils.isNotBlank(receiverAccountNo)) {
                        param.setReceiverAccountNo(bankName + "(" + receiverAccountNo.substring(receiverAccountNo.length() - 4, receiverAccountNo.length()) + ")");
                    }
                }
                param.setOrderInfo(remitOrderQueryRespDTO.getOrderInfo());
                param.setOrderTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getOrderTime()));
                RemitTypeEnum receiveType = remitOrderQueryRespDTO.getReceiveType();
                if(receiveType == RemitTypeEnum.REAL_TIME){
                    param.setReceiveType("实时到账");
                }else if(receiveType == RemitTypeEnum.TWO_HOUR){
                    param.setReceiveType("2小时到账");
                }else if(receiveType == RemitTypeEnum.NEXT_DAY){
                    param.setReceiveType("次日到账");
                }

                boolean isReversed = remitOrderQueryRespDTO.getIsReversed();
                if(isReversed){
                    param.setReverseTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getReverseTime()));
                    param.setStatus("银行冲退");
                }else{
                    param.setReverseTime("--");
                }

                if(StringUtils.isBlank(param.getFailReason())){
                    param.setFailReason("--");
                }
                if(StringUtils.isEmpty(param.getFinishTime())){
                    param.setFinishTime("--");
                }
                if(StringUtils.isEmpty(param.getComments())){
                    param.setComments("--");
                }
                if(StringUtils.isEmpty(param.getOrderInfo())){
                    param.setOrderInfo("--");
                }

                if(StringUtils.isEmpty(param.getBatchNo())){
                    param.setBatchNo("--");
                }

                if(StringUtils.isEmpty(param.getRemark())){
                    param.setRemark("--");
                }

                if (remitOrderQueryRespDTO.getTradeType() == null) {
                    param.setTradeType(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.ENTERPRISE_PAYMENT.getDesc());
                }else {
                    if(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType())!=null) {
                        param.setTradeType(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType()).getDesc());
                    }
                }

                param.setOrderNo(remitOrderQueryRespDTO.getRequestNo());
                MerchantinfoRespDTO merchantInfo = businessCheckRemoteService.queryMerchantInfo(remitOrderQueryRespDTO.getMerchantNo());
                if(merchantInfo != null){
                    param.setMerchantName(merchantInfo.getSignedName());
                }
            }
        }


        logger.info("查询订单明细返回，remitResponseParam={}", JSON.toJSONString(param));
        mav.addObject("orderDetail", param);
        mav.setViewName("subMerchant/detail");
        return mav;
    }

    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(RemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("开始下载付款记录，请求参数{}", JSON.toJSONString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            if(StringUtils.isNotBlank(param.getSubMerchant())) {
                param.setCustomerNumber(param.getSubMerchant());
            }
            MerchantRespDTO merchantRespDTO = getCurrentMerchant();
            param.setInitMerchantNo(merchantRespDTO.getMerchantNo());
            param = dealParams(param);
            if(StringUtils.isEmpty(param.getCreateStartDate()) || StringUtils.isEmpty(param.getCreateEndDate())){
                if(StringUtils.isEmpty(param.getBatchNo()) && StringUtils.isEmpty(param.getRequestNo())){
                    //如果时间范围是空的,那么批次号和订单号必填
                    throw UnionAccountException.PARAM_REQUIRED_ERROR.newInstance("缺少必要的请求参数");
                }
            }
            Map<String, String> merchantNameMap = new HashMap<>();
            List<MerchantAccountInfoEntity> merchantAccountInfoEntityList = accountManageInfoService.queryMerchantList(merchantRespDTO.getMerchantNo());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(merchantAccountInfoEntityList)) {
                merchantNameMap = merchantAccountInfoEntityList.stream().collect(Collectors.toMap(MerchantAccountInfoEntity::getMerchantNo,
                        MerchantAccountInfoEntity::getSignName, (key1, key2) -> key2));
            }
            merchantNameMap.put(merchantRespDTO.getMerchantNo(),merchantRespDTO.getSignName());
            param.setMerchantNameMap(merchantNameMap);

            StringBuilder desc = new StringBuilder();
            desc.append("付款订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            new RemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-",remitOrderService, merchantRemoteService,false).download(request, response);
            logger.info("下载付款记录excel已完成");
        } catch (Throwable ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    @RequestMapping(value = "/batchDownloadElectronic")
    @ResponseBody
    public void batchDownloadElectronic(RemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("开始批量下载付款电子回单，请求参数{}", JSON.toJSONString(param));
            long start = System.currentTimeMillis();
            response.setCharacterEncoding("utf-8");

            if(StringUtils.isNotBlank(param.getSubMerchant())) {
                param.setCustomerNumber(param.getSubMerchant());
            }
            String currentCustomerNumber = getCurrentCustomerNumber();
            param.setInitMerchantNo(currentCustomerNumber);
            param = dealParams(param);
            String description = String.format("电子回单[%s]-%s~%s",currentCustomerNumber,param.getCreateStartDate(),param.getCreateEndDate());

            Map<String, Object> queryMap = com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils.toMapWithResourceType(param);
            if(!CheckUtils.isEmpty(param.getStatus())) {
                if("REVERSED".equals(param.getStatus())) {
                    queryMap.put("status","SUCCESS");
                    queryMap.put("reversed","1");
                }else {
                    queryMap.put("reversednull","0");
                }
            }

            new RemitElectronicReceiptDownloader(getCurrentUser(), queryMap, description, "付款订单查询-", merchantRemoteService,remitOrderService).download(request, response);
            long end = System.currentTimeMillis();
            logger.info("批量下载电子回单已完成,耗时{}s",(end-start)/1000);
        } catch (Throwable ex) {
            logger.error("批量下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }


    public static Map<String, Object> toMapWithResourceType(Object object) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                resultMap.put(field.getName(), field.get(object));
            } catch (Throwable e) {
                logger.error("toMapWithResourceType-error",e);
            }
        }
        return resultMap;
    }

    private RemitQueryParam dealParams(RemitQueryParam param){
        String status = param.getStatus();
        if (status.equals(RemitStatusEnum.REQUEST_ACCEPT.name())) {
            status = RemitStatusEnum.REQUEST_ACCEPT.name() + "," + RemitStatusEnum.REQUEST_RECEIVE.name();
            param.setStatus(status);
        }

        if(StringUtils.isNotBlank(param.getCreateEndDate())){
            param.setCreateEndDate(DateUtil.addDay(param.getCreateEndDate()));
        }
        return param;
    }



    /**
     * 批量调用代付打款
     *
     * @param token
     * @param authCode
     * @param dealPassword
     * @param request
     * @return
     */
//    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/batchSendBac")
    @ResponseBody
    public ResponseMessage batchSendBac(HttpServletRequest request, @RequestParam("token") String token, @RequestParam("dealPassword") String dealPassword,
                                        @RequestParam("authCode") String authCode) {

        logger.info("验证打款提交参数：token:{}", token);
        ShiroUser user = super.getCurrentUser();
        //交易密码改为密文传输，需要解密
        String decryptPassWord = BACRsaUtil.privateDecrypt(dealPassword, ConfigUtils.getPrivateKey());
        //1.验证密码
        ResponseMessage responseMessage = new ResponseMessage(BacNewBatchRemitController.ResponseMsg.Status.SUCCESS);
        Long userId = user.getUserId();
        checkNotNull(userId);
        checkArgument(StringUtils.isNotBlank(dealPassword));
        checkArgument(StringUtils.isNotBlank(authCode));
        if (!userFacade.validateTradePassword(userId, decryptPassWord)) {
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg("交易密码不正确");
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            return responseMessage;
        }

        //短信验证
        try {
            SendSmsUtils.checkVaildFrequency(request, userId, authCode, RemitConstant.BATCH_REMIT_SMS_CODE_TYPE);
        } catch (ExceptionWrapper e) {
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg(e.getMessage());
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            return responseMessage;
        }

        //验证token
        Map<String, String> tokenValue = null;
        try {
            tokenValue = vaildToken(getCurrentUser().getLoginName(), token);
        } catch (Exception e) {
            logger.error("调用redis hgetall异常", e);
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
            return responseMessage;
        }

        RemitBatchReqDTO remitBatchReqDTO = new RemitBatchReqDTO();
        remitBatchReqDTO.setMerchantNo(getCurrentCustomerNumber());
        remitBatchReqDTO.setBatchNo(tokenValue.get("batchNo"));
        remitBatchReqDTO.setSource("mp");
        remitBatchReqDTO.setOperator(getCurrentUserSafe().getLoginName());
        remitBatchReqDTO.setNotNeedAudit(true);
        try {
            RemitBatchRespDTO remitBatchRespDTO = remitFacade.batchInitiateRemitForMP(remitBatchReqDTO);
            if (remitBatchRespDTO != null) {
                String returnCode = remitBatchRespDTO.getReturnCode();
                if (!returnCode.equals(ErrorCode.SUCCESS)) {
                    logger.info("批量出款异常，bathNo={}", tokenValue.get("batchNo"));
                    responseMessage.setStatus(ResponseMessage.Status.ERROR);
                    responseMessage.setErrCode("9999");
                    responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
                    return responseMessage;
                }
            } else {
                logger.info("批量出款异常，bathNo={}", tokenValue.get("batchNo"));
                responseMessage.setStatus(ResponseMessage.Status.ERROR);
                responseMessage.setErrCode("9999");
                responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
                return responseMessage;
            }
        } catch (Throwable e) {
            logger.info("批量出款异常，bathNo={}", tokenValue.get("batchNo"));
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
            return responseMessage;
        }
        boolean needAudit = false;
//        try{
//            CheckNeedAuditReqDTO reqDTO = new CheckNeedAuditReqDTO();
//            reqDTO.setMerchantNo(user.getCustomerNumber());
//            reqDTO.setAuditBizType(UserAuditBizTypeEnum.REMIT);
//            reqDTO.setUserAuditRequestSourceType(UserAuditRequestSourceTypeEnum.SELF_SERVICE);
//            CheckNeedAuditRespDTO resp = userAuditConfigFacade.checkNeedAudit(reqDTO);
//            if("UA00000".equals(resp.getReturnCode())){
//                needAudit = resp.getNeedAudit();
//            }
//        }catch (Exception ex){
//            logger.error("获取商户是否需要复核异常！",ex);
//        }
        responseMessage.put("needAudit",needAudit);
        responseMessage.put("batchNo",tokenValue.get("batchNo"));
        responseMessage.put("taskId", tokenValue.get("taskId"));
        return responseMessage;
    }


    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/getFailResult")
    @ResponseBody
    public ResponseMessage getFailResult(HttpServletRequest request, @RequestParam("taskId") String taskId) {

        logger.info("查询批量打款任务结果： taskId：{}", taskId);
        ResponseMessage responseMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtil.isNotEmpty(taskId)) {

                RemitBatchInfoReqDTO remitBatchReqDto = new RemitBatchInfoReqDTO();
                remitBatchReqDto.setMerchantNo(getCurrentCustomerNumber());  //getCurrentCustomerNumber()
                remitBatchReqDto.setTaskId(Long.valueOf(taskId));
                RemitBatchRespDTO remitBatchRespDTO = remitFacade.queryBatchInfo(remitBatchReqDto);
                String returnCode = remitBatchRespDTO.getReturnCode();
                if(returnCode.equals(ErrorCode.SUCCESS)){
                    TaskStatus taskStatus = remitBatchRespDTO.getTaskStatus();
                    Integer validSuccessCount = (remitBatchRespDTO.getValidSuccessCount() == null?0:remitBatchRespDTO.getValidSuccessCount());
                    Integer validFailCount = (remitBatchRespDTO.getValidFailCount()== null?0:remitBatchRespDTO.getValidFailCount());
                    BigDecimal validFailAmount = (remitBatchRespDTO.getValidFailAmount()== null?BigDecimal.ZERO:remitBatchRespDTO.getValidFailAmount());
                    BigDecimal validSuccessAmount = (remitBatchRespDTO.getValidSuccessAmount()==null?BigDecimal.ZERO:remitBatchRespDTO.getValidSuccessAmount());
                    if(taskStatus == TaskStatus.SENDED){
                        responseMsg.put("status", "compete");

                    }else{
                        int processCount = remitBatchRespDTO.getTotalCount() - validSuccessCount;
                        responseMsg.put("processCount", processCount);
                        responseMsg.put("status", "process");
                    }

                    responseMsg.put("totalCount", remitBatchRespDTO.getTotalCount());
                    responseMsg.put("validSuccessCount", validSuccessCount);
                    responseMsg.put("validFailCount", validFailCount);
                    responseMsg.put("totalAmount", NumberUtils.formateNum(null,remitBatchRespDTO.getOrderAmount()));
                    responseMsg.put("validSuccessAmount",  NumberUtils.formateNum(null,validSuccessAmount));
                    responseMsg.put("validFailAmount",  NumberUtils.formateNum(null,validFailAmount));
                    responseMsg.put("batchNo",remitBatchRespDTO.getBatchNo());

                }else{
                    responseMsg.setStatus(ResponseMessage.Status.ERROR);
                    return responseMsg;
                }
                return responseMsg;
            } else {
                responseMsg.setStatus(ResponseMessage.Status.ERROR);
                return responseMsg;
            }
        } catch (Exception e) {
            logger.error("查询批量打款任务结果异常", e);
            responseMsg.setStatus(ResponseMessage.Status.ERROR);
            return responseMsg;
        }
    }


    /**
     * 验证token令牌
     * @param loginName 登录名
     * @param token
     */
    private Map<String, String> vaildToken(String loginName, String token) {
        Map<String, String> hmget = null;
        try {
            hmget = RedisUtils.hgetall(token);
            logger.info("获取redis,hmget={}" + JSON.toJSONString(hmget)+",loginName={}" + loginName + ",token={}" +token);
        } catch (Exception e) {
            logger.error("调用redis hgetall异常", e);
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        if (null == hmget) {
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        if (!loginName.equals(hmget.get("loginName"))) {
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        if (!"0".equals(hmget.get("validExceptionCount"))) {
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        RedisUtils.delete(token);
        return hmget;
    }

}
