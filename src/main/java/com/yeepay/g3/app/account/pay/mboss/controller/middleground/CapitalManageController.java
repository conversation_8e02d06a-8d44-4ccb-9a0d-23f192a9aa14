package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.service.CapitalManageService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.vo.AccountBookVo;
import com.yeepay.g3.app.account.pay.mboss.vo.QueryAccountBookInfoRespDTO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.capital.manage.dto.req.QueryMatchingLoanReqDTO;
import com.yeepay.g3.capital.manage.dto.req.QueryMatchingUseFundReqDTO;
import com.yeepay.g3.capital.manage.dto.resp.QueryMatchingLoanRespDTO;
import com.yeepay.g3.capital.manage.dto.resp.QueryMatchingUseFundRespDTO;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;

/**
 * @ClassName: 资方
 * @Description: 资方
 * <AUTHOR>
 * @Date 2024/2/5
 * @Version 1.0
 */
@Controller
@RequestMapping("/capital/manage")
public class CapitalManageController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CapitalManageController.class);

    @Autowired
    private CapitalManageService capitalManageService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    /**
     * 查询当前商编关联的资方信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getCapitalInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getCapitalInfo() {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            LOGGER.info("查询当前商编关联的资方信息，请求参数为 customerNumber={}", user.getCustomerNumber());
            QueryAccountBookInfoRespDTO respDTO = capitalManageService.queryAccountBookInfo(user.getCustomerNumber());
            LOGGER.info("查询当前商编关联的资方信息，返回信息为 customerNumber={}", user.getCustomerNumber());
            BigDecimal totalBalance = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(respDTO.getAccountBookInfoVos())) {
                for (AccountBookVo accountBookInfoVo : respDTO.getAccountBookInfoVos()) {
                    totalBalance = totalBalance.add(accountBookInfoVo.getBalance());
                }
            }
            resMsg.put("data", respDTO);
            resMsg.put("totalBalance", totalBalance);
            return resMsg;
        } catch (AccountPayException e) {
            LOGGER.error("查询当前商编关联的资方信息异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("查询当前商编关联的资方信息异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


    /**
     * 查询用款配置
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getBusinessConfigCheck", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getBusinessConfigCheck(@RequestParam("tradeType") String tradeType) {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            LOGGER.info("查询商户用款配置，请求参数为 customerNumber={}，tradeType={}", user.getCustomerNumber(), tradeType);
            QueryMatchingUseFundReqDTO reqDTO = new QueryMatchingUseFundReqDTO();
            reqDTO.setMerchantNo(user.getCustomerNumber());
            reqDTO.setUseWay(tradeType);
            QueryMatchingUseFundRespDTO respDTO = capitalManageService.queryUseFundConfig(reqDTO);
            if (AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(respDTO.getReturnCode())) {
                resMsg.put("capitalBusiness", false);
            } else {
                resMsg.put("capitalBusiness", true);
            }
            LOGGER.info("查询商户用款配置，请求参数为 customerNumber={}，tradeType={}，返回信息为={}", user.getCustomerNumber(), tradeType,
                    JSONUtils.toJsonString(resMsg));
            return resMsg;
        } catch (Exception e) {
            LOGGER.error("查询商户用款配置异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


    /**
     * 查询放款配置
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getLoanBusinessConfig", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getTransferBusinessConfig(@RequestParam("tradeType") String tradeType) {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            LOGGER.info("查询商户放款配置，请求参数为 customerNumber={}，tradeType={}", user.getCustomerNumber(), tradeType);
            QueryMatchingLoanReqDTO reqDTO = new QueryMatchingLoanReqDTO();
            if ("TRANSFER".equals(tradeType)) {
                reqDTO.setFromMerchantNo(user.getCustomerNumber());
            } else {
                reqDTO.setToMerchantNo(user.getCustomerNumber());
            }
            reqDTO.setRequestBiz(tradeType);
            QueryMatchingLoanRespDTO respDTO = capitalManageService.queryLoanConfig(reqDTO);
            if (AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(respDTO.getReturnCode())) {
                resMsg.put("capitalBusiness", false);
            } else {
                resMsg.put("capitalBusiness", true);
            }
            LOGGER.info("查询商户放款配置，请求参数为 customerNumber={}，tradeType={}，返回信息为={}", user.getCustomerNumber(), tradeType,
                    JSONUtils.toJsonString(resMsg));
            return resMsg;
        } catch (Exception e) {
            LOGGER.error("查询商户放款配置异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


    /**
     * 查询融资人放款配置
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getFinancierLoanConfig", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getFinancierLoanConfig(@RequestParam(value = "merchantNo") String merchantNo,
                                                  @RequestParam("tradeType") String tradeType) {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            LOGGER.info("查询融资人放款配置，请求参数为 customerNumber={}，tradeType={}", merchantNo, tradeType);
            QueryMatchingLoanReqDTO reqDTO = new QueryMatchingLoanReqDTO();
            reqDTO.setFromMerchantNo(user.getCustomerNumber());
            reqDTO.setToMerchantNo(merchantNo);
            reqDTO.setRequestBiz(tradeType);
            QueryMatchingLoanRespDTO respDTO = capitalManageService.queryLoanConfig(reqDTO);
            if (AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(respDTO.getReturnCode())) {
                resMsg.put("capitalBusiness", false);
            } else {
                resMsg.put("capitalBusiness", true);
            }
            String merchantName = getMerchantName(merchantNo);
            resMsg.put("merchantName", merchantName);
            LOGGER.info("查询融资人放款配置，请求参数为 customerNumber={}，tradeType={}，返回信息为={}", user.getCustomerNumber(), tradeType,
                    JSONUtils.toJsonString(resMsg));
            return resMsg;
        } catch (Exception e) {
            LOGGER.error("查询融资人放款配置异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


    private String getMerchantName(String merchantNo) {
        try {
            return merchantRemoteService.getMerchantName(merchantNo);
        } catch (Exception e) {
            LOGGER.info("查询融资人放款配置业务异常,商户为={},异常信息为={}", merchantNo, e);
            return null;
        }
    }
}
