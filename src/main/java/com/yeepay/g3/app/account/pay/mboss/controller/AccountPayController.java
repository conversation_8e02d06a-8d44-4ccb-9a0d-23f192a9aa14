package com.yeepay.g3.app.account.pay.mboss.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DataExportUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DataFormater;
import com.yeepay.g3.app.account.pay.mboss.utils.ExportExcelParam;
import com.yeepay.g3.app.account.pay.mboss.utils.HistoryTradeManagementDownLoad;
import com.yeepay.g3.app.account.pay.mboss.utils.QueryServiceUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.response.ResponseMessage.Status;
import com.yeepay.g3.facade.mp.PermissionAndFunctionConstant;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;


@Controller
@RequestMapping("/boss/accountpay/pay")
public class AccountPayController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(AccountPayController.class);
	
	
	protected final DataFormater dataFormater = new DataFormater();
	
	 // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认pageNo
    private static final String PAGE_NO_DEFAULT_VAL = "1";
    
    private static final String QUERY_PERMISSION_CONSTANT = "**********";


    // 金额格式
    private static final String AMOUT_FORMAT = "^([1-9]\\d*\\.\\d*|0\\.\\d+|[1-9]\\d*|0)$";

    // 产品类型集合
    private static final Map<String, List<String>> PRODUCT_TYPE_MAP = Maps.newHashMap();
    //状态集合
    private static final Map<String, List<String>> STATUS_MAP = Maps.newHashMap();
    //剩余可退款金额展示为'-'
    private static final List<String> NOT_SHOW_SURPLUS_REFUND_AMOUNT = Arrays.asList(new String[]{"INIT", "WAIT_PAY", "CLOSE", "REJECT", "TIME_OUT", "FULLY_PAY", "CS_ACCEPT", "CS_SUCCESS", "REPEAL_ACCEPT", "REPEALED"});

    // 网银历史库时间范围配置
    //private List<String> hisDsList = ConfigureSetting.getHisDataSourceRange();
    

    /**
     * 查询账户支付请求订单
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/query")
    public ModelAndView queryPayOrder(){
    	logger.info("payquerySuccess");
        ModelAndView mav = new ModelAndView();
        mav.setViewName("pay/queryPayOrder");
        return mav;
    }


    /**
     * 查询退款请求订单
     * @return
     */
    @RequestMapping("/queryRefundOrder")
    public ModelAndView queryRefundOrder(){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("/pay/queryRefundOrder");
        return mav;
    }

    
    /**
     * 订单列表查询(ajax)
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/list")
    @ResponseBody
    public ResponseMessage queryHistoryOrderList(HttpServletRequest request, HttpServletResponse response,
    		QueryInputParam param,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param), pageSize, pageNo);
        ResponseMessage resMsg = new ResponseMessage("success");
        param.setOrderNo(request.getParameter("orderNo"));
        param.setStartTime(request.getParameter("createStartDate"));
        param.setEndTime(request.getParameter("createEndDate"));
        param.setInnerOrderNo(request.getParameter("innerOrderNo"));
        param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
        //param.setStatus(request.getParameter("status"));
        String status = request.getParameter("status");
        if(StringUtils.equals(status, "RSUCCESS")){
        	param.setRstatus("SUCCESS");
        	param.setStatus(null);
        }else{
        	param.setStatus(status);
        }
        String merchant = getCurrentCustomerNumber();
        param.setCustomerNo(merchant);
        
        logger.info("..."+ToStringBuilder.reflectionToString(param), pageSize, pageNo);

        String pathString = request.getContextPath();
        
       /* try {
            checkInputParam(param);

        } catch (RuntimeException e) {
            resMsg.setStatus(Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }*/

        try {
			QueryResult result = this.queryOrderList(param, pageNo, pageSize);

			if (result != null && !CheckUtils.isEmpty(result.getData())) {
			    for (Map<String, Object> detail : result.getData()) {
			        adaptReturnResult(detail,pathString, pageNo, pageSize);
			    }
			    resMsg.put("pageNo", pageNo);
			    resMsg.put("pageSize", pageSize);
			    resMsg.put("dataList", result.getData());

			}

      List<Map<String, Object>> list =  this.queryOrderListSum(param);
			// 如果查询结果不为空的话
			if (!CheckUtils.isEmpty(list)) {
			    // int sumCount = Integer.parseInt((String) list.get(0).get("SUM_COUNT"));
				int sumCount = (Integer) mergeCount(list, "SUM_COUNT", "int");
			    resMsg.put("totalCount", sumCount);// 这是啥

			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();

		}

        return resMsg;
    }
    
    
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/listsum")
    @ResponseBody
    public ResponseMessage queryHistoryOrderListSum(HttpServletRequest request, HttpServletResponse response,QueryInputParam param) {
    	 logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param));
         ResponseMessage resMsg = new ResponseMessage("success");
         param.setOrderNo(request.getParameter("orderNo"));
         param.setStartTime(request.getParameter("createStartDate"));
         param.setEndTime(request.getParameter("createEndDate"));
         param.setInnerOrderNo(request.getParameter("innerOrderNo"));
         param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));

       //param.setStatus(request.getParameter("status"));
         String status = request.getParameter("status");
         if(StringUtils.equals(status, "RSUCCESS")){
         	param.setRstatus("SUCCESS");
        	param.setStatus(null);

         }else{
         	param.setStatus(status);
         }
         String merchant = getCurrentCustomerNumber();
         param.setCustomerNo(merchant);
         logger.info("..."+ToStringBuilder.reflectionToString(param));

        List<Map<String, Object>> list = this.queryOrderListSum(param);


        // 如果查询结果不为空的话
        if (list != null && !list.isEmpty()) {
            //Map<String, Object> sumResult = list.get(0);
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumFractionDigits(2);
            nf.setMaximumFractionDigits(2);
/*
            resMsg.getData().put("sum_count", sumResult.get("SUM_COUNT").toString());// 总笔数
            resMsg.getData().put("sum_amount", nf.format(new BigDecimal(sumResult.get("SUM_AMOUNT").toString())));// 总金额
            resMsg.getData().put("sum_fee", nf.format(new BigDecimal(sumResult.get("SUM_FEE").toString())));// 总手续费
            (Integer) mergeCount(list, "SUM_COUNT", "int")
*/        
            try {
				resMsg.getData().put("sum_count", (Integer) mergeCount(list, "SUM_COUNT", "int")+"");// 总笔数
				resMsg.getData().put("sum_amount", nf.format((BigDecimal)mergeCount(list, "SUM_AMOUNT", "float")));// 总金额
				resMsg.getData().put("sum_fee", nf.format((BigDecimal)mergeCount(list, "SUM_FEE", "float")));// 总金额
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", 0.00);// 总金额
            resMsg.getData().put("sum_fee", 0.00);// 总手续费
        }

        return resMsg;
    }
    
    /**
     * 下载excel或csv
     *
     * @param request
     * @param response
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/download")
    public void downloadTrade(HttpServletRequest request, HttpServletResponse response,QueryInputParam param) {

        try {
            //checkInputParam(param);
        	logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param));
            //ResponseMessage resMsg = new ResponseMessage("success");
            param.setOrderNo(request.getParameter("orderNo"));
            param.setStartTime(request.getParameter("createStartDate"));
            param.setEndTime(request.getParameter("createEndDate"));
            param.setInnerOrderNo(request.getParameter("innerOrderNo"));
            param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
          //param.setStatus(request.getParameter("status"));
            String status = request.getParameter("status");
            if(StringUtils.equals(status, "RSUCCESS")){
            	param.setRstatus("SUCCESS");
            	param.setStatus(null);

            }else{
            	param.setStatus(status);
            }
            String merchant = getCurrentCustomerNumber();
            param.setCustomerNo(merchant);
            logger.info("..."+ToStringBuilder.reflectionToString(param));

            Map<String, Object> params = BeanUtils.toMapWithResourceType(param);
           /* for (String jsonString : hisDsList) {
                JSONObject jsonObj = JSON.parseObject(jsonString);
                if (param.getDateArea().equals(jsonObj.getString("dsName"))) {
                    params.put("startOrderDate", jsonObj.getString("startDate") + " 00:00:00");
                    params.put("endOrderDate", jsonObj.getString("endDate") + " 23:59:59");
                }
            }
            if ("ORDERDATE".equals(param.getDateType())) {
                params.put("startOrderDateOnly", param.getCreateStartDate());// + " 00:00:00");
                params.put("endOrderDateOnly", param.getCreateEndDate());// + " 23:59:59");
            } else {
                params.put("startCloseDateOnly", param.getCreateStartDate());// + " 00:00:00");
                params.put("endCloseDateOnly", param.getCreateEndDate());// + " 23:59:59");
            }*/
//            params.put("startOrderDate", "2017-03-26 00:00:00");
//            params.put("endOrderDate", "2017-05-26 00:00:00");
            params.put("loginedCustomerId", getCurrentUser().getCustomerId());// TODO: 2018/3/28 测试代码
            logger.info("历史订单下载请求参数{}", params);

            String description = "账户支付订单下载" +"数据";
            new HistoryTradeManagementDownLoad(getCurrentUser(), request.getParameter("type"), params, description).download(request, response);

        } catch (RuntimeException e) {
            String errorMsg = "<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>";
            //setResponseMessage(response, errorMsg);
            e.printStackTrace();
            return;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(this.getClass().getName() + "下载历史收款交易异常", e);
            String errorMsg = "<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>";
           // setResponseMessage(response, errorMsg);
        }

    }
    
    
    /**
     * 适配返回结果,例如FULLY_PAY之后的状态需要变更可退款金额为-等
     *
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail, String path, int pageNo, int pageSize) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }
        
        String innerOrderNoString = (String) detail.get("innerOrderNo");

        Map<String, Object> operation = new HashMap<String, Object>();
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);

        // 判断当前状态是否是支付成功,推送清算中心,成功和已退款,如果是则展示对应的可退款余额,否则展示'-'
        String orderNo = (String) detail.get("orderNo");

        String status = (String) detail.get("status");
        String rstatus = (String) detail.get("rstatus");
       /* if(com.yeepay.g3.utils.common.StringUtils.equals(rstatus, "SUCCESS")){
      	   status =  "<a href=\""+path+"/boss/accountpay/refund/query?orderNo="+orderNo+"\">已退款</a>";

         }else{*/
         	if(com.yeepay.g3.utils.common.StringUtils.equals(status, "INIT")){
          	   status = "初始";
             }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "PROCESSING")){
          	   status = "处理中";
             }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "SUCCESS")){
          	   status = "交易成功";
             }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "FAIL")){
          	   status = "交易失败";
             }else{
          	   status = "账务处理中";
             }
        // }
        detail.put("status", status); 
      
       detail.put("orgorderno","---");
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (null != detail.get("amount")) {
                detail.put("amount", nf.format(new BigDecimal(detail.get("amount").toString())));
            }
            if (null != detail.get("fee")) {
                detail.put("fee", nf.format(new BigDecimal(detail.get("fee").toString())));
            }
            /* if (null != detail.get("payamount") && StringUtils.isNotBlank(detail.get("payamount").toString())) {
                detail.put("payamount", nf.format(new BigDecimal(detail.get("payamount").toString())));
            }
            if (null != detail.get("payamount") && StringUtils.isNotBlank(detail.get("payamount").toString()) && null != detail.get("fee") && StringUtils.isNotBlank(detail.get("fee").toString())) {
                detail.put("realamount", nf.format(new BigDecimal(detail.get("payamount").toString()).subtract(new BigDecimal(detail.get("fee").toString()))));
            }*/
            operation.put("exist_refund", detail.get("exist_refund"));
            detail.put("operation", operation);

            /*if (null != detail.get("refundamount")) {
                detail.put("refundamount", nf.format(new BigDecimal(detail.get("refundamount").toString())));
            } else {
                detail.put("refundamount", nf.format(BigDecimal.ZERO));
            }*/
            //下单时间
            Object obj = detail.get("trxtime");
            if (null != obj) {
                    detail.put("trxtime", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            obj = detail.get("createTime");
            if (null != obj) {
                    detail.put("createTime", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            obj = detail.get("lastModifyTime");
            if (null != obj) {
                    detail.put("lastModifyTime", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            

        } catch (Exception e) {
            logger.error("这都能错..擦....", e);
        }
        
        
        orderNo = (String) detail.get("orderNo");
        QueryInputParam param = new QueryInputParam();
        param.setOrderNo(orderNo);
        param.setInnerOrderNo(innerOrderNoString);
		QueryResult result = this.queryRefundOrder(param, pageNo, pageSize);
		List<Map<String, Object>> list = (List<Map<String, Object>>) result.getData();
		if(list != null && list.size() > 0){
			Map<String, Object> map=list.get(0);
			//detail.put("innerOrderNo", map.get("innerOrderNo")+"");
			if(map.get("id") != null){
	        	detail.put("refund", "是"); 
	        	detail.put("viewrefund", "<a href=\""+path+"/boss/accountpay/refund/query?orderNo="+orderNo+"\">查看退款详情</a>");
	        }else{
	        	detail.put("refund", "否"); 
	        }
		}
		else{
			detail.put("refund", "否"); 
		}
		
		 
		return operation;

    }
   public void exportExcelWithData(HttpServletResponse response, HttpServletRequest request, ExportExcelParam excelParam,List<Map<String, Object>> data)
			throws IOException {
		DataExportUtils dataEcportUtil = new DataExportUtils();
		dataEcportUtil.setTextResource(textResource);
		dataEcportUtil.setMessageFormater(messageFormater);
		dataEcportUtil.setDataFormater(dataFormater);
		dataEcportUtil.exportExcelWithData(response, request, excelParam,data);
	}
  
   /**
    * 历史交易入参绑定dto
    */
   @SuppressWarnings("unused")
   @Data
   @ToString
   @AllArgsConstructor
   @NoArgsConstructor
   private static class QueryInputParam {
       private String orderNo;
       private String status;//银行订单号
       private String startTime;
       private String endTime;
       private String innerOrderNo;
       private String rstatus;
       private String customerNo;
       private String creditCustomerNo;

   	public String getCreditCustomerNo() {
   		return creditCustomerNo;
   	}


   	public void setCreditCustomerNo(String creditCustomerNo) {
   		this.creditCustomerNo = creditCustomerNo;
   	}

       public String getCustomerNo() {
   		return customerNo;
   	}

       public void setCustomerNo(String customerNo) {
   		this.customerNo = customerNo;
   	}

       public String getRstatus() {
       	return rstatus;
       }

       public void setRstatus(String rstatus) {
       	this.rstatus = rstatus;
       }

   	public String getInnerOrderNo() {
   		return innerOrderNo;
   	}

   	public void setInnerOrderNo(String innerOrderNo) {
   		this.innerOrderNo = innerOrderNo;
   	}

   	public String getOrderNo() {
   		return orderNo;
   	}
   	public void setOrderNo(String orderNo) {
   		this.orderNo = orderNo;
   	}
   	public String getStatus() {
   		return status;
   	}
   	public void setStatus(String status) {
   		this.status = status;
   	}
   	public String getStartTime() {
   		return startTime;
   	}
   	public void setStartTime(String startTime) {
   		this.startTime = startTime;
   	}
   	public String getEndTime() {
   		return endTime;
   	}
   	public void setEndTime(String endTime) {
   		this.endTime = endTime;
   	}
   

   }
   private QueryResult queryOrderList(QueryInputParam param, int pageNo, int pageSize) {
       QueryResult result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
  
       Integer startIndex = (pageNo - 1) * pageSize + 1;

       QueryParam queryParam = new QueryParam();
       queryParam.setParams(queryMap);
       queryParam.setStartIndex(startIndex);
       queryParam.setMaxSize(pageSize);
       queryParam.setDoSum(true);

       logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
      result = queryService.query("queryPayOrder", queryParam);

       return result;
   }
   private List<Map<String, Object>> queryOrderListSum(QueryInputParam param) {
	   List<Map<String, Object>> result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
       //queryMap.put("customerNumber", getCurrentCustomerNumber());

      // logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
       result = QueryServiceUtil.query("queryAccntPayService", "queryPayOrderSum", queryMap);

       //result = (List<Map<String, Object>>) queryService.query("queryPayOrderSum", queryParam);

       return result;
   }
    Object mergeCount(List<Map<String, Object>> list, String key, String clazz){
    	if (clazz.equals("int")) {
    		int result = 0;
    		for(Map<String, Object> map:list ){
    			result +=Integer.parseInt((String) map.get(key));
    		}
    		 return result;
			
		}else{
			BigDecimal result = new BigDecimal("0.00").setScale(2, RoundingMode.DOWN);
    		for(Map<String, Object> map:list ){
    			result = result.add(new BigDecimal((String) map.get(key)).setScale(2, RoundingMode.DOWN));
    			//result +=Float.parseFloat((String) map.get(key));
    		}
    		 return result;
			
		}
	   
   }
    /**
     *   辅助查询退款
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    private QueryResult queryRefundOrder(QueryInputParam param, int pageNo, int pageSize) {
        QueryResult result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        //queryMap.put("customerNumber", getCurrentCustomerNumber());

        


        Integer startIndex = (pageNo - 1) * pageSize + 1;

        QueryParam queryParam = new QueryParam();
        queryParam.setParams(queryMap);
        queryParam.setStartIndex(1);
        queryParam.setMaxSize(20);
        queryParam.setDoSum(true);

        logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

        QueryService queryService = (QueryService) QueryServiceUtil.getBean(
                "queryAccntPayService", QueryService.class);
       result = queryService.query("queryRefundOrderHelp", queryParam);

        return result;
    }
  
   
}
