package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.entity.RemitAuditApplyEntity;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RemitAuditAppliesDownloadService;
import com.yeepay.g3.app.account.pay.mboss.trade.dao.RemitAuditApplyDao;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.vo.RemitAuditResultVo;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.bank.management.facade.SearchBankInfoFacade;
import com.yeepay.g3.facade.bankinfo.service.BankInfoQueryFacade;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.BatchAuditReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.BatchAuditRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.AuditStatusEnum;
import com.yeepay.g3.facade.unionaccount.trade.facade.RemitAuditFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 付款复核
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-04-02 5:33 下午
 */
@Controller
@RequestMapping("/remitAudit")
public class RemitAuditController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(RemitAuditController.class);

    private static final String REDIS_KEY_REMIT_AUDIT_APPLY_IDS = "REMIT_AUDIT_BATCH_AUDIT_IDS_%s";
    private static final String  REDIS_KEY_REMIT_AUDIT_APPLY_STATUS = "REMIT_AUDIT_BATCH_AUDIT_STATUS_%s";
    public static final String DO_AUDIT_APPLIES_PERMISSIONS_CODE = "20210415002";
    public static final String DO_AUDIT_APPLIES_PAGE_PERMISSION_CODE = "20210415002";
    public static final String DO_QUERY_APPLIES_PAGE_PERMISSION_CODE = "***********";

    private static final String SUCCESS_CODE = "UA00000";

    @Autowired
    private RemitAuditApplyDao remitAuditApplyDao;

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    private static UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    private static RemitAuditFacade remitAuditFacade = RemoteServiceFactory.getService(RemitAuditFacade.class);
    private static BankInfoQueryFacade bankInfoQueryFacade = RemoteServiceFactory.getService(BankInfoQueryFacade.class);

    private SearchBankInfoFacade searchBankInfoFacade = RemoteServiceFactory.getService(SearchBankInfoFacade.class);

    @RequiresPermissions(DO_QUERY_APPLIES_PAGE_PERMISSION_CODE)
    @RequestMapping(value = "/page/list")
    public ModelAndView listPage(HttpServletRequest request) {
        String passwordRsaPublicKey = BacRsaKeysHolder.getPasswordRsaPublicKey();
        Map<String, Object> params = new HashMap<>();
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        params.put("passwordRsaPublicKey", passwordRsaPublicKey);
        return new ModelAndView("/remitAudit/list", params);
    }

    @RequiresPermissions(DO_AUDIT_APPLIES_PAGE_PERMISSION_CODE)
    @RequestMapping(value = "/page/auditList")
    public ModelAndView auditListPage(HttpServletRequest request) {
        String passwordRsaPublicKey = BacRsaKeysHolder.getPasswordRsaPublicKey();
        Map<String, Object> params = new HashMap<>();
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        params.put("passwordRsaPublicKey", passwordRsaPublicKey);
        return new ModelAndView("/remitAudit/auditList", params);
    }

    /**
     * 审核结果页面
     *
     * @return
     */
    @RequestMapping(value = "/page/auditResult")
    public ModelAndView auditResultPage(@RequestParam String applyRequestNo) {
        Map<String, Object> params = new HashMap<>();
        String auditStatus = RedisUtils.get(String.format(REDIS_KEY_REMIT_AUDIT_APPLY_STATUS,applyRequestNo));
        params.put("applyRequestNo", applyRequestNo);
        params.put("auditStatus",auditStatus);
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("applyRequestNo={},auditStatus={}",applyRequestNo,auditStatus);
        return new ModelAndView("/remitAudit/auditResult", params);
    }

    /**
     * 提交复核申请的结果页面
     *
     * @param batchNo
     * @return
     */
    @RequestMapping(value = "/page/applyResult")
    public ModelAndView applyResultPage(@RequestParam String batchNo) {
        Map<String, Object> params = remitAuditApplyDao.querySumByBatchNo(batchNo, getCurrentCustomerNumber());
        if (CollectionUtils.isEmpty(params)) {
            throw new RuntimeException("查询不到批次信息,batchNo=" + batchNo);
        }
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        return new ModelAndView("/remitAudit/applyResult", params);
    }
    @ResponseBody
    @RequestMapping("/applyResult")
    public BaseRespDTO<Map> applyResult(@RequestParam String batchNo){
        try{
            Map<String, Object> params = remitAuditApplyDao.querySumByBatchNo(batchNo, getCurrentCustomerNumber());
            return BaseRespDTO.success(params);
        }catch (Exception ex){
            logger.error("获取结果异常，batchNo="+batchNo,ex);
            return BaseRespDTO.fail(ex.getLocalizedMessage());
        }

    }

    /**
     * 获取审核结果数据
     *
     * @param applyRequestNo
     * @return
     */
    @RequestMapping("/getRemitAuditResult")
    @ResponseBody
    public BaseRespDTO<RemitAuditResultVo> getRemitAuditResult(@RequestParam String applyRequestNo) {
        String redisKey = String.format(REDIS_KEY_REMIT_AUDIT_APPLY_IDS, applyRequestNo);
        RemitAuditResultVo vo = new RemitAuditResultVo();
        try{
            if (RedisUtils.exists(redisKey)) {
                long n = RedisUtils.llen(redisKey);
                List<String> ids = RedisUtils.lrange(redisKey, 0L, n);
                logger.debug("redisKey={},ids={}",redisKey,ids);
                if(!CollectionUtils.isEmpty(ids)){
                    List<Map<String, Object>> list = remitAuditApplyDao.querySumByIds4Result(ids);
                    if (!CollectionUtils.isEmpty(list)) {
                        for (Map<String, Object> map : list) {
                            AuditStatusEnum status = AuditStatusEnum.valueOf(String.valueOf(map.get("status")));
                            Long totalCount = Long.valueOf(map.get("totalCount") + "");
                            BigDecimal totalAmount = new BigDecimal(map.get("totalAmount") + "");
                            switch (status) {
                                case AUDIT_PASS:
                                case AUDIT_REFUSED:
                                    vo.setTotalSuccessCount(vo.getTotalSuccessCount() + totalCount);
                                    vo.setTotalSuccessAmount(vo.getTotalSuccessAmount().add(totalAmount));
                                    break;
                                case AUDIT_PASS_BUT_FAIL:
                                    vo.setTotalFailureAmount(vo.getTotalFailureAmount().add(totalAmount));
                                    vo.setTotalFailureCount(vo.getTotalFailureCount() + totalCount);
                                    break;
                                default:
                                    logger.warn("存在未处理完成的复核记录,status={0},totalCount={1},totalAmount={2}", Arrays.asList(status, totalCount, totalAmount));
                            }
                            vo.setTotalCount(vo.getTotalCount() + totalCount);
                            vo.setTotalAmount(vo.getTotalAmount().add(totalAmount));
                        }
                    }
                    return RemitAuditResultDTO.success(vo);
                }
            }
            return RemitAuditResultDTO.fail("未查到信息");
        }catch (Exception ex){
            logger.error("getRemitAuditResult error token="+applyRequestNo,ex);
            return RemitAuditResultDTO.fail(ex.getLocalizedMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/getBalanceAndWaitAuditAmount")
    public BaseRespDTO<QueryWaitAuditAmountRespDTO> getBalanceAndWaitAuditAmount() {
        ShiroUser user = getCurrentUser();
        QueryWaitAuditAmountRespDTO respDTO = new QueryWaitAuditAmountRespDTO();
        try {
            // 查询余额
            respDTO.setBalanceAmount(queryBalance("FUND_ACCOUNT", user.getCustomerNumber()));
            respDTO.setSpecialBalanceAmount(getSpecialAccountBalance(user.getCustomerNumber()));
            Map<String, Object> map = remitAuditApplyDao.queryWaitAuditSumByMerchantNo(user.getCustomerNumber());
            if (CollectionUtils.isEmpty(map) || !map.containsKey("totalCount") || !map.containsKey("totalAmount")) {
                respDTO.setTotalCount(0L);
                respDTO.setTotalAmount(BigDecimal.ZERO);
            } else {
                respDTO.setTotalCount((Long) map.getOrDefault("totalCount", 0L));
                respDTO.setTotalAmount((BigDecimal) map.getOrDefault("totalAmount", BigDecimal.ZERO));
            }
            return BaseRespDTO.success(respDTO);
        } catch (Exception ex) {
            logger.error("获取待复核金额异常" + user, ex);
            return BaseRespDTO.fail(ex.getLocalizedMessage());
        }

    }

    /**
     * 获取专款账户的余额
     * todo 让测试验证一下没有开专款账户会不会报错
     *
     * @param currentCustomerNumber
     * @return
     */
    private BigDecimal getSpecialAccountBalance(String currentCustomerNumber) {
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        //查询余额
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.queryAccountBalance(currentCustomerNumber, AccountTypeEnum.SPECIAL_FUND_ACCOUNT);
        if (queryAccountResponseDto == null || !"UA00000".equals(queryAccountResponseDto.getReturnCode())) {
            //查询账户异常，或者账户不存在的场景
            return BigDecimal.ZERO;
        }
        String accountStatus = queryAccountResponseDto.getAccountStatus();
        if ("AVAILABLE".equals(accountStatus) || "FROZEN_CREDIT".equals(accountStatus)) {
            return queryAccountResponseDto.getBalance();
        }
        return BigDecimal.ZERO;
    }

    /**
     * @param accountType:
     * @return java.math.BigDecimal
     * @Description: 查询账户余额，抛异常
     * <AUTHOR>
     * @date 2020-11-02 15:14
     */
    private BigDecimal queryBalance(String accountType, String merchantNo) {
        //查询资金账户余额
        BigDecimal balance = new BigDecimal(BigInteger.ZERO);
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.queryAccountBalance(merchantNo, com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum.valueOf(accountType));
        if (queryAccountResponseDto == null) {
            return balance;
        }
        BigDecimal queryBalance = queryAccountResponseDto.getBalance();
        if (queryBalance != null) {
            balance = queryBalance;
        }
        return balance;
    }

    @ResponseBody
    @RequestMapping(value = "/list")
    public BaseRespDTO<RemitAuditApplyListRespDTO> list(RemitAuditApplyQueryReqDTO reqDto, HttpServletRequest request) {
        logger.info("付款复核列表查询 the param=[{}]", JSONObject.toJSONString(reqDto));
        try{
            ShiroUser user = getCurrentUser();
            RemitAuditApplyListRespDTO resp = new RemitAuditApplyListRespDTO();
            resp.setTotalCount(0);
            resp.setLoginName(user.getLoginName());
            resp.setTotalAmount(BigDecimal.ZERO);
            resp.setData(Collections.emptyList());
            reqDto.setMerchantNo(user.getCustomerNumber());
            Map<String, Object> map = remitAuditApplyDao.queryListByPageCount(reqDto);
            if (CollectionUtils.isEmpty(map) || !map.containsKey("totalCount") || !map.containsKey("totalAmount")) {
                return BaseRespDTO.success(resp);
            }
            /* 老的查询所有-付款复核的结果转化 转换为新的银行信息查询接口
            * 未来做成缓存吧*/
//            Map<String, String> headBank = bankInfoQueryFacade.queryAllHeadBank();
            Integer totalCount = Integer.valueOf(StringUtils.defaultIfEmpty(map.get("totalCount").toString(), "0"));
            BigDecimal totalAmount = new BigDecimal(StringUtils.defaultIfEmpty(map.get("totalAmount").toString(), "0.0"));
            reqDto.doPage();
            List<RemitAuditApplyEntity> list = remitAuditApplyDao.queryListByPage(reqDto);
            resp.setData(list.stream().map(e -> buildDTO(e)).collect(Collectors.toList()));
            resp.setTotalAmount(totalAmount);
            resp.setTotalCount(totalCount);
            return BaseRespDTO.success(resp);
        }catch (Exception e) {
            logger.error("查询复核列表异常.",e);
            return BaseRespDTO.fail(e.getMessage());
        }

    }


    @ResponseBody
    @RequestMapping(value = "/doAuditApplies", method = RequestMethod.POST)
    public BaseRespDTO<AuditAppliesResultDTO> doAuditApplies(@RequestBody RemitAuditApplyAuditReqDto reqDto, HttpServletRequest request) {
        AuditAppliesResultDTO resultDTO = new AuditAppliesResultDTO();
        ShiroUser user = getCurrentUser();
        logger.info("doAuditApplies user={},req={}", user.getLoginName(), reqDto);
        if (AuditStatusEnum.AUDIT_REFUSED.equals(reqDto.getAuditStatus()) && StringUtils.isEmpty(reqDto.getFailReason())) {
            return BaseRespDTO.fail("复核拒绝时，拒绝原因必填！");
        }
        // 校验交易密码
        boolean passResult = false;
        try {
            String pass = BACRsaUtil.privateDecrypt(reqDto.getPassword(), BacRsaKeysHolder.getPasswordRsaPrivateKey());
            passResult = userFacade.validateTradePassword(user.getUserId(), pass);
        } catch (Throwable t) {
            logger.error("密码解密失败", t);
        }
        if (!passResult) {
            logger.info("doAuditApplies 密码错误 user={}", user.getLoginName(), resultDTO);
            return BaseRespDTO.fail("密码错误");
        }
        String token = UUID.randomUUID().toString();
        String redisKey = String.format(REDIS_KEY_REMIT_AUDIT_APPLY_IDS, token);
        RedisUtils.lpush(redisKey, reqDto.getAuditApplyIds().stream().map(e -> e.toString()).collect(Collectors.toList()), 60 * 60);
        RedisUtils.set(String.format(REDIS_KEY_REMIT_AUDIT_APPLY_STATUS,token),reqDto.getAuditStatus().name(),60*60);

        BatchAuditReqDTO remitAuditReq = new BatchAuditReqDTO();
        remitAuditReq.setMerchantNo(user.getCustomerNumber());
        remitAuditReq.setAuditApplyIds(reqDto.getAuditApplyIds());
        remitAuditReq.setAuditStatusEnum(reqDto.getAuditStatus());
        remitAuditReq.setOperator(user.getLoginName());
        remitAuditReq.setFailReason(reqDto.getFailReason());
        logger.info("batchAudit req={}", remitAuditReq);
        BatchAuditRespDTO batchAuditRespDTO = remitAuditFacade.batchAudit(remitAuditReq);
        logger.info("batchAudit resp={},token={}", batchAuditRespDTO,token);
        if (SUCCESS_CODE.equals(batchAuditRespDTO.getReturnCode())) {
            resultDTO.setApplyRequestNo(token);
            return BaseRespDTO.success(resultDTO);
        } else {
            return BaseRespDTO.fail(batchAuditRespDTO.getReturnMsg());
        }

    }

    @RequestMapping("/download")
    public void download(RemitAuditApplyDownloadReqDTO downloadReqDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("下载请求:{}", downloadReqDTO);
        try {
            if ("appointSync".equals(downloadReqDTO.getSyncType())) {
                new RemitAuditAppliesDownloadService(getCurrentUser(), downloadReqDTO, "", "复核申请下载", remitAuditApplyDao, bankInfoQueryFacade.queryAllHeadBank(), merchantRemoteService)
                        .syncDownload(request, response);
            } else {
                new RemitAuditAppliesDownloadService(getCurrentUser(), downloadReqDTO, "", "复核申请下载", remitAuditApplyDao, bankInfoQueryFacade.queryAllHeadBank(), merchantRemoteService)
                        .download(request, response);
            }
        } catch (Exception ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    private RemitAuditApplyDTO buildDTO(RemitAuditApplyEntity e) {
        RemitAuditApplyDTO remitAuditApplyDTO = new RemitAuditApplyDTO();
        remitAuditApplyDTO.setId(e.getId().toString());
        remitAuditApplyDTO.setCreateTime(e.getCreateTime());
        remitAuditApplyDTO.setLastModifyTime(e.getLastModifyTime());
        remitAuditApplyDTO.setMerchantNo(e.getMerchantNo());
        remitAuditApplyDTO.setStatus(e.getStatus());
        remitAuditApplyDTO.setAuditOperator(e.getAuditOperator());
        remitAuditApplyDTO.setApplyOperator(e.getApplyOperator());
        remitAuditApplyDTO.setUniqueOrderNo(e.getUniqueOrderNo());
        remitAuditApplyDTO.setRequestNo(e.getRequestNo());
        remitAuditApplyDTO.setOrderAmount(e.getOrderAmount());
        remitAuditApplyDTO.setReceiverAccountNo(e.getReceiverAccountNo());
        remitAuditApplyDTO.setReceiverAccountName(e.getReceiverAccountName());
        remitAuditApplyDTO.setReceiverBankCode(e.getReceiverBankCode());
        remitAuditApplyDTO.setBranchBankCode(e.getBranchBankCode());
        remitAuditApplyDTO.setOrderDesc(e.getOrderDesc());
        remitAuditApplyDTO.setRemarks(e.getRemarks());
        remitAuditApplyDTO.setBankAccountType(e.getBankAccountType());
        remitAuditApplyDTO.setReturnCode(e.getReturnCode());
        remitAuditApplyDTO.setReturnMsg(e.getReturnMsg());
        remitAuditApplyDTO.setMarketProductCode(e.getMarketProductCode());
        remitAuditApplyDTO.setRemitType(e.getRemitType());
        remitAuditApplyDTO.setBatchNo(e.getBatchNo());
        remitAuditApplyDTO.setClientIp(e.getClientIp());
        remitAuditApplyDTO.setSource(e.getSource());
        remitAuditApplyDTO.setTerminalType(e.getTerminalType());
        /* 付款记录 跳转 查询明细*/
        String bankName = merchantRemoteService.getBankNameByCode(e.getReceiverBankCode());
        remitAuditApplyDTO.setReceiverBankName(bankName);
        return remitAuditApplyDTO;
    }

}
