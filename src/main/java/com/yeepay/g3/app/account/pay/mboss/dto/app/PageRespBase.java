package com.yeepay.g3.app.account.pay.mboss.dto.app;

import java.io.Serializable;
import java.util.List;

/**
 * title:分页相应基础类 <br>
 * description: 分页相应基础类 <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 17:58
 */
public class PageRespBase<T> implements Serializable {

    private int total;
    private List<T> result;
    private int pageNo = 1;

    public PageRespBase() {
    }

    public int getTotal() {
        return this.total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<T> getResult() {
        return this.result;
    }

    public void setResult(List<T> result) {
        this.result = result;
    }

    public int getPageNo() {
        return this.pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }
}
