package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import java.io.Serializable;

public class BatchProxyTransferDTO implements Serializable {
    /**
     * 总数
     */
    private Integer totalCount;
    /**
     * 总金额
     */
    private String totalAmount;
    /**
     * 检查总笔数
     */
    private Integer totalCountCheck;

    /**
     * 检查总金额
     */
    private String totalAmountCheck;

    /**
     * 检查结果
     */
    private Boolean checkResult;

    /**
     * 重定向地址
     */
    private String redirectUrl;

    /**
     * 错误条数
     */
    private Integer errCount;

    /**
     * 批次号
     *
     * @return
     */
    private String batchNo;
    /**
     * 批次号
     *
     * @return
     */
    private String errorToken;

    public String getErrorToken() {
        return errorToken;
    }

    public void setErrorToken(String errorToken) {
        this.errorToken = errorToken;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getErrCount() {
        return errCount;
    }

    public void setErrCount(Integer errCount) {
        this.errCount = errCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalCountCheck() {
        return totalCountCheck;
    }

    public void setTotalCountCheck(Integer totalCountCheck) {
        this.totalCountCheck = totalCountCheck;
    }

    public String getTotalAmountCheck() {
        return totalAmountCheck;
    }

    public void setTotalAmountCheck(String totalAmountCheck) {
        this.totalAmountCheck = totalAmountCheck;
    }

    public Boolean getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(Boolean checkResult) {
        this.checkResult = checkResult;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

}
