package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import java.io.Serializable;
import java.math.BigDecimal;

public class ProxyTransferDetailCountSumRspDTO implements Serializable {
    private Integer count;
    private BigDecimal amount;
    private BigDecimal successAmount;
    private BigDecimal failAmount;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getSuccessAmount() {
        return successAmount;
    }

    public void setSuccessAmount(BigDecimal successAmount) {
        this.successAmount = successAmount;
    }

    public BigDecimal getFailAmount() {
        return failAmount;
    }

    public void setFailAmount(BigDecimal failAmount) {
        this.failAmount = failAmount;
    }
}
