package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import java.io.Serializable;
import java.math.BigDecimal;

public class ProxyTransferCountRspDTO  implements Serializable {
    private Integer count;
    private BigDecimal amount;
    private  BigDecimal serviceFee;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
