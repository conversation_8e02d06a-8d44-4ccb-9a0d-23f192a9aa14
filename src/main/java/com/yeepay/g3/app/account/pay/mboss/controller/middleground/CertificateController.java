package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.constant.PermissionConstant;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.CertificateTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.model.AccountInfoModel;
import com.yeepay.g3.app.account.pay.mboss.model.AccountSnapshotModel;
import com.yeepay.g3.app.account.pay.mboss.service.AccountRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.CertificateGeneratorUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.CustomerCertificateModel;
import com.yeepay.g3.app.account.pay.mboss.utils.LogFormatter;
import com.yeepay.g3.app.account.pay.mboss.vo.CapitalCertificateQueryResponseVO;
import com.yeepay.g3.app.account.pay.mboss.vo.CertificatePageVO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.mp.exception.UserBusinessException;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.config.utils.ConfigUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 凭证相关Controller
 *
 * <AUTHOR>
 * @since 2022/8/10 20:10
 */
@Controller
@RequestMapping("/certificate")
public class CertificateController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CertificateController.class);
    private static final String YEEPAY_NAME = "易宝支付有限公司";
    private static final String MERCHANT_INFO_TEMPLATE = "%s  %s";
    private static final List<String> REMOVE_ACCOUNT_TYPE_LIST = Lists.newArrayList("HANDLE_ACCOUNT", "REBATE_ACCOUNT");

    @Resource
    private AccountRemoteService accountRemoteService;

    @RequestMapping(value = "/view", method = RequestMethod.GET)
    @RequiresPermissions(PermissionConstant.CERTIFICATE_DOWNLOADER)
    public ModelAndView electronicCertificateUI() {
        return new ModelAndView("certificate/certificateIndex");
    }

    @RequestMapping(value = "/pageInit", method = RequestMethod.GET)
    @ResponseBody
    @RequiresPermissions(PermissionConstant.CERTIFICATE_DOWNLOADER)
    public BaseRespDTO<CertificatePageVO> pageInit() {
        CertificatePageVO certificatePageVO = null;
        String returnCode = null;
        String returnMessage = null;
        try {
            MerchantRespDTO currentMerchant = getCurrentMerchant();
            String merchantNo = currentMerchant.getMerchantNo();
            String signName = currentMerchant.getSignName();
            LOGGER.info("[电子凭证] - [页面渲染] merchantNo: {}, signName: {}, 电子凭证页面渲染.", merchantNo, signName);
            List<AccountInfoModel> accountInfoModelList = accountRemoteService.queryAccountInfoListByCustomerNo(merchantNo);
            String[] dateRange = getFormDateRange(accountInfoModelList);
            if (null == dateRange) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("未找到账户信息");
            }
            certificatePageVO = convertToPageVO(merchantNo, signName, dateRange);
            LOGGER.info("[电子凭证] - [页面渲染] 页面渲染结果: {}", LogFormatter.LOG_JSON_PRINTER.toJson(certificatePageVO));
        } catch (AccountPayException | UserBusinessException e) {
            LOGGER.error("[电子凭证] - [页面渲染] 页面渲染失败, cased by ", e.getMessage());
            returnCode = e.getDefineCode();
            returnMessage = e.getMessage();
        } catch (Exception e) {
            LOGGER.error("[电子凭证] - [页面渲染] 页面渲染失败, 系统未知异常, cased by ", e);
            returnCode = AccountPayException.SYS_ERROR.getDefineCode();
            returnMessage = AccountPayException.SYS_ERROR.getMessage();
        }
        if (null == certificatePageVO) {
            return BaseRespDTO.fail(returnCode, returnMessage);
        } else {
            return BaseRespDTO.success(certificatePageVO);
        }

    }

    private static CertificatePageVO convertToPageVO(String merchantNo, String signName, String[] dateRange) {
        CertificatePageVO certificatePageVO = new CertificatePageVO();
        certificatePageVO.setCertificateSupport(YEEPAY_NAME);
        certificatePageVO.setMerchantInfo(String.format(MERCHANT_INFO_TEMPLATE, merchantNo, signName));
        CertificatePageVO.CertificateInfo certificateInfo = new CertificatePageVO.CertificateInfo();
        certificateInfo.setCertificateType(CertificateTypeEnum.CAPITAL_CERTIFICATE.name());
        certificateInfo.setCertificateName(CertificateTypeEnum.CAPITAL_CERTIFICATE.getDescription());
        certificatePageVO.setCertificateTypeList(Lists.newArrayList(certificateInfo));
        certificatePageVO.setCertificateStartDate(dateRange[0]);
        certificatePageVO.setCertificateEndDate(dateRange[1]);
        return certificatePageVO;
    }

    @RequestMapping(value = "/querySnapshotBalance", method = RequestMethod.GET)
    @RequiresPermissions(PermissionConstant.CERTIFICATE_DOWNLOADER)
    @ResponseBody
    public BaseRespDTO<CapitalCertificateQueryResponseVO> querySnapshotBalance(
            @RequestParam("snapshotDate") String snapshotDateStr
    ) {
        CapitalCertificateQueryResponseVO responseVO = null;
        String returnCode = null;
        String returnMessage = null;
        try {
            MerchantRespDTO currentMerchant = this.getCurrentMerchant();
            String merchantNo = currentMerchant.getMerchantNo();
            LOGGER.info("[电子凭证] - [资产证明] - [查询快照] 请求参数, merchantNo: {}, snapshotDate: {}", merchantNo, snapshotDateStr);
            Date snapshotDate = getFormattedDate(snapshotDateStr);
            List<AccountSnapshotModel> snapshotModelList = accountRemoteService.queryAccountSnapshotByCustomerNo(merchantNo, snapshotDate);
            LOGGER.info("[电子凭证] - [资产证明] - [查询快照] 筛选并累计余额, merchantNo: {}, snapshotModelList: {}", merchantNo, LogFormatter.LOG_JSON_PRINTER.toJson(snapshotModelList));
            String formattedBalance = getFormattedBalance(snapshotModelList);
            LOGGER.info("[电子凭证] - [资产证明] - [查询快照] 累计结果, merchantNo: {}, formattedBalance: {}", merchantNo, formattedBalance);
            responseVO = new CapitalCertificateQueryResponseVO();
            responseVO.setAmount(formattedBalance);
        } catch (AccountPayException | UserBusinessException e) {
            LOGGER.error("[电子凭证] - [资产证明] - [查询快照] 快照查询失败, cased by ", e.getMessage());
            returnCode = e.getDefineCode();
            returnMessage = e.getMessage();
        } catch (ParseException e) {
            LOGGER.error("[电子凭证] - [资产证明] - [查询快照] 快照查询失败, 时间转换异常  snapshotDateStr: {} ", snapshotDateStr);
            returnCode = AccountPayException.SYS_ERROR.getDefineCode();
            returnMessage = "查询日期有误";
        } catch (Exception e) {
            LOGGER.error("[电子凭证] - [资产证明] - [查询快照] 快照查询失败, 系统未知异常, cased by ", e);
            returnCode = AccountPayException.SYS_ERROR.getDefineCode();
            returnMessage = AccountPayException.SYS_ERROR.getMessage();
        }
        if (null == responseVO) {
            return BaseRespDTO.fail(returnCode, returnMessage);
        } else {
            return BaseRespDTO.success(responseVO);
        }
    }

    @RequestMapping(value = "/downloadCertificate", method = RequestMethod.GET)
    @RequiresPermissions(PermissionConstant.CERTIFICATE_DOWNLOADER)
    @ResponseBody
    public void querySnapshotBalance(
            @RequestParam("snapshotDate") String snapshotDateStr,
            @RequestParam("certificateType") String certificateType,
            HttpServletResponse response
    ) {
        ByteArrayOutputStream outputStream = null;
        try {
            // 请求参数
            MerchantRespDTO currentMerchant = this.getCurrentMerchant();
            String merchantNo = currentMerchant.getMerchantNo();
            String signName = currentMerchant.getSignName();
            LOGGER.info("[电子凭证] - [资产证明] - [下载] 请求参数, merchantNo: {}, signName: {}, snapshotDate: {}, certificateType: {}",
                    merchantNo, signName, snapshotDateStr, certificateType);
            // 转换快照日期
            Date snapshotDate = getFormattedDate(snapshotDateStr);
            // 查快照
            List<AccountSnapshotModel> snapshotModelList = accountRemoteService.queryAccountSnapshotByCustomerNo(merchantNo, snapshotDate);
            LOGGER.info("[电子凭证] - [资产证明] - [下载] 筛选并累计余额, merchantNo: {}, snapshotModelList: {}", merchantNo, LogFormatter.LOG_JSON_PRINTER.toJson(snapshotModelList));
            // 筛选并且累计
            BigDecimal snapshotBalance = getSnapshotBalance(snapshotModelList);
            LOGGER.info("[电子凭证] - [资产证明] - [下载] 累计结果, merchantNo: {}, snapshotBalance: {}", merchantNo, snapshotBalance);
            // 凭证模型生成
            CustomerCertificateModel customerCertificateModel = generatorDownloadModel(merchantNo, signName, snapshotDate, snapshotBalance);
            // 文件流
            outputStream = CertificateGeneratorUtils.createCustomerCertificate(customerCertificateModel);
            String fileName = String.format("易宝支付资产证明凭证%s.pdf", new SimpleDateFormat("yyyyMMdd").format(snapshotDate));
            // 设置下载头
            setDownloadHeader(response, fileName);
            // 下载
            downloadFile(response, outputStream);
        } catch (AccountPayException | UserBusinessException e) {
            LOGGER.error("[电子凭证] - [资产证明] - [下载] 下载失败, cased by ", e.getMessage());
            throw e;
        } catch (ParseException e) {
            LOGGER.error("[电子凭证] - [资产证明] - [下载] 下载失败, 时间转换异常  snapshotDateStr: {} ", snapshotDateStr);
            throw AccountPayException.SYS_ERROR.newInstance("下载日期有误");
        } catch (Exception e) {
            LOGGER.error("[电子凭证] - [资产证明] - [下载] 快照查询失败, 系统未知异常, cased by ", e);
            throw AccountPayException.SYS_ERROR.newInstance("系统异常");
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                }
            }
        }
    }

    private static void setDownloadHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setContentType("application/octet-stream; charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
    }

    private static void downloadFile(HttpServletResponse response, ByteArrayOutputStream outputStream) throws IOException {
        outputStream.writeTo(response.getOutputStream());
        outputStream.flush();
    }

    private static CustomerCertificateModel generatorDownloadModel(String merchantNo, String signName, Date snapshotDate, BigDecimal snapshotBalance) {
        CustomerCertificateModel customerCertificateModel = new CustomerCertificateModel();
        customerCertificateModel.setCertificateNo(UUID.randomUUID().toString().replace("-", ""));
        customerCertificateModel.setMerchantNo(merchantNo);
        customerCertificateModel.setMerchantSignName(signName);
        customerCertificateModel.setCertificateDate(snapshotDate);
        customerCertificateModel.setAmount(snapshotBalance);
        customerCertificateModel.setApplyDate(new Date());
        return customerCertificateModel;
    }

    private Date getFormattedDate(String snapshotDateStr) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date snapshotDate = simpleDateFormat.parse(snapshotDateStr + " 00:00:00");
        return snapshotDate;
    }

    private BigDecimal getSnapshotBalance(List<AccountSnapshotModel> snapshotModelList) {
        List<String> filterAccountList = getFilterAccountList();
        BigDecimal snapshotBalance = Optional
                /* 容器化确保非空 */
                .ofNullable(snapshotModelList).orElse(Lists.newArrayList())
                /* 流化 */
                .stream()
                /* 筛掉后收手续费账户和返佣账户 */
                .filter(e -> !filterAccountList.contains(e.getAccountType()))
                /* 取金额 */
                .map(e -> e.getBalance())
                /* 累加 */
                .reduce(new BigDecimal("0"), (balance1, balance2) -> balance1.add(balance2));
        return snapshotBalance;
    }

    private String getFormattedBalance(List<AccountSnapshotModel> snapshotModelList) {
        BigDecimal snapshotBalance = getSnapshotBalance(snapshotModelList);
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        return decimalFormat.format(snapshotBalance);
    }


    private String[] getFormDateRange(List<AccountInfoModel> accountInfoModelList) {
        LOGGER.info("[电子凭证] - [页面渲染] 准备筛选符合条件的账户信息: {}", new Gson().toJson(accountInfoModelList));
        List<String> filterAccountList = getFilterAccountList();
        Date startDate = Optional
                /* 容器化确保非空 */
                .ofNullable(accountInfoModelList).orElse(Lists.newArrayList())
                /* 流化 */
                .stream()
                /* 过滤掉需要屏蔽的账户 */
                .filter(e -> !filterAccountList.contains(e.getAccountType()))
                /* 取开户时间 */
                .map(e -> e.getOpenDate())
                /* 找最小的那个 */
                .reduce((sourceDate, targetDate) -> Optional.ofNullable(sourceDate).orElse(targetDate).compareTo(targetDate) < 0 ? sourceDate : targetDate)
                .get();
        LOGGER.info("[电子凭证] - [页面渲染] 账户信息筛选结果: {}", startDate == null ? "null" : startDate);
        if (/* 开始时间是空, 说明虽然查到账户, 但是都被排除掉了 */ null == startDate) {
            return null;
        }
        Date endDate = DateUtils.addDay(new Date(), -1);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return new String[]{simpleDateFormat.format(startDate), simpleDateFormat.format(endDate)};
    }

    private List<String> getFilterAccountList() {
        List<String> resultList = Optional.ofNullable(ConfigUtils.getSnapshotFilterAccountList()).orElse(REMOVE_ACCOUNT_TYPE_LIST);
        return resultList;
    }

}
