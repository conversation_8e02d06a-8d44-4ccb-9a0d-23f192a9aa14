package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.BatchTransferCheckDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.BatchTransferValidateDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.ExcelValidateDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.annotation.ExcelColumn;
import com.yeepay.g3.app.account.pay.mboss.enumtype.FeeTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.ProductTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.TransferSecondProductCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.utils.excel.BatchTransferValidateFileUtil;
import com.yeepay.g3.app.account.pay.mboss.vo.BatchTransferCheckRespVo;
import com.yeepay.g3.app.account.pay.mboss.vo.BatchTransferConfirmRequestVo;
import com.yeepay.g3.app.account.pay.mboss.vo.BatchTransferConfirmResponseVo;
import com.yeepay.g3.app.account.pay.mboss.vo.BatchTransferSameDetailVo;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.BaseProductDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantProductQueryRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantinfoRespDTO;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.request.BatchTransferDetailDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.request.BatchTransferRequestDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.response.BatchTransferResponseDTO;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.FeeChargeTypeEnum;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.ReqSourceEnum;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.TransferTypeEnum;
import com.yeepay.g3.facade.unionaccount.transfer.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.transfer.facade.MgTransferFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.web.IpUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量转账
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/batchTransfer")
public class BatchTransferController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BatchTransferController.class);
    private static final Gson GSON = new Gson();
    private BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);
    MgTransferFacade mgTransferFacade = RemoteServiceFactory.getService(MgTransferFacade.class);

    @RequestMapping("/view")
    public ModelAndView view() {
        Map<String, Object> params = new HashMap<>();
        // check产品开通
        boolean isOpenTransferProduct=checkOpenTransferProduct(getCurrentCustomerNumber());
        params.put("isOpenTransferProduct", isOpenTransferProduct);
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        params.put("passwordRsaPublicKey", BacRsaKeysHolder.getPasswordRsaPublicKey());
        return new ModelAndView("mgTransfer/batchTransferIndex",params);
    }

    /**
     * 下载模版
     *
     * @param request
     * @param response
     */
    @RequestMapping("/downloadBatchTemplate")
    public void downloadBatchTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = ResourceUtils.getFile("classpath:/fileTemplate/易宝批量转账文件上传模板.xls");
            Workbook wb = WorkbookFactory.create(file);
            POIUtil.downLoadData(response, file.getName(), (HSSFWorkbook) wb);
        } catch (Exception ex) {
            logger.error("下载模版异常", ex);
            try {
                response.getWriter().write("<script type='text/javascript'>parent.mpAlert('下载模版异常')</script>");
            } catch (IOException e) {

            }
        }
    }


    /**
     * 批量转账预校验
     *
     * @return
     */
    @RequestMapping("/batchTransferCheck")
    @ResponseBody
    public BaseRespDTO batchTransferCheck(@RequestParam(value = "batchNo", required = false) String batchNo, @RequestParam("totalCount") Integer totalCount, @RequestParam("totalAmount") BigDecimal totalAmount, @RequestParam("file") MultipartFile file) {
        Long maxTotalCount = ConfigUtils.getBatchTransferMaxCount();
        if (totalCount > maxTotalCount) {
            return BaseRespDTO.fail("330204", "总笔数不能超过"+maxTotalCount+"条");
        }
        //1, 校验文件类型
        String filename = file.getOriginalFilename();
        if (filename == null || !(ExcelUtil.isExcel2003(filename))) {
            // 文件有问题
            return BaseRespDTO.fail("文件格式不符,请上传.xls格式的批量付款文件");
        }
        if(filename.length()>120){
            return BaseRespDTO.fail("文件名称长度不能超过120位");
        }
        if(totalCount == 0){
            return BaseRespDTO.fail("文件内容为空!请重新上传有效文件");
        }

        try {
            MerchantRespDTO currentMerchant = getCurrentMerchant();
            AccountInfoRespDTO accountInfo = businessCheckRemoteService.accountStatusAndBalance(currentMerchant.getMerchantNo(), AccountTypeEnum.FUND_ACCOUNT);

            if (StringUtils.isEmpty(batchNo)) {
                batchNo = String.valueOf(IdGenUtils.generateId());
                checkBatchNoAndFileName(currentMerchant.getMerchantNo(),null, filename);
            } else {
                if(batchNo.length()>32){
                    return BaseRespDTO.fail("批次号长度不能超过32位");
                }
                // 查询批次是否存在
                checkBatchNoAndFileName(currentMerchant.getMerchantNo(),batchNo, filename);
            }
            //解析excel
            List<BatchTransferValidateDTO> dataList = getBatchTransferValidateDTOS(file);
            if(dataList.size()==0){
                return BaseRespDTO.fail("文件内容为空!请重新上传有效文件");
            }
            if (dataList.size() != totalCount) {
                // 总笔数不一致
                throw AccountPayException.BATCH_TRANSFER_TOTAL_COUNT_ERROR;
            }


            int failCount = 0;
            BigDecimal failAmount = BigDecimal.ZERO;
            BigDecimal totalAmountSum = BigDecimal.ZERO;
            for (BatchTransferValidateDTO data : dataList) {
                BigDecimal transferAmount = BigDecimal.ZERO;
                try {
                    transferAmount = new BigDecimal(data.getTransferAmount());
                } catch (Exception ex) {
                    transferAmount = BigDecimal.ZERO;
                }
                if (!data.getValid()) {
                    failCount++;
                    failAmount=failAmount.add(transferAmount);
                }
                totalAmountSum = totalAmountSum.add(transferAmount);
                data.setRequestNo(IdGenUtils.getRequestNo(new Date()));
            }
            if (totalAmountSum.subtract(totalAmount).compareTo(BigDecimal.ZERO) != 0) {
                // 总金额不一致
                throw AccountPayException.BATCH_TRANSFER_TOTAL_AMOUNT_ERROR;
            }
            if (accountInfo.getBalance().subtract(totalAmount).compareTo(BigDecimal.ZERO) < 0) {
                return BaseRespDTO.fail("实付总金额需小于当前账户余额。");
            }
            if(!"AVAILABLE".equals(accountInfo.getAccountStatus())){
                return BaseRespDTO.fail("商户"+accountInfo.getMerchantNo()+"账户冻结,不能发起交易。");
            }
            String token = UUID.randomUUID().toString().replaceAll("-", "");
            BatchTransferCheckRespVo batchTransferCheckResponse = new BatchTransferCheckRespVo(token, batchNo, currentMerchant.getMerchantNo(), currentMerchant.getSignName(), accountInfo.getBalance(), totalCount, totalAmount, failCount, failAmount);
            if ("PASS".equals(batchTransferCheckResponse.getCheckStatus())) {
                // 查询疑似重复的
                List<BatchTransferSameDetailVo> sameList = queryAndBuildSameList(dataList, currentMerchant.getMerchantNo());
                batchTransferCheckResponse.setSameList(sameList);
            }
            RedisUtils.set(token, GSON.toJson(batchTransferCheckResponse), 2 * 3600);
            RedisUtils.set(token + "_data", GSON.toJson(new BatchTransferCheckDTO(batchNo, filename, dataList,token, currentMerchant.getMerchantNo(), totalAmount)), 2 * 3600);
            logger.info("batchTransferCheck finish resp={}", batchTransferCheckResponse);
            return BaseRespDTO.success(batchTransferCheckResponse);
        } catch (AccountPayException ex) {
            logger.error("batchTransferCheck 业务异常", ex);
            return BaseRespDTO.fail(ex.getDefineCode(), ex.getMessage());
        } catch (Exception ex) {
            logger.error("batchTransferCheck 未知异常", ex);
            return BaseRespDTO.fail(ex.getMessage());
        }
    }

    @RequestMapping("/batchTransferCheckResult")
    @ResponseBody
    public BaseRespDTO batchTransferCheckResult(@RequestParam("token") String token) {
        try {
            String str = RedisUtils.get(token);
            if (StringUtils.isEmpty(str)) {
                return BaseRespDTO.fail("数据不存在或已过期");
            }
            String merchantNo = getCurrentCustomerNumber();
            BatchTransferCheckRespVo respVo = GSON.fromJson(str, BatchTransferCheckRespVo.class);
            if (respVo.getMerchantNo().equals(merchantNo)) {
                return BaseRespDTO.success(respVo);
            } else {
                return BaseRespDTO.fail("非本人操作");
            }
        } catch (Exception ex) {
            logger.error("获取批量转账结果异常,token=" + token, ex);
            return BaseRespDTO.fail(ex.getMessage());
        }

    }


    @RequestMapping("/downloadBatchTransferCheckResult")
    public void downloadBatchTransferCheckResult(@RequestParam("token") String token, @RequestParam("bizType") String bizType, HttpServletResponse response) throws Exception {
        String dataStr = RedisUtils.get(token + "_data");
        if (StringUtils.isEmpty(dataStr)) {
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('下载数据不存在或已过期')</script>");
        }

        BatchTransferCheckDTO checkDTO = GSON.fromJson(dataStr, BatchTransferCheckDTO.class);
        String fileName = checkDTO.getBatchFileName().substring(0,checkDTO.getBatchFileName().lastIndexOf(".xls"));
        switch (bizType) {
            case "SAME_LIST":
                String batchTransferCheckResponseStr = RedisUtils.get(token);
                if (StringUtils.isEmpty(batchTransferCheckResponseStr)) {
                    response.getWriter().write("<script type='text/javascript'>parent.mpAlert('下载数据不存在或已过期')</script>");
                    return;
                }
                BatchTransferCheckRespVo batchTransferCheckRespVO = GSON.fromJson(batchTransferCheckResponseStr, BatchTransferCheckRespVo.class);
                if (CollectionUtils.isEmpty(batchTransferCheckRespVO.getSameList())) {
                    response.getWriter().write("<script type='text/javascript'>parent.mpAlert('下载数据不存在或已过期')</script>");
                    return;
                } else {
                    // 将重复项标记成校验未通过,然后去下载
                    checkDTO.getDataList().stream().forEach(e -> {
                        String key = new StringBuilder().append(e.getMerchantNo()).append("_").append(new BigDecimal(e.getTransferAmount()).setScale(2, RoundingMode.CEILING).toPlainString()).toString();
                        batchTransferCheckRespVO.getSameList().stream().forEach(se -> {
                            String key2 = new StringBuilder().append(se.getToMerchantNo()).append("_").append(se.getTransferAmount().setScale(2, RoundingMode.CEILING).toPlainString()).toString();
                            if (key2.equals(key)) {
                                e.setValid(false);
                                e.setErrorMessage(se.getMessage());
                            }
                        });
                    });
                    downloadList(fileName+ "_疑似重复转账结果.xls", checkDTO.getDataList(), response);
                }
                break;
            case "FAIL_LIST":
                downloadList(fileName+ "_校验失败结果.xls", checkDTO.getDataList(), response);
                break;
            default:
                response.getWriter().write("<script type='text/javascript'>parent.mpAlert('不支持的类型')</script>");
        }
    }


    @ResponseBody
    @RequestMapping("/confirm")
    public BaseRespDTO confirm(@RequestBody BatchTransferConfirmRequestVo batchTransferConfirmRequestVO, HttpServletRequest request) {
        try {
            //交易密码改为密文传输，需要解密
            String decryptPassWord = BACRsaUtil.privateDecrypt(batchTransferConfirmRequestVO.getPassword(), BacRsaKeysHolder.getPasswordRsaPrivateKey());
            ShiroUser user = getCurrentUser();
            MerchantRespDTO merchantInfo = getCurrentMerchant();
            //1.验证密码
            Long userId = user.getUserId();
            if (!userFacade.validateTradePassword(userId, decryptPassWord)) {
                return BaseRespDTO.fail("400333", "密码不正确");
            }
            String dataString = RedisUtils.get(batchTransferConfirmRequestVO.getToken() + "_data");
            if (StringUtils.isEmpty(dataString)) {
                logger.warn("token=[{}],已过期", batchTransferConfirmRequestVO.getToken());
                return BaseRespDTO.fail("token已过期");
            }
            logger.debug("confirm dataString=[{}]",dataString);
            BatchTransferCheckDTO batchTransferCheckDTO = GSON.fromJson(dataString, BatchTransferCheckDTO.class);
            BatchTransferRequestDTO batchTransferRequestDTO = new BatchTransferRequestDTO();
            batchTransferRequestDTO.setBatchNo(batchTransferCheckDTO.getBatchNo());
            batchTransferRequestDTO.setInitiateMerchantNo(batchTransferCheckDTO.getMerchantNo());
            batchTransferRequestDTO.setParentMerchantNo(batchTransferCheckDTO.getMerchantNo());
            batchTransferRequestDTO.setOperator(user.getLoginName());
            batchTransferRequestDTO.setReqSource(ReqSourceEnum.MP);
            batchTransferRequestDTO.setBatchFileName(batchTransferCheckDTO.getBatchFileName());
            batchTransferRequestDTO.setClientIp(IpUtils.getIpAddr(request));
            batchTransferRequestDTO.setSalesProductCode(businessCheckRemoteService.queryMarketProduct(batchTransferCheckDTO.getMerchantNo()));
            List<BatchTransferDetailDTO> details = new LinkedList<>();
            for (BatchTransferValidateDTO detail : batchTransferCheckDTO.getDataList()) {
                BatchTransferDetailDTO detailDTO = new BatchTransferDetailDTO();
                detailDTO.setOrderAmount(new BigDecimal(detail.getTransferAmount()).setScale(2, BigDecimal.ROUND_CEILING));
                detailDTO.setUsage(detail.getUsage());
                detailDTO.setRequestNo(detail.getRequestNo());
                detailDTO.setToMerchantName(detail.getMerchantName());
                detailDTO.setToMerchantNo(detail.getMerchantNo());
                detailDTO.setTransferType(TransferTypeEnum.B2B);
                detailDTO.setFromAccountType(AccountTypeEnum.FUND_ACCOUNT);
                detailDTO.setToAccountType(AccountTypeEnum.FUND_ACCOUNT);
                detailDTO.setFromMerchantName(merchantInfo.getSignName());
                detailDTO.setFromMerchantNo(merchantInfo.getMerchantNo());
                if(!StringUtils.isEmpty(detail.getFeeTaker())){
                    switch (detail.getFeeTaker()){
                        case "收款方":
                            detailDTO.setFeeChargeSide(FeeChargeTypeEnum.INSIDE);
                            break;
                        case "付款方":
                            detailDTO.setFeeChargeSide(FeeChargeTypeEnum.OUTSIDE);
                            break;
                    }
                }
                details.add(detailDTO);
            }
            batchTransferRequestDTO.setTransferDetails(details);
            logger.info("批量转账请求参数=[{}]",batchTransferRequestDTO);
            BatchTransferResponseDTO batchTransferResponseDTO = mgTransferFacade.batchTransferRequest(batchTransferRequestDTO);
            logger.info("批量转账返回[{}]",batchTransferResponseDTO);
            if(ErrorCode.SUCCESS.equals(batchTransferResponseDTO.getReturnCode())){
                try{
                    //用过了删除
                    RedisUtils.delete(batchTransferConfirmRequestVO.getToken());
                    RedisUtils.delete(batchTransferConfirmRequestVO.getToken() + "_data");
                }catch (Exception ex){}
                return BaseRespDTO.success(new BatchTransferConfirmResponseVo(batchTransferResponseDTO.getBatchNo(),DateUtils.getLongDateStr(),batchTransferResponseDTO.getAuditRequest(),batchTransferCheckDTO.getTotalAmount(),batchTransferCheckDTO.getTotalCount()));
            }else{
                return BaseRespDTO.fail(batchTransferResponseDTO.getReturnMsg());
            }
        } catch (Exception ex) {
            logger.error("批量转账确认异常", ex);
            return BaseRespDTO.fail(ex.getMessage());
        }

    }

    /**
     * 下载文件
     *
     * @param fileName
     * @param dataList
     * @param response
     * @throws Exception
     */
    private void downloadList(String fileName, List<BatchTransferValidateDTO> dataList, HttpServletResponse response) throws Exception {
        File destFile = null;
        try {
            File file = ResourceUtils.getFile("classpath:/fileTemplate/易宝批量转账文件上传模板.xls");
            destFile = File.createTempFile("data_", ".xls");
            FileUtil.copyFile(file, destFile);
            Workbook wb = WorkbookFactory.create(destFile);
            Sheet sheet = wb.getSheetAt(0);
            CellStyle errorStyle = wb.createCellStyle();
            //填充单元格
            errorStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
            //填黄色
            errorStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
            if (!CollectionUtils.isEmpty(dataList)) {
                int n = 2;
                for (BatchTransferValidateDTO data : dataList) {
                    Row row = sheet.getRow(n);
                    if (row == null) {
                        row = sheet.createRow(n);
                    }
                    if (data.getValid()) {
                        export2Cell(data, row, null);
                    } else {
                        export2Cell(data, row, errorStyle);
                    }
                    n++;
                }
            }
            POIUtil.downLoadData(response, fileName, (HSSFWorkbook) wb);
        } catch (Exception ex) {
            throw ex;
        } finally {
            if (destFile != null) {
                destFile.delete();
            }
        }


    }

    private <T extends ExcelValidateDTO> void export2Cell(T data, Row row, CellStyle errorStyle) {
        for (Field field : BatchTransferValidateDTO.class.getDeclaredFields()) {
            field.setAccessible(true);
            ExcelColumn an = field.getAnnotation(ExcelColumn.class);
            if (an != null) {
                Cell cell = getCell(row, an.col(), errorStyle);
                Object o = ReflectionUtils.getField(field, data);
                cell.setCellValue(o == null ? null : o.toString());
            }
        }
        if (!StringUtils.isEmpty(data.getErrorMessage())) {
            Cell cell = getCell(row, data.getErrorCellIndex(), errorStyle);
            cell.setCellValue(data.getErrorMessage());
        }

    }

    private Cell getCell(Row row, int rowIndex, CellStyle errorStyle) {
        Cell cell = row.getCell(rowIndex);
        if (cell == null) {
            cell = row.createCell(rowIndex);
        }
        if (errorStyle != null) {
            cell.setCellStyle(errorStyle);
        }
        return cell;
    }

    private void checkBatchNoAndFileName(String merchantNo,String batchNo, String fileName) {
        Map params = new HashMap();
        params.put("batch_no", batchNo);
        params.put("file_name", fileName);
        params.put("merchant_no",merchantNo);
        List<Map<String, Object>> list = QueryServiceUtil.query("accountTradeService", "queryTransferBatchOrFileNameExist", params);
        if (!CollectionUtils.isEmpty(list)) {
            String existFileName = list.stream().findFirst().get().get("batch_file_name").toString();
            if (fileName.equals(existFileName)) {
                throw AccountPayException.BATCH_TRANSFER_FILE_NAME_EXIST;
            } else {
                throw AccountPayException.BATCH_TRANSFER_BATCH_EXIST;
            }
        }

    }

    private List<BatchTransferValidateDTO> getBatchTransferValidateDTOS(MultipartFile file) throws IOException, InvalidFormatException {
        Workbook wb = WorkbookFactory.create(file.getInputStream());
        CellStyle errorStyle = wb.createCellStyle();
        //填充单元格
        errorStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        //填黄色
        errorStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
        Sheet sheet = wb.getSheetAt(0);
        List<BatchTransferValidateDTO> dataList = BatchTransferValidateFileUtil.validateAndGetData(sheet, 2, errorStyle, BatchTransferValidateDTO.class, (BatchTransferValidateDTO data) -> {
            if(!StringUtils.isEmpty(data.getFeeTaker())){
                switch (data.getFeeTaker()){
                    case "收款方":
                    case "付款方":
                        break;
                    default:
                        return "承担方仅支持传入[收款方]/[付款方]";
                }
            }
            if (data.getValid()) {
                try{
                    if(new BigDecimal(data.getTransferAmount()).scale()>2){
                        data.setValid(false);
                        data.setErrorMessage("[金额]小数位不能超过2位");
                    }
                }catch (Exception ex){
                    data.setValid(false);
                    data.setErrorMessage("[金额]格式不正确");
                }
            }
            if (data.getValid()) {
                try{
                    if(new BigDecimal(data.getTransferAmount()).scale()>2){
                        data.setValid(false);
                        data.setErrorMessage("[金额]小数位不能超过2位");
                    }
                }catch (Exception ex){
                    data.setValid(false);
                    data.setErrorMessage("[金额]格式不正确");
                }
                // 参数都填了
                try {
                    //校验商户名称去  ,这个接口加了SmartCache 所以不单独写缓存了
                    MerchantinfoRespDTO merchantInfo = businessCheckRemoteService.queryMerchantInfo(data.getMerchantNo());
                    if (!com.yeepay.g3.utils.common.StringUtils.equals(data.getMerchantName(), merchantInfo.getSignedName())) {
                        return "商户编号与商户名称不一致";
                    }
                } catch (Exception ex) {
                    return "校验商户名称异常," + ex.getMessage();
                }
            }
            return "";
        });
        return dataList;
    }

    private List<BatchTransferSameDetailVo> queryAndBuildSameList(List<BatchTransferValidateDTO> dataList, final String merchantNo) {
        Set<BatchTransferSameDetailVo> sameList = new HashSet<>();
        Date now = new Date();
        final Date minDate = DateUtils.truncate(DateUtils.addMinute(now, 0 - Math.abs(ConfigUtils.getTransferSameListDays())),Calendar.DATE);
        final Date maxDate = DateUtils.truncate(DateUtils.addDays(now, 1),Calendar.DATE);

        if (!CollectionUtils.isEmpty(dataList)) {
            Set<BatchTransferSameDetailVo> tmpSet = new HashSet<>();
            dataList.stream().forEach(e -> {
                if (tmpSet.contains(e)) {
                    BatchTransferSameDetailVo sameDetailVo = new BatchTransferSameDetailVo();
                    sameDetailVo.setTransferAmount(new BigDecimal(e.getTransferAmount()));
                    sameDetailVo.setToMerchantNo(e.getMerchantNo());
                    sameDetailVo.setToMerchantName(e.getMerchantName());
                    sameDetailVo.setMessage("同批次存在相同记录");
                    sameList.add(sameDetailVo);
                }
            });

            Collection<Collection<String>> collections = CollectionSplitUtil.splitCollection(dataList.stream().map(e -> new StringBuffer().append(e.getMerchantNo()).append("_").append(new BigDecimal(e.getTransferAmount()).setScale(2, RoundingMode.CEILING).toPlainString()).toString()).collect(Collectors.toSet()), 50);
            collections.stream().forEach(collection -> {
                Map<String, Object> params = new HashMap();
                params.put("from_customer_no", merchantNo);
                params.put("min_date", minDate);
                params.put("max_date",maxDate);
                params.put("condition", collection);
                List<Map<String, Object>> list = QueryServiceUtil.query("accountTradeService", "queryTransfer4SameList", params);
                if(logger.isDebugEnabled()){
                    logger.debug("queryTransfer4SameList params=[{}],result=[{}]", JSONUtils.toJsonString(params),JSONUtils.toJsonString(list));
                }
                if (!CollectionUtils.isEmpty(list)) {
                    sameList.addAll(list.stream().map(e -> {
                        BatchTransferSameDetailVo sameDetailVo = new BatchTransferSameDetailVo();
                        sameDetailVo.setTransferAmount(new BigDecimal(e.get("transfer_amount")==null?"0.00":e.get("transfer_amount").toString()));
                        sameDetailVo.setToMerchantNo(e.get("to_customer_no").toString());
                        sameDetailVo.setToMerchantName(e.get("to_customer_name").toString());
                        sameDetailVo.setMessage("近期有相同金额转账");
                        return sameDetailVo;
                    }).collect(Collectors.toSet()));
                }
            });
        }
        if (CollectionUtils.isEmpty(sameList)) {
            return Collections.emptyList();
        } else {
            return Lists.newArrayList(sameList);
        }
    }

    private boolean checkOpenTransferProduct(String merchantNo){
        String saleProductCode = businessCheckRemoteService.queryMarketProduct(merchantNo);
        try {
            MerchantProductQueryRespDTO merchantProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(merchantNo, ProductTypeEnum.ACCOUNT.toString(), saleProductCode, Costants.TRANSFER_BASICSPRODUCTFIRST, null);
            logger.info("查询产品开通，merchantProductQueryRespDTO={}", JSON.toJSONString(merchantProductQueryRespDTO));
            if ("0000".equals(merchantProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductQueryRespDTO.getBaseProductList())) {
                for (BaseProductDTO baseProductDTO : merchantProductQueryRespDTO.getBaseProductList()) {
                    if (!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode())) {
                        String secondBaseProductCode = baseProductDTO.getSecondBaseProductCode();
                        if (TransferSecondProductCodeEnum.B2B.name().equals(secondBaseProductCode)) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("查询产品开通发生异常，异常信息为: ", e);
            throw e;
        }
        return false;
    }
}
