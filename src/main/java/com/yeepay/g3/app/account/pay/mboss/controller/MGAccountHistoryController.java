package com.yeepay.g3.app.account.pay.mboss.controller;

import com.yeepay.g3.app.account.pay.mboss.dto.MGAccountHistoryQueryParam;
import com.yeepay.g3.app.account.pay.mboss.dto.MGAccountHistoryQueryResultParam;
import com.yeepay.g3.app.account.pay.mboss.enumtype.MpAccountHisBizTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MGAccountHistoryDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.DateUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.QueryAccountHistoryRequestDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.QueryAccountHistoryDetailDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.QueryAccountHistoryRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.AccountHisBizTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.facade.AccountHistoryFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.yeepay.g3.app.account.pay.mboss.utils.CheckParamUtils.isOver100DaysInterval;

/**
 * @Description: 中台账务历史相关
 * <AUTHOR>
 * @date 2020-07-30 17:50
 */
@Controller
@RequestMapping("/accountHistory")
public class MGAccountHistoryController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MGAccountHistoryController.class);

    /**
     * 账户管理系统-资金流水相关
     */
    private AccountHistoryFacade accountHistoryFacade = RemoteServiceFactory.getService(AccountHistoryFacade.class);

    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";

    // 默认当前
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    //时间格式yyyy-MM-dd
    private static final String DATE_FORMAT = "^[1-9]\\d{3}-[01][0-9]-[0-9]{2}$";

    /**
     * @Description:
     * <AUTHOR>
     * @date 2020-07-30 18:14
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping("/view")
    public ModelAndView view(){
        ModelAndView mav = new ModelAndView();
        boolean isAccount = true;
        //是否开通支付账户
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(getCurrentCustomerNumber(), AccountTypeEnum.FUND_ACCOUNT);
        //账户不存在
        if("UA30010".equalsIgnoreCase(queryAccountResponseDto.getReturnCode())) {
            isAccount = false;
        }
        //定义的账务历史查询的业务枚举类型，放map里为了展示
        MpAccountHisBizTypeEnum[] accountHisBizTypeEnums = MpAccountHisBizTypeEnum.values();
        Map<String, String> accountHisBizTypeMap = new HashMap<String, String>();
        for (int i = 0; i < accountHisBizTypeEnums.length; i++) {
            accountHisBizTypeMap.put(accountHisBizTypeEnums[i].getEnumName(), accountHisBizTypeEnums[i].getDesc());
        }
        mav.addObject("accountHisBizTypeMap", accountHisBizTypeMap);
        mav.addObject("isAccount", isAccount);
        mav.setViewName("mgAccountHistory/view");
        return mav;
    }


    /**
     * @Description: 查询账户历史
     * <AUTHOR>
     * @date 2020-07-30 18:42
     * @param mgAccountHistoryQueryParam:
     * @param pageSize:
     * @param pageNo:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/query")
    @ResponseBody
    public ResponseMessage query(
            MGAccountHistoryQueryParam mgAccountHistoryQueryParam,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage("success");
        LOGGER.info("查询资金流水 param={},pageSize={},pageNo={}",
                ToStringBuilder.reflectionToString(mgAccountHistoryQueryParam), pageSize, pageNo);

        //参数校验
        checkQueryHisParam(mgAccountHistoryQueryParam);

        QueryAccountHistoryRespDTO queryAccountHistoryRespDTO;
        try {
            QueryAccountHistoryRequestDTO queryAccountHistoryRequestDTO = new QueryAccountHistoryRequestDTO();
            //商户编号
            queryAccountHistoryRequestDTO.setMerchantNo(getCurrentCustomerNumber());
            //收支类型
            queryAccountHistoryRequestDTO.setDirection(mgAccountHistoryQueryParam.getDirection());
            //查询开始时间
            queryAccountHistoryRequestDTO.setStartTime(DateUtil.string2Date(mgAccountHistoryQueryParam.getCreateStartDate() + " 00:00:00", DateUtil.PATTERN_STANDARD19H));
            //查询结束时间
            queryAccountHistoryRequestDTO.setEndTime(DateUtil.string2Date(mgAccountHistoryQueryParam.getCreateEndDate() + " 24:00:00", DateUtil.PATTERN_STANDARD19H));
            //页
            queryAccountHistoryRequestDTO.setPageNo(pageNo);
            //条
            queryAccountHistoryRequestDTO.setPageSize(pageSize);
            //业务类型
            queryAccountHistoryRequestDTO.setBizTypes(mgAccountHistoryQueryParam.getBizType());
            LOGGER.info("查询资金流水，入参{}", JSONUtils.toJsonString(queryAccountHistoryRequestDTO));
            queryAccountHistoryRespDTO = accountHistoryFacade.query(queryAccountHistoryRequestDTO);
            LOGGER.info("查询资金流水，返参{}", JSONUtils.toJsonString(queryAccountHistoryRespDTO));

        } catch (Throwable e) {
            LOGGER.error("账务历史查询异常",e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("账务历史查询异常");
            return resMsg;
        }
        if ("UA00000".equals(queryAccountHistoryRespDTO.getReturnCode()) && queryAccountHistoryRespDTO.getTotalNum() > 0) {
            resMsg.put("totalCount", queryAccountHistoryRespDTO.getTotalNum());
            List<QueryAccountHistoryDetailDTO> queryAccountHistoryDetailDTOS = queryAccountHistoryRespDTO.getAccountHistoryDetailDtoList();
            //处理结果集
            resMsg.put("accountHistoryQueryResult", dealResult(queryAccountHistoryDetailDTOS));
            resMsg.put("debitCount", queryAccountHistoryRespDTO.getTotalDebitNum());
            resMsg.put("debitAmount", queryAccountHistoryRespDTO.getTotalDebitAmount());
            resMsg.put("creditCount", queryAccountHistoryRespDTO.getTotalCreditNum());
            resMsg.put("creditAmount", queryAccountHistoryRespDTO.getTotalCreditAmount());
        }else {
            resMsg.put("totalCount", 0);
            resMsg.put("accountHistoryQueryResult", new ArrayList<QueryAccountHistoryDetailDTO>());
            resMsg.put("debitCount", 0);
            resMsg.put("debitAmount", 0.00);
            resMsg.put("creditCount", 0);
            resMsg.put("creditAmount", 0.00);
        }
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        return resMsg;
    }

    /**
     * @Description: 下载资金流水
     * <AUTHOR>
     * @date 2020-08-03 18:26
     * @param mgAccountHistoryQueryParam:
     * @param request:
     * @param response:
     * @return void
     */
    @RequestMapping(value = "/download")
    public void download(MGAccountHistoryQueryParam mgAccountHistoryQueryParam, HttpServletRequest request, HttpServletResponse response) throws Exception{
        try{
            //参数校验
            checkQueryHisParam(mgAccountHistoryQueryParam);
            mgAccountHistoryQueryParam.setCustomerNumber(getCurrentCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("账务资金流水查询,").append(mgAccountHistoryQueryParam.getCreateStartDate()).append("至").append(mgAccountHistoryQueryParam.getCreateEndDate()).append("数据");
            new MGAccountHistoryDownloadService(getCurrentUser(), mgAccountHistoryQueryParam, desc.toString(),"账务资金流水查询-").download(request,response);
        }catch (Throwable ex){
            LOGGER.error("downloadRecord-error",ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('"+ex.getMessage()+"')</script>");
        }
    }


    /**
     * @Description: 校验查询资金流水参数
     * <AUTHOR>
     * @date 2020-08-03 18:40
     * @param param:
     * @return void
     */
    public static void checkQueryHisParam(MGAccountHistoryQueryParam param) {

        String direction = param.getDirection();
        String createStartDate = param.getCreateStartDate();
        String createEndDate = param.getCreateEndDate();
        if(CheckUtils.isEmpty(direction)) {
            param.setDirection("ALL");
        }else {
            if(!"CREDIT".equals(direction) && !"DEBIT".equals(direction) && !"ALL".equals(direction)) {
                throw new RuntimeException("收支类型不正确!");
            }
        }

        if(CheckUtils.isEmpty(createStartDate)) {
            throw new RuntimeException("请选择查询开始时间!");
        }
        if(CheckUtils.isEmpty(createEndDate)) {
            throw new RuntimeException("请选择查询结束时间!");
        }

        // 请求开始时间
        if (org.apache.commons.lang.StringUtils.isNotEmpty(createStartDate)
                && !createStartDate.matches(DATE_FORMAT)) {
            LOGGER.error("请求开始时间 createStartDate入参格式有误! param = {}",
                    createStartDate);
            throw new RuntimeException("请输入正确的时间格式!");
        }

        // 请求结束时间
        if (org.apache.commons.lang.StringUtils.isNotEmpty(createEndDate)
                && !createEndDate.matches(DATE_FORMAT)) {
            LOGGER.error("请求结束时间 createEndDate入参格式有误! param = {}",
                    createEndDate);
            throw new RuntimeException("请输入正确的时间格式!");
        }

        // 请求开始时间不能超过结束时间
        if (org.apache.commons.lang.StringUtils.isNotEmpty(createStartDate)
                && org.apache.commons.lang.StringUtils.isNotEmpty(createEndDate)) {
            try {
                Date startDate = DateUtils.parseDate(
                        createStartDate,
                        DateUtils.DATE_FORMAT_DATEONLY);
                Date endDate = DateUtils.parseDate(createEndDate,
                        DateUtils.DATE_FORMAT_DATEONLY);

                if (DateUtils.compareDate(startDate, endDate, Calendar.DATE) > 0) {
                    LOGGER.error(
                            "请求开始时间大于请求结束时间!入参格式有误! createStartDate={},createEndDate={}",
                            createStartDate, createEndDate);
                    throw new RuntimeException("查询结束时间不能早于开始时间!");
                }
                // 请求开始时间和请求结束时间不能超过100天
                if (isOver100DaysInterval(startDate, endDate)) {
                    LOGGER.error(
                            "请求开始时间和请求结束时间间隔超过100天!入参格式有误! createStartDate={},createEndDate={}",
                            createStartDate, createEndDate);
                    throw new RuntimeException("查询时间范围不能超过3个月!");
                }
            } catch (ParseException e) {
                LOGGER.error("这不可能报错,别闹了...");
            }
        }
    }

    /**
     * @Description: 处理查询出来的结果集
     * <AUTHOR>
     * @date 2020-08-07 10:36
     * @param queryAccountHistoryDetailDTOS:
     * @return java.util.List<com.yeepay.g3.facade.unionaccount.manage.dto.response.QueryAccountHistoryDetailDTO>
     */
    public static List<MGAccountHistoryQueryResultParam> dealResult(List<QueryAccountHistoryDetailDTO> queryAccountHistoryDetailDTOS) {
        List<MGAccountHistoryQueryResultParam> mgAccountHistoryQueryResultParams = new ArrayList<MGAccountHistoryQueryResultParam>();
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for(int i = 0 ; i < queryAccountHistoryDetailDTOS.size(); i++) {
            QueryAccountHistoryDetailDTO queryAccountHistoryDetailDTO = queryAccountHistoryDetailDTOS.get(i);
            MGAccountHistoryQueryResultParam accountHistoryQueryResultParam = new MGAccountHistoryQueryResultParam();
            BeanUtils.copyProperties(queryAccountHistoryDetailDTO, accountHistoryQueryResultParam);
            accountHistoryQueryResultParam.setTrxTime(smf.format(queryAccountHistoryDetailDTO.getTrxTime()));
            accountHistoryQueryResultParam.setTrxOppNo("-");
            accountHistoryQueryResultParam.setTrxAmount(queryAccountHistoryDetailDTO.getTrxAmount().toString());
            accountHistoryQueryResultParam.setPostBalance(queryAccountHistoryDetailDTO.getPostBalance().toString());
            String bizType = queryAccountHistoryDetailDTO.getBizType();
            if(AccountHisBizTypeEnum.RECHARGE.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("充值");
            }else if(AccountHisBizTypeEnum.TRANSFER.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setTrxOppNo(queryAccountHistoryDetailDTO.getTrxOppNo());
                accountHistoryQueryResultParam.setBizType("转账");
            }else if(AccountHisBizTypeEnum.CASH_OUT.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("提现");
            }else if(AccountHisBizTypeEnum.PAYMENT_OUT.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("企业付款");
            }else if(AccountHisBizTypeEnum.REFUND.toString().equals(bizType) ||
                    AccountHisBizTypeEnum.REFUND_CANCEL.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("退款");
            }else if(AccountHisBizTypeEnum.ACQUIRING.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("交易手续费");
            }else if(AccountHisBizTypeEnum.SEPARATE_REFUND.toString().equals(bizType) ||
                    AccountHisBizTypeEnum.SEPARATE.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("分账");
            }else if(AccountHisBizTypeEnum.INCREASE.toString().equals(bizType) ||
                    AccountHisBizTypeEnum.REDUCE.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("调账");
            }else if(AccountHisBizTypeEnum.ACCOUNT_PAY_CREDIT.toString().equals(bizType) ||
                    AccountHisBizTypeEnum.ACCOUNT_PAY_DEBIT.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("账户支付");
            }else if(AccountHisBizTypeEnum.INNER_SYSTEM_TRANSFER.toString().equals(bizType) ||
                    AccountHisBizTypeEnum.INNER_SYSTEM_TRANSFER_REVERSE.toString().equals(bizType)) {
                accountHistoryQueryResultParam.setBizType("资金调整");
            }else {
                accountHistoryQueryResultParam.setBizType("其他");
            }
            accountHistoryQueryResultParam.setDirection("CREDIT".equals(queryAccountHistoryDetailDTO.getDirection()) ? "收入" : "支出");
            mgAccountHistoryQueryResultParams.add(accountHistoryQueryResultParam);
        }
        return mgAccountHistoryQueryResultParams;
    }

}
