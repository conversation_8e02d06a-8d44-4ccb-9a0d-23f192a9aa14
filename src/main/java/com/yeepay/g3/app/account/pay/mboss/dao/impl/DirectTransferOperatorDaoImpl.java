package com.yeepay.g3.app.account.pay.mboss.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.yeepay.g3.app.account.pay.mboss.dao.DirectTransferOperatorDao;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorEntity;
import com.yeepay.g3.utils.persistence.mybatis.GenericDaoDefault;


public class DirectTransferOperatorDaoImpl  extends GenericDaoDefault<DirectTransferOperatorEntity> implements DirectTransferOperatorDao {

    @Override
    public List<DirectTransferOperatorEntity> queryByDebitCustomerNo(String debutCustomerNo) {
        return (List<DirectTransferOperatorEntity>)super.getSqlSession().selectList("DirectTransferOperatorEntity.queryByDebitCustomerNo", debutCustomerNo);
    }

    @Override
    public DirectTransferOperatorEntity queryByDebitCustomerNoAndLoginName(
            String debutCustomerNo, String loginName) {
        Map<String,String> parameter = new HashMap<String,String>();
        parameter.put("debitCustomerNo",debutCustomerNo);
        parameter.put("loginName", loginName);
        return (DirectTransferOperatorEntity) super.getSqlSession().selectOne("queryByDebitCustomerNoAndLoginName", parameter);
    }
    
    
}
