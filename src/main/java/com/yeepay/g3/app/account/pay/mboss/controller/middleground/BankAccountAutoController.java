package com.yeepay.g3.app.account.pay.mboss.controller.middleground;


import com.yeepay.g3.app.account.pay.mboss.dto.AddAutoRechargeRuleReqParam;
import com.yeepay.g3.app.account.pay.mboss.dto.CancelAutoRechargeRuleReqParam;
import com.yeepay.g3.app.account.pay.mboss.dto.QueryAutoRechargeRuleReqParam;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.service.BankAccountManageService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AddAutoRechargeRuleRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.CancelAutoRechargeRuleRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.QueryAutoRechargeRuleRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.QueryBankAccountAutoRuleRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;


/**
 * @ClassName: BankAccountAutoController
 * @Description: 银行账户自动管理
 * <AUTHOR>
 * @Date 2024/4/25
 * @Version 1.0
 */
@Controller
@Api(tags = "银行账户自动管理-API")
@RequestMapping("/bankAccount/auto")
public class BankAccountAutoController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BankAccountAutoController.class);

    @Autowired
    private BankAccountManageService bankAccountManageService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;


    /**
     * 查询生效规则
     *
     * @param payerAccountNo
     * @return
     */
    @RequestMapping(value = "rule/queryEffective", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询是否设置过规则")
    public ResponseMessage queryEffectiveRule(@RequestParam(value = "payerAccountNo") String payerAccountNo,
                                              @RequestParam(value = "bankCode") String bankCode,
                                              @RequestParam(value = "merchantNo", required = false) String merchantNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtils.isBlank(payerAccountNo)) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("银行编码不能为空");
            }
            ShiroUser currentUser = getCurrentUser();
            if (StringUtils.isBlank(merchantNo)) {
                merchantNo = currentUser.getCustomerNumber();
            }
            String merchantName = merchantRemoteService.getMerchantName(merchantNo);
            Map<String, Object> resp = new HashMap<>();
            logger.info("查询当前账户有没有设置过自动充值规则，商编为={},银行={}", merchantNo, payerAccountNo);
            QueryBankAccountAutoRuleRespDTO effectiveRule = bankAccountManageService.queryEffectAutoRechargeRule(merchantNo, payerAccountNo);
            if (!"UA30019".equals(effectiveRule.getReturnCode())) {
                resp.put("effectiveRule", effectiveRule);
            }
            resp.put("merchantName", merchantName);
            resp.put("merchantNo", merchantNo);
            StringBuilder payAccountNoView = new StringBuilder();
            payAccountNoView.append(BankAccountBankCodeEnum.getDescriptionByBankCode(bankCode));
            payAccountNoView.append(payerAccountNo.substring(payerAccountNo.length() - 4));
            resp.put("payAccountNoView", payAccountNoView.toString());
            resp.put("payerAccountNo", payerAccountNo);
            resp.put("bankCode", bankCode);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询当前账户有没有设置过自动充值规则,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询当前账户有没有设置过自动充值规则,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 新增自动充值规则
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "rule/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("新增自动充值规则")
    public ResponseMessage addRule(@RequestBody AddAutoRechargeRuleReqParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("新增自动充值规则，请求参数{}", JSONUtils.toJsonString(param));
        try {
            param.validateParam();
            ShiroUser currentUser = getCurrentUser();
            if (StringUtils.isBlank(param.getMerchantNo())) {
                param.setMerchantNo(currentUser.getCustomerNumber());
            }
            param.setOperator(currentUser.getLoginName());
            AddAutoRechargeRuleRespDTO resp = bankAccountManageService.addRule(param);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("新增自动充值规则,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("新增自动充值规则,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 失效自动充值规则
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "rule/cancel", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("失效自动充值规则")
    public ResponseMessage cancelRule(@RequestBody CancelAutoRechargeRuleReqParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("失效自动充值规则，请求参数{}", JSONUtils.toJsonString(param));
        try {
            param.validateParam();
            ShiroUser currentUser = getCurrentUser();
            param.setOperator(currentUser.getLoginName());
            CancelAutoRechargeRuleRespDTO resp = bankAccountManageService.cancelRule(param);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("失效自动充值规则,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("失效自动充值规则,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询规则列表
     *
     * @param payerAccountNo
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "rule/queryAll", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询自动充值规则列表")
    public ResponseMessage queryAllRule(@RequestParam(value = "payerAccountNo") String payerAccountNo,
                                        @RequestParam(value = "pageNo") Integer pageNo,
                                        @RequestParam(value = "pageSize") Integer pageSize,
                                        @RequestParam(value = "merchantNo", required = false) String merchantNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            ShiroUser currentUser = getCurrentUser();
            QueryAutoRechargeRuleReqParam param = new QueryAutoRechargeRuleReqParam();
            param.setPageSize(pageSize);
            param.setPageNo(pageNo);
            if (StringUtils.isBlank(merchantNo)) {
                merchantNo = currentUser.getCustomerNumber();
            }
            param.setMerchantNo(merchantNo);
            param.setPayerAccountNo(payerAccountNo);
            logger.info("查询自动充值规则列表，请求参数为={}", JSONUtils.toJsonString(param));
            QueryAutoRechargeRuleRespDTO resp = bankAccountManageService.queryPageRule(param);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询自动充值规则列表,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询自动充值规则列表,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

}
