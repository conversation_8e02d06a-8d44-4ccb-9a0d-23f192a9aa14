package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.service.SingleRemitService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.vo.RemitSingleRequestVo;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.facade.mp.exception.ExceptionWrapper;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.RemitRequestDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitBankAccountTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.trade.facade.RemitFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yibao.utils.json.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/5 19:08
 */
@Controller
@RequestMapping("/remit")
public class SingleRemitController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SingleRemitController.class);


    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    //企业付款
    private RemitFacade remitFacade = RemoteServiceFactory.getService(RemitFacade.class);

    @Autowired
    private SingleRemitService singleRemitService;

    public static final String SINGLE_REMIT_SMS_CODE_TYPE = "CONFIRM_SEND_REMIT";


    @RequestMapping("/perRemit")
    public ModelAndView perRemit(@RequestParam String tradeType) throws Exception{
        ModelAndView mv = new ModelAndView("singleRemit/perRemit");
        mv.addObject("productType",tradeType);
        mv.addObject("UIWebRootUrl",WebPropertiesHolder.getUIWebRootUrl());
        return mv;
    }


    /**
     * 下单前的参数预校验
     * @param remitSingleRequestVo
     * @return
     */
    @RequestMapping("/checkParam")
    @ResponseBody
    public BaseRespDTO checkParam(@RequestBody RemitSingleRequestVo remitSingleRequestVo){
        try{
            logger.info("单笔付款参数预校验：remitSingleRequestVo:{}", JsonUtils.toJson(remitSingleRequestVo));
            remitSingleRequestVo.validateParam();
            if(StringUtils.isNotBlank(remitSingleRequestVo.getRequestNo())){
                ShiroUser user = super.getCurrentUser();
                singleRemitService.checkRequestNo(remitSingleRequestVo.getRequestNo(),user.getCustomerNumber());
            }
            return BaseRespDTO.success();
        }catch (Exception ex){
            logger.error("下单前的参数预校验，返回信息为={}",ex);
            return BaseRespDTO.fail(ex.getMessage());
        }

    }


    /**
     * 单笔付款下单
     * @param remitSingleRequestVo
     * @return
     */
    @RequestMapping("/single")
    @ResponseBody
    public BaseRespDTO single(HttpServletRequest request,@RequestBody RemitSingleRequestVo remitSingleRequestVo){
        logger.info("单笔付款下单参数：remitSingleRequestVo:{}", JsonUtils.toJson(remitSingleRequestVo));
        ShiroUser user = super.getCurrentUser();
        checkArgument(StringUtils.isNotBlank(remitSingleRequestVo.getPasswd()));
        checkArgument(StringUtils.isNotBlank(remitSingleRequestVo.getSmsCode()));
        //交易密码改为密文传输，需要解密
        String decryptPassWord = BACRsaUtil.privateDecrypt(remitSingleRequestVo.getPasswd(), ConfigUtils.getPrivateKey());
        //1.验证密码
        if (!userFacade.validateTradePassword(user.getUserId(), decryptPassWord)) {
            return BaseRespDTO.fail("单笔付款交易密码不正确");
        }
        //短信验证
        try {
            SendSmsUtils.checkVaildFrequency(request, user.getUserId(), remitSingleRequestVo.getSmsCode(), SINGLE_REMIT_SMS_CODE_TYPE);
        } catch (ExceptionWrapper e) {
            return BaseRespDTO.fail(e.getMessage());
        }
        RemitRequestDTO remitReqDTO = new RemitRequestDTO();
        BeanUtils.copyProperties(remitSingleRequestVo,remitReqDTO);
        //ip地址获取
        remitReqDTO.setClientIp(NetUtils.getRemoteIP(request));
        remitReqDTO.setMerchantNo(getCurrentCustomerNumber());
        remitReqDTO.setInitiateMerchantNo(getCurrentCustomerNumber());
        remitReqDTO.setParentMerchantNo(getCurrentCustomerNumber());
        //商户来源的区分
        remitReqDTO.setSource("singleMp");
        remitReqDTO.setOperator(getCurrentUserSafe().getLoginName());


        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
        if(CheckUtils.isEmpty(markProductCode)){
            return BaseRespDTO.fail("单笔付款请求营销产品码异常");
        }
        remitReqDTO.setMarketProductCode(markProductCode);
        remitReqDTO.setRequestNo(remitSingleRequestVo.getRequestNo());
        remitReqDTO.setRemark(remitSingleRequestVo.getOrderInfo());
        remitReqDTO.setOrderInfo(remitSingleRequestVo.getOrderInfo());
        remitReqDTO.setComments(remitSingleRequestVo.getComments());
        remitReqDTO.setOrderAmount(remitSingleRequestVo.getOrderAmount());
        remitReqDTO.setBranchBankCode(remitSingleRequestVo.getBranchBankCode());
        remitReqDTO.setReceiverBankCode(remitSingleRequestVo.getReceiverBankCode());
        remitReqDTO.setReceiverAccountName(remitSingleRequestVo.getReceiverAccountName());
        remitReqDTO.setReceiverAccountNo(remitSingleRequestVo.getReceiverAccountNo());
        remitReqDTO.setTradeType(TradeTypeEnum.ENTERPRISE_PAYMENT);
        if("RJT".equals(remitSingleRequestVo.getProductType())){
            remitReqDTO.setTradeType(TradeTypeEnum.RJT);
            remitReqDTO.setReceiveType(RemitTypeEnum.REAL_TIME);
        }else{
            //时效
            if(!StringUtils.isEmpty(remitSingleRequestVo.getReceiveType())){
                remitReqDTO.setReceiveType(RemitTypeEnum.valueOf(remitSingleRequestVo.getReceiveType()));
            }else{
                return BaseRespDTO.fail("到账时效必填");
            }
        }
        remitReqDTO.setBankAccountType(RemitBankAccountTypeEnum.valueOf(singleRemitService.dealBankAccountType(remitSingleRequestVo.getReceiverAccountName(), remitSingleRequestVo.getReceiverAccountNo())));
        if(StringUtils.isEmpty(remitReqDTO.getRequestNo())){
            remitReqDTO.setRequestNo(IdGenUtils.getRequestNo(new Date()));
        }
        try {
            RemitRespDTO remitRespDTO = remitFacade.initiateRemit(remitReqDTO);
            if (remitRespDTO != null) {
                String returnCode = remitRespDTO.getReturnCode();
                if (returnCode.equals(ErrorCode.SUCCESS)) {
                    Map<String,Object> resultMap = new HashMap<>();
                    resultMap.put("requestNo",remitRespDTO.getRequestNo());
                    resultMap.put("orderNo",remitRespDTO.getOrderNo());
                    resultMap.put("status",remitRespDTO.getStatus());
                    resultMap.put("orderTime",DateUtil.date2String(new Date(),"yyyy-MM-dd HH:mm:ss"));
                    resultMap.put("orderAmount", NumberUtils.formateNum(null, remitReqDTO.getOrderAmount()));
                    return BaseRespDTO.success(resultMap);
                }else{
                    return BaseRespDTO.fail(remitRespDTO.getReturnMsg());
                }
            }
        } catch (Throwable e) {
            logger.info("单笔付款异常，requestNo={}，异常信息={}",remitSingleRequestVo.getRequestNo(),e.getMessage());
        }
        return BaseRespDTO.fail("单笔付款异常，请核实出资情况，避免重复出资风险！");
    }


    /**
     * 获取初始化的信息
     * @param request
     * @return
     */
    @RequestMapping("/init")
    @ResponseBody
    public BaseRespDTO init(HttpServletRequest request){
        ShiroUser user = super.getCurrentUser();
        if (user != null) {
            try {
                Map<String,Object> map = singleRemitService.getInitInfo(user.getCustomerNumber());
                return BaseRespDTO.success(map);
            }catch (Exception e){
                logger.error("获取初始化的信息异常", e);
                return BaseRespDTO.fail("获取初始化的信息接口异常");
            }
        }
        return BaseRespDTO.success(null);
    }


    /**
     * 联系人的页面
     * @return
     * @throws Exception
     */
    @RequestMapping("/contact")
    public ModelAndView contact(HttpServletRequest request) throws Exception{
        ModelAndView mv = new ModelAndView("singleRemit/contact");
        mv.addObject("UIWebRootUrl",WebPropertiesHolder.getUIWebRootUrl());
        logger.info("联系人的页面菜单,{}",request.getAttribute("tabMenu"));
        return mv;
    }
}
