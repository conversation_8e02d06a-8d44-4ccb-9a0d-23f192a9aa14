package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Mr.yin
 * @date: 2024/7/25  14:55
 */
@ApiModel(description = "预估订单手续费信息")

public class PreFeeInfoModel implements Serializable {
    private static final long serialVersionUID = -1L;
    @ApiModelProperty(value = "预计到账金额")
    private String arriveAmount;

    @ApiModelProperty(value = "订单手续费")
    private String fee;

    @ApiModelProperty(value = "是否展示手续费")
    private Boolean showFee;

    @ApiModelProperty(value = "收费模式 REAL_TIME、UN_REAL_TIME、PREPAID_REAL（扣手续费账户）")
    private String feeChargeType;

    public String getArriveAmount() {
        return arriveAmount;
    }

    public void setArriveAmount(String arriveAmount) {
        this.arriveAmount = arriveAmount;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public Boolean getShowFee() {
        return showFee;
    }

    public void setShowFee(Boolean showFee) {
        this.showFee = showFee;
    }

    public String getFeeChargeType() {
        return feeChargeType;
    }

    public void setFeeChargeType(String feeChargeType) {
        this.feeChargeType = feeChargeType;
    }
}
