package com.yeepay.g3.app.account.pay.mboss.controller.middleground;


import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.entity.MerchantAccountInfoEntity;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.helper.handler.BankAccountOperate;
import com.yeepay.g3.app.account.pay.mboss.helper.handler.BankAccountOperateFactory;
import com.yeepay.g3.app.account.pay.mboss.model.MerchantGroupInfoModel;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.AccountManageInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.BankAccountManageService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.GsonUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.SmartCacheUtilsHelper;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.agent.QueryAgentRelationInfoRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customerplatform.MerchantShuntQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.*;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.recharge.dto.response.QueryBankAccountRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.utils.lock.Lock;
import com.yeepay.utils.lock.impl.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @ClassName: SubBankAccountManageController
 * @Description: 下级银行账户管理
 * <AUTHOR>
 * @Date 2023/11/28
 * @Version 1.0
 */
@Controller
@Api(tags = "【下级管理】银行账户管理-API")
@RequestMapping("/sub/bankAccount")
public class SubBankAccountManageController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SubBankAccountManageController.class);

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private BankAccountManageService bankAccountManageService;

    @Resource
    private BankAccountOperateFactory bankAccountOperateFactory;

    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;

    @Resource
    private AccountManageInfoService accountManageInfoService;

    /**
     * 子商户银行账户管理
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/manage")
    @ApiOperation(hidden = true, value = "子商户银行账户管理")
    public ModelAndView manage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("subBankAccount/bankAccountManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("【下级管理】银行账户管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    /**
     * 子商户银行进度查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/openProcess")
    @ApiOperation(hidden = true, value = "子商户银行进度查询")
    public ModelAndView openProcess(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("subBankAccount/bankAccountOpen");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("【下级管理】银行进度查询页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    @RequiresPermissions("***********")
    @RequestMapping("/orderManagementSub")
    @ApiOperation(hidden = true, value = "下级订单管理")
    public ModelAndView orderManagementSub(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("subBankAccount/orderManagementSub");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("【下级管理】下级订单管理,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    @RequiresPermissions("***********")
    @RequestMapping("/accountProcessQuerySub")
    @ApiOperation(hidden = true, value = "下级银行流水查询")
    public ModelAndView accountProcessQuerySub(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("subBankAccount/accountProcessQuerySub");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("【下级管理】下级银行流水查询,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 查询银行开户有没有过
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openAccountAlready", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询当前银行是否开户")
    public ResponseMessage openAccountAlready(@RequestParam(value = "bankCode") String bankCode,
                                              @RequestParam(value = "merchantNo") String merchantNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("【下级管理】查询当前银行有没有开户，商编为={},银行={}", merchantNo, bankCode);
            String currentCustomerNumber = getCurrentCustomerNumber();
            BankAccountOpenRecord resp = bankAccountManageService.getProcessBankAccountRecord(merchantNo, bankCode);
            if (resp != null) {
                resp.setOperatePermission(currentCustomerNumber.equals(resp.getOperateMerchantNo()) ? true : false);
            }
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】查询当前银行有没有开户,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】查询当前银行有没有开户,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 查询开户信息详情
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openDetail", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询开户信息详情")
    public ResponseMessage queryOpenDetail(@RequestParam(value = "requestNo") String requestNo,
                                           @RequestParam(value = "merchantNo") String merchantNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("【下级管理】查询开户信息详情，商编为={},请求号为={}", merchantNo, requestNo);
            MgBankAccountOpenDetailRespDTO resp = bankAccountManageService.mgQueryOpenDetail(merchantNo, requestNo);
            logger.info("【下级管理】查询开户信息详情，resp={}", JSONUtils.toJsonString(resp));
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】查询开户信息详情,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】查询开户信息详情,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 多渠道资金归集开户接口
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openAccount", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("多渠道银行开户接口")
    public ResponseMessage openAccount(HttpServletRequest request, @RequestBody BankAccountOpenParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("【下级管理】多渠道资金归集开户接口，请求参数{}", JSONUtils.toJsonString(param));
        //商户名称
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        try {
            //校验参数
            param.validateParam();
            BankAccountOperate bankAccountOperate = bankAccountOperateFactory.getBankAccountOperateByBankCode(param.getOpenBankCode());
            bankAccountOperate.validateBasicParams(request, param);
            if (StringUtils.isBlank(param.getMerchantNo())) {
                throw UnionAccountException.PARAM_REQUIRED_ERROR.newInstance("商编不能为空");
            }
        } catch (YeepayBizException e) {
            logger.info("【下级管理】多渠道资金归集开户接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        } catch (Exception e) {
            logger.error("【下级管理】多渠道资金归集开户接口,参数校验系统异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("参数校验处理异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        }
        Lock lock = new RedisLock("sub_bank_account_open" + "_" + currentMerchant.getMerchantNo() + "_" + param.getCertificateNo()
                , 4);
        try {
            if (lock.tryLock(3)) {
                logger.info("【下级管理】多渠道资金归集开户接口拿到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                BankAccountOpenRespDTO resp = bankAccountManageService.mgOpenBankAccount(param, currentMerchant.getMerchantNo());
                resMsg.put("data", resp);
            } else {
                logger.info("多渠道资金归集开户接口没有获取到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请勿重复开户，请稍候重试");
            }
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】多渠道资金归集开户接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】多渠道资金归集开户接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                logger.error("【下级管理】释放分布式锁异常为={}", e);
            }
        }
        return resMsg;
    }

    /**
     * 开户信息分页查询
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("开户信息分页查询接口")
    public ResponseMessage queryOpenInfo(@RequestParam(value = "bankCode", required = false) String bankCode,
                                         @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                         @RequestParam(value = "merchantNo", required = false) String merchantNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String currentMerchant = getCurrentCustomerNumber();

            logger.info("【下级管理】查询银行开户列表，登录商编为={},商编为={},银行={}", currentMerchant, merchantNo, bankCode);
            String cacheKey = SmartCacheUtilsHelper.structureKey(SmartCacheUtilsHelper.SmartCacheKeyConstants.QUERY_BANK_OPEN_RECORD, currentMerchant, merchantNo, bankCode);
            String result = SmartCacheUtilsHelper.getWithOutException(cacheKey);
            List<BankAccountOpenRecord> allList = Lists.newArrayList();
            if (StringUtils.isBlank(result)) {
                List<String> merchantNoList = null;
                if (StringUtils.isBlank(merchantNo)) {
                    List<MerchantAccountInfoEntity> subMerchantList = accountManageInfoService.querySubMerchant(currentMerchant);
                    if(CollectionUtils.isEmpty(subMerchantList)){
                        logger.info("【下级管理】查询银行开户列表，下级商户为空，返回结果为空 登录商编为={},商编为={},银行={}", currentMerchant, merchantNo, bankCode);
                        BankAccountRecordRespDTO resp = new BankAccountRecordRespDTO();
                        resp.setBankAccountOpenRecordDTOS(Lists.newArrayList());
                        resp.setTotalSize(0);
                        resp.setPageSize(pageSize);
                        resp.setPageNo(pageNo);
                        resMsg.put("data", resp);
                        return resMsg;
                    }
                    merchantNoList = subMerchantList.stream().map(e -> e.getMerchantNo()).collect(Collectors.toList());
                } else {
                    merchantNoList = Lists.newArrayList(merchantNo);
                }
                allList = bankAccountManageService.getBankAccountRecordByMerchantNoList(merchantNoList, bankCode, null,null);
                logger.info("【下级管理】查询银行开户列表， size={}  登录商编为={},商编为={},银行={}", allList.size(), currentMerchant, merchantNo, bankCode);
                if ( ConfigUtils.getSubMerchantQueryCacheSwitch() && CollectionUtils.isNotEmpty(allList)) {
                    SmartCacheUtilsHelper.setWithOutException(cacheKey, GsonUtils.entityToJsonStr(allList), 600);
                }
            } else {
                logger.info("【下级管理】查询银行开户列表， 获取缓存 登录商编为={},商编为={},银行={}", currentMerchant, merchantNo, bankCode);
                allList = GsonUtils.fromJsonByEntityStrAndTypeToken(result, new TypeToken<List<BankAccountOpenRecord>>() {
                }.getType());
            }
            int  allCount = allList.size();
            List<BankAccountOpenRecord> subList = getPartBankAccountOpenRecords(pageNo, pageSize, allList, allCount);
            BankAccountRecordRespDTO resp = new BankAccountRecordRespDTO();
            resp.setBankAccountOpenRecordDTOS(subList);
            resp.setTotalSize(allCount);
            resp.setPageSize(pageSize);
            resp.setPageNo(pageNo);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】查询银行开户列表,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】查询银行开户列表,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 获取子列表 分页
     * @param pageNo
     * @param pageSize
     * @param allList
     * @param allCount
     * @return
     */
    private List<BankAccountOpenRecord> getPartBankAccountOpenRecords(Integer pageNo,  Integer pageSize, List<BankAccountOpenRecord> allList, int allCount) {
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(pageNo * pageSize, allCount); // 防止越界
        List<BankAccountOpenRecord> subList;
        if (startIndex < allCount) {
            subList = allList.subList(startIndex, endIndex);
        } else {
            subList = Lists.newArrayList(); // 如果起始索引超出范围，返回空列表
        }
        return subList;
    }


    /**
     * 短验申请
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openAccountAuthApply", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("银行开户-短验申请接口")
    public ResponseMessage openAccountAuthApply(@RequestBody OpenAccountConfirmParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtils.isBlank(param.getRequestNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("开户请求号不能为空");
            }
            if (StringUtils.isBlank(param.getMerchantNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求商编不能为空");
            }
            logger.info("【下级管理】短验申请接口，商编为={},开户请求号={}", param.getMerchantNo(), param.getRequestNo());
            BankAccountAuthApplyRespDTO respDTO = remoteService.openAccountAuthApply(param.getMerchantNo(), param.getRequestNo());
            logger.info("【下级管理】短验申请接口，开户请求号={},返回信息为={}", param.getRequestNo(), JSONUtils.toJsonString(respDTO));
            if ("FAIL".equals(respDTO.getStatus())) {
                resMsg.setErrCode(respDTO.getReturnCode());
                resMsg.setErrMsg(respDTO.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】短验申请接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】短验申请接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 短验验证
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openAccountAuthConfirm", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("银行开户-短验验证接口")
    public ResponseMessage openAccountAuthConfirm(@RequestBody OpenAccountConfirmParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            param.validateParam();
            if (StringUtils.isBlank(param.getMerchantNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求商编不能为空");
            }
            logger.info("【下级管理】短验验证接口，商编为={},请求信息为={}", param.getMerchantNo(), JSONUtils.toJsonString(param));
            BankAccountAuthConfirmRespDTO respDTO = remoteService.openAccountAuthConfirm(param.getMerchantNo(), param.getRequestNo(), param.getAuthCode());
            logger.info("【下级管理】短验验证接口，开户请求号={},返回信息为={}", param.getRequestNo(), JSONUtils.toJsonString(respDTO));
            if ("FAIL".equals(respDTO.getStatus())) {
                resMsg.setErrCode(respDTO.getReturnCode());
                resMsg.setErrMsg(respDTO.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】短验验证接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】短验验证接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 查询开立成功银行账户信息
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/bankAccountInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询开立成功银行账户信息接口")
    public ResponseMessage bankAccountInfo(@RequestParam(value = "bankCode", required = false) String bankCode,
                                           @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                           @RequestParam(value = "merchantNo", required = false) String merchantNo,
                                           @RequestParam(value = "accountStatus", required = false) String accountStatus) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String currentMerchant = getCurrentCustomerNumber();
            logger.info("【下级管理】查询开立成功银行账户信息，登录商编为={},商编为={},银行={},账户状态{}", currentMerchant, merchantNo, bankCode,accountStatus);
            String cacheKey = SmartCacheUtilsHelper.structureKey(SmartCacheUtilsHelper.SmartCacheKeyConstants.QUERY_SUCCESS_BANK_OPEN_RECORD, currentMerchant, merchantNo, bankCode);
            String result = SmartCacheUtilsHelper.getWithOutException(cacheKey);
            List<BankAccountOpenRecord> allList = Lists.newArrayList();
            if (StringUtils.isBlank(result)) {
                List<String> merchantNoList = null;
                if (StringUtils.isBlank(merchantNo)) {
                    List<MerchantAccountInfoEntity> subMerchantList = accountManageInfoService.querySubMerchant(currentMerchant);
                    if(CollectionUtils.isEmpty(subMerchantList)){
                        logger.info("【下级管理】查询开立成功银行账户信息，下级商户为空，返回结果为空 登录商编为={},商编为={},银行={},账户状态{}", currentMerchant, merchantNo, bankCode,accountStatus);
                        BankAccountRecordRespDTO resp = new BankAccountRecordRespDTO();
                        resp.setBankAccountOpenRecordDTOS(Lists.newArrayList());
                        resp.setTotalSize(0);
                        resp.setPageSize(pageSize);
                        resp.setPageNo(pageNo);
                        resMsg.put("data", resp);
                        return resMsg;
                    }
                    merchantNoList = subMerchantList.stream().map(e -> e.getMerchantNo()).collect(Collectors.toList());
                } else {
                    merchantNoList = Lists.newArrayList(merchantNo);
                }
                allList = bankAccountManageService.getBankAccountRecordByMerchantNoList(merchantNoList, bankCode, "SUCCESS",accountStatus);
                logger.info("【下级管理】查询银行开户列表，未查询到缓存 size={}  登录商编为={},商编为={},银行={},账户状态{}", allList.size(), currentMerchant, merchantNo, bankCode, accountStatus);
                if (ConfigUtils.getSubMerchantQueryCacheSwitch() && CollectionUtils.isNotEmpty(allList)) {
                    SmartCacheUtilsHelper.setWithOutException(cacheKey, GsonUtils.entityToJsonStr(allList), 600);
                }
            } else {
                logger.info("【下级管理】查询银行开户列表， 走缓存 登录商编为={},商编为={},银行={},账户状态{}", currentMerchant, merchantNo, bankCode, accountStatus);
                allList = GsonUtils.fromJsonByEntityStrAndTypeToken(result, new TypeToken<List<BankAccountOpenRecord>>() {
                }.getType());
            }

            int  allCount = allList.size();
            List<BankAccountOpenRecord> subList = getPartBankAccountOpenRecords(pageNo, pageSize, allList, allCount);
            BankAccountRecordRespDTO resp = new BankAccountRecordRespDTO();
            resp.setBankAccountOpenRecordDTOS(subList);
            resp.setTotalSize(allCount);
            resp.setPageSize(pageSize);
            resp.setPageNo(pageNo);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】查询开立成功银行账户信息,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】查询开立成功银行账户信息,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询银行账户余额
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/accountBalance", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询银行账户余额接口")
    public ResponseMessage accountBalance(@RequestParam(value = "bankCode") String bankCode,
                                          @RequestParam(value = "accountNo") String accountNo,
                                          @RequestParam(value = "merchantNo") String merchantNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("【下级管理】查询银行账户余额，商编为={},银行编码={},银行账户号={}", merchantNo, bankCode, accountNo);
            QueryBankAccountRespDTO respDTO = remoteService.queryBankAccountInfo(merchantNo, bankCode, accountNo);
            if (respDTO != null && "UA00000".equals(respDTO.getReturnCode())) {
                resMsg.put("accountAmt", respDTO.getAccountAmt());
            }
            QueryBankAccountBalanceRespDTO accountRespDTO = remoteService.queryChannelBankAccountInfo(merchantNo, bankCode, accountNo);
            if (accountRespDTO != null && "UA00000".equals(accountRespDTO.getReturnCode())) {
                resMsg.put("useableAmt", accountRespDTO.getUseableAmt());
                resMsg.put("frozenAmt", accountRespDTO.getFrozenAmt());
            }
            return resMsg;
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】查询银行账户余额,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】查询银行账户余额,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 入金通知和开户通知配置新增
     *
     * @param bankAccountNotifyConfigParam
     * @return
     */
    @ApiOperation("通知配置保存")
    @RequestMapping(value = "/notify/config/save", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage openAccountNotifyConfigSave(@RequestBody BankAccountNotifyConfigParam bankAccountNotifyConfigParam) {
        logger.info("【下级管理】保存银行开户通知配置 request={}", new Gson().toJson(bankAccountNotifyConfigParam));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String loginName = getCurrentUser().getLoginName();
        try {

            remoteService.addOpenAccountNotifyConfig(bankAccountNotifyConfigParam, bankAccountNotifyConfigParam.getMerchantNo(), loginName);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】新增通知配置异常,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】新增通知配置异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 入金通知和开户通知配置修改
     *
     * @param
     * @return
     */
    @ApiOperation("通知配置更新")
    @RequestMapping(value = "/notify/config/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage merchantNotifyConfigUpdate(@RequestBody BankAccountNotifyConfigParam bankAccountNotifyConfigParam) {
        logger.info("【下级管理】更新银行开户通知配置 request={}", new Gson().toJson(bankAccountNotifyConfigParam));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String loginName = getCurrentUser().getLoginName();
        try {
            bankAccountNotifyConfigParam.validateParam();
            if (StringUtils.isBlank(bankAccountNotifyConfigParam.getMerchantNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求商编不能为空");
            }
            remoteService.updateOpenAccountNotifyConfig(bankAccountNotifyConfigParam, bankAccountNotifyConfigParam.getMerchantNo(), loginName);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】更新银行开户通知配置异常,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】更新通知配置异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 入金通知配置修改
     *
     * @param depositNotifyModifyParam
     * @return
     */
    @ApiOperation("入金配置更新")
    @RequestMapping(value = "/deposit/notify/config/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage depositNotifyConfigUpdate(@RequestBody DepositNotifyModifyParam depositNotifyModifyParam) {
        logger.info("【下级管理】更新入金通知配置 request={}", new Gson().toJson(depositNotifyModifyParam));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String loginName = getCurrentUser().getLoginName();
        try {
            if (StringUtils.isBlank(depositNotifyModifyParam.getMerchantNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求商编不能为空");
            }
            remoteService.depositNotifyConfigUpdate(depositNotifyModifyParam, depositNotifyModifyParam.getMerchantNo(), loginName);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】更新入金通知配置异常,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】银行账户入金通知配置更新异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 入金通知和开户通知配置查询
     *
     * @param bankAccountNotifyConfigQuery
     * @return
     */
    @ApiOperation("通知配置查询")
    @RequestMapping(value = "/notify/config/query", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage notifyConfigQuery(@RequestBody BankAccountNotifyConfigQueryDTO bankAccountNotifyConfigQuery) {
        logger.info("【下级管理】查询开户通知配置 request={}", new Gson().toJson(bankAccountNotifyConfigQuery));
        bankAccountNotifyConfigQuery.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtils.isBlank(bankAccountNotifyConfigQuery.getMerchantNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求商编不能为空");
            }
            BankAccountNotifyConfigRespDTO resp = remoteService.queryBankOpenAccountNotifyConfig(bankAccountNotifyConfigQuery, bankAccountNotifyConfigQuery.getMerchantNo());
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】通知配置查询异常,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】银行账户通知查询异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    @ApiOperation("入金配置查询")
    @RequestMapping(value = "/deposit/config/query", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage queryDepositNotifyConfig(@RequestBody DepositNotifyConfigParam depositNotifyConfigParam) {
        logger.info("【下级管理】查询入金通知配置 request={}", new Gson().toJson(depositNotifyConfigParam));
        depositNotifyConfigParam.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtils.isBlank(depositNotifyConfigParam.getMerchantNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请求商编不能为空");
            }
            DepositNotifyConfigRespDTO depositNotifyConfigRespDTO = remoteService.queryDepositNotifyConfig(depositNotifyConfigParam);
            resMsg.put("data", depositNotifyConfigRespDTO);
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】新增通知配置异常,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】银行账户入金通知查询异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询开立账户类型
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/merchantType", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询商户开立账户类型")
    public ResponseMessage queryMerchantTypeDTO(@RequestParam(value = "bankCode") String bankCode,
                                                @RequestParam(value = "merchantNo") String merchantNo) {
        logger.info("【下级管理】查询商户开立账户类型请求,bankCode={},merchantNo={}", bankCode, merchantNo);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            MerchantShuntQueryRespDTO respDTO = remoteService.queryMerchant(merchantNo);
            BankAccountOperate bankAccountOperate = bankAccountOperateFactory.getBankAccountOperateByBankCode(bankCode);
            MerchantTypeDTO merchantTypeDTO = bankAccountOperate.assembleOpenBankAccountMerchantType(respDTO.getSignType());
            merchantTypeDTO.setMerchantNo(merchantNo);
            resMsg.put("data", merchantTypeDTO);
            logger.info("【下级管理】查询商户开立账户类型bankCode={},响应前端内容={}", bankCode, JSONUtils.toJsonString(resMsg));
        } catch (YeepayBizException e) {
            logger.warn("【下级管理】查询商户开立账户类型,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("【下级管理】查询商户开立账户类型,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 查询产品是否开通
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getProductInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询产品是否开通")
    public ResponseMessage getProductInfo(@RequestParam(value = "bankCode") String bankCode,
                                          @RequestParam(value = "merchantNo") String merchantNo) {
        logger.info("【下级管理】查询产品是否开通入参,bankCode={},merchantNo={}", bankCode, merchantNo);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if(Objects.equals(bankCode, BankAccountBankCodeEnum.XWB_Z.name())){
                bankCode = BankAccountBankCodeEnum.XWB.getChannelBankCode();
            }
            Boolean productOpen = bankAccountManageService.getProductOpen(merchantNo, bankCode);
            resMsg.put("productOpen", productOpen);
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("【下级管理】获取产品开通异常,商户为=" + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("【下级管理】获取产品开通异常,商户为=" + merchantNo + ",异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


    /**
     * 查询当前登录商编的角色
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getMerchantRole", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询当前登录商编能否发起")
    public ResponseMessage getMerchantRole() {
        String merchantNo = getCurrentCustomerNumber();
        logger.info("【下级管理】查询当前登录商编能否发起,merchantNo={}", merchantNo);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Boolean merchantRole = false;
            QueryAgentRelationInfoRespDTO agentRelationInfoRespDTO = remoteService.queryMerchantAgentInfo(merchantNo);
            if (/*代理商模式*/"AGENT".equals(agentRelationInfoRespDTO.getBizType())) {
                if (Lists.newArrayList("SAAS_SERVICE_PROVIDER", "PLATFORM_MERCHANT", "ORGAN_SERVICE_PROVIDER", "AGENT_SELLER").contains(agentRelationInfoRespDTO.getType())) {
                    merchantRole = true;
                }
            } else/*非代理商模式*/ {
                logger.info("当前登录商编非代理商体系,需在查询商户角色,merchantNo={}", merchantNo);
                MerchantGroupInfoModel groupInfo = businessCheckRemoteService.queryMerchantGroup(merchantNo);
                if ("PLATFORM_MERCHANT_BUSINESS_GROUP".equals(groupInfo.getGroupType()) || "SAAS_MERCHANT_BUSINESS_GROUP".equals(groupInfo.getGroupType())) {
                    if (groupInfo.getTopMerchantNo().equals(merchantNo)) {
                        merchantRole = true;
                    }
                }
            }
            resMsg.put("merchantRole", merchantRole);
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("【下级管理】查询当前登录商编的角色异常,商户为={}," + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("【下级管理】查询当前登录商编的角色异常,商户为={}," + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


    /**
     * 查询指定商户的结算周期
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getSettleConfig", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询指定商户的结算周期")
    public ResponseMessage getSettleInfo(@RequestParam(value = "merchantNo") String merchantNo,
                                         @RequestParam(value = "bankCode") String bankCode) {
        logger.info("【下级管理】查询指定商户的结算周期,merchantNo={},bankCode={}", merchantNo, bankCode);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            bankCode = BankAccountBankCodeEnum.valueOf(bankCode).getChannelBankCode();
            if (BankAccountBankCodeEnum.XWB_Z.name().equals(bankCode)) {
                bankCode = BankAccountBankCodeEnum.XWB.name();
            }
            QuerySettleConfigRespDTO merchantSettleConfigEntity = remoteService.querySettleEffectConfig(merchantNo, bankCode);
            if (merchantSettleConfigEntity == null || !"UA00000".equals(merchantSettleConfigEntity.getReturnCode())) {
                return resMsg;
            }
            resMsg.put("settleType", merchantSettleConfigEntity.getSettleType());
            resMsg.put("settleStartWay", merchantSettleConfigEntity.getSettleStartWay());
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("【下级管理】查询指定商户的结算周期,商户为={}," + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("【下级管理】查询指定商户的结算周期,商户为={}," + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }

    @RequestMapping(value = "/querySubOpenBankByProduct", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("下级管理/查询银行列表根据开通产品")
    public ResponseMessage querySubOpenBankByProduct(@RequestParam(value = "merchantNo") String merchantNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("下级管理/查询银行列表根据开通产品 merchantNo={}", merchantNo);
            Assert.isTrue(!StringUtils.isEmpty(merchantNo), "商户编号不能为空");
            List<BankInfoDTO> bankInfoDTOS = bankAccountManageService.queryOpenBankByProduct(merchantNo);
            resMsg.put("bankInfoDTOS", bankInfoDTOS);
            logger.info("下级管理/查询银行列表根据开通产品,响应={}", JSONUtils.toJsonString(resMsg));
        } catch (IllegalArgumentException e) {
            logger.warn("下级管理/查询银行列表根据开通产品,参数异常={}", e.getMessage());
            resMsg.setErrCode(ErrorCode.PARAM_VALIDATE_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (AccountPayException e) {
            logger.error("下级管理/查询银行列表根据开通产品,业务异常={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("下级管理/查询银行列表根据开通产品,异常=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    @RequestMapping(value = "/querySubOpenBankByRecord", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("下级管理/查询银行列表根据开户记录")
    public ResponseMessage querySubOpenBankByRecord(@RequestParam(value = "merchantNo") String merchantNo, @RequestParam(value = "pageSource") String pageSource) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("下级管理/查询银行列表根据开户记录 merchantNo={},pageSource={}", merchantNo, pageSource);
            List<String> merchantNoList = Lists.newArrayList();
            if (!StringUtils.isEmpty(merchantNo)) {
                merchantNoList.add(merchantNo);
            } else {
                logger.info("下级管理/查询银行列表根据开户记录,查询当前登录商编所有子商户编号,merchantNo={}", getCurrentCustomerNumber());
                List<MerchantAccountInfoEntity> accountInfoEntityList = accountManageInfoService.querySubMerchant(getCurrentCustomerNumber());
                if (CollectionUtils.isEmpty(accountInfoEntityList)) {
                    throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("下级子商户不存在");
                }
                List<String> subMerchantNos = accountInfoEntityList.stream().map(MerchantAccountInfoEntity::getMerchantNo).collect(Collectors.toList());
                merchantNoList.addAll(subMerchantNos);
            }
            List<BankInfoDTO> bankInfoDTOS = bankAccountManageService.queryOpenBankByRecord(merchantNoList, pageSource);
            resMsg.put("bankInfoDTOS", bankInfoDTOS);
            logger.info("下级管理/查询银行列表根据开户记录,响应={}", JSONUtils.toJsonString(resMsg));
        } catch (IllegalArgumentException e) {
            logger.warn("下级管理/查询银行列表根据开户记录,参数异常={}", e.getMessage());
            resMsg.setErrCode(ErrorCode.PARAM_VALIDATE_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (AccountPayException e) {
            logger.error("下级管理/查询银行列表根据开户记录,业务异常={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("下级管理/查询银行列表根据开户记录,异常=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询下级商户编号和名称
     *
     * @return
     */
    @RequestMapping(value = "/querySubMerchantNoList", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询下级商户编号和名称")
    public ResponseMessage querySubMerchantNoList() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("查询下级商户编号和名称 currentCustomerNumber={}", getCurrentCustomerNumber());
            List<MerchantAccountInfoEntity> accountInfoEntityList = accountManageInfoService.querySubMerchant(getCurrentCustomerNumber());
            List<MerchantInfoParam> merchantInfoParamList = accountInfoEntityList.stream().map(item -> {
                MerchantInfoParam merchantInfoParam = new MerchantInfoParam();
                merchantInfoParam.setMerchantNo(item.getMerchantNo());
                merchantInfoParam.setMerchantName(item.getSignName());
                return merchantInfoParam;
            }).collect(Collectors.toList());
            resMsg.put("merchantList", merchantInfoParamList);
            logger.info("查询下级商户编号和名称,响应={}", JSONUtils.toJsonString(resMsg));
        } catch (AccountPayException e) {
            logger.error("查询下级商户编号和名称,业务异常={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询下级商户编号和名称,异常=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }
}
