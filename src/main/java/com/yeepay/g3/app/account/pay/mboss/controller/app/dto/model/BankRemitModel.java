package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "订单付款查询结果")
public class BankRemitModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 请求号
     */
    @ApiModelProperty(value = "请求号")
    private String requestNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;


    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private Date createTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;


    /**
     * 冲退时间
     */
    @ApiModelProperty(value = "冲退时间")
    private Date reverseTime;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String status;

    /**
     * 关联订单号
     */
    @ApiModelProperty(value = "关联订单号")
    private String relationOrderNo;

    /**
     * 商户编号
     */
    @ApiModelProperty(value = "商户编号")
    private String merchantNo;

    /**
     *付款方户名
     */
    @ApiModelProperty(value = "付款方户名")
    private String payerAccountName;

    /**
     * 付款方银行账号
     */
    @ApiModelProperty(value = "付款方银行账号")
    private String payerAccountNo;

    /**
     * 失败附言
     */
    @ApiModelProperty(value = "失败附言")
    private String returnMsg;

    /**
     *付款金额
     */
    @ApiModelProperty(value = "付款金额")
    private String orderAmount;


    /**
     * 扣账金额
     */
    @ApiModelProperty(value = "扣账金额")
    private String debitAmount;


    /**
     * 到账金额
     */
    @ApiModelProperty(value = "到账金额")
    private String receiveAmount;


    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private String fee;

    /**
     * 到账类型
     */
    @ApiModelProperty(value="到账类型")
    private String remitType;

    /**
     * 收款方户名
     */
    @ApiModelProperty(value = "收款方户名")
    private String receiverAccountName;

    /**
     * 收款方银行账号
     */
    @ApiModelProperty(value = "收款方银行账号")
    private String receiverAccountNo;

    /**
     * 银行附言
     */

    @ApiModelProperty(value = "银行附言")
    private String comments;

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getReverseTime() {
        return reverseTime;
    }

    public void setReverseTime(Date reverseTime) {
        this.reverseTime = reverseTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRelationOrderNo() {
        return relationOrderNo;
    }

    public void setRelationOrderNo(String relationOrderNo) {
        this.relationOrderNo = relationOrderNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getPayerAccountName() {
        return payerAccountName;
    }

    public void setPayerAccountName(String payerAccountName) {
        this.payerAccountName = payerAccountName;
    }

    public String getPayerAccountNo() {
        return payerAccountNo;
    }

    public void setPayerAccountNo(String payerAccountNo) {
        this.payerAccountNo = payerAccountNo;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getDebitAmount() {
        return debitAmount;
    }

    public void setDebitAmount(String debitAmount) {
        this.debitAmount = debitAmount;
    }

    public String getReceiveAmount() {
        return receiveAmount;
    }

    public void setReceiveAmount(String receiveAmount) {
        this.receiveAmount = receiveAmount;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getRemitType() {
        return remitType;
    }

    public void setRemitType(String remitType) {
        this.remitType = remitType;
    }

    public String getReceiverAccountName() {
        return receiverAccountName;
    }

    public void setReceiverAccountName(String receiverAccountName) {
        this.receiverAccountName = receiverAccountName;
    }

    public String getReceiverAccountNo() {
        return receiverAccountNo;
    }

    public void setReceiverAccountNo(String receiverAccountNo) {
        this.receiverAccountNo = receiverAccountNo;
    }
}
