package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Mr.yin
 * @date: 2024/7/25  16:51
 */
@ApiModel(description = "提现订单 基础 信息")

public class WithdrawOrderModel implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "提现唯一订单号、查详情时需要")
    private String orderNo;

    @ApiModelProperty(value = "订单创建时间 yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @ApiModelProperty(value = "订单状态 REQUEST_RECEIVE 已接收;REQUEST_ACCEPT 已受理;SUCCESS已到账;FAIL失败;REVERSED银行撤销;REMITING 处理中")
    private String orderStatus;

    @ApiModelProperty(value = "订单金额")
    private String orderAmount;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }
}
