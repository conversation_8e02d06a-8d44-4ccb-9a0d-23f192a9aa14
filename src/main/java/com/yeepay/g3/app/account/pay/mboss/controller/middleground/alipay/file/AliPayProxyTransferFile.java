package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.file;

import com.yeepay.g3.app.account.pay.mboss.controller.file.FileAnalysis;
import com.yeepay.g3.app.account.pay.mboss.controller.file.FileHandle;
import com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req.AliPayProxyFileInfoCheckDTO;
import com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req.AliPayProxyFileInfoDTO;
import com.yeepay.g3.app.account.pay.mboss.remote.FileStorageService;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.FileUtil;
import com.yeepay.g3.app.account.pay.mboss.utils.POIUtil;
import com.yeepay.g3.app.account.pay.mboss.utils.RedisUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.ss.util.CellUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Component
public class AliPayProxyTransferFile extends FileAnalysis<AliPayProxyFileInfoDTO> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AliPayProxyTransferFile.class);

    private FileStorageService fileStorageService = new FileStorageService();

    final static String[] headStr = {"序号（必填）", "收款方支付宝账号（必填）", "收款方支付宝名称（必填）", "金额（必填，单位：元）", "备注（必填）"};

    @Override
    public boolean checkHead(Row head) {
        if (head == null) {
            return false;
        }
        for (int i1 = 0; i1 < headStr.length; i1++) {
            String strValue = POIUtil.getStrValue(head.getCell(i1));
            if (!StringUtils.equals(strValue.trim(), headStr[i1].trim())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<AliPayProxyFileInfoDTO> analysisData(Workbook wb) throws IllegalAccessException {
        Sheet sheet = wb.getSheetAt(0);
        List<AliPayProxyFileInfoDTO> batchDetailList = new ArrayList<>();
        for (int i = 2; i < sheet.getPhysicalNumberOfRows(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || row.getLastCellNum() <= 0) {
                continue;
            }
            AliPayProxyFileInfoDTO reqDto = new AliPayProxyFileInfoDTO();
            boolean hasValue = false;
            for (Field declaredField : reqDto.getClass().getDeclaredFields()) {
                if (declaredField.isAnnotationPresent(FileHandle.class)) {
                    FileHandle fileHandle = declaredField.getAnnotation(FileHandle.class);
                    int dex = fileHandle.index();
                    String strValue1 = POIUtil.getStrValue(row.getCell(dex));
                    if (!StringUtils.isEmpty(strValue1)) {
                        declaredField.setAccessible(true);
                        declaredField.set(reqDto, strValue1);
                        hasValue = true;
                    }
                }
            }
            if (hasValue) {
                batchDetailList.add(reqDto);
            }
        }

        return batchDetailList;
    }

    @Override
    public List<AliPayProxyFileInfoDTO> dealFile(MultipartFile file) throws Exception {
        Workbook wb = null;
        try {
            wb = WorkbookFactory.create(file.getInputStream());
        } catch (Exception e) {
            LOGGER.error("生成异常", e);
            throw new IllegalArgumentException("文件打开异常");
        }
        Sheet sheet = wb.getSheetAt(0);
        Row head = sheet.getRow(1);
        //请求头校验
        if (!checkHead(head)) {
            throw new IllegalArgumentException("文件表头异常");
        }
        try {
            File fileSource = FileUtil.multipartFileToFile(file);
            File destFile = new File(System.getProperty("java.io.tmpdir") + 1 + ".xls");
            FileUtil.copyFile(fileSource, destFile);
            InputStream inputStream = null;
            inputStream = new FileInputStream(destFile);
            Workbook wbCopy = WorkbookFactory.create(inputStream);
            List<AliPayProxyFileInfoDTO> transferParamDTOS = analysisData(wbCopy);
            return transferParamDTOS;
        } catch (Exception e) {
            LOGGER.error("复制后的文件读取失败");
            throw new Exception("复制后的文件读取失败");
        }
    }

    public void writeErrorFile(List<AliPayProxyFileInfoCheckDTO> collect, String token, File file) throws Exception {
        //文件复制
        File destFile = new File(System.getProperty("java.io.tmpdir") + token + ".xls");
        InputStream inputStream = null;
        FileUtil.copyFile(file, destFile);
        try {
            inputStream = new FileInputStream(destFile);
        } catch (Exception e) {
            LOGGER.error("复制后的文件读取失败");
            throw new Exception("复制后的文件读取失败");
        }
        //处理单条记录
        Workbook wb = WorkbookFactory.create(inputStream);
        Sheet sheet = wb.getSheetAt(0);
        //新文件追加一列
        Row headRow = sheet.getRow(1);
        Row secondRow = sheet.getRow(1); // 获取第二行
        if (secondRow == null) {
            secondRow = sheet.createRow(1); // 如果第二行不存在，则创建
        }
        // 创建新列（假设列索引为 5）
        Cell cell1 = CellUtil.createCell(secondRow, 5, "异常原因");


        for (int i = 1; i <= collect.size(); i++) {
            int index = i - 1;
            AliPayProxyFileInfoCheckDTO transferParamCheckDTO = collect.get(index);
            Row row = sheet.getRow(i+1);
            if (row == null) {
                row = sheet.createRow(i);
            }
            CellStyle cellStyle = wb.createCellStyle();
            if (StringUtils.isNotBlank(transferParamCheckDTO.getErrorMsg())) {
                //填充单元格
                cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
                //填黄色
                cellStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
            }
            for (Field declaredField : transferParamCheckDTO.getClass().getDeclaredFields()) {
                if (declaredField.isAnnotationPresent(FileHandle.class)) {
                    if (declaredField.isAnnotationPresent(FileHandle.class)) {
                        FileHandle fileHandle = declaredField.getAnnotation(FileHandle.class);
                        int dex = fileHandle.index();
                        declaredField.setAccessible(true);
                        Object o = declaredField.get(transferParamCheckDTO);
                        if (!(o == null)) {
                            Cell cell = row.getCell(dex);
                            if (cell == null) {
                                cell = row.createCell(dex);
                            }
                            cell.setCellStyle(cellStyle);
                            cell.setCellValue(String.valueOf(o));
                        }
                    }
                }
            }
        }
        //发往云存储
        FileOutputStream outputStream = null;
        try {
            //如果全部校验通过，则上传原文件； 有校验失败，则上传新文件
            outputStream = new FileOutputStream(destFile);
            wb.write(outputStream);
        } catch (Exception e) {
            LOGGER.error("系统处理异常", e);
            throw new Exception("系统处理异常");
        } finally {
            outputStream.close();
            LOGGER.info("开始发往云存储");
            Map<String, String> map = new HashMap<>();
            map.put("fileName", file.getName());
            RedisUtils.hmset(token, map, ConfigUtils.getBatchSendExpire());
            fileStorageService.uploadFileCloud(token + ".xls", new FileInputStream(destFile));
        }

    }
}
