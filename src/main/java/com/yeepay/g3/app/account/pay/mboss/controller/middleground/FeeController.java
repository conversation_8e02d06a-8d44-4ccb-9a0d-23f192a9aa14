package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.constant.BankPayProductConstant;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.CalculateWithdrawFeeReqDTO;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.PreFeeInfoModel;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.RechargeBankPayPreCalFeeReqDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.FeeCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.FeeRemoteService;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.remote.bo.CalculateFeeReqBO;
import com.yeepay.g3.app.account.pay.mboss.remote.bo.PreFeeInfoBO;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.Costants;
import com.yeepay.g3.app.account.pay.mboss.utils.ValidateUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.merchant.fee.dto.CalFeeRequestDto;
import com.yeepay.g3.facade.merchant.fee.dto.CalFeeResponseDto;
import com.yeepay.g3.facade.merchant.fee.facade.CalFeeFacade;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.BaseProductDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.QueryMerchantProductRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.QuerySettleConfigRespDTO;
import com.yeepay.g3.facade.unionaccount.recharge.enumtype.FeeTypeEnum;
import com.yeepay.g3.facade.unionaccount.recharge.enumtype.PayTypeEnum;
import com.yeepay.g3.facade.unionaccount.recharge.params.AccountBasicParam;
import com.yeepay.g3.facade.unionaccount.recharge.params.ProductCodeParam;
import com.yeepay.g3.facade.unionaccount.recharge.utils.AccountBasicConfigUtils;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.WithdrawTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.utils.WithdrawProductBasicConfigUtils;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CommonUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/fee")
@Api(tags = "手续费-预计费API")
public class FeeController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FeeController.class);

    private static Map<String, String> accountTypeAndFirstProductCode = Maps.newHashMap();

    static {
        /*第一期app提现只支持资金账户*/
        accountTypeAndFirstProductCode.put(AccountTypeEnum.FUND_ACCOUNT.name(), Costants.WITHDRAW_BASICSPRODUCTFIRST);
    }

    //计费相关
    private CalFeeFacade calFeeFacade = RemoteServiceFactory.getService(CalFeeFacade.class);

    @Resource
    private RemoteService remoteService;
    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;

    @Resource
    private FeeRemoteService feeRemoteService;

    /**
     * @Description: 预计费
     * <AUTHOR>
     * @date 2020-03-11 11:32
     * @param request:
     * @param tradeType:
     * @param tradeAmount:
     * @param productType:
     * @param arriveType:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @Deprecated/*这个不符合swagger对接规范 因此使用下面的*/
    @RequestMapping(value = "/preCalFee/{tradeType}", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage preCallfee(HttpServletRequest request, @PathVariable("tradeType") String tradeType,
                                      @RequestParam(value = "balance",required = false) String balance,
                                      @RequestParam("tradeAmount") String tradeAmount,
                                      @RequestParam(value = "accountType",required = false) String accountType,
                                      @RequestParam(value = "productType",required = false) String productType,
                                      @RequestParam(value = "arriveType",required = false) String arriveType){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchant = getCurrentCustomerNumber();
        CalFeeRequestDto calFeeRequestDto = new CalFeeRequestDto();
        //易宝流水号
        calFeeRequestDto.setYeepayTrxFlowNo(CommonUtils.getUUID());
        //业务方
        calFeeRequestDto.setBizCode(Costants.ACCOUNT_BIZ_CODE);
        //商户订单请求号
        calFeeRequestDto.setMerchantOrderNo(CommonUtils.getUUID());
        //商户编号
        calFeeRequestDto.setMerchantNo(merchant);
        //手续费承担商编
        calFeeRequestDto.setFeeMerchantNo(merchant);
        //算费金额
        calFeeRequestDto.setAmount(new BigDecimal(tradeAmount));
        //交易时间
        calFeeRequestDto.setTrxTime(new Date());
        //营销产品码
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        calFeeRequestDto.setMarketingProduct(businessCheckRemoteService.queryMarketProduct(merchant));
        String basicProductCode = null;
        String secondProductCode = null;
        String thirdProductCode = null;
        String productAttribute = null;
        if("withdraw".equals(tradeType)) {
            com.yeepay.g3.facade.unionaccount.trade.params.
              AccountBasicParam basicParam =  WithdrawProductBasicConfigUtils.getAccountBasicConfigByAccountType(accountType);
            if(basicParam == null || basicParam.getPayTypeMap() == null || !basicParam.getPayTypeMap().containsKey(arriveType)){
                throw new RuntimeException("不支持该账户类型，请联系运营处理");
            }
            com.yeepay.g3.facade.unionaccount.trade.params.ProductCodeParam productParam = basicParam.getPayTypeMap().get(arriveType);
            productAttribute = basicParam.getProductType();
            basicProductCode = productParam.getFirstProductCode();
            secondProductCode = productParam.getSecondProductCode();
        }else if("recharge".equals(tradeType)){
            AccountBasicParam accountBasicConfig = AccountBasicConfigUtils.getAccountBasicConfigByAccountType(accountType);
            if(accountBasicConfig == null){
                throw new RuntimeException("不支持该账户类型，请联系运营处理");
            }
            Map<String, ProductCodeParam> payTypeMap = accountBasicConfig.getPayTypeMap();
            if (payTypeMap == null) {
                throw new RuntimeException("不支持该账户类型，请联系运营处理");
            }
            ProductCodeParam productCodeParam = payTypeMap.get(productType);
            if(productCodeParam == null){
                throw new RuntimeException("不支持该账户类型，请联系运营处理");
            }

            productAttribute = accountBasicConfig.getProductAttribute();

            basicProductCode = productCodeParam.getFirstProductCode();
            secondProductCode = productCodeParam.getSecondProductCode();
            thirdProductCode = productCodeParam.getThirdProductCode();
        }else if("transfer".equals(tradeType)){
            //基础产品二级
            basicProductCode = Costants.TRANSFER_BASICSPRODUCTFIRST;
            secondProductCode = productType;
        }else{
            throw new RuntimeException("不支持此交易类型");
        }
        calFeeRequestDto.setBasicsProductFirst(basicProductCode);
        calFeeRequestDto.setBasicsProductSecond(secondProductCode);
        //基础产品三级
        calFeeRequestDto.setBasicsProductThird("-");
        if("recharge".equals(tradeType) && isRechargeOpenSameProduct(productType, merchant, accountType)){
            calFeeRequestDto.setBasicsProductThird("SAMENAME");
        }
        LOGGER.info("productType{}", productType);
        if("recharge".equals(tradeType) && PayTypeEnum.UNION_PAY_B2B.name().equals(productType) && StringUtils.isNotBlank(thirdProductCode)){
            calFeeRequestDto.setBasicsProductThird(thirdProductCode);
        }
        LOGGER.info("调用账务预计费接口，入参{}", JSON.toJSONString(calFeeRequestDto));
        CalFeeResponseDto calFeeResponseDto;
        Boolean prepaid = false;
        try{
            calFeeResponseDto = calFeeFacade.preCalFee(calFeeRequestDto);
            LOGGER.info("调用账务预计费接口，返回参数{}", JSON.toJSONString(calFeeResponseDto));
            if("FAIL".equals(calFeeResponseDto.getStatus())){
                String errorCode = calFeeResponseDto.getErrorCode();
                if(FeeCodeEnum.CODE_300002.getCode().equals(errorCode) || FeeCodeEnum.CODE_300005.getCode().equals(errorCode)){
                    LOGGER.error("预计费接口错误，返回错误");
                    resMsg.setErrCode("9999");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("产品费率配置有误，请联系销售经理或客服");
                    return resMsg;
                }else if(FeeCodeEnum.CODE_300007.getCode().equals(errorCode)) {
                    //预付实扣
                    prepaid = true;
                }else {
                    resMsg.setErrCode("9999");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("系统异常，请联系销售经理或客服处理");
                    return resMsg;
                }
            }
        }catch (Exception e) {
            LOGGER.error("调用账务预计费接口错误", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统调用异常，请稍后再试");
            return resMsg;
        }

        if(prepaid) {
            resMsg.put("fee", "0.00");
            if("withdraw".equals(tradeType)){
                if(new BigDecimal(tradeAmount).compareTo(new BigDecimal(balance))>0){
                    resMsg.put("arrivalAmount", new BigDecimal(balance).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                }else {
                    resMsg.put("arrivalAmount", tradeAmount);
                }
            }else if("recharge".equals(tradeType)){
                //实际充值金额
                resMsg.put("deductAmount", new BigDecimal(tradeAmount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }else{
                throw new RuntimeException("不支持此交易类型");
            }

        }else {
            BigDecimal fee = new BigDecimal(0.00);
            if(null != calFeeResponseDto.getFee()) {
                fee = calFeeResponseDto.getFee().setScale(2, BigDecimal.ROUND_HALF_UP);
            }

            resMsg.put("fee", fee.toString());
            if("withdraw".equals(tradeType)){
                String feeMerchantNo = businessCheckRemoteService.queryFeeMerchantNo(merchant,productAttribute,calFeeRequestDto.getMarketingProduct(),basicProductCode,secondProductCode,null);
                boolean isPlateMerchantTaker = !merchant.equals(feeMerchantNo);
                if(isPlateMerchantTaker){
                    //非商户承担,手续费设置成0返回
                    fee = BigDecimal.ZERO;
                    resMsg.put("fee", fee.toString());
                }
                //手续费和余额的比较
                if(fee.compareTo(new BigDecimal(balance)) > 0) {
                    resMsg.setErrCode("9999");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("账户余额不足以支付服务费，不能发起提现");
                    return resMsg;
                }
                if(new BigDecimal(tradeAmount).add(fee).compareTo(new BigDecimal(balance))>0){
                    resMsg.put("arrivalAmount", new BigDecimal(balance).subtract(fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                }else {
                    resMsg.put("arrivalAmount", tradeAmount);
                }
            }else if("recharge".equals(tradeType)){
                // 查询手续费承担方
                String feeMerchantNo = businessCheckRemoteService.queryFeeMerchantNo(merchant,productAttribute,calFeeRequestDto.getMarketingProduct(),basicProductCode,secondProductCode,null);
                boolean isPlateMerchantTaker = !merchant.equals(feeMerchantNo);
                if(isPlateMerchantTaker){
                    resMsg.put("fee", BigDecimal.ZERO);
                    //实际充值金额
                    resMsg.put("deductAmount", new BigDecimal(tradeAmount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                }else{
                    //实际充值金额
                    resMsg.put("deductAmount", new BigDecimal(tradeAmount).add(fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                }
            }else{
                throw new RuntimeException("不支持此交易类型");
            }
        }
        return resMsg;
    }


    @RequestMapping(value = "/estimatedFee/{tradeType}", method = RequestMethod.POST)
    @ApiOperation(value = "预估提现手续费")
    @ResponseBody
    public BaseRespDTO<PreFeeInfoModel> calculateWithdrawFee(@RequestBody @Valid CalculateWithdrawFeeReqDTO reqDTO) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[预估提现手续费] 商户={} reqDTO={}", currentCustomerNumber, JSONUtils.toJsonString(reqDTO));
        try {
            reqDTO.validateParam();
            String accountType = reqDTO.getAccountType();
            String firstProduct = accountTypeAndFirstProductCode.get(accountType);
            if (StringUtils.isEmpty(firstProduct)) {
                LOGGER.warn("[预估提现手续费] 商编为={}，未找到该账户类型的提现产品 accountType={}", currentCustomerNumber, accountType);
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("暂不支持该账户类型提现:" + accountType);
            }
            String arriveType = reqDTO.getArriveType();
            String secondProduct = WithdrawTypeEnum.parsePayWayProductByCode(arriveType);
            if (StringUtils.isEmpty(secondProduct)) {
                LOGGER.warn("[预估提现手续费] 商编为={}，暂不支持的到账类型 arriveType={}", currentCustomerNumber, arriveType);
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("暂不支持该到账时效:" + arriveType);
            }
            /*1.查询手续费承担方*/
            String feeMerchantNo = businessCheckRemoteService.queryFeeMerchantNo(currentCustomerNumber, null, null, firstProduct, secondProduct, null);
            if (!feeMerchantNo.equals(currentCustomerNumber)) {
                LOGGER.info("[预估提现手续费] 商编为={}，手续费承担方不是自己，feeMerchantNo={}", currentCustomerNumber, feeMerchantNo);
                PreFeeInfoModel model = new PreFeeInfoModel();
                model.setArriveAmount(reqDTO.getTradeAmount());
                model.setShowFee(false);
                LOGGER.info("[预估提现手续费] 商编为={}，result={}", currentCustomerNumber, JSONUtils.toJsonString(model));
                return BaseRespDTO.success(model);
            }
            /*2.算手续费*/
            CalculateFeeReqBO reqBO = assembleCalculateFeeReqBO(reqDTO, currentCustomerNumber, firstProduct, secondProduct);
            PreFeeInfoBO feeInfoBO = feeRemoteService.onlyCalculateFee(reqBO);
            BigDecimal fee = feeInfoBO.getFee();
            PreFeeInfoModel model = new PreFeeInfoModel();
            model.setFee(fee.setScale(2).toPlainString());
            model.setShowFee(true);/*如果手续费是自己出，则展示*/
            model.setFeeChargeType(feeInfoBO.getChargeType());
            /*计算到账金额*/
            String arriveAmount = getArriveAmount(reqDTO, feeInfoBO, fee);
            model.setArriveAmount(arriveAmount);

            LOGGER.info("[预估提现手续费] 商编为={}，result={}", currentCustomerNumber, JSONUtils.toJsonString(model));
            return BaseRespDTO.success(model);
        } catch (YeepayBizException e) {
            LOGGER.warn("[预估提现手续费] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[预估提现手续费] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    private CalculateFeeReqBO assembleCalculateFeeReqBO(CalculateWithdrawFeeReqDTO reqDTO, String currentCustomerNumber, String firstProduct, String secondProduct) {
        CalculateFeeReqBO reqBO = new CalculateFeeReqBO();
        reqBO.setMerchantNo(currentCustomerNumber);
        reqBO.setTradeAmount(new BigDecimal(reqDTO.getTradeAmount()));
        reqBO.setFirst(firstProduct);
        reqBO.setSecond(secondProduct);
        return reqBO;
    }

    /**
     * 计算到账金额
     * @param reqDTO
     * @param feeInfoBO
     * @param fee
     * @return
     */
    private String getArriveAmount(CalculateWithdrawFeeReqDTO reqDTO, PreFeeInfoBO feeInfoBO, BigDecimal fee) {
        String arriveAmount = null;
        if (!"REAL_TIME".equals(feeInfoBO.getChargeType())) {
            arriveAmount = reqDTO.getTradeAmount();
        } else {
            //手续费和余额的比较
            BigDecimal bal = new BigDecimal(reqDTO.getBalance());
            BigDecimal tradeAmount = new BigDecimal(reqDTO.getTradeAmount());
            if (/*余额不足以支持手续费*/fee.compareTo(bal) >= 0) {
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("账户余额不足以支付服务费，不能发起提现");
            }
            if (/*余额和金额的差值不足以支持手续费*/fee.compareTo(bal.subtract(tradeAmount)) > 0) {
                arriveAmount = bal.subtract(fee).setScale(2).toPlainString();
            } else {
                arriveAmount = reqDTO.getTradeAmount();
            }
        }
        return arriveAmount;
    }



    /**
     * 专门给充值银行扣款用的接口
     * @param payPreCalFeeReqDTO
     * @return
     */
    @RequestMapping(value = "/rechargeBankPayPreCalFee", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("充值银行扣款预计费接口")
    public ResponseMessage rechargeBankPayPreCalFee(@RequestBody RechargeBankPayPreCalFeeReqDTO payPreCalFeeReqDTO) {
        LOGGER.info("充值银行扣款预计费请求参数={}", JSONUtils.toJsonString(payPreCalFeeReqDTO));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            payPreCalFeeReqDTO.validateParam();
            ValidateUtils.judgeBigDecimal(payPreCalFeeReqDTO.getTradeAmount());
            BigDecimal tradeAmount = new BigDecimal(payPreCalFeeReqDTO.getTradeAmount());
            String merchantNo = getCurrentCustomerNumber();
            String bankCode = payPreCalFeeReqDTO.getBankCode();
            if (BankAccountBankCodeEnum.XWB_Z.name().equals(bankCode)) {
                bankCode = BankAccountBankCodeEnum.XWB.name();
            }
            // 获取产品码
            String productCode = BankPayProductConstant.bankPayProductMap.get(bankCode);
            if (StringUtils.isEmpty(productCode)) {
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("暂不支持该银行");
            }
            List<String> productCodeList = Lists.newArrayList(productCode.split(","));
            String firstProductCode = productCodeList.get(0);
            String secondProductCode = productCodeList.size() >= 2 ? productCodeList.get(1) : "";

            String thirdProductCode = productCodeList.size() == 3 ? productCodeList.get(2) : "";
            //获取结算周期三级码
            thirdProductCode = getThirdProductCode(merchantNo, thirdProductCode);

            String marketProduct = businessCheckRemoteService.queryMarketProduct(merchantNo);
            // 获取产品属性
            AccountBasicParam accountBasicConfig = AccountBasicConfigUtils.getAccountBasicConfigByAccountType(payPreCalFeeReqDTO.getAccountType());
            if (accountBasicConfig == null) {
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("不支持该账户类型，请联系运营处理");
            }
            String productAttribute = accountBasicConfig.getProductAttribute();
            // 查询手续费承担方
            String feeMerchantNo = businessCheckRemoteService.queryFeeMerchantNo(merchantNo, productAttribute, marketProduct, firstProductCode, secondProductCode, thirdProductCode);
            if (!merchantNo.equals(feeMerchantNo)) {
                /** 非自己承担 **/
                resMsg.put("fee", "0.00");
                //实际充值金额
                resMsg.put("deductAmount", tradeAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                return resMsg;
            }

            // 调用预计费
            CalFeeRequestDto calFeeRequestDto = assembleCalFeeRequestDto(tradeAmount, firstProductCode, secondProductCode, thirdProductCode, marketProduct);
            CalFeeResponseDto calFeeResponseDto = remoteService.preCalFee(calFeeRequestDto);
            LOGGER.info("#充值银行扣款预计费查询# 计费结果={}",JSONObject.toJSONString(calFeeResponseDto));
            if ("FAIL".equals(calFeeResponseDto.getStatus())) {
                String errorCode = calFeeResponseDto.getErrorCode();
                if (FeeCodeEnum.CODE_300007.getCode().equals(errorCode)) {
                    // 预付实扣 or 后收
                    resMsg.put("fee", "0.00");
                    // 实际充值金额
                    resMsg.put("deductAmount", tradeAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                } else if (FeeCodeEnum.CODE_300002.getCode().equals(errorCode) || FeeCodeEnum.CODE_300005.getCode().equals(errorCode)) {
                    throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("产品费率配置有误，请联系销售经理或客服");
                } else {
                    throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("系统异常，请联系销售经理或客服配置产品费率");
                }
            } else {
                // 实收
                BigDecimal fee = BigDecimal.ZERO;
                if (calFeeResponseDto.getFee() != null) {
                    fee = calFeeResponseDto.getFee().setScale(2, BigDecimal.ROUND_HALF_UP);
                }
                resMsg.put("fee", fee.toString());
                LOGGER.info("#充值银行扣款预计费查询# 计费费率={}",fee.toString());
                //实际充值金额
                // 商户后台默认为外扣
                if (StringUtils.isBlank(payPreCalFeeReqDTO.getFeeType()) || FeeTypeEnum.OUTER.name().equals(payPreCalFeeReqDTO.getFeeType())) {
                    resMsg.put("deductAmount", tradeAmount.add(fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    resMsg.put("arrivalAmount", String.valueOf(tradeAmount));
                    LOGGER.info("#外扣充值银行扣款预计费查询结果# feeType={},result={}", payPreCalFeeReqDTO.getFeeType(), JSONObject.toJSONString(resMsg));
                    return resMsg;
                }
                //内扣
                if (FeeTypeEnum.INTER.name().equals(payPreCalFeeReqDTO.getFeeType())) {
                    resMsg.put("deductAmount", String.valueOf(tradeAmount));
                    BigDecimal arrivalAmount = tradeAmount.subtract(fee).setScale(2, BigDecimal.ROUND_HALF_UP);
                    if (arrivalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        resMsg.put("arrivalAmount", "0.00");
                    } else {
                        resMsg.put("arrivalAmount", String.valueOf(arrivalAmount));
                    }
                    LOGGER.info("#内扣充值银行扣款预计费查询结果# feeType={},result={}", payPreCalFeeReqDTO.getFeeType(), JSONObject.toJSONString(resMsg));
                }
            }
        } catch (YeepayBizException e) {
            LOGGER.error("充值-银行扣款-预计费接口业务异常, cause by={}", e.getMessage());
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("充值-银行扣款-预计费接口系统异常, cause by=", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统调用异常，请稍后再试");
        }
        LOGGER.info("#充值银行扣款预计费查询最终结果# result={}", JSONObject.toJSONString(resMsg));
        return resMsg;
    }

    /**
     * 获取结算的三级产品码
     *
     * @return
     */
    private String getThirdProductCode(String merchantNo, String bankCode) {
        QuerySettleConfigRespDTO merchantSettleConfigEntity = remoteService.querySettleEffectConfig(merchantNo, bankCode);
        LOGGER.info("查询结算周期,merchantNo={}, bankCode={},返回参数为={}", merchantNo, bankCode, JSONUtils.toJsonString(merchantSettleConfigEntity));
        if (merchantSettleConfigEntity == null || !"UA00000".equals(merchantSettleConfigEntity.getReturnCode())) {
            //不存在，就查默认的产品码
            return bankCode;
        }
        return merchantSettleConfigEntity.getBasicProductThird();
    }


    private CalFeeRequestDto assembleCalFeeRequestDto(BigDecimal tradeAmount, String firstProductCode,
                                                      String secondProductCode, String thirdProductCode,
                                                      String marketProductCode) {
        String merchantNo = getCurrentCustomerNumber();
        CalFeeRequestDto calFeeRequestDto = new CalFeeRequestDto();
        //业务方
        calFeeRequestDto.setBizCode(Costants.ACCOUNT_BIZ_CODE);
        //易宝流水号
        calFeeRequestDto.setYeepayTrxFlowNo(CommonUtils.getUUID());
        //商户订单请求号
        calFeeRequestDto.setMerchantOrderNo(CommonUtils.getUUID());
        //商户编号
        calFeeRequestDto.setMerchantNo(merchantNo);
        //手续费承担商编
        calFeeRequestDto.setFeeMerchantNo(merchantNo);
        //算费金额
        calFeeRequestDto.setAmount(tradeAmount);
        //交易时间
        calFeeRequestDto.setTrxTime(new Date());
        //营销产品码
        calFeeRequestDto.setMarketingProduct(marketProductCode);
        calFeeRequestDto.setBasicsProductFirst(firstProductCode);
        calFeeRequestDto.setBasicsProductSecond(StringUtils.isNotBlank(secondProductCode) ? secondProductCode : "-");
        calFeeRequestDto.setBasicsProductThird(StringUtils.isNotBlank(thirdProductCode) ? thirdProductCode : "-");
        return calFeeRequestDto;
    }

    private Boolean isRechargeOpenSameProduct(String productType, String merchant, String accountType) {
        AccountBasicParam accountBasicConfig = AccountBasicConfigUtils.getAccountBasicConfigByAccountType(accountType);
        Map<String, ProductCodeParam> payTypeMap = accountBasicConfig.getPayTypeMap();
        if(Objects.equals(PayTypeEnum.B2C.getSecondProductCode(), productType) || Objects.equals(PayTypeEnum.B2B.getSecondProductCode(), productType)){
            List<String> allProductList = getAllProductByMerchantNo(merchant);
            LOGGER.info("allProductList:" + new Gson().toJson(allProductList));
            if(CollectionUtils.isNotEmpty(allProductList)){
                ProductCodeParam productCodeParam = payTypeMap.get(productType);
                if (Objects.nonNull(productCodeParam) && Objects.nonNull(productCodeParam.getFirstProductCode()) && Objects.nonNull(productCodeParam.getSecondProductCode())) {
                    StringBuilder stringBuilder = new StringBuilder().append(productCodeParam.getFirstProductCode()).append(",").append(productCodeParam.getSecondProductCode());
                    List<String> productList = allProductList.stream().filter(x -> x.contains(stringBuilder.toString())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(productList) && productList.size() == 1 && productList.get(0).contains("SAMENAME")) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private List<String> getAllProductByMerchantNo(String merchantNo){
        List<String> allProductList = Lists.newArrayList();
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        QueryMerchantProductRespDTO merchantProductRespDTO = businessCheckRemoteService.queryMerchantProduct(merchantNo);
        if("0000".equals(merchantProductRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductRespDTO.getBaseProductList())){
            for (BaseProductDTO baseProductDTO : merchantProductRespDTO.getBaseProductList()) {
                StringBuffer buffer = new StringBuffer();
                if(!"AVAILABLE".equals(baseProductDTO.getProductStatus())){
                    continue;
                }

                if(!CheckUtils.isEmpty(baseProductDTO.getProductType())){
                    buffer.append(baseProductDTO.getProductType());
                }
                if (!CheckUtils.isEmpty(baseProductDTO.getFirstBaseProductCode())) {
                    buffer.append(","+baseProductDTO.getFirstBaseProductCode());
                }
                if(!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode())){
                    buffer.append(","+baseProductDTO.getSecondBaseProductCode());
                }
                if(!CheckUtils.isEmpty(baseProductDTO.getThirdBaseProductCode())){
                    buffer.append(","+baseProductDTO.getThirdBaseProductCode());
                }
                if(StringUtils.isNotBlank(buffer.toString())){
                    allProductList.add(buffer.toString());
                }
            }
        }
        return allProductList;

    }

}
