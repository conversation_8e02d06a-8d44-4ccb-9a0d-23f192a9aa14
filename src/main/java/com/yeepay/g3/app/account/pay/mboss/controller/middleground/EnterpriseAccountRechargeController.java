package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.EnterpriseRechargeQueryReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.EnterpriseRechargeReqDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.OrderStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.service.EnterpriseAccountOrderService;
import com.yeepay.g3.app.account.pay.mboss.utils.BACRsaUtil;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.account.pay.mboss.vo.EnterpriseAccountOrderSumVO;
import com.yeepay.g3.app.account.pay.mboss.vo.EnterpriseAccountRechargeOrderVO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.recharge.dto.response.RechargeRespDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yibao.utils.json.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * @ClassName: EnterpriseAccountRechargeController
 * @Description:
 * <AUTHOR>
 * @Date 2023/12/5
 * @Version 1.0
 */

@Controller
@Api(tags = "企业号账户充值管理-API")
@RequestMapping("/enterpriseRecharge")
public class EnterpriseAccountRechargeController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseAccountRechargeController.class);


    @Resource
    private EnterpriseAccountOrderService enterpriseAccountOrderService;

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    @RequiresPermissions("***********")
    @RequestMapping("/orderManage")
    @ApiOperation(hidden = true, value = "充值订单管理")
    public ModelAndView bankPaymentManage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("enterpriseAccount/enterpriseRechargeOrder");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("订单管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 企业号充值初始化信息
     * 余额
     * 密钥
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/init", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("企业号充值初始化信息")
    public ResponseMessage init(@RequestParam(value = "bankCode") String bankCode,
                                @RequestParam(value = "enterpriseAccountNo") String enterpriseAccountNo) {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Map<String, Object> map = enterpriseAccountOrderService.getWithdrawInitInfo(user.getCustomerNumber(), bankCode, enterpriseAccountNo);
            resMsg.put("map", map);
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("获取企业号充值初始化的信息异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("获取企业号充值初始化的信息异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("加载信息异常");
        }
        return resMsg;
    }


    /**
     * 企业号充值下单
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("企业号充值下单")
    public ResponseMessage confirm(@RequestBody EnterpriseRechargeReqDTO reqDTO) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("企业号充值下单参数：reqDTO:{}", JsonUtils.toJson(reqDTO));
            //校验参数
            reqDTO.validateParam();
            ShiroUser user = super.getCurrentUser();
            checkArgument(StringUtils.isNotBlank(reqDTO.getPasswd()));
            //交易密码改为密文传输，需要解密
            String decryptPassWord = BACRsaUtil.privateDecrypt(reqDTO.getPasswd(), ConfigUtils.getPrivateKey());
            //1.验证密码
            enterpriseAccountOrderService.queryTradePasswordValidateResult(user.getLoginName(), decryptPassWord);
            String merchantNo = getCurrentCustomerNumber();
            reqDTO.setMerchantNo(merchantNo);
            RechargeRespDTO respDTO = enterpriseAccountOrderService.mpRecharge(reqDTO);
            if (CheckUtils.isEmpty(respDTO) || !"UA00000".equals(respDTO.getReturnCode())) {
                logger.info("企业号提现失败,请求参数为={}，返回信息为={}", JSONUtils.toJsonString(reqDTO), JSONUtils.toJsonString(respDTO));
                resMsg.setErrCode(respDTO.getReturnCode());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg(respDTO.getReturnMsg());
            }
        } catch (NumberFormatException e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("充值金额格式不正确");
        } catch (YeepayBizException e) {
            logger.info("企业号充值业务异常,请求参数为={}", JSONUtils.toJsonString(reqDTO), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Throwable e) {
            logger.error("企业号充值异常,请求参数为={}", JSONUtils.toJsonString(reqDTO), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 企业号充值订单查询
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/queryOrderList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("企业号充值订单查询")
    public ResponseMessage queryOrderList(@RequestBody EnterpriseRechargeQueryReqDTO reqDTO) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("企业号订单查询，请求参数{}", JSON.toJSONString(reqDTO));
            //参数校验
            reqDTO.setMerchantNo(getCurrentCustomerNumber());
            String status = reqDTO.getStatus();
            if (status.equals(OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.name())) {
                status = OrderStatusEnum.PAY_SUCCESS.name() + "," + OrderStatusEnum.ACCOUNTING.name() + "," + OrderStatusEnum.ACCOUNTING_EXCEPTION.name();
                reqDTO.setStatus(status);
            } else if (OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(status)) {
                status = OrderStatusEnum.ACCOUNTING_FAIL.name();
                reqDTO.setStatus(status);
            }
            //查询列表
            List<EnterpriseAccountRechargeOrderVO> queryResult = enterpriseAccountOrderService.queryRechargeOrderList(reqDTO);
            resMsg.put("pageNo", reqDTO.getPageNo());
            resMsg.put("pageSize", reqDTO.getPageSize());
            resMsg.put("dataList", queryResult);
            EnterpriseAccountOrderSumVO sumVO = enterpriseAccountOrderService.queryRechargeOrderSum(reqDTO);
            resMsg.put("sumAmount", sumVO.getSumAmount());
            resMsg.put("sumCount", sumVO.getSumCount());
            resMsg.put("sumFee", sumVO.getSumFee());
        } catch (Throwable e) {
            logger.error("企业号提现查询异常,请求参数为={}", JSONUtils.toJsonString(reqDTO), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 对公快捷查询产品是否开通
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getProductInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("对公快捷查询产品是否开通")
    public ResponseMessage getProductInfo(@RequestParam(value = "bankCode", required = false) String bankCode) {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("校验对公快捷产品请求参数 customerNumber={}, bankCode={}", user.getCustomerNumber(), bankCode);
            Boolean productOpen = enterpriseAccountOrderService.getQuickPublicProductOpen(user.getCustomerNumber(), bankCode);
            resMsg.put("productOpen", productOpen);
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("获取企业号产品开通异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("获取企业号产品开通异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


}
