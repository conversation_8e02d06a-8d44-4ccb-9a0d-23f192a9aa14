package com.yeepay.g3.app.account.pay.mboss.controller.app;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.BankInfoModel;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.WithdrawBindCardInitModel;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.WithdrawBindCardRequestDTO;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.Costants;
import com.yeepay.g3.app.account.pay.mboss.utils.SmartCacheUtilsHelper;
import com.yeepay.g3.app.account.pay.mboss.utils.UniformConfigUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.handler.RemoteFacadeProxyFactory;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantinfoRespDTO;
import com.yeepay.g3.facade.merchant_platform.facade.customermanagement.CustomerMerchantInfoFacade;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.BindCardReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BindCardRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BankCardTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BindSourceEnum;
import com.yeepay.g3.facade.unionaccount.manage.facade.BindCardFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 老板管账app 企业账户 提现
 *
 * @author: Mr.yin
 * @date: 2024/7/24  15:11
 */
@Controller
@Api(tags = "app-企业账户-提现卡管理")
@RequestMapping("/app/withdraw/card")
public class WithdrawCardController extends BaseController {

    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    //查询客户信息
    private CustomerMerchantInfoFacade customerMerchantInfoFacade = RemoteFacadeProxyFactory.getService(CustomerMerchantInfoFacade.class);

    private BindCardFacade bindCardFacade = RemoteFacadeProxyFactory.getService(BindCardFacade.class);

    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;


    @RequestMapping(value = "/initBindCard", method = RequestMethod.GET)
    @ApiOperation(value = "初始化绑定提现卡")
    @ResponseBody
    public BaseRespDTO<WithdrawBindCardInitModel> initBindCard() {
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            WithdrawBindCardInitModel result = new WithdrawBindCardInitModel();
            LOGGER.info("[初始化绑定提现卡] 商户={}", currentCustomerNumber);
            MerchantinfoRespDTO merchantinfoRespDTO = businessCheckRemoteService.queryMerchantInfo(currentCustomerNumber);
            if (!CheckUtils.isEmpty(merchantinfoRespDTO) && "0000".equals(merchantinfoRespDTO.getRetCode())) {
                LOGGER.info("[初始化绑定提现卡] 调用客户中心查询客户信息，返回的签约类型为{}", merchantinfoRespDTO.getSignType());
                result.setMerchantName(merchantinfoRespDTO.getSignedName());
                Pair<String, String> info = businessCheckRemoteService.convertWithdrawCheckInfo(merchantinfoRespDTO);
                result.setProductType(info.getLeft());
                result.setCorporationName(info.getRight());
            } else {
                LOGGER.error("调用客户中心查询客户信息，异常，返回参数：{}", JSONUtils.toJsonString(merchantinfoRespDTO));
            }
            return BaseRespDTO.success(result);
        } catch (Exception e) {
            LOGGER.error("初始化绑定提现卡 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    @RequestMapping(value = "/notSupportBankList", method = RequestMethod.GET)
    @ApiOperation(value = "提现卡银行类型筛选")
    @ResponseBody
    public BaseRespDTO<List<BankInfoModel>> notSupportBankList() {
        try {
            List<BankInfoModel> result = new ArrayList<>();
            Map<String, String> map = UniformConfigUtils.getNotSupportBankMap();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                result.add(new BankInfoModel(entry.getKey(), entry.getValue()));
            }
            return BaseRespDTO.success(result);
        } catch (Exception e) {
            LOGGER.error("提现卡类型筛选 异常，异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    @RequestMapping(value = "/bindCard", method = RequestMethod.POST)
    @ApiOperation(value = "绑定提现卡")
    @ResponseBody
    public BaseRespDTO bindCard(@RequestBody WithdrawBindCardRequestDTO requestDTO){
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("添加提现卡 请求参数 = {}", JSON.toJSONString(requestDTO));
        try {
            BindCardReqDTO bindCardReqDTO = new BindCardReqDTO();
            bindCardReqDTO.setBankCardType(BankCardTypeEnum.valueOf(requestDTO.getBankCardType()));
            bindCardReqDTO.setInitiateMerchantNo(currentCustomerNumber);
            bindCardReqDTO.setMerchantNo(currentCustomerNumber);
            bindCardReqDTO.setAccountNo(requestDTO.getCardNo());//银行账号/卡号
            bindCardReqDTO.setBankCode(requestDTO.getHeadBankCode());//开户总行编码
            bindCardReqDTO.setBranchCode(requestDTO.getBankCode());
            bindCardReqDTO.setBindSource(BindSourceEnum.APP);
            bindCardReqDTO.setBasicProductCode(Costants.WITHDRAW_BASICSPRODUCTFIRST);
            BindCardRespDTO bindCardRespDTO = bindCardFacade.bindCard(bindCardReqDTO);
            LOGGER.info("添加提现卡 返回结果{}", JSON.toJSONString(bindCardRespDTO));
            if("UA00000".equals(bindCardRespDTO.getReturnCode())) {
                String cacheKey = SmartCacheUtilsHelper.structureKey(SmartCacheUtilsHelper.SmartCacheKeyConstants.MERCHANT_LAST_WITHDRAW_CARD, currentCustomerNumber);
                SmartCacheUtilsHelper.setWithOutException(cacheKey, bindCardRespDTO.getBindId() + "", 90 * 60 * 24);
                return BaseRespDTO.success();
            }else {
                if("UA30001".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("系统异常，请稍后再试");
                }else if("UA40008".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("商户信息有误，请核对后再试");
                }else if("UA40013".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("银行账户信息有误，请核对");
                }else if("UA40011".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("卡片无法识别，请检查后再试");
                }else if("UA40015".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("开户银行有误，请核对");
                }else if("UA40012".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("请添加" + bindCardReqDTO.getBankCardType().getDesc());
                }else if("UA40014".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("省市关系对应有误，请核对");
                }else if("UA40006".equals(bindCardRespDTO.getReturnCode())){
                    return BaseRespDTO.fail("该银行账户已存在，请勿重复添加");
                }else {
                    return BaseRespDTO.fail(null == bindCardRespDTO.getReturnMsg() ? "添加提现卡失败" : bindCardRespDTO.getReturnMsg());
                }
            }
        }catch (Throwable e) {
            LOGGER.error("添加提现卡失败", e);
            return BaseRespDTO.fail("添加提现卡失败");
        }
    }


}
