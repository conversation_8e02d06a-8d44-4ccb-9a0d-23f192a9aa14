package com.yeepay.g3.app.account.pay.mboss.controller.file;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public abstract class FileAnalysis<T> {

    /**
     * 文件头检查
     * @param head
     * @return
     */
    public abstract boolean checkHead(Row head);

    /**
     * 文件分析
     * @param wb
     * @return
     */
    public abstract List<T> analysisData(Workbook wb) throws IllegalAccessException;

    /**
     * 文件处理  开始节点
     * @param file
     * @return
     * @throws Exception
     */
    public abstract List<T> dealFile(MultipartFile file) throws Exception;

}
