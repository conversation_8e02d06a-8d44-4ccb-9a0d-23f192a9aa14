package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.BindCardParam;
import com.yeepay.g3.app.account.pay.mboss.dto.DicCodeAutoWithdrawDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.WithdrawQueryParam;
import com.yeepay.g3.app.account.pay.mboss.dto.req.AddSubAutoRuleReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.AddSubBindCardReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.QuerySubAutoRuleReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.QuerySubCardsReqDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.ProductTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.ProvinceCityService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.WithdrawOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.handler.RemoteFacadeProxyFactory;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.bank.management.facade.SearchBankInfoFacade;
import com.yeepay.g3.facade.bank.management.facade.dto.BranchBankDTO;
import com.yeepay.g3.facade.bank.management.facade.dto.DistrictNameAndCode;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.BaseProductDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantProductQueryRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantinfoRespDTO;
import com.yeepay.g3.facade.merchant_platform.facade.customermanagement.CustomerMerchantInfoFacade;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.BindCardQueryReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.BindCardReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.ModifyBindCardReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.SingleBindCardQueryReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.*;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BankCardOperateTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BankCardTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BindSourceEnum;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.manage.facade.BindCardFacade;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.AutoWithdrawRuleCancelReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.AutoWithdrawRuleQueryReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.AutoWithdrawRuleSetReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.AutoWithdrawRule;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.AutoWithdrawRuleCancelRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.AutoWithdrawRuleQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.AutoWithdrawRuleSetRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.WithdrawTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.facade.MGWithdrawFacade;
import com.yeepay.g3.facade.unionaccount.trade.params.AccountBasicParam;
import com.yeepay.g3.facade.unionaccount.trade.params.ProductCodeParam;
import com.yeepay.g3.facade.unionaccount.trade.utils.WithdrawProductBasicConfigUtils;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.RuleStatusEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;

/**
 * @ClassName: SubWithdrawController
 * @Description:
 * <AUTHOR>
 * @Date 2024/6/21
 * @Version 1.0
 */
@Controller
@RequestMapping("/sub/withdraw")
public class SubWithdrawController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubWithdrawController.class);

    //标准提现产品一级码
    private static final Set<String> MG_FIRST_PRODUCT_CODE_SET = Sets.newHashSet();

    //到账类型
    private static final Map<String, String> ARRIVE_TYPE_MAP = Maps.newLinkedHashMap();

    //到账类型
    private static final Map<String, String> ACCOUNT_TYPE_MAP = Maps.newLinkedHashMap();

    //提现状态
    private static final Map<String, String> WITHDRAW_STATUS_MAP = Maps.newLinkedHashMap();


    private CustomerMerchantInfoFacade customerMerchantInfoFacade = RemoteFacadeProxyFactory.getService(CustomerMerchantInfoFacade.class);

    private BindCardFacade bindCardFacade = RemoteServiceFactory.getService(BindCardFacade.class);

    private MGWithdrawFacade MGWithdrawFacade = RemoteServiceFactory.getService(MGWithdrawFacade.class);

    private SearchBankInfoFacade searchBankInfoFacade = RemoteServiceFactory.getService(SearchBankInfoFacade.class);

    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;

    static {
        WITHDRAW_STATUS_MAP.put("REQUEST_RECEIVE", "提现已接收");
        WITHDRAW_STATUS_MAP.put("REQUEST_ACCEPT", "提现已受理");
        WITHDRAW_STATUS_MAP.put("REMITING,DEBIT_EXCEPTION,REMIT_EXCEPTION,REFUND_EXCEPTION,REFUND_FAIL", "银行处理中");
        WITHDRAW_STATUS_MAP.put("SUCCESS", "提现成功");
        WITHDRAW_STATUS_MAP.put("REVERSED", "银行冲退");
        WITHDRAW_STATUS_MAP.put("FAIL", "提现失败");

        ARRIVE_TYPE_MAP.put("REAL_TIME", "实时到账");
        ARRIVE_TYPE_MAP.put("TWO_HOUR", "2小时到账");
        ARRIVE_TYPE_MAP.put("NEXT_DAY", "次日到账");

        getConfigAccountTypeList();
        MG_FIRST_PRODUCT_CODE_SET.add("-");
        getConfigProductCodeList();
    }

    private static void getConfigAccountTypeList() {
        List<AccountBasicParam> basicList = WithdrawProductBasicConfigUtils.getAccountConfigList();
        for (AccountBasicParam basic : basicList) {
            ACCOUNT_TYPE_MAP.put(basic.getAccountType(), AccountTypeEnum.valueOf(basic.getAccountType()).getDesc());
        }
    }

    private static void getConfigProductCodeList() {
        List<ProductCodeParam> productList = WithdrawProductBasicConfigUtils.getProductConfigList();
        for (ProductCodeParam param : productList) {
            MG_FIRST_PRODUCT_CODE_SET.add(param.getFirstProductCode());
        }
    }


    @RequestMapping(value = "/queryOrderList")
    @ResponseBody
    public ResponseMessage queryOrderList(WithdrawQueryParam withdrawQueryParam,
                                          @RequestParam(value = "pageSize", defaultValue = Costants.PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                          @RequestParam(value = "pageNo", defaultValue = Costants.PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("上级查询下级商户的提现订单，请求参数{}", JSONUtils.toJsonString(withdrawQueryParam));
        //参数校验
        checkInputParam(withdrawQueryParam);
        withdrawQueryParam.setFirstCodes(StringUtils.join(MG_FIRST_PRODUCT_CODE_SET.toArray(), ","));
        //查询列表
        QueryResult queryResult = this.queryWithOrderList(withdrawQueryParam, pageNo, pageSize);
        if (!CheckUtils.isEmpty(queryResult.getData())) {
            for (Map<String, Object> detail : queryResult.getData()) {
                adaptReturnResult(detail);
            }
        }
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        resMsg.put("dataList", queryResult.getData());
        this.queryWithOrderListSum(withdrawQueryParam, resMsg);
        return resMsg;
    }


    /**
     * 上级查询下级商户查询详情
     *
     * @param orderNo
     * @param merchantNo
     * @return
     */
    @RequestMapping(value = "/query/detail", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryDetail(@RequestParam(value = "orderNo") String orderNo, @RequestParam(value = "merchantNo") String merchantNo) {
        LOGGER.info("上级查询下级商户的提现订单明细，订单号：orderNo={}，商编为={}", orderNo, merchantNo);
        try {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("customerNumber", merchantNo);
            queryMap.put("orderNo", orderNo);
            //查询组件查询
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
            QueryResult queryResult = queryService.query("queryWithdrawOrderList", queryParam);
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                Map<String, Object> map = (Map) ((List) queryResult.getData()).get(0);
                String accountType = (String) map.get("account_type");
                map.put("account_type", AccountTypeEnum.valueOf(accountType).getDesc());
                String bank_name = (String) map.get("bank_name");
                String bank_account_no = (String) map.get("bank_account_no");
                bank_name = (bank_name == null ? "" : bank_name);
                bank_account_no = (bank_account_no == null ? "" : "(" + processBankNoRemain4Numbers(bank_account_no) + ")");
                map.put("bank_name", bank_name + bank_account_no);

                String status = (String) map.get("status");
                String remitStatus = (String) map.get("remit_status");
                if ("SUCCESS".equals(status) || "REVERSED".equals(remitStatus)) {
                    if ("REVERSED".equals(remitStatus)) {
                        map.put("status", "银行冲退");
                        map.put("finish_time", map.get("debit_back_time"));
                    } else {
                        map.put("status", "提现成功");
                    }
                } else if ("REMITING".equals(status) || "DEBIT_EXCEPTION".equals(status) || "REMIT_EXCEPTION".equals(status)
                        || "REFUND_EXCEPTION".equals(status) || "REFUND_FAIL".equals(status)) {
                    map.put("status", "银行处理中");
                } else if ("REQUEST_RECEIVE".equals(status)) {
                    map.put("status", "提现已接收");
                } else if ("REQUEST_ACCEPT".equals(status)) {
                    map.put("status", "提现已受理");
                } else if ("FAIL".equals(status)) {
                    map.put("status", "提现失败");
                }

                String arriveType = (String) map.get("arrive_type");
                if ("REAL_TIME".equals(arriveType)) {
                    map.put("arrive_type", "实时到账");
                } else if ("TWO_HOUR".equals(arriveType)) {
                    map.put("arrive_type", "2小时到账");
                } else {
                    map.put("arrive_type", "次日到账");
                }
                Object received_amount = map.get("received_amount");
                if (received_amount != null) {
                    map.put("received_amount", dealAmount(received_amount) + "元");
                }
                Object payer_fee = map.get("payer_fee");
                if (payer_fee != null) {
                    map.put("payer_fee", dealAmount(payer_fee) + "元");
                }
                if (AccountTypeEnum.FEE_ACCOUNT.equals(accountType)) {
                    map.put("payer_fee", "无");
                }
                if (map.get("fee_undertaker_merchant_no") != null && !ObjectUtils.equals(map.get("from_customer_no"), map.get("fee_undertaker_merchant_no"))) {
                    map.put("payer_fee", "无");
                }
                Object deduct_amount = map.get("deduct_amount");
                if (deduct_amount != null) {
                    map.put("deduct_amount", dealAmount(deduct_amount) + "元");
                }
                Object withdraw_amount = map.get("withdraw_amount");
                if (withdraw_amount != null) {
                    map.put("withdraw_amount", dealAmount(withdraw_amount) + "元");
                }

                LOGGER.info("上级查询下级商户的提现订单明细返回，transferResponseParam={}", JSON.toJSONString(map));
                return BaseRespDTO.success(map);

            } else {
                return BaseRespDTO.success("暂无数据");
            }
        } catch (Exception e) {
            return BaseRespDTO.fail(e.getMessage());
        }
    }


    /**
     * 上级下载下级提现记录
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/download")
    public void downloadRecord(WithdrawQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            this.checkInputParam(param);
            if ("REVERSED".equals(param.getStatus())) {
                param.setStatus(null);
                param.setRemitStatus("REVERSED");
            }
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            param.setCustomerNumber(param.getMerchantNo());
            param.setFirstCodes(StringUtils.join(MG_FIRST_PRODUCT_CODE_SET.toArray(), ","));
            StringBuilder desc = new StringBuilder();
            desc.append("提现订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            if ("appointSync".equals(param.getSyncType())) {
                new WithdrawOrderDownloadService(getCurrentUser(), param, desc.toString(), "提现订单查询-").syncDownload(request, response);
            } else {
                new WithdrawOrderDownloadService(getCurrentUser(), param, desc.toString(), "提现订单查询-").download(request, response);
            }
        } catch (Throwable ex) {
            LOGGER.error("downloadRecord-error", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }


    private Object dealAmount(Object amount) {
        if (amount == null) {
            return null;
        }
        if (amount instanceof BigDecimal) {
            return ((BigDecimal) amount).setScale(2, BigDecimal.ROUND_DOWN);
        } else if (StringUtils.isNumeric(amount.toString())) {
            return new BigDecimal(amount.toString()).setScale(2, BigDecimal.ROUND_DOWN);
        }
        return amount;
    }

    /**
     * 适配返回结果
     *
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }
        Map<String, AccountBasicParam> config = WithdrawProductBasicConfigUtils.getAccountBasicConfig();
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);
        try {
            if (null != detail.get("received_amount")) {
                detail.put("received_amount", nf.format(new BigDecimal(detail.get("received_amount").toString())));
            }
            if (null != detail.get("withdraw_amount") && StringUtils.isNotBlank(detail.get("withdraw_amount").toString())) {
                detail.put("withdraw_amount", nf.format(new BigDecimal(detail.get("withdraw_amount").toString())));
            }
            if (null != detail.get("payer_fee")) {
                detail.put("payer_fee", nf.format(new BigDecimal(detail.get("payer_fee").toString())));
            }
            if (null != detail.get("account_type")) {
                detail.put("service_type", config.get(detail.get("account_type").toString()).getServiceType());
                detail.put("account_type", AccountTypeEnum.valueOf(detail.get("account_type").toString()).getDesc());
            }
            //下单时间
            Object obj = detail.get("create_time");
            if (null != obj) {
                detail.put("create_time", DateUtils.toString((Date) obj, "yyyy-MM-dd HH:mm:ss"));
            }
            //处理卡号后四位
            Object banCard = detail.get("bank_account_no");
            if (null != banCard) {
                detail.put("bank_account_no", processBankNoRemain4Numbers(banCard));
            }

        } catch (Throwable e) {
            LOGGER.error("这都能错..擦....", e);
        }
        return detail;
    }


    private QueryResult queryWithOrderList(WithdrawQueryParam param, int pageNo, int pageSize) {
        if ("REVERSED".equals(param.getStatus())) {
            param.setStatus(null);
            param.setRemitStatus("REVERSED");
        }
        //构造查询参数
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getMerchantNo());
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("queryWithdrawOrderList", queryParam);

    }

    private String processBankNoRemain4Numbers(Object bankNo) {
        if (StringUtils.isBlank(bankNo.toString())) {
            return "";
        }
        return HiddenCodeUtils.hiddenBankCardNo4End(AESUtils.decryptWithBase64(bankNo.toString()));
    }


    private void queryWithOrderListSum(WithdrawQueryParam param, ResponseMessage resMsg) {
        if ("REVERSED".equals(param.getStatus())) {
            param.setStatus(null);
            param.setRemitStatus("REVERSED");
        }
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getMerchantNo());
        List<Map<String, Object>> withOrderListSum = QueryServiceUtil.query("accountTradeService", "queryWithdrawOrderListSum", queryMap);
        // 如果查询结果不为空的话
        if (!CheckUtils.isEmpty(withOrderListSum)) {
            Map<String, Object> sumResult = withOrderListSum.get(0);
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumFractionDigits(2);
            nf.setMaximumFractionDigits(2);
            resMsg.getData().put("sum_count", sumResult.get("sum_count").toString());// 总笔数
            resMsg.getData().put("sum_amount", CheckUtils.isEmpty(sumResult.get("sum_amount")) ? "0.00" : nf.format(new BigDecimal(sumResult.get("sum_amount").toString())));// 总金额
            resMsg.getData().put("sum_fee", CheckUtils.isEmpty(sumResult.get("sum_fee")) ? "0.00" : nf.format(new BigDecimal(sumResult.get("sum_fee").toString())));// 总手续费
            resMsg.getData().put("totalCount", sumResult.get("sum_count").toString());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", 0.00);// 总金额
            resMsg.getData().put("sum_fee", 0.00);// 总手续费
        }
    }

    /**
     * 参数校验
     *
     * @param param
     */
    private void checkInputParam(WithdrawQueryParam param) {
        if (param == null || param.isEmptyCheck()) {
            LOGGER.error("上级查询下级商户的提现订单，参数都为空");
            throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("参数为空");
        }
        // 请求开始时间不能查过结束时间
        if (StringUtils.isNotEmpty(param.getCreateStartDate())
                && StringUtils.isNotEmpty(param.getCreateEndDate())) {
            try {
                Date startDate = DateUtils.parseDate(
                        param.getCreateStartDate(),
                        DateUtils.DATE_FORMAT_DATEONLY);
                Date endDate = DateUtils.parseDate(param.getCreateEndDate(),
                        DateUtils.DATE_FORMAT_DATEONLY);
                if (DateUtils.compareDate(startDate, endDate, Calendar.DATE) > 0) {
                    LOGGER.error(
                            "请求开始时间大于请求结束时间!入参格式有误! createStartDate={},createEndDate={}",
                            param.getCreateStartDate(),
                            param.getCreateEndDate());
                    throw new RuntimeException("请求开始时间大于请求结束时间!");
                }
                // 请求开始时间和请求结束时间不能超过100天
                if (CheckParamUtils.isOver100DaysInterval(startDate, endDate)) {
                    LOGGER.error(
                            "请求开始时间和请求结束时间间隔超过100天!入参格式有误! createStartDate={},createEndDate={}",
                            param.getCreateStartDate(),
                            param.getCreateEndDate());
                    throw new RuntimeException("请求开始时间和请求结束时间间隔超过100天!");
                }
            } catch (ParseException e) {
                LOGGER.error("解决时间异常，异常原因为", e);
            }
        }
    }


    /**
     * 查询下级绑卡列表
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/query/cards", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryCards(QuerySubCardsReqDTO reqDTO) {
        try {
            LOGGER.info("查询下级绑卡列表，请求参数为={}", JSON.toJSONString(reqDTO));
            reqDTO.validateParam();
            Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(reqDTO);
            Integer startIndex = (reqDTO.getPageNo() - 1) * reqDTO.getPageSize() + 1;
            QueryParam queryParam = new QueryParam();
            queryParam.setStartIndex(startIndex);
            queryParam.setParams(queryMap);
            queryParam.setMaxSize(reqDTO.getPageSize());
            queryParam.setDoSum(true);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("acoountManageService", QueryService.class);
            QueryResult queryResult = queryService.query("queryBindCardList", queryParam);
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                for (Map<String, Object> detail : queryResult.getData()) {
                    convertBindCardList(detail);
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("data", queryResult.getData());
            map.put("totalCount", queryResult.getTotalCount());
            LOGGER.info("查询下级绑卡列表，请求参数{}", JSON.toJSONString(reqDTO));
            return BaseRespDTO.success(map);
        } catch (YeepayBizException e) {
            LOGGER.error("查询下级绑卡列表异常，请求参数为=" + JSONUtils.toJsonString(reqDTO) + "异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("查询下级绑卡列表异常，请求参数为=" + JSONUtils.toJsonString(reqDTO) + "异常为={}", e);
            return BaseRespDTO.fail("查询失败");
        }
    }


    /**
     * 查询下级自动提现规则
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/query/autoWithdrawRule", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryAutoWithdrawRule(QuerySubAutoRuleReqDTO reqDTO) {
        try {
            LOGGER.info("查询下级自动提现规则，请求参数为={}", JSON.toJSONString(reqDTO));
            reqDTO.validateParam();
            //构造查询参数
            Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(reqDTO);
            Integer startIndex = (reqDTO.getPageNo() - 1) * reqDTO.getPageSize() + 1;
            //查询组件查询
            QueryParam queryParam = new QueryParam();
            queryParam.setStartIndex(startIndex);
            queryParam.setParams(queryMap);
            queryParam.setMaxSize(reqDTO.getPageSize());
            queryParam.setDoSum(true);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
            QueryResult queryResult = queryService.query("querySubAutoWithdrawRuleList", queryParam);
            Map<String, Object> map = new HashMap<>();
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                for (Map<String, Object> detail : queryResult.getData()) {
                    convertAutoRuleList(detail);
                }
            }
            map.put("data", queryResult.getData());
            map.put("totalCount", queryResult.getTotalCount());
            LOGGER.info("查询下级自动提现规则，响应信息为={}", JSON.toJSONString(reqDTO));
            return BaseRespDTO.success(map);
        } catch (YeepayBizException e) {
            LOGGER.error("查询下级自动提现规则异常，请求参数为=" + JSONUtils.toJsonString(reqDTO) + "异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("查询下级自动提现规则异常，请求参数为=" + JSONUtils.toJsonString(reqDTO) + "异常为={}", e);
            return BaseRespDTO.fail("查询失败");
        }
    }


    @RequestMapping(value = "/cardManage/init", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO cardManageInit(@Param(value = "merchantNo") String merchantNo) {
        Map<String, Object> map = new HashMap<>();
        try {
            LOGGER.info("下级新增卡初始化信息,请求参数为={}", merchantNo);
            map = buildModelView(map, merchantNo);
            LOGGER.info("下级新增卡初始化信息，商编={}，返回为={}", merchantNo, JSONUtils.toJsonString(map));
            return BaseRespDTO.success(map);
        } catch (Exception e) {
            LOGGER.error("下级新增卡初始化信息异常，商编=" + merchantNo + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }


    @RequestMapping(value = "/updateOrCancelCard", method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO updateOrCancelCard(@RequestBody BindCardParam reqDTO) {
        try {
            LOGGER.info("下级修改卡信息,请求参数为={}", JSONUtils.toJsonString(reqDTO));
            if (StringUtils.isNotBlank(reqDTO.getBindIdCharacter())) {
                reqDTO.setBindId(Long.valueOf(reqDTO.getBindIdCharacter()));
            }
            String bankCardOperateType = reqDTO.getBankCardOperateType();
            ModifyBindCardReqDTO modifyBindCardReqDTO = new ModifyBindCardReqDTO();
            modifyBindCardReqDTO.setInitiateMerchantNo(reqDTO.getMerchantNo());
            modifyBindCardReqDTO.setMerchantNo(reqDTO.getMerchantNo());
            modifyBindCardReqDTO.setBindId(Long.parseLong(reqDTO.getBindIdCharacter()));
            if (bankCardOperateType.equals(BankCardOperateTypeEnum.MODIFY.name())) {
                modifyBindCardReqDTO.setBankCode(reqDTO.getBankCode());
                modifyBindCardReqDTO.setBranchCode(reqDTO.getBranchCode());
                modifyBindCardReqDTO.setBankCardOperateType(BankCardOperateTypeEnum.MODIFY);
            } else if (bankCardOperateType.equals(BankCardOperateTypeEnum.CANCELLED.name())) {
                modifyBindCardReqDTO.setBankCardOperateType(BankCardOperateTypeEnum.CANCELLED);
            } else {
                return BaseRespDTO.fail("操作类型有误");
            }
            ModifyBindCardRespDTO respDTO = bindCardFacade.modifyBindCard(modifyBindCardReqDTO);
            LOGGER.info("下级修改卡信息,响应信息为={}", JSONUtils.toJsonString(reqDTO));
            if (respDTO != null && "UA00000".equals(respDTO.getReturnCode())) {
                return BaseRespDTO.success();
            } else {
                return BaseRespDTO.fail("修改失败");
            }
        } catch (Exception e) {
            LOGGER.error("修改下级提现卡异常，请求参数为=" + JSONUtils.toJsonString(reqDTO) + ",异常为={}", e);
            return BaseRespDTO.fail("修改失败");
        }
    }


    /**
     * 作废下级的自动提现规则
     *
     * @param merchantNo
     * @return
     */
    @RequestMapping(value = "/cancelAutoWithdrawRule", method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO cancelWithdrawRule(@Param(value = "merchantNo") String merchantNo) {
        try {
            LOGGER.info("作废下级的自动提现规则,请求参数为={}", merchantNo);
            AutoWithdrawRuleCancelReqDTO req = new AutoWithdrawRuleCancelReqDTO();
            req.setMerchantNo(merchantNo);
            AutoWithdrawRuleCancelRespDTO resp = MGWithdrawFacade.cancelAutoWithdrawRule(req);
            LOGGER.info("作废下级的自动提现规则,响应信息为={}", JSON.toJSONString(resp));
            if (resp != null && "UA00000".equals(resp.getReturnCode())) {
                return BaseRespDTO.success();
            } else {
                return BaseRespDTO.fail(resp.getReturnMsg());
            }
        } catch (Exception e) {
            LOGGER.error("作废下级的自动提现规则异常,请求参数为=" + merchantNo + "异常信息为={}", e);
            return BaseRespDTO.fail("作废失败");
        }
    }

    /**
     * 设置下级的自动提现规则
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/setAutoWithdrawRule", method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO setAutoWithdrawRule(@RequestBody AddSubAutoRuleReqDTO reqDTO) {
        try {
            LOGGER.info("设置下级的自动提现规则,参数为 = {}", JSONUtils.toJsonString(reqDTO));
            AutoWithdrawRuleSetReqDTO autoWithdrawRuleSetReqDTO = new AutoWithdrawRuleSetReqDTO();
            autoWithdrawRuleSetReqDTO.setMerchantNo(reqDTO.getMerchantNo());
            autoWithdrawRuleSetReqDTO.setBindId(reqDTO.getBindId());
            autoWithdrawRuleSetReqDTO.setRemark(getRemarkAutoWithdraw(reqDTO.getRemark()));
            autoWithdrawRuleSetReqDTO.setTriggerTime(reqDTO.getTriggerTime());
            autoWithdrawRuleSetReqDTO.setOperator(getCurrentUser().getLoginName());
            autoWithdrawRuleSetReqDTO.setBankName(reqDTO.getBankName());
            BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
            String markProductCode = businessCheckRemoteService.queryMarketProduct(reqDTO.getMerchantNo());
            if (CheckUtils.isEmpty(markProductCode)) {
                return BaseRespDTO.fail("请稍候重试");
            }
            autoWithdrawRuleSetReqDTO.setMarketProductCode(markProductCode);
            BindCardQueryRespDTO bindCardQueryRespDTO = queryBindCardV2(reqDTO.getMerchantNo());
            if (!CheckUtils.isEmpty(bindCardQueryRespDTO.getBankCardAccountList())) {
                LOGGER.info("商户{}进入管理提现卡查询绑定银行卡成功，卡数量为{}", bindCardQueryRespDTO.getMerchantNo(), bindCardQueryRespDTO.getBankCardAccountList().size());
                for (BankCardAccount bankCardAccount : bindCardQueryRespDTO.getBankCardAccountList()) {
                    if (bankCardAccount.getBindCardId().equals(reqDTO.getBindId())) {
                        autoWithdrawRuleSetReqDTO.setBankAccountNo(bankCardAccount.getAccountNo());
                        break;
                    }
                }
            }
            autoWithdrawRuleSetReqDTO.setRemainAmount(StringUtils.isBlank(reqDTO.getRemainAmount()) ? null :
                    new BigDecimal(reqDTO.getRemainAmount()));
            if (!CheckUtils.isEmpty(reqDTO.getReceiveType())) {
                if ("REALTIME".equals(reqDTO.getReceiveType())) {
                    autoWithdrawRuleSetReqDTO.setReceiveType(WithdrawTypeEnum.REAL_TIME);
                } else if ("TWOHOURS".equals(reqDTO.getReceiveType())) {
                    autoWithdrawRuleSetReqDTO.setReceiveType(WithdrawTypeEnum.TWO_HOUR);
                } else if ("TOMORROW".equals(reqDTO.getReceiveType())) {
                    autoWithdrawRuleSetReqDTO.setReceiveType(WithdrawTypeEnum.NEXT_DAY);
                }
            }
            AutoWithdrawRuleSetRespDTO respDTO = MGWithdrawFacade.setAutoWithdrawRule(autoWithdrawRuleSetReqDTO);
            LOGGER.info("设置下级的自动提现规则，响应信息为={}", JSON.toJSONString(respDTO));
            if (CheckUtils.isEmpty(respDTO) || !"UA00000".equals(respDTO.getReturnCode())) {
                LOGGER.warn("设置下级的自动提现规则, 调用不成功 respDTO={},autoWithdrawRuleSetReqDTO={}",JSON.toJSONString(respDTO),JSON.toJSONString(autoWithdrawRuleSetReqDTO));
                return BaseRespDTO.fail(respDTO.getReturnMsg());
            } else {
                return BaseRespDTO.success();
            }
        } catch (Exception e) {
            LOGGER.error("设置自动提现规则异常", e);
            return BaseRespDTO.fail("请稍候重试");
        }
    }

    /**
     * 新增绑卡
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/firmAddCard", method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO confirmAddCard(@RequestBody AddSubBindCardReqDTO reqDTO) {
        try {
            LOGGER.info("下级新增绑卡信息,参数为 = {}", JSONUtils.toJsonString(reqDTO));
            String cardType = reqDTO.getProductType();
            BindCardReqDTO bindCardReqDTO = new BindCardReqDTO();
            String returnCardType = "";
            if ("person".equals(cardType) || "individual".equals(cardType)) {
                bindCardReqDTO.setBankCardType(BankCardTypeEnum.DEBIT_CARD);
                returnCardType = "借记卡";
            } else if ("personBusiness".equals(cardType) || "business".equals(cardType)) {
                bindCardReqDTO.setBankCardType(BankCardTypeEnum.ENTERPRISE_ACCOUNT);
                returnCardType = "对公卡";
                //开户支行编码
            } else if ("settle_account".equals(cardType)) {
                returnCardType = "单位结算卡";
                bindCardReqDTO.setBankCardType(BankCardTypeEnum.UNIT_SETTLEMENT_CARD);
                //开户支行编码
            }
            bindCardReqDTO.setInitiateMerchantNo(reqDTO.getMerchantNo());
            bindCardReqDTO.setMerchantNo(reqDTO.getMerchantNo());
            //银行账号/卡号
            bindCardReqDTO.setAccountNo(reqDTO.getCardNo());
            //开户总行编码
            bindCardReqDTO.setBankCode(reqDTO.getHeadBankCode());
            bindCardReqDTO.setBranchCode(reqDTO.getBankCode());
            bindCardReqDTO.setBindSource(BindSourceEnum.MP);
            bindCardReqDTO.setBasicProductCode(Costants.WITHDRAW_BASICSPRODUCTFIRST);
            BindCardRespDTO bindCardRespDTO = bindCardFacade.bindCard(bindCardReqDTO);
            LOGGER.info("下级新增绑卡信息,响应信息为={}", JSON.toJSONString(bindCardRespDTO));
            if (bindCardRespDTO != null && "UA00000".equals(bindCardRespDTO.getReturnCode())) {
                return BaseRespDTO.success();
            } else {
                switch (bindCardRespDTO.getReturnCode()) {
                    case "UA30001":
                        return BaseRespDTO.fail("系统异常，请稍后再试");
                    case "UA40008":
                        return BaseRespDTO.fail("商户信息有误，请核对后再试");
                    case "UA40013":
                        return BaseRespDTO.fail("银行账户信息有误，请核对");
                    case "UA40011":
                        return BaseRespDTO.fail("卡片无法识别，请检查后再试");
                    case "UA40015":
                        return BaseRespDTO.fail("开户银行有误，请核对");
                    case "UA40012":
                        return BaseRespDTO.fail("请添加" + returnCardType);
                    case "UA40014":
                        return BaseRespDTO.fail("省市关系对应有误，请核对");
                    case "UA40006":
                        return BaseRespDTO.fail("该银行账户已存在，请勿重复添加");
                    default:
                        return BaseRespDTO.fail(StringUtils.isBlank(bindCardRespDTO.getReturnMsg()) ? "添加提现卡失败" : bindCardRespDTO.getReturnMsg());
                }
            }
        } catch (Throwable e) {
            LOGGER.error("添加提现卡失败", e);
            return BaseRespDTO.fail("新增失败");
        }
    }


    @RequestMapping(value = "/auto/rule/init", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO autoRuleInit(@Param(value = "merchantNo") String merchantNo) {
        try {
            LOGGER.info("初始化自动提现规则,商编为 = {}", merchantNo);
            Map<String, Object> map = new HashMap<>();
            boolean hasAutoWithdrawRule = false;
            boolean hasEffectiveAutoWithdrawRule = false;
            AutoWithdrawRuleQueryReqDTO autoWithdrawRuleQueryReqDTO = new AutoWithdrawRuleQueryReqDTO();
            autoWithdrawRuleQueryReqDTO.setMerchantNo(merchantNo);
            AutoWithdrawRuleQueryRespDTO autoWithdrawRuleQueryRespDTO = MGWithdrawFacade.queryAutoWithdrawRule(autoWithdrawRuleQueryReqDTO);
            if (!CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO) && !CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList())) {
                hasAutoWithdrawRule = true;
                for (AutoWithdrawRule rule : autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList()) {
                    if (RuleStatusEnum.EFFECTIVE.equals(rule.getStatus())) {
                        hasEffectiveAutoWithdrawRule = true;
                    }
                }
            }
            map.put("hasAutoWithdrawRule", hasAutoWithdrawRule);
            map.put("hasEffectiveAutoWithdrawRule", hasEffectiveAutoWithdrawRule);
            map = buildWithdrawProductMap(map, merchantNo);
            LOGGER.info("初始化自动提现规则,响应信息为 = {}", JSONUtils.toJsonString(map));
            return BaseRespDTO.success(map);
        } catch (Exception e) {
            LOGGER.error("初始化自动提现规则,商编为=" + merchantNo + "异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }


    /**
     * 提现卡查询
     *
     * @return
     */
    @RequestMapping(value = "/withdrawCardDetail", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryWithdrawCardDetail(@Param(value = "merchantNo") String merchantNo,
                                               @Param(value = "bindId") String bindId) {
        SingleBindCardQueryReqDTO dto = new SingleBindCardQueryReqDTO();
        dto.setInitiateMerchantNo(merchantNo);
        dto.setMerchantNo(merchantNo);
        dto.setBindId(Long.valueOf(bindId));
        BindCardParam bindCardParam = new BindCardParam();
        try {
            LOGGER.info("下级查询提现卡详情,请求参数为={}", JSON.toJSONString(dto));
            SingleBindCardQueryRespDTO bindCardQueryRespDto = bindCardFacade.querySingleBankCard(dto);
            if (bindCardQueryRespDto == null || !ErrorCode.SUCCESS.equals(bindCardQueryRespDto.getReturnCode())) {
                return BaseRespDTO.fail("查询失败");
            }
            BankCardAccount bankCardAccount = bindCardQueryRespDto.getBankCardAccount();
            if (bankCardAccount == null) {
                return BaseRespDTO.fail("查询失败");
            }
            String branchBankCode = bankCardAccount.getBranchBankCode();
            bindCardParam.setBankName(bankCardAccount.getBankName());
            bindCardParam.setBankCardType(bankCardAccount.getBankCardType().name());
            bindCardParam.setBankCode(bankCardAccount.getBankCode());
            bindCardParam.setAccountName(bankCardAccount.getAccountName());
            bindCardParam.setCardNo(HiddenCodeUtils.hiddenBankCardNo(bankCardAccount.getAccountNo()));
            doBranchBankInfo(bindCardParam, branchBankCode);
            LOGGER.info("下级查询提现卡详情,响应信息为={}", JSON.toJSONString(bindCardParam));
            return BaseRespDTO.success(bindCardParam);
        } catch (Exception e) {
            LOGGER.error("下级查询提现卡异常,请求参数为" + bindId + "异常信息为={}", e);
            return BaseRespDTO.fail("查询异常");
        }
    }

    private BindCardParam doBranchBankInfo(BindCardParam bindCardParam, String branchBankCode) {
        if (StringUtils.isBlank(branchBankCode)) {
            return bindCardParam;
        }
        BranchBankDTO branchBankDTO = searchBankInfoFacade.searchBranchBankInfoByCnapsCode(branchBankCode);
        if (branchBankDTO == null) {
            return bindCardParam;
        }
        String branchBankName = branchBankDTO.getBranchBankName();
        bindCardParam.setBranchCode(branchBankCode);
        bindCardParam.setBranchCodeName(branchBankName);
        Map<String, DistrictNameAndCode> districtNameAndCodeMap = branchBankDTO.getDistrictNameAndCodeMap();
        if (districtNameAndCodeMap == null) {
            return bindCardParam;
        }
        DistrictNameAndCode province = districtNameAndCodeMap.get("province");
        if (province != null) {
            String districtCode = province.getDistrictCode();
            String districtName = province.getDistrictName();
            bindCardParam.setBankProvinceCode(districtCode);
            bindCardParam.setBankProvinceName(districtName);
        }
        DistrictNameAndCode city = districtNameAndCodeMap.get("city");
        if (city != null) {
            String districtCode = city.getDistrictCode();
            String districtName = city.getDistrictName();
            bindCardParam.setBankCityCode(districtCode);
            bindCardParam.setBankCityName(districtName);
        }
        return bindCardParam;
    }

    private BindCardQueryRespDTO queryBindCardV2(String merchantNo) {
        BindCardQueryReqDTO bindCardQueryReqDTO = new BindCardQueryReqDTO();
        bindCardQueryReqDTO.setInitiateMerchantNo(merchantNo);
        bindCardQueryReqDTO.setMerchantNo(merchantNo);
        //根据产品码 只查出提现卡
        bindCardQueryReqDTO.setBasicProductCode(Costants.WITHDRAW_BASICSPRODUCTFIRST);
        BindCardQueryRespDTO bindCardQueryRespDTO;
        try {
            bindCardQueryRespDTO = bindCardFacade.queryBankCards(bindCardQueryReqDTO);
            if ("UA00000".equals(bindCardQueryRespDTO.getReturnCode())) {
                return bindCardQueryRespDTO;
            } else {
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("查询提现卡列表失败");
            }
        } catch (Exception e) {
            LOGGER.error("查询中台管理系统提现卡异常，请求商编为=" + merchantNo + "异常为={}", e);
            throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("查询提现卡列表失败");
        }
    }


    private Map<String, Object> buildWithdrawProductMap(Map<String, Object> map, String merchantNo) {
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        boolean hasWithdrawProduct = false;
        List<DicCodeAutoWithdrawDTO> secondProductList = new ArrayList<>();
        String markProductCode = businessCheckRemoteService.queryMarketProduct(merchantNo);
        MerchantProductQueryRespDTO merchantProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(merchantNo, ProductTypeEnum.ACCOUNT.toString(), markProductCode, Costants.WITHDRAW_BASICSPRODUCTFIRST, null);
        Object feeType = null;
        String realTimeProduct = "";
        String twoHourProduct = "";
        String nextDayProduct = "";
        if ("0000".equals(merchantProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductQueryRespDTO.getBaseProductList())) {
            for (BaseProductDTO baseProductDTO : merchantProductQueryRespDTO.getBaseProductList()) {
                if (!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode())) {
                    if (!CollectionUtils.isEmpty(baseProductDTO.getProductAttributeMap())) {
                        feeType = baseProductDTO.getProductAttributeMap().get("FEE_MERCHANT_TYPE");
                    }
                    if (WithdrawTypeEnum.REAL_TIME.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())) {
                        realTimeProduct = baseProductDTO.getSecondBaseProductCode();
                    } else if (WithdrawTypeEnum.TWO_HOUR.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())) {
                        twoHourProduct = baseProductDTO.getSecondBaseProductCode();
                    } else if (WithdrawTypeEnum.NEXT_DAY.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())) {
                        nextDayProduct = baseProductDTO.getSecondBaseProductCode();
                    }
                    hasWithdrawProduct = true;
                }
            }
        }
        if (StringUtils.isNotBlank(realTimeProduct)) {
            secondProductList.add(new DicCodeAutoWithdrawDTO(realTimeProduct, WithdrawTypeEnum.REAL_TIME.getMqDesc(), feeType == null ? "" : feeType.toString()));
        }
        if (StringUtils.isNotBlank(twoHourProduct)) {
            secondProductList.add(new DicCodeAutoWithdrawDTO(twoHourProduct, WithdrawTypeEnum.TWO_HOUR.getMqDesc(), feeType == null ? "" : feeType.toString()));
        }
        if (StringUtils.isNotBlank(nextDayProduct)) {
            secondProductList.add(new DicCodeAutoWithdrawDTO(nextDayProduct, WithdrawTypeEnum.NEXT_DAY.getMqDesc(), feeType == null ? "" : feeType.toString()));
        }
        map.put("hasWithdrawProduct", hasWithdrawProduct);
        map.put("secondProductList", secondProductList);
        return map;
    }


    /**
     * 自动提现规则转换
     *
     * @param remark
     * @return
     */
    public String getRemarkAutoWithdraw(String remark) {
        if (remark == null) {
            return null;
        }
        try {
            StringBuffer remarkResult = new StringBuffer();
            if (StringUtils.isNotBlank(remark) && remark.contains("YYYYMMDD")) {
                if (remark.indexOf("YYYYMMDD") > 0 && StringUtils.isNotBlank(remark.substring(0, remark.indexOf("YYYYMMDD")))) {
                    remarkResult.append("'").append(remark.substring(0, remark.indexOf("YYYYMMDD"))).append("'+");
                }
                remarkResult.append("#today_yyyyMMdd");
                Integer afterLength = remark.length() - remark.indexOf("YYYYMMDD");
                if (afterLength > 8 && StringUtils.isNotBlank(remark.substring(remark.indexOf("YYYYMMDD") + 8))) {
                    remarkResult.append("+'").append(remark.substring(remark.indexOf("YYYYMMDD") + 8)).append("'");
                }
            } else {
                remarkResult.append(remark);
            }
            return remarkResult.toString();
        } catch (Exception e) {
            LOGGER.error("设置自动提现规则时转换备注异常", e);
        }
        return remark;
    }


    private Map<String, Object> buildModelView(Map<String, Object> map, String merchantNo) throws Exception {
        String productType = "";
        String merchantName = "";
        MerchantinfoRespDTO merchantinfoRespDTO = businessCheckRemoteService.queryMerchantInfo(merchantNo);
        if (!CheckUtils.isEmpty(merchantinfoRespDTO) && "0000".equals(merchantinfoRespDTO.getRetCode())) {
            LOGGER.info("调用客户中心查询客户信息，返回的签约类型为{}", merchantinfoRespDTO.getSignType());
            merchantName = merchantinfoRespDTO.getSignedName();
            Pair<String, String> info = businessCheckRemoteService.convertWithdrawCheckInfo(merchantinfoRespDTO);
            if (StringUtils.isNotEmpty(info.getLeft())) {
                productType = info.getLeft();
            }
            if (StringUtils.isNotEmpty(info.getRight())) {
                map.put("corporationName", info.getRight());
            }
        } else {
            LOGGER.error("调用客户中心查询客户信息，异常，返回参数：{}", JSONUtils.toJsonString(merchantinfoRespDTO));
        }
        map.put("bankMap", ProvinceCityService.queryBankMap());
        map.put("provinceList", ProvinceCityService.getAreasV2(null));
        map.put("productType", productType);
        map.put("merchantName", merchantName);
        map.put("notSupportBankMap", JSON.toJSONString(UniformConfigUtils.getNotSupportBankMap()));
        return map;
    }


    private Map<String, Object> convertAutoRuleList(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<>();
        }
        try {
            Object obj = detail.get("create_time");
            if (null != obj) {
                detail.put("create_time", DateUtils.toString((Date) obj, "yyyy-MM-dd HH:mm:ss"));
            }
            Object bankAccountNo = detail.get("bank_account_no");
            if (null != bankAccountNo && StringUtils.isNotBlank(bankAccountNo.toString())) {
                detail.put("bank_account_no", processBankNoRemain4Numbers(bankAccountNo.toString()));
            }

        } catch (Throwable e) {
            LOGGER.error("查询下级自动提现规则异常,参数为={}" + JSONUtils.toJsonString(detail) + ",异常为={}", e);
        }
        return detail;
    }


    private Map<String, Object> convertBindCardList(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<>();
        }
        try {
            Object obj = detail.get("create_time");
            if (null != obj) {
                detail.put("create_time", DateUtils.toString((Date) obj, "yyyy-MM-dd HH:mm:ss"));
            }
            Object accountNo = detail.get("account_no");
            if (null != accountNo && StringUtils.isNotBlank(accountNo.toString())) {
                detail.put("account_no", HiddenCodeUtils.hiddenBankCardNo(AESUtils.decryptWithBase64(accountNo.toString())));
                detail.put("short_account_no", processBankNoRemain4Numbers(accountNo.toString()));
            }
            Object accountName = detail.get("account_name");
            if (null != accountName && StringUtils.isNotBlank(accountName.toString())) {
                detail.put("account_name", AESUtils.decryptWithBase64(accountName.toString()));
            }
            Object bindId = detail.get("bind_id");
            if (null != bindId && StringUtils.isNotBlank(bindId.toString())) {
                detail.put("bind_id", bindId.toString());
            }
        } catch (Throwable e) {
            LOGGER.error("查询下级绑卡列表异常,参数为={}" + JSONUtils.toJsonString(detail) + ",异常为={}", e);
        }
        return detail;
    }

}
