package com.yeepay.g3.app.account.pay.mboss.convertor;

import com.google.common.collect.Lists;
import com.yeepay.g3.app.account.pay.mboss.dto.app.req.PaymentReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.app.response.PaymentAggregateResponseDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.app.response.PaymentDetailResponseDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.app.response.PaymentPageDetailResponseDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.RemitStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.utils.AESUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DateUtil;
import com.yeepay.g3.facade.bank.management.facade.dto.HeadBankDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitOrderQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitOrderStatusEnum;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 16:11
 */
public class PaymentConvertor {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentConvertor.class);

    /**
     * 转换查询参数
     *
     * @param param
     * @return
     */
    public static Map<String, Object> toQueryMap(PaymentReqDTO param) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();
        String status = param.getStatus();
        if (StringUtils.isNotBlank(status)) {
            if (status.equals(RemitStatusEnum.REQUEST_ACCEPT.name())) {
                status = RemitStatusEnum.REQUEST_ACCEPT.name() + "," + RemitStatusEnum.REQUEST_RECEIVE.name();
                queryMap.put("status", status);
            } else if ("REVERSED".equals(param.getStatus())) {
                queryMap.put("status", "SUCCESS");
                queryMap.put("reversed", "1");
            } else {
                queryMap.put("status", status);
                queryMap.put("reversednull", "0");
            }
        }
        queryMap.put("customerNumber", param.getCustomerNumber());
        queryMap.put("createStartDate", param.getCreateStartDate());
        queryMap.put("createEndDate", param.getCreateEndDate());
        queryMap.put("notFirstProductCode", "PAYMENT_SUPPLIER");
        return queryMap;
    }

    public static QueryParam toQueryParam(PaymentReqDTO param, Map<String, Object> queryMap) {
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        Integer startIndex = (param.getPageNo() - 1) * param.getPageSize() + 1;
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(param.getPageSize());
        queryParam.setDoSum(true);
        return queryParam;
    }



    /**
     * 获取转换结果list
     *
     * @param queryResult 查询结果
     * @return 结果list
     */
    public static List<PaymentPageDetailResponseDTO> toResponseDtoList(QueryResult queryResult) {
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        List<PaymentPageDetailResponseDTO> allList = Lists.newArrayList();
        for (Map<String, Object> map : queryResult.getData()) {
            PaymentPageDetailResponseDTO responseDTO = new PaymentPageDetailResponseDTO();
            assemblyData(map, responseDTO);
            allList.add(responseDTO);
        }
        return allList;
    }

    /**
     * 分页查询数据异构
     *
     * @param map
     * @param responseDTO
     */
    private static void assemblyData(Map<String, Object> map, PaymentPageDetailResponseDTO responseDTO) {
        if (MapUtils.isEmpty(map)) {
            return;
        }
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            //订单金额
            if (Objects.nonNull(map.get("order_amount"))) {
                responseDTO.setOrderAmount(nf.format(new BigDecimal(map.get("order_amount").toString())));
            }

            //下单时间
            Object createTime = map.get("create_time");
            if (Objects.nonNull(createTime)) {
                if (createTime instanceof String) {
                    String str = String.valueOf(createTime);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            responseDTO.setCreateTime(smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            responseDTO.setCreateTime(smf.format(smf.parse(str)));
                        }
                    }
                } else if (createTime instanceof Timestamp) {
                    responseDTO.setCreateTime(DateUtils.toSqlTimestampString((Timestamp) createTime, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

            //收款方名字
            Object name = map.get("receiver_account_name");
            if (Objects.nonNull(name)) {
                String accountName = AESUtils.decryptWithBase64(name.toString());
                responseDTO.setReceiverAccountName(accountName);
            }

            //订单状态
            String orderStatus = map.get("status").toString();
            if (orderStatus.equals(RemitStatusEnum.REQUEST_ACCEPT.name()) || orderStatus.equals(RemitStatusEnum.REQUEST_RECEIVE.name())) {
                responseDTO.setStatus(RemitStatusEnum.REQUEST_ACCEPT.name());
            } else {
                String reversed = map.get("reversed").toString();
                if (orderStatus.equals(RemitStatusEnum.SUCCESS.name()) && reversed.equals("1")) {
                    responseDTO.setStatus(RemitStatusEnum.REVERSED.name());
                } else {
                    responseDTO.setStatus(RemitStatusEnum.valueOf(orderStatus).name());
                }
            }

            //订单id
            String orderId = Objects.isNull(map.get("order_no")) ? "" : String.valueOf(map.get("order_no"));
            responseDTO.setOrderId(orderId);
        } catch (Exception e) {
            LOGGER.error("查询付款分页数据,异构数据异常 the errorMsg=[{}]", e);
        }
    }

    public static PaymentAggregateResponseDTO toAggregateResponseDTO(List<Map<String, Object>> withOrderListSum){
        PaymentAggregateResponseDTO aggregateResponseDTO = new PaymentAggregateResponseDTO();
        if (CollectionUtils.isEmpty(withOrderListSum)) {
            aggregateResponseDTO.setSumAmount("0.00");
            aggregateResponseDTO.setSumFee("0.00");
            aggregateResponseDTO.setTotalCount("0");
            return aggregateResponseDTO;
        }
        Map<String, Object> sumResult = withOrderListSum.get(0);
        if (MapUtils.isEmpty(sumResult)) {
            aggregateResponseDTO.setSumAmount("0.00");
            aggregateResponseDTO.setSumFee("0.00");
            aggregateResponseDTO.setTotalCount("0");
            return aggregateResponseDTO;
        }
        String sumAmount = Objects.isNull(sumResult.get("sum_amount")) ? "0.00" : sumResult.get("sum_amount").toString();
        String sumFee = Objects.isNull(sumResult.get("sum_fee")) ? "0.00" : sumResult.get("sum_fee").toString();
        String sumCount = Objects.isNull(sumResult.get("sum_count")) ? "0" : sumResult.get("sum_count").toString();

        aggregateResponseDTO.setSumAmount(new BigDecimal(sumAmount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总金额
        aggregateResponseDTO.setSumFee(new BigDecimal(sumFee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        aggregateResponseDTO.setTotalCount(sumCount);// 总数

        return aggregateResponseDTO;
    }

    /**
     * 查询付款明细
     * @param remitOrderQueryRespDTO
     * @param headBankDTO
     * @return
     */
    public static PaymentDetailResponseDTO toResponseDetail(RemitOrderQueryRespDTO remitOrderQueryRespDTO, HeadBankDTO headBankDTO){
        PaymentDetailResponseDTO param = new PaymentDetailResponseDTO();
        BeanUtils.copyProperties(remitOrderQueryRespDTO, param);
        RemitOrderStatusEnum status = remitOrderQueryRespDTO.getStatus();
        if (null != status) {
            RemitOrderStatusEnum remitOrderStatusEnum = RemitOrderStatusEnum.valueOf(status.name());
            param.setStatus(remitOrderStatusEnum.name());
            if (remitOrderStatusEnum.equals(RemitOrderStatusEnum.REQUEST_RECEIVE)) {
                param.setStatus(RemitStatusEnum.REQUEST_ACCEPT.name());
            }
        }

        if (!CheckUtils.isEmpty(remitOrderQueryRespDTO.getFinishTime())) {
            param.setFinishTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getFinishTime()));
        }

        param.setOrderAmount(remitOrderQueryRespDTO.getOrderAmount().toString());
        if (null != remitOrderQueryRespDTO.getFee()) {
            param.setFee(remitOrderQueryRespDTO.getFee().toString());
        }
        if (null!=remitOrderQueryRespDTO.getDebitAmount()){
            param.setDebitAmount(remitOrderQueryRespDTO.getDebitAmount().toString());
        }
        if (null!=remitOrderQueryRespDTO.getReceiveAmount()){
            param.setReceiveAmount(remitOrderQueryRespDTO.getReceiveAmount().toString());
        }

        String receiverAccountNo = remitOrderQueryRespDTO.getReceiverAccountNo();
        /* 付款记录 跳转 查询明细*/
        if (headBankDTO != null) {
            String bankName = headBankDTO.getBankName();
            if (StringUtils.isNotBlank(bankName) && StringUtils.isNotBlank(receiverAccountNo)) {
                param.setReceiverAccountNo(bankName + "(" + receiverAccountNo.substring(receiverAccountNo.length() - 4, receiverAccountNo.length()) + ")");
            }
        }
        param.setOrderInfo(remitOrderQueryRespDTO.getOrderInfo());
        param.setOrderTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getOrderTime()));
        RemitTypeEnum receiveType = remitOrderQueryRespDTO.getReceiveType();
        if (receiveType == RemitTypeEnum.REAL_TIME) {
            param.setReceiveType("实时到账");
        } else if (receiveType == RemitTypeEnum.TWO_HOUR) {
            param.setReceiveType("2小时到账");
        } else if (receiveType == RemitTypeEnum.NEXT_DAY) {
            param.setReceiveType("次日到账");
        }

        boolean isReversed = remitOrderQueryRespDTO.getIsReversed();
        if (isReversed) {
            param.setReverseTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getReverseTime()));
            param.setStatus(RemitStatusEnum.REVERSED.name());
        }

        param.setCapitalInfo(remitOrderQueryRespDTO.getCapitalInfo());

        if (StringUtils.isBlank(remitOrderQueryRespDTO.getTradeType())) {
            param.setTradeType(TradeTypeEnum.ENTERPRISE_PAYMENT.getDesc());
            return param;
        }
        if (TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType()) != null) {
            param.setTradeType(TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType()).getDesc());
        }
        return param;
    }
}
