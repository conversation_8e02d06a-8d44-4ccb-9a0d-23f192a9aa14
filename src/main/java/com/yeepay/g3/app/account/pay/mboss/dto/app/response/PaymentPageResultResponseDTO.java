package com.yeepay.g3.app.account.pay.mboss.dto.app.response;

import com.yeepay.g3.app.account.pay.mboss.dto.app.PageRespBase;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 19:24
 */
@Data
public class PaymentPageResultResponseDTO implements Serializable {
    /**
     * 汇总详情
     */
    private PaymentAggregateResponseDTO aggregateDto;
    /**
     * 分页list
     */
    private List<PaymentPageDetailResponseDTO> dataList;
    /**
     * 当前页号
     */
    private int pageNo;
    /**
     * 页大小
     */
    private int pageSize;
}
