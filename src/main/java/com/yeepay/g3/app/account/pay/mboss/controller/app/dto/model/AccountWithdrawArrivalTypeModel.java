package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;

import com.yeepay.g3.app.account.pay.mboss.dto.DicCodeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: Mr.yin
 * @date: 2024/7/25  14:37
 */
@ApiModel(description = "支持的账户提现到账时效信息，纯粹一点 不带余额")

public class AccountWithdrawArrivalTypeModel implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * {@link com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum}
     */
    @ApiModelProperty(value = "支持发起提现的账户类型")
    private String accountType;

    @ApiModelProperty(value = "支持发起提现的账户类型 中文描述")
    private String accountTypeDesc;

    @ApiModelProperty(value = "支持的到账时效List")
    private List<DicCodeDTO> arriveTypeModelList;

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountTypeDesc() {
        return accountTypeDesc;
    }

    public void setAccountTypeDesc(String accountTypeDesc) {
        this.accountTypeDesc = accountTypeDesc;
    }

    public List<DicCodeDTO> getArriveTypeModelList() {
        return arriveTypeModelList;
    }

    public void setArriveTypeModelList(List<DicCodeDTO> arriveTypeModelList) {
        this.arriveTypeModelList = arriveTypeModelList;
    }
}
