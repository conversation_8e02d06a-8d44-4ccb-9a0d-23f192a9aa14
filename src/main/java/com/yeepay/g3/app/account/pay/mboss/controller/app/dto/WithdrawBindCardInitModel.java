package com.yeepay.g3.app.account.pay.mboss.controller.app.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-07-24
 * @Time 14:22
 */
@ApiModel
public class WithdrawBindCardInitModel implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "商户签约名称")
    private String merchantName;

    @ApiModelProperty(value = "公司法人名称")
    private String corporationName;

    @ApiModelProperty(value = "签约类型")
    private String productType;

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCorporationName() {
        return corporationName;
    }

    public void setCorporationName(String corporationName) {
        this.corporationName = corporationName;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }
}
