package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 支付宝记账本拨入查询
 */
@ApiModel(description = "支付宝记账本拨入查询")
public class AliPayCapitalTransferQueryDTO implements Serializable {
    /**
     * 记账本id
     */
    private String channelBookId;
    /**
     * 记账本名称
     */
    private String channelBookName;
    /**
     * 易宝订单号
     */
    private String orderNo;

    /**
     * 渠道号
     *
     * @return
     */
    private String channelNo;

    /**
     * 支付开始时间
     *
     * @return
     */
    private Date createTimeStart;
    /**
     * 支付结束时间
     *
     * @return
     */
    private Date createTimeEnd;

    /**
     * 商编
     *
     * @return
     */
    private String merchantNo;
    private Integer pageSize=10;

    private Integer pageNo=1;

    private String  downType;

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public String getDownType() {
        return downType;
    }

    public void setDownType(String downType) {
        this.downType = downType;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public Date getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(Date createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public Date getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(Date createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getChannelBookName() {
        return channelBookName;
    }

    public void setChannelBookName(String channelBookName) {
        this.channelBookName = channelBookName;
    }
}
