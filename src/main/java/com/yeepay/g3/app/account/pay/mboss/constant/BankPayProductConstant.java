package com.yeepay.g3.app.account.pay.mboss.constant;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2023/10/27 14:34
 */

public class BankPayProductConstant {
    /**
     * 支持多渠道银行页面充值
     */
    public static final Map<String, String> bankPayProductMap = new HashMap<>();

    static {
        bankPayProductMap.put("XWB", "ENTERPRISE_RECHARGE_STANDARD,BANKACCOUNTPAY,XWB");
        bankPayProductMap.put("HXBXB_GATHER", "ENTERPRISE_RECHARGE_STANDARD,BANKACCOUNTPAY,HXBXB");
        bankPayProductMap.put("SUNINGBANK_MULTICHANNEL", "ENTERPRISE_RECHARGE_STANDARD,BANKACCOUNTPAY,SUNINGBANK");
        bankPayProductMap.put("WHLHB", "ENTERPRISE_RECHARGE_STANDARD,BANKACCOUNTPAY,WHLHB");
    }
}
