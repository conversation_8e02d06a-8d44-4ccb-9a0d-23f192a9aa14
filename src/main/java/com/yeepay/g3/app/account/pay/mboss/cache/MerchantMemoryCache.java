package com.yeepay.g3.app.account.pay.mboss.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


/**
 * @ClassName: MerchantMemoryCache
 * @Description: 客户信息本地缓存
 * <AUTHOR>
 * @Date 2022/11/9
 * @Version 1.0
 */
@Component
public class MerchantMemoryCache extends AbstractMemoryCache<String, Object> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantMemoryCache.class);

    @Override
    protected CacheBuilder<Object, Object> getCacheBuilder(CacheBuilder<Object, Object> cacheBuilder) {
        LOGGER.info("客户信息本地缓存启动");
        // 允许同时并发更新操作数
        cacheBuilder.concurrencyLevel(MemoryCacheConstant.CONCURRENCY_LEVEL_10)
                // 指定失效策略为在指定的时间段内没有更新被回收。
                .expireAfterWrite(MemoryCacheConstant.EXPIRE_TIME_MINUTES_10, TimeUnit.MINUTES)
                // 初始化大小
                .initialCapacity(MemoryCacheConstant.INITIAL_CAPACITY_128)
                .maximumSize(MemoryCacheConstant.MAX_KEY_SIZE_5000)
                .recordStats()
                .build();
        return cacheBuilder;
    }

    @Override
    protected CacheLoader<String, Object> getCacheLoader() {
        return new CacheLoader<String, Object>() {
            @Override
            public Object load(String key) throws Exception {
                return new Object();
            }
        };
    }
}
