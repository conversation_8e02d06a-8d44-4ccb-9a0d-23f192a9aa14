package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.SubAccountDto;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.Costants;
import com.yeepay.g3.app.account.pay.mboss.utils.UniformConfigUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.manage.dto.AccBalAndStatusQueryResultDTO;
import com.yeepay.g3.facade.account.manage.enums.AccountGtypeEnum;
import com.yeepay.g3.facade.account.manage.facade.AccountBalanceAndStatusQueryFacade;
import com.yeepay.g3.facade.account.manage.params.BalanceQueryParams;
import com.yeepay.g3.facade.account.special.enums.AccountTypeEnum;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationRespDTO;
import com.yeepay.g3.facade.merchant_platform.enumtype.BusinessType;
import com.yeepay.g3.facade.merchant_platform.facade.GroupBusinessRelationFacade;
import com.yeepay.g3.facade.merchant_platform.facade.MerchantFacade;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Description: 下级账户信息
 * <AUTHOR>
 * @date 2020-04-13 10:59
 */
@Controller
@RequestMapping("/subAccount")
public class SubAccountController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubAccountController.class);

    /**
     * 客户中心接口，查询上下级关系
     */
    private GroupBusinessRelationFacade groupBusinessRelationFacade = RemoteServiceFactory.getService(GroupBusinessRelationFacade.class);

    /**
     * 账务接口，查询账户
     */
    private AccountBalanceAndStatusQueryFacade accountBalanceAndStatusQueryFacade = RemoteServiceFactory.getService(AccountBalanceAndStatusQueryFacade.class);

    /**
     * 客户中心接口
     */
    private MerchantFacade merchantFacade = RemoteServiceFactory.getService(MerchantFacade.class);

    private static final String ACCOUNT_FUND = "ACCOUNT_FUND";

    @Resource
    private AccountInfoService accountInfoService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    /**
     * @Description: 下级账户信息查询页面
     * <AUTHOR>
     * @date 2020-04-13 11:15
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequiresPermissions(Costants.SUBMERCHANG_ACCOUNT)
    @RequestMapping
    @ResponseBody
    public ModelAndView forwardQuery() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("subGroup", getGroupBusinessRelationReqDTO());
        mav.setViewName("subAccount/queryAccount");
        return mav;
    }

    /**
     * @Description: 查询下级账户信息
     * <AUTHOR>
     * @date 2020-04-13 14:18
     * @param subMerchantNo : 子商户编号
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping("/queryAccount")
    @ResponseBody
    public ResponseMessage queryAccount(@RequestParam(value = "subMerchantNo", required = false) String subMerchantNo,
                                        @RequestParam(value = "pageSize", defaultValue = Costants.PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                        @RequestParam(value = "pageNo", defaultValue = Costants.PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage rmg = new ResponseMessage(ResponseMessage.Status.ERROR);
        try {
            List<SubAccountDto> subAccountDtos = new ArrayList<SubAccountDto>();
            String parentMerchantNo = getCurrentCustomerNumber();
            Boolean isZhongTaiMerchantNo = accountInfoService.isZhongTaiMerchantNo(parentMerchantNo);
            //如果不传子商户编号，就查所有的子商户
            if(CheckUtils.isEmpty(subMerchantNo)) {
                //查询所有子商户,及其账户信息
                List<GroupBusinessRelationDTO> groupBusinessRelationDTOS = getGroupBusinessRelationReqDTO();
                for(GroupBusinessRelationDTO groupBusinessRelationDTO : groupBusinessRelationDTOS) {
                    if(isZhongTaiMerchantNo){
                        LOGGER.info("集团账户商编：{} 是中台商户，子商户：{}查询也走中台",parentMerchantNo,groupBusinessRelationDTO.getCustomerNumber());
                        SubAccountDto subAccountDto = getSubUnionAccount(groupBusinessRelationDTO.getCustomerNumber(),groupBusinessRelationDTO.getFullname());
                        subAccountDtos.add(subAccountDto);
                    }else{
                        LOGGER.info("集团账户商编：{} 是非中台商户，子商户：{}查询也走非中台",parentMerchantNo,subMerchantNo);
                        AccBalAndStatusQueryResultDTO accBalAndStatusQueryResultDTO =
                                getAccountMsg(groupBusinessRelationDTO.getCustomerNumber(), Costants.ACCOUNT_FUND);
                        SubAccountDto subAccountDto = new SubAccountDto();
                        subAccountDto.setSubMerchantNo(accBalAndStatusQueryResultDTO.getCustomerNo());
                        subAccountDto.setSubMerchantName(groupBusinessRelationDTO.getFullname());
                        subAccountDto.setAvaibleBalance(accBalAndStatusQueryResultDTO.getAvaibleBalance().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                        subAccountDto.setFrozenFund(accBalAndStatusQueryResultDTO.getFrozenFund().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                        subAccountDto.setAccountStatus(accBalAndStatusQueryResultDTO.getAccountStatus().getDesc());
                        subAccountDtos.add(subAccountDto);
                    }
                }
                //按照金额大小排序
                Collections.sort(subAccountDtos);
                //查询分页
                int startIndex = (pageNo - 1) * pageSize;
                int endIndex;
                if(subAccountDtos.size() >= pageNo * pageSize) {
                    endIndex = pageNo * pageSize;
                    rmg.put("dataList", subAccountDtos.subList(startIndex, endIndex));
                }else {
                    rmg.put("dataList", subAccountDtos.subList(startIndex, subAccountDtos.size()));
                }

            }else {
                if(isZhongTaiMerchantNo){
                    LOGGER.info("集团账户商编：{} 是中台商户，子商户：{}查询也走中台",parentMerchantNo,subMerchantNo);
                    SubAccountDto subAccountDto = getSubUnionAccount(subMerchantNo,null);
                    subAccountDtos.add(subAccountDto);
                }else{
                    LOGGER.info("集团账户商编：{} 是非中台商户，子商户：{}查询也走非中台",parentMerchantNo,subMerchantNo);
                    AccBalAndStatusQueryResultDTO accBalAndStatusQueryResultDTO = getAccountMsg(subMerchantNo, Costants.ACCOUNT_FUND);
                    SubAccountDto subAccountDto = new SubAccountDto();
                    subAccountDto.setSubMerchantNo(accBalAndStatusQueryResultDTO.getCustomerNo());
                    subAccountDto.setSubMerchantName(getSignName(accBalAndStatusQueryResultDTO.getCustomerNo()));
                    subAccountDto.setAvaibleBalance(accBalAndStatusQueryResultDTO.getAvaibleBalance().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    subAccountDto.setFrozenFund(accBalAndStatusQueryResultDTO.getFrozenFund().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    subAccountDto.setAccountStatus(accBalAndStatusQueryResultDTO.getAccountStatus().getDesc());
                    subAccountDtos.add(subAccountDto);
                }
                rmg.put("dataList", subAccountDtos);
            }
            rmg.put("totalCount", subAccountDtos.size());
            return rmg;
        } catch(Throwable e) {
            LOGGER.error("商编{}查询子商户账户信息失败{}", getCurrentCustomerNumber(), e);
            rmg.setErrMsg("查询子商户账户信息失败");
            return rmg;
        }
    }

    /**
     * 获取子商户中台账户信息
     * @return
     */
    private SubAccountDto getSubUnionAccount(String merchantNo,String merchantName) {
        AccountInfoRespDTO accountInfoRespDTO = accountInfoService.getUnionAccountAvailableBalance(merchantNo);
        SubAccountDto subAccountDto = new SubAccountDto();
        subAccountDto.setSubMerchantNo(accountInfoRespDTO.getMerchantNo());
        if(StringUtils.isEmpty(merchantName)){
            merchantName = getSignName(merchantNo);
        }
        subAccountDto.setSubMerchantName(merchantName);
        subAccountDto.setAvaibleBalance(accountInfoRespDTO.getBalance().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        subAccountDto.setFrozenFund("0.00");/*中台账户没有冻结金额概念*/
        subAccountDto.setAccountStatus(accountInfoService.covertAccountStatus(accountInfoRespDTO.getAccountStatus()));
        subAccountDto.setUrl(UniformConfigUtils.getZhongTaiAccountHistoryURL());
        return subAccountDto;
    }

    /**
     * @Description: 查询账户信息
     * <AUTHOR>
     * @date 2020-04-13 14:49
     * @param merchantNo:
     * @param accountType:
     * @return com.yeepay.g3.facade.account.manage.dto.AccBalAndStatusQueryResultDTO
     */
    private AccBalAndStatusQueryResultDTO getAccountMsg(String merchantNo, String accountType) {
        BalanceQueryParams balanceQueryParams = new BalanceQueryParams();
        balanceQueryParams.setRequestor("account-pay");
        balanceQueryParams.setCustomerNo(merchantNo);
        balanceQueryParams.setAccGennerationType(AccountGtypeEnum.G3_ACCOUNT);
        balanceQueryParams.setAccountType(accountType);
        if (AccountTypeEnum.ACCOUNT_FUND.name().equals(accountType)) {
            balanceQueryParams.setAccGennerationType(AccountGtypeEnum.G2_ACCOUNT);
            balanceQueryParams.setAccountType("SVA");
        }
        List<AccBalAndStatusQueryResultDTO> queryResultDTOList;
        try {
            queryResultDTOList = accountBalanceAndStatusQueryFacade.queryAccountBalAndStatusByCustomerNoAndAccountType(balanceQueryParams);
        } catch (Throwable e) {
            LOGGER.error("查询账户发生异常 ", e);
            throw new RuntimeException("查询账户异常，商编是" + merchantNo);
        }
        if (null == queryResultDTOList || queryResultDTOList.size() <= 0) {
            throw new RuntimeException("查询账户异常，商编是" + merchantNo);
        }
        return queryResultDTOList.get(0);
    }

    /**
     * @Description: 查询下级机构
     * <AUTHOR>
     * @date 2020-04-13 11:32
     * @return java.lang.Object
     */
    private List<GroupBusinessRelationDTO> getGroupBusinessRelationReqDTO() {
        GroupBusinessRelationReqDTO dtoP = new GroupBusinessRelationReqDTO();
        dtoP.setBusinessType(BusinessType.GROUP_BUSINESS);
        dtoP.setParentNo(getCurrentCustomerNumber());
        dtoP.setSystem(Costants.SYSTEM_CODE);
        dtoP.setUid(UniformConfigUtils.getSystemUidForReqCusCenter());
        dtoP.setReqTime(new Date());
        dtoP.setCharSet("UTF-8");
        GroupBusinessRelationRespDTO dto;
        try {
            dto = groupBusinessRelationFacade.queryChildrenMerchant(dtoP);
            LOGGER.info("下级账户信息查询下级关系返回参数{}",JSON.toJSONString(dto));
        }catch (Throwable e) {
            LOGGER.error("查询下级机构异常 ", e);
            throw new RuntimeException("查询下级机构异常");
        }
        if (dto == null || dto.getGroupBusinessRelationDTOList().isEmpty()) {
            throw new RuntimeException("查询下级机构异常");
        }
        return dto.getGroupBusinessRelationDTOList();
    }

    /**
     * 获取商户名称
     */
    private String getSignName(String merchantNo) {
        return merchantRemoteService.getMerchantName(merchantNo);
    }
}
