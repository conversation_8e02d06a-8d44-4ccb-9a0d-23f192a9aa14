package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.account.pay.mboss.dto.TransferResponseMessage;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.handler.RemoteFacadeProxyFactory;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.pay.dto.TransferResultDTO;
import com.yeepay.g3.facade.account.pay.enums.TransferStatus;
import com.yeepay.g3.facade.account.pay.enums.TransferTypeEnum;
import com.yeepay.g3.facade.account.pay.enums.TransferWayEnum;
import com.yeepay.g3.facade.account.pay.facade.AccountPayTransferFacade;
import com.yeepay.g3.facade.account.pay.params.TransferRequestParam;
import com.yeepay.g3.facade.account.pay.params.TransferResendSmsParam;
import com.yeepay.g3.facade.mp.dto.UserDTO;
import com.yeepay.g3.facade.mp.facade.CusAuditConfigFacade;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.utils.common.CommonUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Controller
@RequestMapping("/boss/accountpay/transfer")
public class TransferController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(TransferController.class);
	private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);
	private AccountPayTransferFacade accountPayTransferFacade = RemoteServiceFactory.getService(AccountPayTransferFacade.class);
//	private CustomerQueryFacade customerQueryFacade = RemoteServiceFactory.getService(CustomerQueryFacade.class);
    private CusAuditConfigFacade cusAuditConfigFacade = RemoteFacadeProxyFactory.getService(CusAuditConfigFacade.class);

	
	
	protected final DataFormater dataFormater = new DataFormater();
	
	 // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认pageNo
    private static final String PAGE_NO_DEFAULT_VAL = "1";
    
    private static final String QUERY_PERMISSION_CONSTANT = "1002";

    private static final Long TRANSFER_FUNCTION_ID = 70000013L;

    // 金额格式
    private static final String AMOUT_FORMAT = "^([1-9]\\d*\\.\\d*|0\\.\\d+|[1-9]\\d*|0)$";

    // 产品类型集合
    private static final Map<String, List<String>> PRODUCT_TYPE_MAP = Maps.newHashMap();
    //状态集合
    private static final Map<String, List<String>> STATUS_MAP = Maps.newHashMap();
    //剩余可退款金额展示为'-'
    private static final List<String> NOT_SHOW_SURPLUS_REFUND_AMOUNT = Arrays.asList(new String[]{"INIT", "WAIT_PAY", "CLOSE", "REJECT", "TIME_OUT", "FULLY_PAY", "CS_ACCEPT", "CS_SUCCESS", "REPEAL_ACCEPT", "REPEALED"});

    // 网银历史库时间范围配置
    //private List<String> hisDsList = ConfigureSetting.getHisDataSourceRange();
    

    /**
     * 查询账户支付请求订单
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/index")
    public ModelAndView queryPayOrder(HttpServletRequest request, HttpServletResponse response){
    	logger.info("refundquerySuccess");

        ModelAndView mav = new ModelAndView();
        mav.setViewName("transfer/transfer");
        return mav;
    }
    
    /**
     * 查询账户支付请求订单
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/queryS0")
    public ModelAndView queryPayOrderS0(HttpServletRequest request, HttpServletResponse response){
    	logger.info("refundquerySuccess");

        ModelAndView mav = new ModelAndView();
        //String orderString = request.getParameter("orderNo");
        mav.setViewName("transfer/queryTransferS0");
        //mav.addObject("orderNo",orderString);
        return mav;
    }



    
    /**
     * 订单列表查询(ajax)
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/transfer")
    @ResponseBody
    public TransferResponseMessage transfer(HttpServletRequest request, HttpServletResponse response) {
        TransferResponseMessage resMsg = new TransferResponseMessage("success");
		   try {

				String transferType = request.getParameter("transferType");
				String creditCustomerNo = request.getParameter("debitCustomerNo");
				String amount = request.getParameter("amount");
				String transferWay = request.getParameter("transferWay");
				String passWord = request.getParameter("passWord");
				String usage = request.getParameter("usage");
				String remark = request.getParameter("remark");

				//交易密码需要解密
				 passWord = BACRsaUtil.privateDecrypt(passWord, ConfigUtils.getPrivateKey());
				TransferRequestParam param2 = new TransferRequestParam();
				if(transferType.equals("OUTER")){
					param2.setTransferType(TransferTypeEnum.OUTER);

				}else if(transferType.equals("INNER")){
					param2.setTransferType(TransferTypeEnum.INNER);

				}
				param2.setCreditCustomerNo(creditCustomerNo);
				param2.setAmount(new BigDecimal(amount));
				if(transferWay.equals("S0")){
					param2.setTransferWay(TransferWayEnum.S0);

				}else if(transferWay.toString().equals("T1")){
					param2.setTransferWay(TransferWayEnum.T1);

				}
				param2.setDebitCustomerPwd(passWord);
				param2.setUsage(usage);
				param2.setRemark(remark);
				//param2.setCreateTime(new Date());
				String merchant = getCurrentCustomerNumber();

				param2.setDebitCustomerNo(merchant);
				param2.setDebitCustomerLoginName(((ShiroUser)SecurityUtils.getSubject().getPrincipal()).getLoginName());
				param2.setRequestSys("MP");
				param2.setRequestNo(CommonUtils.getUUID());
				param2.setOrderNo(param2.getRequestNo());
				param2.setBasicProductCode("3011001004002");
				param2.setSalesProductCode("3011001004002A");
				param2.setSmsVerify(true);
				param2.setStatus(TransferStatus.INIT);
				// 判断是否迁移中台 如果迁移中台了 就拦截
				logger.info("转账 param={}", ToStringBuilder.reflectionToString(param2));
				Map<String, String> map = ConfigUtils.getTransferMgWhiteMerchantNoMap();
				if(map.keySet().contains(merchant)){
					// 拦截
					resMsg.setStatus(TransferResponseMessage.Status.ERROR);
					resMsg.setErrMsg("服务已升级，请使用新的转账页面发起交易");
				}else{
					TransferResultDTO dto = accountPayTransferFacade.transfer(param2);
					resMsg.setInnerOrderNo(dto.getInnerOrderNo());
					resMsg.setPhone(dto.getMobile());
					resMsg.setInnerOrderNo(dto.getInnerOrderNo());
					resMsg.setErrMsg(dto.getErrorMsg());
				}
		} catch (Exception e) {
			resMsg.setStatus(TransferResponseMessage.Status.ERROR);
			resMsg.setErrMsg("转账失败");
			return resMsg;
		}
       return resMsg;
    }
    
    
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/resendsmscode")
    @ResponseBody
    public TransferResponseMessage resendsmscode(HttpServletRequest request, HttpServletResponse response,
    		QueryInputParam param1,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        logger.info("转账 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param1), pageSize, pageNo);
       
        TransferResponseMessage resMsg = new TransferResponseMessage("success");

       try {
		
		    String transferType = request.getParameter("transferType");
		    String creditCustomerNo = request.getParameter("debitCustomerNo");
		    String amount = request.getParameter("amount");
		    String transferWay = request.getParameter("transferWay");
		    String passWord = request.getParameter("passWord");
		    String usage = request.getParameter("usage");
		    String remark = request.getParameter("remark");
		    String innerOrderNo = request.getParameter("innerOrderNo");
		    
		    TransferResendSmsParam param3 = new TransferResendSmsParam();
		    param3.setInnerOrderNo(innerOrderNo);
		    
		    
		    logger.info("转账 param={}", ToStringBuilder.reflectionToString(param3));
		   
		    TransferResultDTO dto = accountPayTransferFacade.reSendSmsCode(param3);
		    //resMsg.setErrMsg(dto.getInnerOrderNo());
		    resMsg.setInnerOrderNo(dto.getInnerOrderNo());
		    resMsg.setPhone(dto.getMobile());
		    resMsg.setInnerOrderNo(dto.getInnerOrderNo());
		    resMsg.setErrMsg(dto.getErrorMsg());
	} catch (Exception e) {
		e.printStackTrace();
		resMsg.setStatus(TransferResponseMessage.Status.ERROR);
		resMsg.setErrMsg("重发短验失败");
		return resMsg;
		
		
	}

        return resMsg;
    }
    
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/verify")
    @ResponseBody
    public TransferResponseMessage verify(HttpServletRequest request, HttpServletResponse response,
    		QueryInputParam param1,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        logger.info("转账 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param1), pageSize, pageNo);
       
        TransferResponseMessage resMsg = new TransferResponseMessage("success");

       try {

           String innerOrderNo = request.getParameter("innerOrderNo");
           String code = request.getParameter("code");
           //判断是否开通转账复核
           boolean flag =isTransferAudit(TRANSFER_FUNCTION_ID);
           TransferResultDTO dto = accountPayTransferFacade.verifySmsCode(innerOrderNo, code, flag);
           resMsg.setInnerOrderNo(dto.getInnerOrderNo());
           resMsg.setPhone(dto.getMobile());
           resMsg.setTransferStatus(dto.getStatus());
           resMsg.setInnerOrderNo(dto.getInnerOrderNo());
           resMsg.setErrMsg(dto.getErrorMsg());
	} catch (Exception e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
		resMsg.setStatus(TransferResponseMessage.Status.ERROR);
		resMsg.setErrMsg("转账失败");
		return resMsg;
		
		
	}

        return resMsg;
    }
    
    
   @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
   @RequestMapping(value = "/listsum")
    @ResponseBody
    public ResponseMessage queryHistoryOrderListSum(HttpServletRequest request, HttpServletResponse response,QueryInputParam param) {
    	 logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param));
         ResponseMessage resMsg = new ResponseMessage("success");
         param.setOrderNo(request.getParameter("orderNo"));
         param.setInnerOrderNo(request.getParameter("innerOrderNo"));
         param.setStartTime(request.getParameter("startTime"));
         param.setEndTime(request.getParameter("endTime"));
         param.setRequestSys(request.getParameter("requestSys"));
         param.setStatus(request.getParameter("status"));
         
         param.setDebitCustomerNo(request.getParameter("debitCustomerNo"));
         param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
         param.setTrxStartTime(request.getParameter("trxStartTime"));
         param.setTrxEndTime(request.getParameter("trxEndTime"));
         param.setTransferType(request.getParameter("transferType"));
         param.setAccountNo(request.getParameter("accountNo"));
         param.setRequestNo(request.getParameter("requestNo"));
         String merchant = getCurrentCustomerNumber();
         
         
         if(StringUtils.isNotBlank(request.getParameter("mark"))){
            param.setDebitCustomerNo(merchant);
         }

         logger.info("..."+ToStringBuilder.reflectionToString(param));

        List<Map<String, Object>> list = this.queryOrderListSum(param);


        // 如果查询结果不为空的话
        if (list != null && !list.isEmpty()) {
            Map<String, Object> sumResult = list.get(0);
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumFractionDigits(2);
            nf.setMaximumFractionDigits(2);

            /*resMsg.getData().put("sum_count", sumResult.get("SUM_COUNT").toString());// 总笔数
            resMsg.getData().put("sum_amount", nf.format(new BigDecimal(sumResult.get("SUM_AMOUNT").toString())));// 总金额
            resMsg.getData().put("sum_fee", nf.format(new BigDecimal(sumResult.get("SUM_FEE").toString())));// 总手续费
*/      
           try {
				resMsg.getData().put("sum_count", (Integer) mergeCount(list, "SUM_COUNT", "int")+"");// 总笔数
				resMsg.getData().put("sum_amount", nf.format((BigDecimal)mergeCount(list, "SUM_AMOUNT", "float")));// 总金额
				resMsg.getData().put("sum_fee", nf.format((BigDecimal)mergeCount(list, "SUM_FEE", "float")));// 总金额
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", 0.00);// 总金额
            resMsg.getData().put("sum_fee", 0.00);// 总手续费
        }

        return resMsg;
    }
    
   
    
    
    /**
     * 适配返回结果,例如FULLY_PAY之后的状态需要变更可退款金额为-等
     *
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail, int pageNo, int pageSize, String path) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }

        Map<String, Object> operation = new HashMap<String, Object>();
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);

        // 判断当前状态是否是支付成功,推送清算中心,成功和已退款,如果是则展示对应的可退款余额,否则展示'-'
        String operatorString = (String) detail.get("operator");
		if(StringUtils.isNotBlank(operatorString)){
	        UserDTO userDTO = userFacade.getUserByLoginName(operatorString);
	        if(userDTO != null)
	        detail.put("mobile", userDTO.getMobile());

		}
		
		 String sms_verify =  detail.get("sms_verify") + "";
	        if(com.yeepay.g3.utils.common.StringUtils.equals(sms_verify, "0")){
	        	sms_verify = "是";
	        }else if(com.yeepay.g3.utils.common.StringUtils.equals(sms_verify, "1")){
	        	sms_verify = "否";
	        }
	        detail.put("sms_verify", sms_verify); 
	        
	        String transfer_type = (String) detail.get("transfer_type");
	        if(com.yeepay.g3.utils.common.StringUtils.equals(transfer_type, "OUTER")){
	        	transfer_type = "外部转账";
	        }else {
	        	transfer_type = "内部转账";
	        }
	        detail.put("transfer_type", transfer_type); 
	      
	        String fee_type = (String) detail.get("fee_type");
	        if(com.yeepay.g3.utils.common.StringUtils.equals(fee_type, "CREDIT")){
	        	fee_type = "收款方";
	        }else {
	        	fee_type = "付款方";
	        }
	        detail.put("fee_type", fee_type);

        
        String status = (String) detail.get("status");
        if(com.yeepay.g3.utils.common.StringUtils.equals(status, "SEND")){
     	   status = "已发送";
        }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "VERIFIED")){
     	   status = "已验证";
        }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "PROCESSING")){
     	   status = "账务处理中";
        }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "SUCCESS")){
     	   status = "转账成功";
        }else{
     	   status = "转账失败";
        }
        detail.put("status", status); 
      
        detail.put("orgorderno","---");

        if (null != detail.get("amount")) {
            detail.put("amount", nf.format(new BigDecimal(detail.get("amount").toString())));
        }
        if (null != detail.get("fee")) {
            detail.put("fee", nf.format(new BigDecimal(detail.get("fee").toString())));
        }
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
          
            Object obj = detail.get("trx_time");
            if (null != obj) {
                    detail.put("trx_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            
             obj = detail.get("create_time");
            if (null != obj) {
                    detail.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            obj = detail.get("last_modify_time");
            if (null != obj) {
                    detail.put("last_modify_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            
            

        } catch (Exception e) {
            logger.error("这都能错..擦....", e);
        }
        
       /* String orderNo = (String) detail.get("orderNo");
        QueryInputParam param = new QueryInputParam();
        param.setOrderNo(orderNo);
		QueryResult result = this.queryOrderListHelp(param, pageNo, pageSize);
		List<Map<String, Object>> list = (List<Map<String, Object>>) result.getData();
		if(list != null && list.size() > 0){
			Map<String, Object> map=list.get(0);
			detail.put("innerOrderNo", map.get("innerOrderNo")+"");
		}*/
		

		return operation;

    }
   public void exportExcelWithData(HttpServletResponse response, HttpServletRequest request, ExportExcelParam excelParam,List<Map<String, Object>> data)
			throws IOException {
		DataExportUtils dataEcportUtil = new DataExportUtils();
		dataEcportUtil.setTextResource(textResource);
		dataEcportUtil.setMessageFormater(messageFormater);
		dataEcportUtil.setDataFormater(dataFormater);
		dataEcportUtil.exportExcelWithData(response, request, excelParam,data);
	}
  
   /**
    * 历史交易入参绑定dto
    */
   @SuppressWarnings("unused")
   @Data
   @ToString
   @AllArgsConstructor
   @NoArgsConstructor
   private static class QueryInputParam {
	   public String getOrderNo() {
			return orderNo;
		}




		public void setOrderNo(String orderNo) {
			this.orderNo = orderNo;
		}




		public String getInnerOrderNo() {
			return innerOrderNo;
		}




		public void setInnerOrderNo(String innerOrderNo) {
			this.innerOrderNo = innerOrderNo;
		}




		public String getRequestSys() {
			return requestSys;
		}




		public void setRequestSys(String requestSys) {
			this.requestSys = requestSys;
		}




		public String getStatus() {
			return status;
		}




		public void setStatus(String status) {
			this.status = status;
		}




		public String getDebitCustomerNo() {
			return debitCustomerNo;
		}




		public void setDebitCustomerNo(String debitCustomerNo) {
			this.debitCustomerNo = debitCustomerNo;
		}




		public String getCreditCustomerNo() {
			return creditCustomerNo;
		}




		public void setCreditCustomerNo(String creditCustomerNo) {
			this.creditCustomerNo = creditCustomerNo;
		}




		public String getTransferType() {
			return transferType;
		}




		public void setTransferType(String transferType) {
			this.transferType = transferType;
		}




		public String getStartTime() {
			return startTime;
		}




		public void setStartTime(String startTime) {
			this.startTime = startTime;
		}




		public String getEndTime() {
			return endTime;
		}




		public void setEndTime(String endTime) {
			this.endTime = endTime;
		}




		public String getTrxStartTime() {
			return trxStartTime;
		}




		public void setTrxStartTime(String trxStartTime) {
			this.trxStartTime = trxStartTime;
		}




		public String getTrxEndTime() {
			return trxEndTime;
		}




		public void setTrxEndTime(String trxEndTime) {
			this.trxEndTime = trxEndTime;
		}




		public String getAccountNo() {
			return accountNo;
		}




		public void setAccountNo(String accountNo) {
			this.accountNo = accountNo;
		}




		public String getRequestNo() {
			return requestNo;
		}




		public void setRequestNo(String requestNo) {
			this.requestNo = requestNo;
		}




		private String orderNo;
	    private String innerOrderNo;
	    private String requestSys;

	    private String status;
	    private String debitCustomerNo;
	    private String creditCustomerNo;
	    private String transferType;
	    private String startTime;
	    private String endTime;
	    private String trxStartTime;
	    private String trxEndTime;
	    private String accountNo;
	    private String requestNo;
   

   }
   private QueryResult queryOrderList(QueryInputParam param, int pageNo, int pageSize) {
       QueryResult result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
       //queryMap.put("customerNumber", getCurrentCustomerNumber());

       


       Integer startIndex = (pageNo - 1) * pageSize + 1;

       QueryParam queryParam = new QueryParam();
       queryParam.setParams(queryMap);
       queryParam.setStartIndex(startIndex);
       queryParam.setMaxSize(pageSize);
       queryParam.setDoSum(true);

       logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
      result = queryService.query("queryTransferOrder", queryParam);

       return result;
   }
   private List<Map<String, Object>> queryOrderListSum(QueryInputParam param) {
	   List<Map<String, Object>> result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
       //queryMap.put("customerNumber", getCurrentCustomerNumber());

      // logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
       result = QueryServiceUtil.query("queryAccntPayService", "queryTransferOrderSum", queryMap);

       //result = (List<Map<String, Object>>) queryService.query("queryPayOrderSum", queryParam);

       return result;
   }
    Object mergeCount(List<Map<String, Object>> list, String key, String clazz){
    	if (clazz.equals("int")) {
    		int result = 0;
    		for(Map<String, Object> map:list ){
    			result +=Integer.parseInt((String) map.get(key));
    		}
    		 return result;
			
		}else{
			BigDecimal result = new BigDecimal("0.00").setScale(2, RoundingMode.DOWN);
    		for(Map<String, Object> map:list ){
    			result = result.add(new BigDecimal((String) map.get(key)).setScale(2, RoundingMode.DOWN));
    			//result +=Float.parseFloat((String) map.get(key));
    		}
    		 return result;
			
		}
	   
   }
    
    private QueryResult queryOrderListHelp(QueryInputParam param, int pageNo, int pageSize) {
        QueryResult result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        //queryMap.put("customerNumber", getCurrentCustomerNumber());

        


        Integer startIndex = (pageNo - 1) * pageSize + 1;

        QueryParam queryParam = new QueryParam();
        queryParam.setParams(queryMap);
        queryParam.setStartIndex(startIndex);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);

        logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

        QueryService queryService = (QueryService) QueryServiceUtil.getBean(
                "queryAccntPayService", QueryService.class);
       result = queryService.query("queryPayOrder", queryParam);

        return result;
    }

    private boolean isTransferAudit(Long functionId){
        Boolean flag = cusAuditConfigFacade.isAuditConfigSelected(getCurrentUser().getCustomerId(),functionId);
        return flag;
    }


   
   
}
