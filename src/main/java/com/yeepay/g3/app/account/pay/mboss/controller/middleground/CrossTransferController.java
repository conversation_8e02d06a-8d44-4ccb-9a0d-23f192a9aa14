package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.yeepay.g3.app.account.pay.mboss.dto.TransferResponseParam;
import com.yeepay.g3.app.account.pay.mboss.dto.cross.*;
import com.yeepay.g3.app.account.pay.mboss.model.CloudStorageFileBO;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.CrossTransferOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.transfer.dto.request.AdvanceCrossTransferReqDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.response.AdvanceCrossTransferOrderRespDTO;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.ReqSourceEnum;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.TransferCrossNotifyTypeEnum;
import com.yeepay.g3.facade.unionaccount.transfer.facade.TransferExpandFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;


@Controller
@RequestMapping("/cross-transfer")
public class CrossTransferController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrossTransferController.class);

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    private TransferExpandFacade transferExpandFacade = RemoteServiceFactory.getService(TransferExpandFacade.class);

    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;


    @RequestMapping(value = "/view", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView view(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("crossTransfer/index");
        return mav;
    }

    @RequestMapping(value = "/query", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView home(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("crossTransfer/query");
        return mav;
    }

    /**
     * 查询汇款账户余额
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/balance", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage balance(HttpServletRequest request) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        //当前商编
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[跨境汇款] [账户余额查询] 请求参数 currentCustomerNumber = {}", currentCustomerNumber);
        //查询资金账户余额
        BigDecimal balance = new BigDecimal(BigInteger.ZERO);
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(currentCustomerNumber, AccountTypeEnum.FUND_ACCOUNT);
        BigDecimal queryBalance = queryAccountResponseDto.getBalance();
        if (queryBalance != null) {
            balance = queryBalance;
        }
        resMsg.put("balance", balance);
        List<CrossAccountInfoDTO> crossAccountList = new ArrayList<>();
        List<AccountDTO> accountList = businessCheckRemoteService.queryAccountInfos(currentCustomerNumber);
        if (CollectionUtils.isNotEmpty(accountList)) {
            for (AccountDTO account : accountList) {
                if (AccountTypeEnum.CNH.name().equals(account.getAccountType())) {
                    CrossAccountInfoDTO crossAccount = new CrossAccountInfoDTO();
                    crossAccount.setAccountType(account.getAccountType());
                    crossAccount.setBalance(account.getBalance().setScale(2).toPlainString());
                    crossAccountList.add(crossAccount);
                }
            }
        }
        resMsg.put("accountInfoList", crossAccountList);
        LOGGER.info("[跨境汇款] [账户余额查询] 响应参数 currentCustomerNumber = {} , resMsg = {}", currentCustomerNumber, JSONUtils.toJsonString(resMsg));
        return resMsg;
    }


    /**
     * 确认转账
     *
     * @param requestDTO
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage confirm(@RequestBody CrossTransferConfirmRequestDTO requestDTO) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("[跨境汇款] [转账确认] 请求参数，requestDTO = {}", JSONUtils.toJsonString(requestDTO));
        ShiroUser currentUser = getCurrentUser();
        if (currentUser == null) {
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("登陆已过期，请重新登陆");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        try {
            Assert.isTrue(StringUtils.isNotEmpty(requestDTO.getAmount()), "金额不能为空");
            Assert.isTrue(StringUtils.isNotEmpty(requestDTO.getUsage()), "转账备注不能为空");
            Assert.isTrue(StringUtils.isNotEmpty(requestDTO.getToAccountType()), "转入账户不能为空");
            Assert.isTrue(StringUtils.isNotEmpty(requestDTO.getAuditFile()), "审核文件不能为空");
            Assert.isTrue(StringUtils.isNotEmpty(requestDTO.getTradePassword()), "密码不能为空");
            String password = BACRsaUtil.privateDecrypt(requestDTO.getTradePassword(), ConfigUtils.getPrivateKey());// 交易密码需要解密
            if (!userFacade.validateTradePassword(currentUser.getUserId(), password)) {
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("交易密码不正确");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            }
            //构建转账参数
            AdvanceCrossTransferReqDTO requestParam = getAdvanceCrossTransferReqDTO(requestDTO, currentUser);
            LOGGER.info("[跨境汇款] [转账确认] 远程调用请求 = {}", JSONUtils.toJsonString(requestParam));
            AdvanceCrossTransferOrderRespDTO responseDTO = transferExpandFacade.advanceCrossTransfer(requestParam);
            LOGGER.info("[跨境汇款] [转账确认] 远程调用响应 = {}", JSONUtils.toJsonString(responseDTO));
            TransferResponseParam response = new TransferResponseParam();
            if (responseDTO != null) {
                String returnCode = responseDTO.getReturnCode();
                if ("UA00000".equals(returnCode)) {
                    response.setTransferStatus(responseDTO.getTransferStatus().name());
                    response.setFinishTime(responseDTO.getFinishTime());
                    response.setOrderNo(responseDTO.getOrderNo());
                    response.setOrderAmount(responseDTO.getOrderAmount());
                    response.setReceiveAmount(responseDTO.getReceiveAmount());
                    response.setFee(responseDTO.getFee());
                    resMsg.put("transferResult", response);
                } else {
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg(responseDTO.getReturnMsg());
                }
            } else {
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("转账异常，请稍后重试");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        } catch (Exception e) {
            LOGGER.error("转账异常，e={}", e);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常，请稍后再试");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        LOGGER.info("[跨境汇款] [转账确认] 结果返回 resMsg = {}", JSONUtils.toJsonString(resMsg));
        return resMsg;
    }

    private AdvanceCrossTransferReqDTO getAdvanceCrossTransferReqDTO(CrossTransferConfirmRequestDTO requestDTO, ShiroUser currentUser) {
        AdvanceCrossTransferReqDTO requestParam = new AdvanceCrossTransferReqDTO();
        requestParam.setClientIp(NetUtils.getIpAddress());
        requestParam.setReqSource(ReqSourceEnum.MP);
        requestParam.setInitiateMerchantNo(currentUser.getCustomerNumber());
        requestParam.setFromMerchantNo(currentUser.getCustomerNumber());
        requestParam.setParentMerchantNo(currentUser.getCustomerNumber());
        requestParam.setOrderAmount(requestDTO.getAmount());
        requestParam.setUsage(requestDTO.getUsage());
        requestParam.setFromAccountType(AccountTypeEnum.FUND_ACCOUNT);
        requestParam.setToAccountType(AccountTypeEnum.valueOf(requestDTO.getToAccountType()));
        requestParam.setTargetCurrency(requestDTO.getToAccountType());
        requestParam.setAuditFile(requestDTO.getAuditFile());
        requestParam.setOperator(currentUser.getLoginName());
        Long requestNo = SnowflakeIdFactory.generateId();
        requestParam.setRequestNo("ZZ" + requestNo);
        String merchantName = merchantRemoteService.getMerchantName(currentUser.getCustomerNumber());
        requestParam.setFromMerchantName(merchantName);
        String saleProductCode = businessCheckRemoteService.queryMarketProduct(currentUser.getCustomerNumber());
        requestParam.setSalesProductCode(saleProductCode);
        requestParam.setTransferType(com.yeepay.g3.facade.unionaccount.transfer.enumtype.TransferTypeEnum.B2B);
        requestParam.setToMerchantNo(currentUser.getCustomerNumber());
        requestParam.setNotifyType(TransferCrossNotifyTypeEnum.SMS);
        requestParam.setNotifyUrl(currentUser.getBindedMobile());
        return requestParam;
    }

    /**
     * 跨境汇款订单列表查询
     *
     * @param requestDTO
     * @return
     */
    @RequestMapping(value = "/orderList")
    @ResponseBody
    public ResponseMessage orderList(@RequestBody CrossTransferOrderListQueryReqDTO requestDTO) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("[跨境汇款] [订单列表查询] 请求参数 requestDTO = {}", JSONUtils.toJsonString(requestDTO));
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            Assert.isTrue(StringUtils.isNotEmpty(requestDTO.getCreateEndDate()), "下单开始时间不能为空");
            Assert.isTrue(StringUtils.isNotEmpty(requestDTO.getCreateEndDate()), "下单结束时间不能为空");
        } catch (Exception e) {
            resMsg.setErrCode("9999");
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        }
        try {
            CrossTransferOrderParam param = new CrossTransferOrderParam();
            param.setRequestNo(requestDTO.getRequestNo());
            param.setStatus(CrossOrderConvertUtils.convertQueryStatus(requestDTO.getStatus()));
            param.setCreateStartDate(requestDTO.getCreateStartDate());
            param.setCreateEndDate(requestDTO.getCreateEndDate());
            param.setCustomerNumber(currentCustomerNumber);
            param.setPageSize(requestDTO.getPageSize());
            param.setPageNo(requestDTO.getPageNo());
            //查列表
            List<CrossTransferOrderDTO> orderList = this.queryTransferOrderList(param);
            if (CollectionUtils.isEmpty(orderList)) {
                resMsg.put("data", new CrossTransferOrderListQueryRespDTO());
                return resMsg;
            }
            //查汇总
            CrossTransferOrderListQueryRespDTO respDTO = this.queryTransferOrderListSum(param);
            respDTO.setOrderList(orderList);
            resMsg.put("data", respDTO);
            LOGGER.error("[跨境汇款] [订单列表查询] 响应结果 resMsg = ", JSONUtils.toJsonString(resMsg));
            return resMsg;
        } catch (Exception e) {
            LOGGER.error("[跨境汇款] [订单列表查询] 查询异常 cause by ", e);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("查询异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        }
    }


    @RequestMapping(value = "/orderListDownload")
    @ResponseBody
    public void orderListDownload(CrossTransferOrderDownloadReqDTO requestDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setHeader("Content-type", "text/html;charset=UTF-8");
        LOGGER.info("[跨境汇款] [订单列表下载] 请求参数 requestDTO = {}", JSONUtils.toJsonString(requestDTO));
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            CheckUtils.notEmpty(requestDTO.getFileType(), "fileType");
            CrossTransferOrderParam param = new CrossTransferOrderParam();
            param.setRequestNo(requestDTO.getRequestNo());
            param.setStatus(CrossOrderConvertUtils.convertQueryStatus(requestDTO.getStatus()));
            param.setCreateStartDate(requestDTO.getCreateStartDate());
            param.setCreateEndDate(requestDTO.getCreateEndDate());
            param.setFileType(requestDTO.getFileType());
            param.setCustomerNumber(currentCustomerNumber);
            String merchantName = merchantRemoteService.getMerchantName(currentCustomerNumber);
            param.setFromCustomerName(merchantName);
            StringBuilder desc = new StringBuilder();
            desc.append("跨境汇款记录查询,").append(requestDTO.getCreateStartDate()).append("至").append(requestDTO.getCreateEndDate()).append("数据");
            new CrossTransferOrderDownloadService(getCurrentUser(), param, desc.toString(), "转账订单查询-").download(request, response);
            LOGGER.info("[跨境汇款] [订单列表下载] 操作成功 ");
        } catch (Throwable ex) {
            LOGGER.error("[跨境汇款] [订单列表下载] 下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * 下载文件  跨境的云储存
     *
     * @throws Exception
     */
    @RequestMapping(value = "/downLoadCrossFile", method = RequestMethod.GET)
    @ResponseBody
    public void downLoadCrossFile(@RequestParam("orderNo") String orderNo, HttpServletResponse response) throws IOException {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        LOGGER.info("[跨境汇款] [订单明细文件下载] 请求单号 : ", orderNo);
        try {
            CrossTransferOrderDTO order = this.queryTransferOrder(orderNo);
            CloudStorageFileBO cloudStorageFileBO = KJFileUtils.download(order.getAuditFile());
            response.addHeader("Content-Disposition", "attachment;filename=" + order.getRequestNo() + ".xlsx");
            response.addHeader("Content-Length", String.valueOf(cloudStorageFileBO.getLength()));
            response.setContentType("application/octet-stream");
            ByteArrayOutputStream byteArrayOutputStream = cloudStorageFileBO.getByteArrayOutputStream();
            ServletOutputStream outputStream = response.getOutputStream();
            byteArrayOutputStream.writeTo(outputStream);
            byteArrayOutputStream.flush();
            outputStream.flush();
            LOGGER.info("[跨境汇款] [订单明细文件下载] 操作成功 ");
        } catch (Throwable ex) {
            LOGGER.error("[跨境汇款] [订单明细文件下载] 下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * 根据订单号查询跨境订单信息
     * @param orderNo
     * @return
     */
    private CrossTransferOrderDTO queryTransferOrder(String orderNo) {
        Map<String, Object> param = new HashMap<String, Object>(1);
        param.put("orderNo", orderNo);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("crossTransferOrderService", QueryService.class);
        Map<String, Object> result = queryService.queryUnique("queryCrossTransferOrder", param, true);
        return convertDataToDTO(result);

    }

    /**
     * @param param:
     * @return com.yeepay.g3.utils.query.QueryResult
     * @Description: 查询跨境汇款订单列表
     */
    private List<CrossTransferOrderDTO> queryTransferOrderList(CrossTransferOrderParam param) {
        //构造查询参数
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        Integer startIndex = (param.getPageNo() - 1) * param.getPageSize() + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(param.getPageSize());
        queryParam.setDoSum(false);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("crossTransferOrderService", QueryService.class);
        QueryResult queryResult = queryService.query("queryCrossTransferOrderList", queryParam);
        return convertOrderList(queryResult);
    }

    private List<CrossTransferOrderDTO> convertOrderList(QueryResult queryResult) {
        List<CrossTransferOrderDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(queryResult.getData())) {
            return list;
        }
        for (Map<String, Object> detail : queryResult.getData()) {
            CrossTransferOrderDTO order = convertDataToDTO(detail);
            list.add(order);
        }
        return list;
    }

    private CrossTransferOrderDTO convertDataToDTO(Map<String, Object> detail) {
        CrossTransferOrderDTO order = new CrossTransferOrderDTO();
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            //下单时间
            Object obj = detail.get("create_time");
            if (null != obj) {
                if (obj instanceof String) {
                    String str = String.valueOf(obj);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            order.setCreateTime(smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            order.setCreateTime(smf.format(smf.parse(str)));
                        }
                    }
                } else if (obj instanceof Timestamp) {
                    order.setCreateTime(DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                }
            }
            order.setRequestNo(detail.get("request_no").toString());
            order.setOrderNo(detail.get("order_no").toString());
            if (null != detail.get("to_account_type")) {
                order.setToAccountType(detail.get("to_account_type").toString());
            }
            if (null != detail.get("transfer_amount")) {
                order.setTransferAmount(formatBigDecimalToString(detail.get("transfer_amount").toString()));
            }
            if (null != detail.get("fee")) {
                order.setFeeAmount(formatBigDecimalToString(detail.get("fee").toString()));
            } else {
                order.setFeeAmount("-");
            }
            if (null != detail.get("status")) {
                order.setStatus(CrossOrderConvertUtils.convertResultStatus(detail.get("status").toString()));
            }
            if (null != detail.get("audit_file")) {
                order.setAuditFile(detail.get("audit_file").toString());
            }
            if (null != detail.get("return_message")) {
                order.setErrorMessage(detail.get("return_message").toString());
            }
        } catch (Exception e) {
            LOGGER.error("跨境汇款订单明细转换异常", e);
        }
        return order;
    }

    private CrossTransferOrderListQueryRespDTO queryTransferOrderListSum(CrossTransferOrderParam param) {
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        List<Map<String, Object>> sumResultList = QueryServiceUtil.query("crossTransferOrderService", "queryCrossTransferOrderListSum", queryMap);
        CrossTransferOrderListQueryRespDTO sumDTO = new CrossTransferOrderListQueryRespDTO();
        if (CollectionUtils.isEmpty(sumResultList)) {
            return sumDTO;
        }
        Map<String, Object> sumResult = sumResultList.get(0);
        sumDTO.setTotalNum(sumResult.get("sum_count").toString());
        sumDTO.setTotalTransferAmount(formatBigDecimalToString(sumResult.get("sum_amount").toString()));
        if (StringUtils.isNotEmpty((CharSequence) sumResult.get("sum_fee"))) {
            sumDTO.setTotalFeeAmount(formatBigDecimalToString(sumResult.get("sum_fee").toString()));
        }
        return sumDTO;
    }

    public String formatBigDecimalToString(String amount) {
        return new BigDecimal(amount).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
    }


}
