package com.yeepay.g3.app.account.pay.mboss.controller;

import com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DataFormater;
import com.yeepay.g3.app.account.pay.mboss.utils.QueryServiceUtil;
import com.yeepay.g3.app.account.pay.mboss.utils.SecurityUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.core.member.util.ConfigureUtil;
import com.yeepay.g3.facade.account.signDeduction.facade.SignDeductionFacade;
import com.yeepay.g3.facade.account.signDeduction.params.SignDeductionPactApplyParam;
import com.yeepay.g3.facade.account.special.enums.AccountTypeEnum;
import com.yeepay.g3.facade.bankinfo.service.BankInfoQueryFacade;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.facade.MerchantFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.trade.bankinterface.nocard.BankInterfaceNoCardFacade;
import com.yeepay.g3.facade.trade.bankinterface.nocard.result.CardBinResultDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang.builder.ToStringBuilder;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Controller
@RequestMapping("/sign/apply")
public class SignApplycontroller extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SignApplycontroller.class);

    private SignDeductionFacade signDeductionFacade = RemoteServiceFactory.getService(SignDeductionFacade.class);
    private MerchantFacade merchantFacade = RemoteServiceFactory.getService(MerchantFacade.class);
    private BankInfoQueryFacade bankInfoQueryFacade = RemoteServiceFactory.getService(BankInfoQueryFacade.class);


    private static final String SIGN_PERMISSION_CONSTANT = "********";

    @RequiresPermissions(SIGN_PERMISSION_CONSTANT)
    @RequestMapping("/list")
    public ModelAndView list() {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("signDeduction/signApplyList");
        return mv;
    }

    @RequiresPermissions(SIGN_PERMISSION_CONSTANT)
    @RequestMapping("/add")
    public ModelAndView add() {
        ModelAndView mv = new ModelAndView();
        mv.setViewName("signDeduction/signApplyAdd");
        return mv;
    }

    @RequiresPermissions(SIGN_PERMISSION_CONSTANT)
    @RequestMapping("/view")
    public ModelAndView view(String signDeductionPactNo) {
        MerchantRespDTO merchantRespDTO = super.getCurrentMerchant();
        logger.info("SignApplycontroller.view signDeductionPactNo :{}", signDeductionPactNo);
        if (StringUtils.isBlank(signDeductionPactNo)) {
            return null;
        }

        Map<String, Object> map = new HashMap<String, Object>(1);
        map.put("signDeductionPactNo", signDeductionPactNo);
        Map<String, Object> returnMap = this.queryOne(map, "getSignApplyByNo");
        if(!merchantRespDTO.getMerchantNo().equals(returnMap.get("sign_customer_no"))){
            return null;
        }
        //签约方
        returnMap.put("sign_customer_name", SecurityUtil.decrypt(String.valueOf(returnMap.get("sign_customer_name"))));
        //授权扣账户
        String[] accountTypes = String.valueOf(returnMap.get("account_type")).split(",");
        StringBuffer accounttypeName = new StringBuffer("");
        for (int i = 0; i < accountTypes.length; i++) {
            accounttypeName.append(AccountTypeEnum.getValue(accountTypes[i]));
            if (i != accountTypes.length - 1) {
                accounttypeName.append(" , ");
            }
        }
        returnMap.put("account_type", accounttypeName.toString());
        //授权账户
        returnMap.put("confer_customer_name", SecurityUtil.decrypt(String.valueOf(returnMap.get("confer_customer_name"))));
        //签约完成时间
        returnMap.put("sign_datetime", DataFormater.date2StringDatetime((Date) returnMap.get("sign_datetime")));
        //银行卡类型
        returnMap.put("bank_card_type", "PublicCash".equals(returnMap.get("bank_card_type")) ? "对公" : "对私");
        //账户名称
        returnMap.put("account_name", SecurityUtil.decrypt(String.valueOf(returnMap.get("account_name"))));
        //银行卡号
        returnMap.put("bank_card_no", DataFormater.maskBankcardNo(SecurityUtil.decrypt(String.valueOf(returnMap.get("bank_card_no")))));
        ModelAndView mv = new ModelAndView();
        mv.addObject("data", returnMap);
        mv.setViewName("signDeduction/signApplyView");
        return mv;
    }


    @RequiresPermissions(SIGN_PERMISSION_CONSTANT)
    @RequestMapping("/querySignApply")
    @ResponseBody
    public ResponseMessage querySignApply(HttpServletRequest request, HttpServletResponse response, QueryInputParam param,
                                          @RequestParam(value = "pageSize", defaultValue = "20") int pageSize,
                                          @RequestParam(value = "pageNo", defaultValue = "1") int pageNo) {
        logger.info("查询历史签约信息 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param), pageSize, pageNo);
        ShiroUser user = super.getCurrentUser();
        param.setSignCustomerNo(user.getCustomerNumber());
        param.setConferCustomerName(StringUtils.isBlank(param.getSignCustomerName()) ? "" : SecurityUtil.encrypt(param.getSignCustomerName()));
        param.setSignStatus(StringUtils.isBlank(param.getSignStatus()) ? "" : param.getSignStatus());
        ResponseMessage resMsg = new ResponseMessage("success");
        if ("end".equals(param.getTimeType())) {
            param.setSignDatetimeStart(param.getCreateStartDate());
            param.setSignDatetimeEnd(param.getCreateEndDate());
        } else {
            param.setSignApplyDatetimeEnd(param.getCreateEndDate());
            param.setSignApplyDatetimeStart(param.getCreateStartDate());
        }
        QueryResult result = this.queryList(param, pageNo, pageSize, "querySignApply");
        if (null == result || 0 == result.getData().size()) {
            resMsg.put("totalCount", "0");
            resMsg.put("pageNo", pageNo);
            resMsg.put("pageSize", pageSize);
            resMsg.put("dataList", result.getData());
            return resMsg;
        }
        for (Map map : result.getData()) {
            map.put("confer_customer_name", SecurityUtil.decrypt(String.valueOf(map.get("confer_customer_name"))));
            map.put("sign_apply_datetime", DataFormater.date2StringDatetime((Date) map.get("sign_apply_datetime")));
            map.put("sign_datetime", DataFormater.date2StringDatetime((Date) map.get("sign_datetime")));
            map.put("account_name", SecurityUtil.decrypt(String.valueOf( map.get("account_name"))));
            String[] accountTypes = String.valueOf(map.get("account_type")).split(",");
            StringBuffer accounttypeName = new StringBuffer("");
            for (int i = 0; i < accountTypes.length; i++) {
                accounttypeName.append(AccountTypeEnum.getValue(accountTypes[i]));
                if (i != accountTypes.length - 1) {
                    accounttypeName.append(" , ");
                }
            }
            map.put("account_type", accounttypeName.toString());
        }
        List<Map<String, Object>> resultCount = this.queryListCount(param, "querySignApplyCount");
        if (!CheckUtils.isEmpty(resultCount)) {
            resMsg.put("totalCount", resultCount.get(0).get("1"));
        }
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        resMsg.put("dataList", result.getData());
        return resMsg;
    }


    @RequiresPermissions(SIGN_PERMISSION_CONSTANT)
    @RequestMapping("/signApply")
    @ResponseBody
    public ResponseMessage signApply(HttpServletRequest request, HttpServletResponse response, QueryInputParam param) {
        logger.info("SignApplycontroller.signApply param is bankName:{},bankcardNo:{},accountType:{},accountName:{},conferCustomerNo:{}",
                param.getBankName(), param.getBankCardNo(), param.getAccountType(), param.getAccountName(), param.getConferCustomerNo());
        ResponseMessage responseMessage = new ResponseMessage("success");
       Map<String,String> mapParam =  param.isBlank();
        if ("false".equals(mapParam.get("isSuccess"))) {
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            responseMessage.setErrMsg(mapParam.get("message"));
            return responseMessage;
        }
        ShiroUser user = super.getCurrentUser();
        SignDeductionPactApplyParam signDeductionPactApplyParam = new SignDeductionPactApplyParam();
        signDeductionPactApplyParam.setSignCustomerNo(user.getCustomerNumber());
        signDeductionPactApplyParam.setConferCustomerNo(param.getConferCustomerNo());
        signDeductionPactApplyParam.setBankName(param.getBankName());
        signDeductionPactApplyParam.setBankCardNo(param.getBankCardNo());
        signDeductionPactApplyParam.setAccountType(param.getAccountType());
        signDeductionPactApplyParam.setAccountName(param.getAccountName());
        signDeductionPactApplyParam.setSignCustomerName(user.getRealName());
        try {
            Map<String, String> map = signDeductionFacade.applySign(signDeductionPactApplyParam);
            if ("true".equals(map.get("isSuccess"))) {
                responseMessage.setStatus(ResponseMessage.Status.SUCCESS);
                responseMessage.setErrMsg("签约成功");
            } else {
                responseMessage.setStatus(ResponseMessage.Status.ERROR);
                responseMessage.setErrMsg(map.get("message"));
            }
            return responseMessage;
        } catch (Throwable e) {
            logger.error(ExceptionUtils.getFullStackTrace(e));
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            responseMessage.setErrMsg("系统异常");
            return responseMessage;
        }
    }

//    @RequestMapping("/getCurrentUserByNo")
//    @ResponseBody
//    public ResponseMessage getCurrentUserByNo(String no) {
//        logger.info("查询商户信息 商编:{}", no);
//        ResponseMessage responseMessage = new ResponseMessage("success");
//
//        if (StringUtils.isEmpty(no)) {
//            responseMessage.setErrMsg("参数为空");
//            responseMessage.setStatus(ResponseMessage.Status.ERROR);
//            return responseMessage;
//        }
//        MerchantReqDTO merchantReqDTO = new MerchantReqDTO();
//        merchantReqDTO.setSystem("member-center");
//        merchantReqDTO.setUid(ConfigureUtil.getConfigureValue("MERCHANT_SYS_MEMBER_UID"));
//        merchantReqDTO.setReqTime(new Date());
//        merchantReqDTO.setCharSet("UTF-8");
//        merchantReqDTO.setMerchantNo(no);
//        MerchantRespDTO merchantRespDTO = merchantFacade.getMerchantUsingCache(merchantReqDTO);
//        if (null == merchantRespDTO || null == merchantRespDTO.getName()) {
//            responseMessage.setErrMsg("商户不存在");
//            responseMessage.setStatus(ResponseMessage.Status.ERROR);
//            return responseMessage;
//        } else {
//            return new ResponseMessage(merchantRespDTO.getName());
//        }
//    }

    @RequiresPermissions(SIGN_PERMISSION_CONSTANT)
    @RequestMapping("/getSessionUserName")
    @ResponseBody
    public ResponseMessage getSessionUser() {
        MerchantRespDTO merchantRespDTO = super.getCurrentMerchant();
        return new ResponseMessage(merchantRespDTO.getName());
    }


    @RequiresPermissions(SIGN_PERMISSION_CONSTANT)
    @RequestMapping("/getBankName")
    @ResponseBody
    public ResponseMessage getBankName() {
        ResponseMessage msg = new ResponseMessage("SUCCESS");
         Map<String, String> map =  bankInfoQueryFacade.queryAllHeadBank();
        msg.put("data", map);
        return msg;
    }


    private QueryResult queryList(QueryInputParam param, int pageNo, int pageSize, String queryKey) {
        QueryResult result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        QueryParam queryParam = new QueryParam();
        queryParam.setParams(queryMap);
        queryParam.setStartIndex(startIndex);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("transferQueryService", QueryService.class);
        result = queryService.query(queryKey, queryParam);
        return result;
    }

    private Map<String, Object> queryOne(Map<String, Object> param, String queryKey) {
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("transferQueryService", QueryService.class);
        Map<String, Object> map = queryService.queryUnique(queryKey, param, true);
        return map;
    }


    private List<Map<String, Object>> queryListCount(QueryInputParam param, String queryKey) {
        List<Map<String, Object>> result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        result = QueryServiceUtil.query("transferQueryService", queryKey, queryMap);
        return result;
    }


    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    private static class QueryInputParam {
        private String createStartDate;
        private String createEndDate;

        private String signDeductionPactNo;
        /**
         * 签约完成开始时间
         */
        private String signDatetimeStart;
        /**
         * 签约完成结束时间
         */
        private String signDatetimeEnd;
        /**
         * 签约申请开始时间
         */
        private String signApplyDatetimeStart;
        /**
         * 签约申请结束时间
         */
        private String signApplyDatetimeEnd;
        /**
         * 签约状态
         */
        private String signStatus;
        /**
         * 签约商户名称
         */
        private String signCustomerName;
        /**
         * 签约商户名称
         */
        private String signCustomerNo;

        private String timeType;
        /**
         * 扣款账户类型
         */
        private String accountType;
        /**
         * 银行卡号
         */
        private String bankCardNo;
        /**
         * 开户行
         */
        private String bankName;

        /**
         * 账户名称
         */
        private String accountName;

        /**
         * 授权商户编号
         */
        private String conferCustomerNo;
        /**
         * 授权商户编号
         */
        private String conferCustomerName;

        public String getSignCustomerNo() {
            return signCustomerNo;
        }

        public void setSignCustomerNo(String signCustomerNo) {
            this.signCustomerNo = signCustomerNo;
        }

        public String getConferCustomerName() {
            return conferCustomerName;
        }

        public void setConferCustomerName(String conferCustomerName) {
            this.conferCustomerName = conferCustomerName;
        }

        public String getSignDeductionPactNo() {
            return signDeductionPactNo;
        }

        public void setSignDeductionPactNo(String signDeductionPactNo) {
            this.signDeductionPactNo = signDeductionPactNo;
        }

        public String getConferCustomerNo() {
            return conferCustomerNo;
        }

        public void setConferCustomerNo(String conferCustomerNo) {
            this.conferCustomerNo = conferCustomerNo;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }

        public String getAccountType() {
            return accountType;
        }

        public void setAccountType(String accountType) {
            this.accountType = accountType;
        }

        public String getBankCardNo() {
            return bankCardNo;
        }

        public void setBankCardNo(String bankCardNo) {
            this.bankCardNo = bankCardNo;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getCreateStartDate() {
            return createStartDate;
        }

        public void setCreateStartDate(String createStartDate) {
            this.createStartDate = createStartDate;
        }

        public String getCreateEndDate() {
            return createEndDate;
        }

        public void setCreateEndDate(String createEndDate) {
            this.createEndDate = createEndDate;
        }

        public String getTimeType() {
            return timeType;
        }

        public void setTimeType(String timeType) {
            this.timeType = timeType;
        }

        public String getSignDatetimeStart() {
            return signDatetimeStart;
        }

        public void setSignDatetimeStart(String signDatetimeStart) {
            this.signDatetimeStart = signDatetimeStart;
        }

        public String getSignDatetimeEnd() {
            return signDatetimeEnd;
        }

        public void setSignDatetimeEnd(String signDatetimeEnd) {
            this.signDatetimeEnd = signDatetimeEnd;
        }

        public String getSignApplyDatetimeStart() {
            return signApplyDatetimeStart;
        }

        public void setSignApplyDatetimeStart(String signApplyDatetimeStart) {
            this.signApplyDatetimeStart = signApplyDatetimeStart;
        }

        public String getSignApplyDatetimeEnd() {
            return signApplyDatetimeEnd;
        }

        public void setSignApplyDatetimeEnd(String signApplyDatetimeEnd) {
            this.signApplyDatetimeEnd = signApplyDatetimeEnd;
        }

        public String getSignStatus() {
            return signStatus;
        }

        public void setSignStatus(String signStatus) {
            this.signStatus = signStatus;
        }

        public String getSignCustomerName() {
            return signCustomerName;
        }

        public void setSignCustomerName(String signCustomerName) {
            this.signCustomerName = signCustomerName;
        }

        public Map<String,String> isBlank() {
            Map returnData = new HashMap<String,String>(2);
            if (StringUtils.isBlank(conferCustomerNo)) {
                returnData.put("message","授权商户编号必填");
                returnData.put("isSuccess","false");
                return returnData;
            }
            if (StringUtils.isBlank(accountName)) {
                returnData.put("message","账户名称必填");
                returnData.put("isSuccess","false");
                return returnData;
            }
            if (StringUtils.isBlank(bankName)) {
                returnData.put("message","开户行必填");
                returnData.put("isSuccess","false");
                return returnData;
            }
            if (StringUtils.isBlank(bankCardNo)) {
                returnData.put("message","银行卡号必填");
                returnData.put("isSuccess","false");
                return returnData;
            }

            if (StringUtils.isBlank(accountType)) {
                returnData.put("message","授权扣款账户类型必填");
                returnData.put("isSuccess","false");
                return returnData;
            }

            returnData.put("isSuccess","true");
            return returnData;
        }

    }

    Object mergeCount(List<Map<String, Object>> list, String key, String clazz) {
        if (clazz.equals("int")) {
            int result = 0;
            for (Map<String, Object> map : list) {
                result += Integer.parseInt((String) map.get(key));
            }
            return result;

        } else {
            BigDecimal result = new BigDecimal("0.00").setScale(2, RoundingMode.DOWN);
            for (Map<String, Object> map : list) {
                result = result.add(new BigDecimal((String) map.get(key)).setScale(2, RoundingMode.DOWN));
                //result +=Float.parseFloat((String) map.get(key));
            }
            return result;

        }

    }

}
