package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.constant.RemitConstant;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.dto.req.RemitDownloadReq;
import com.yeepay.g3.app.account.pay.mboss.entity.RemitFileDetailEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.RemitFileTaskEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.RemitOrder;
import com.yeepay.g3.app.account.pay.mboss.enumtype.PlatformTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.RemitStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.FileStorageService;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.CapitalManageService;
import com.yeepay.g3.app.account.pay.mboss.service.CookieService;
import com.yeepay.g3.app.account.pay.mboss.service.RemitOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RemitElectronicReceiptDownloader;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RemitOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.utils.DateUtil;
import com.yeepay.g3.app.account.pay.mboss.validate.BacNewBatchRemitCheckBlank;
import com.yeepay.g3.app.account.pay.mboss.validate.StrategyContext;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.capital.manage.dto.req.QueryMatchingUseFundReqDTO;
import com.yeepay.g3.capital.manage.dto.resp.QueryMatchingUseFundRespDTO;
import com.yeepay.g3.facade.bac.dto.enumtype.NewRemitSendType;
import com.yeepay.g3.facade.bank.management.facade.SearchBankInfoFacade;
import com.yeepay.g3.facade.bank.management.facade.dto.HeadBankDTO;
import com.yeepay.g3.facade.bankinfo.service.BankInfoQueryFacade;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.BaseProductDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantProductQueryRespDTO;
import com.yeepay.g3.facade.mp.exception.ExceptionWrapper;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.mp.utils.StringUtil;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.BatchReceiptReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.CheckNeedAuditReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BatchReceiptRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.CheckNeedAuditRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.GatherTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.TradeTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.UserAuditBizTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.UserAuditRequestSourceTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.exception.UnionAccountManageException;
import com.yeepay.g3.facade.unionaccount.manage.facade.ReceiptFacade;
import com.yeepay.g3.facade.unionaccount.manage.facade.UserAuditConfigFacade;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.*;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitBatchCheckRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitBatchDetailRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitBatchRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitOrderQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitOrderStatusEnum;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.RemitTypeEnum;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.TaskStatus;
import com.yeepay.g3.facade.unionaccount.trade.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.facade.unionaccount.trade.facade.RemitFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.encrypt.Digest;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.smartcache.utils.SmartCacheUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.yeepay.g3.app.account.pay.mboss.constant.NewRemitConstant.PAGE_NO_DEFAULT_VAL;
import static com.yeepay.g3.app.account.pay.mboss.constant.NewRemitConstant.PAGE_SIZE_DEFAULT_VAL;
import static com.yeepay.g3.app.account.pay.mboss.utils.DateUtil.PATTERN_STANDARD19H;


/**
 * 批量发起代付
 */
@Controller
@RequestMapping(value = "/batchRemit")
public class BacNewBatchRemitController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(BacNewBatchRemitController.class);
    static final String[] headStr = {"订单号（非必填）","收款方银行账号（必填）", "收款方开户名（必填）", "开户银行（对公付款必填）", "银行账户类型（非必填）", "付款金额（必填）", "支行名称（非必填）", "银行附言（非必填）","订单备注（非必填）", "收款方开户行行号（同开户银行 二选一必填）"};
    private RemitFacade remitFacade = RemoteServiceFactory.getService(RemitFacade.class);
    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);
    private BankInfoQueryFacade bankInfoQueryFacade = RemoteServiceFactory.getService(BankInfoQueryFacade.class);
    private SearchBankInfoFacade searchBankInfoFacade = RemoteServiceFactory.getService(SearchBankInfoFacade.class);
    private ReceiptFacade receiptFacade = RemoteServiceFactory.getService(ReceiptFacade.class);
    private FileStorageService fileStorageService = new FileStorageService();
    private static final UserAuditConfigFacade userAuditConfigFacade =RemoteServiceFactory.getService(UserAuditConfigFacade.class);

    @Autowired
    private MerchantRemoteService merchantRemoteService;
    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private CookieService cookieService;

    @Autowired
    private CapitalManageService capitalManageService;

    @RequestMapping(value = "/init/WTJS")
    public ModelAndView initWTJSSubmitPage(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        ShiroUser user = super.getCurrentUser();
        String type = request.getParameter("tradeType");
        mav.addObject("hasAvailableStatus",true);
        Boolean hasRJTProduct =false;
        Boolean hasRemitProduct = false;


        String currentCustomerNumber = getCurrentCustomerNumber();
        if (user != null) {
            try {

                String markProductCode = businessCheckRemoteService.queryMarketProduct(currentCustomerNumber);
                //查询开通产品
                List<String> secondProductList = buildRemitProduct(markProductCode,businessCheckRemoteService);
                mav.addObject("sendType", secondProductList);
                if(!CollectionUtils.isEmpty(secondProductList)){
                    hasRemitProduct = true;
                }
                //判断日结通产品
                hasRJTProduct = checkOpenProduct(currentCustomerNumber, markProductCode, businessCheckRemoteService);

                //查询可用余额
                BigDecimal balance = null;
                AccountInfoRespDTO responseDto = getRemitBalance(user.getCustomerNumber());
                if (responseDto != null && "UA00000".equals(responseDto.getReturnCode())) {
                    String accountStatus = responseDto.getAccountStatus();
                    if(!("AVAILABLE".equals(accountStatus)||"FROZEN_CREDIT".equals(accountStatus))){
                        mav.addObject("hasAvailableStatus",false);
                    }else{
                        balance = responseDto.getBalance();
                    }

                }else {
                    balance = BigDecimal.ZERO;
                }
                mav.addObject("amount", NumberUtils.formateNum(null, balance));

                //专款账户的信息
                mav.addObject("specialAccountAvailableStatus", false);
                AccountInfoRespDTO specialAccountResp = getSpecialAccountBalance(user.getCustomerNumber(), markProductCode);
                if (specialAccountResp != null) {
                    mav.addObject("specialAccountAvailableStatus", true);
                    mav.addObject("specialAccountAmount", NumberUtils.formateNum(null, specialAccountResp.getBalance()));
                }

                mav.addObject("productType", "WTJS");
            } catch (Exception e) {
                mav.addObject("exception", "系统异常，请联系开发人员");
                mav.addObject("path", request.getRequestURL());
                mav.setViewName("common/error");
                return mav;
            }

        }
        //跳转产品未开通页面
        if(!hasRJTProduct && !hasRemitProduct){
            mav.setViewName("batchRemit/batchSendSubmit");
        }else{
            mav.setViewName("batchRemit/index");
            mav.addObject("hasRemitProduct",hasRemitProduct);
            mav.addObject("hasRJTProduct",hasRJTProduct);

        }

        logger.info("init wtjs batchRemit 返回值：" + JSONUtils.toJsonString(mav));
        return mav;
    }


    /**
     * 批量代付页面初始化加载请求
     * 与该接口逻辑相同 /initBatchRemitPage/{tradeType}
     * @param tradeType
     * @return
     */
    @RequestMapping("/batchRemitPage/{tradeType}")
    public ModelAndView batchRemitPage(@PathVariable String tradeType){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("batchRemit/rjtBatchSendSubmit");
        String currentCustomerNumber = getCurrentCustomerNumber();
        String markProductCode = businessCheckRemoteService.queryMarketProduct(currentCustomerNumber);
        boolean hasRemitProduct = false;
        boolean hasAvailableStatus = false;
        BigDecimal balance = null;
        AccountInfoRespDTO responseDto = getRemitBalance(currentCustomerNumber);
        if (responseDto != null && "UA00000".equals(responseDto.getReturnCode())) {
            String accountStatus = responseDto.getAccountStatus();
            if(!("AVAILABLE".equals(accountStatus)||"FROZEN_CREDIT".equals(accountStatus))){

            }else{
                hasAvailableStatus = true;
                balance = responseDto.getBalance();
            }

        }else {
            balance = BigDecimal.ZERO;
        }
        mav.addObject("amount", NumberUtils.formateNum(null, balance));

        mav.addObject("hasAvailableStatus",hasAvailableStatus);
        if (Costants.ENTERPRISE_PAYMENT.equals(tradeType)) {
            mav.setViewName("batchRemit/batchSendSubmit");
            List<String> list = buildRemitProduct(markProductCode, businessCheckRemoteService);
            if(!CollectionUtils.isEmpty(list)){
                hasRemitProduct=true;
                mav.addObject("sendType", list);
            }
            mav.addObject("hasRemitProduct",hasRemitProduct);
            mav.addObject("productType", "WTJS");
        }

        if (Costants.RJT.equals(tradeType)) {
            List<String> rjtProductList = new ArrayList<>();
            boolean hasRJTProduct = checkOpenProduct(currentCustomerNumber, markProductCode, businessCheckRemoteService);
            rjtProductList.add("URGENCY");
            mav.addObject("sendType", rjtProductList);
            mav.addObject("hasRJTProduct",hasRJTProduct);
            mav.addObject("productType", "RJT");
        }


        //专款账户的信息
        mav.addObject("specialAccountAvailableStatus", false);
        AccountInfoRespDTO specialAccountResp = getSpecialAccountBalance(currentCustomerNumber, markProductCode);
        if (specialAccountResp != null) {
            mav.addObject("specialAccountAvailableStatus", true);
            mav.addObject("specialAccountAmount", NumberUtils.formateNum(null, specialAccountResp.getBalance()));
        }

        return mav;
    }

    /**
     * 批量代付页面初始化加载请求
     * 这是前后端分离的接口
     * 与该接口逻辑相同 /batchRemitPage/{tradeType}
     * @param tradeType
     * @return
     */
    @RequestMapping("/initBatchRemitPage/{tradeType}")
    @ResponseBody
    public BaseRespDTO initBatchRemitPage(@PathVariable String tradeType){
        Map<String, Object> map = new HashMap<>();
        String currentCustomerNumber = getCurrentCustomerNumber();
        String markProductCode = businessCheckRemoteService.queryMarketProduct(currentCustomerNumber);
        boolean hasRemitProduct = false;
        boolean hasAvailableStatus = false;
        BigDecimal balance = null;
        AccountInfoRespDTO responseDto = getRemitBalance(currentCustomerNumber);
        if (responseDto != null && "UA00000".equals(responseDto.getReturnCode())) {
            String accountStatus = responseDto.getAccountStatus();
            if(!("AVAILABLE".equals(accountStatus)||"FROZEN_CREDIT".equals(accountStatus))){

            }else{
                hasAvailableStatus = true;
                balance = responseDto.getBalance();
            }
        }else {
            balance = BigDecimal.ZERO;
        }
        map.put("amount", NumberUtils.formateNum(null, balance));
        map.put("hasAvailableStatus",hasAvailableStatus);
        if (Costants.ENTERPRISE_PAYMENT.equals(tradeType)) {
            List<String> list = buildRemitProduct(markProductCode, businessCheckRemoteService);
            if(!CollectionUtils.isEmpty(list)){
                hasRemitProduct=true;
                map.put("sendType", list);
            }
            map.put("hasRemitProduct",hasRemitProduct);
            map.put("productType", "WTJS");
        }
        if (Costants.RJT.equals(tradeType)) {
            List<String> rjtProductList = new ArrayList<>();
            boolean hasRJTProduct = checkOpenProduct(currentCustomerNumber, markProductCode, businessCheckRemoteService);
            rjtProductList.add("URGENCY");
            map.put("sendType", rjtProductList);
            map.put("hasRJTProduct",hasRJTProduct);
            map.put("productType", "RJT");
        }
        //专款账户的信息
        map.put("specialAccountAvailableStatus", false);
        AccountInfoRespDTO specialAccountResp = getSpecialAccountBalance(currentCustomerNumber, markProductCode);
        if (specialAccountResp != null) {
            map.put("specialAccountAvailableStatus", true);
            map.put("specialAccountAmount", NumberUtils.formateNum(null, specialAccountResp.getBalance()));
        }
        return BaseRespDTO.success(map);
    }


    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/batchPayResult")
    public ModelAndView batchPayResultPage(HttpServletRequest request, @RequestParam("taskId") String taskId) {
        ModelAndView mav = new ModelAndView();
        mav.addObject("taskId", taskId);
        mav.setViewName("batchRemit/batchPayResult");
        return mav;
    }


    /**
     * 批量代付结果页面 初始化加载请求
     * 与该接口逻辑相同 /initFileResultPage
     * @param taskId
     * @return
     */
//    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/getFileResultPage")
    public ModelAndView getFileResultPage(HttpServletRequest request, @RequestParam("taskId") String taskId, @RequestParam("validResult") String validResult) {
        ModelAndView mav = new ModelAndView();
        //获取银行列表
        Map<String, String> bankMap = queryAllHeadBank();
        mav.addObject("bankMap", bankMap);
        RemitBatchInfoReqDTO reqDTO  = new RemitBatchInfoReqDTO();
        reqDTO.setTaskId(Long.valueOf(taskId));
        reqDTO.setMerchantNo(getCurrentCustomerNumber());
        RemitBatchRespDTO remitBatchRespDTO = remitFacade.queryBatchInfo(reqDTO);
        mav.addObject("batchNo", remitBatchRespDTO.getBatchNo());
        mav.addObject("validResult", validResult);

        String customerNumber = super.getCurrentCustomerNumber();
        if(StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            mav.addObject("customerSign", customerSign);
        }

        mav.setViewName("batchRemit/batchRemitQuery");
        return mav;
    }

    /**
     * 批量代付结果页面 初始化加载请求
     * 这是前后端分离的接口
     * 与该接口逻辑相同 /getFileResultPage
     * @param taskId
     * @return
     */
    @RequestMapping(value = "/initFileResultPage")
    @ResponseBody
    public BaseRespDTO initFileResultPage(HttpServletRequest request, @RequestParam("taskId") String taskId) {
        Map<String, Object> map = new HashMap<>();
        //获取银行列表
        Map<String, String> bankMap = queryAllHeadBank();
        map.put("bankMap", bankMap);
        RemitBatchInfoReqDTO reqDTO  = new RemitBatchInfoReqDTO();
        reqDTO.setTaskId(Long.valueOf(taskId));
        reqDTO.setMerchantNo(getCurrentCustomerNumber());
        RemitBatchRespDTO remitBatchRespDTO = remitFacade.queryBatchInfo(reqDTO);
        map.put("batchNo", remitBatchRespDTO.getBatchNo());
        String customerNumber = super.getCurrentCustomerNumber();
        if(StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            map.put("customerSign", customerSign);
        }
        return BaseRespDTO.success(map);
    }

    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/getFileResultPageByBatch")
    public ModelAndView getFileResultPage(HttpServletRequest request, @RequestParam("batchNo") String batchNo) {
        ModelAndView mav = new ModelAndView();
        Map<String, String> bankMap = queryAllHeadBank();
        mav.addObject("bankMap", bankMap);
        mav.addObject("batchNo", batchNo);
        mav.addObject("validResult", "");

        String customerNumber = super.getCurrentCustomerNumber();
        if(StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            mav.addObject("customerSign", customerSign);
        }

        mav.setViewName("batchRemit/batchRemitQuery");
        return mav;
    }


    @RequestMapping(value = "/ajaxBatchSendConfirm")
    @ResponseBody
    public String ajaxBatchSendConfirm(
            HttpServletResponse response,
            HttpServletRequest request,
            @RequestParam("file") MultipartFile file,
            @RequestParam("sendType") NewRemitSendType remitSendType,
            @RequestParam("batchNo") String batchNo,
            @RequestParam("totalCount") Integer totalCount,
            @RequestParam("productType") String productType,
            @RequestParam("totalAmount") String totalAmount,
            @RequestParam("tradeType") String tradeType
    ) {
        logger.info("预提交接口入参： sendType：" + remitSendType + "， batchNo：" + batchNo + "， totalCount：" + totalCount + ", totalAmount: " + totalAmount + "，productType：" + productType+",tradeType:"+tradeType);
        ResponseMsg responseMsg = new ResponseMsg();
        try {
            String redirectUrl = "/batchRemit/batchSendConfirm?token=";
            //ip地址获取
            String ipAddress = NetUtils.getRemoteIP(request);
            ShiroUser shiroUser = super.getCurrentUser();
            String customerNumber = super.getCurrentCustomerNumber();
            String token = customerNumber + System.currentTimeMillis();
            logger.info("预提交, merchantNo: {}, token: {}, ip: {}", customerNumber, token, ipAddress);
            String marketingProductCode = businessCheckRemoteService.queryMarketProduct(customerNumber);
            Map<String, Object> stringMap = preBatchSendConfirm(
                    file,
                    batchNo,
                    totalCount,
                    totalAmount,
                    shiroUser,
                    token,
                    remitSendType.name(),
                    productType.toUpperCase(),
                    marketingProductCode,
                    ipAddress,
                    tradeType
            );
            /*对比余额，本次输入金额*/
            try {
                BigDecimal balance = BigDecimal.ZERO;
                AccountInfoRespDTO remitBalance = getRemitBalance(shiroUser.getCustomerNumber());
                if (remitBalance != null && "UA00000".equals(remitBalance.getReturnCode())) {
                    balance = remitBalance.getBalance();
                }
                //专款账户和资金账户比较
                AccountInfoRespDTO specialAccountResp = getSpecialAccountBalance(shiroUser.getCustomerNumber(), marketingProductCode);
                if (specialAccountResp != null) {
                    BigDecimal specialAccountBalance = specialAccountResp.getBalance();
                    if (specialAccountBalance.compareTo(balance) > 0) {
                        balance = specialAccountBalance;
                    }
                }
                BigDecimal bigDecimal = new BigDecimal(totalAmount);
                if (bigDecimal.compareTo(balance) > 0) {
                    Map<String, String> hashMap = new HashMap<>();
                    hashMap.put("errorCode", ERROR_CODE.TOTAL_AMOUNT_LESS.getName());
                    hashMap.put("description",ERROR_CODE.TOTAL_AMOUNT_LESS.getValue());
                    stringMap.put("totalAmount", hashMap);
                }
            } catch (Exception e) {
                logger.error("对比可用余额，本次输入金额异常", e);
            }
            if (stringMap.size() > 0) {
                responseMsg.setStatus(ResponseMsg.Status.FAILED);
                responseMsg.setRetMsg(stringMap);
            } else {
                responseMsg.setStatus(ResponseMsg.Status.SUCCESS);
                responseMsg.setToken(token);
                responseMsg.setRedirectUrl(redirectUrl + token);
                responseMsg.setRetMsg(stringMap);
            }
        } catch (Exception e) {
            logger.error("系统异常，", e);
            responseMsg.setStatus(ResponseMsg.Status.FAILED);
            responseMsg.setSystemCode(ResponseMsg.SystemCode.SYS_ERROR.name());
        }
        logger.info("ajax异步批量提交 返回信息： " + JSONUtils.toJsonString(responseMsg));
        return JSONUtils.toJsonString(responseMsg);
    }

    /**
     * 批量代付页面 ajax校验文件结果
     * 与该接口逻辑相同 /ajaxBatchSendConfirmResult
     * @param token
     * @return
     */
    @RequestMapping(value = "/batchSendConfirm")
    public ModelAndView batchSendConfirm(@RequestParam("token") String token) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("batchRemit/batchSendConfirm");

        Map<String, String> hgetall = RedisUtils.hgetall(token);
        String totalCount = hgetall.get("totalCount");
        String totalAmount = hgetall.get("totalAmount");
        String validExceptionCount = hgetall.get("validExceptionCount");
        String validExceptionAmount = hgetall.get("validExceptionAmount");
        String validSuccessCount = hgetall.get("validSuccessCount");
        String validSuccessAmount = hgetall.get("validSuccessAmount");
        String batchNo = hgetall.get("batchNo");
        String isRepeated = hgetall.get("isRepeated");

        logger.info("校验数量："+JSON.toJSONString(hgetall));
        if(StringUtils.isEmpty(validSuccessAmount)){
            validSuccessAmount ="0";
        }
        if(StringUtils.isEmpty(validSuccessCount)){
            validSuccessCount="0";
        }
        if(StringUtils.isEmpty(validExceptionAmount)){
            validExceptionAmount="0";
        }
        if(StringUtils.isEmpty(validExceptionCount)){
            validExceptionCount="0";
        }
        Integer totalCount_ = Integer.parseInt(totalCount);
        Integer validExceptionCount_ = Integer.parseInt(validExceptionCount);

        modelAndView.addObject("totalCount", Integer.parseInt(totalCount==""?"0":totalCount));
        modelAndView.addObject("totalAmount", NumberUtils.formateNum(null, new BigDecimal(totalAmount)));
        modelAndView.addObject("validExceptionCount", Integer.parseInt(validExceptionCount));
        modelAndView.addObject("validExceptionAmount", NumberUtils.formateNum(null, new BigDecimal(validExceptionAmount)));
        modelAndView.addObject("validSuccessCount", Integer.parseInt(validSuccessCount));
        modelAndView.addObject("validSuccessAmount", NumberUtils.formateNum(null, new BigDecimal(validSuccessAmount)));

        modelAndView.addObject("batchNo", batchNo);
        modelAndView.addObject("isRepeated", isRepeated);
        modelAndView.addObject("token", token);
        String result = "";

        if (validExceptionCount_ == 0) {
            result = "全部校验成功";
        } else if (totalCount_.equals(validExceptionCount_)) {
            result = "全部校验异常";
        } else {
            result = "部分校验异常";
        }
        modelAndView.addObject("result", result);

        return modelAndView;

    }

    /**
     * 批量代付页面 ajax校验文件结果
     * 这是前后端分离的接口
     * 与该接口逻辑相同 /batchSendConfirm
     * @param token
     * @return
     */
    @RequestMapping(value = "/ajaxBatchSendConfirmResult")
    @ResponseBody
    public BaseRespDTO ajaxBatchSendConfirmResult(@RequestParam("token") String token) {
        Map<String, Object> map = new HashMap<>();
        Map<String, String> hgetall = RedisUtils.hgetall(token);
        String totalCount = hgetall.get("totalCount");
        String totalAmount = hgetall.get("totalAmount");
        String validExceptionCount = hgetall.get("validExceptionCount");
        String validExceptionAmount = hgetall.get("validExceptionAmount");
        String validSuccessCount = hgetall.get("validSuccessCount");
        String validSuccessAmount = hgetall.get("validSuccessAmount");
        String batchNo = hgetall.get("batchNo");
        String isRepeated = hgetall.get("isRepeated");

        logger.info("校验数量："+JSON.toJSONString(hgetall));
        if(StringUtils.isEmpty(validSuccessAmount)){
            validSuccessAmount ="0";
        }
        if(StringUtils.isEmpty(validSuccessCount)){
            validSuccessCount="0";
        }
        if(StringUtils.isEmpty(validExceptionAmount)){
            validExceptionAmount="0";
        }
        if(StringUtils.isEmpty(validExceptionCount)){
            validExceptionCount="0";
        }
        Integer totalCount_ = Integer.parseInt(totalCount);
        Integer validExceptionCount_ = Integer.parseInt(validExceptionCount);

        map.put("totalCount", Integer.parseInt(totalCount==""?"0":totalCount));
        map.put("totalAmount", NumberUtils.formateNum(null, new BigDecimal(totalAmount)));
        map.put("validExceptionCount", Integer.parseInt(validExceptionCount));
        map.put("validExceptionAmount", NumberUtils.formateNum(null, new BigDecimal(validExceptionAmount)));
        map.put("validSuccessCount", Integer.parseInt(validSuccessCount));
        map.put("validSuccessAmount", NumberUtils.formateNum(null, new BigDecimal(validSuccessAmount)));
        map.put("batchNo", batchNo);
        map.put("isRepeated", isRepeated);
        map.put("token", token);
        String result = "";
        String resultFlag = "";
        if (validExceptionCount_ == 0) {
            result = "全部校验成功";
            resultFlag = "SUCCESS";
        } else if (totalCount_.equals(validExceptionCount_)) {
            result = "全部校验异常";
            resultFlag = "ALL_ERROR";
        } else {
            result = "部分校验异常";
            resultFlag = "PART_ERROR";
        }
        map.put("result", result);
        map.put("resultFlag", resultFlag);
        return BaseRespDTO.success(map);
    }


    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/downBatchDetail")
    public void downBatchDetail(@RequestParam("token") String token, HttpServletRequest request, HttpServletResponse response) {

        logger.info("token：{}, 开始下载异常文件...", token);
        //根据token 下载云存储的文件
        if (StringUtils.isBlank(token)) {
            logger.error("下载接口 token 为空");
            return;
        }
        //下载
        String cloudName = token + ".xls";
        List<String> lists = RedisUtils.hmget(token, new String[]{"fileName"});
        String fileName = null;
        if (null != lists && lists.size() > 0) {
            String list = lists.get(0);
            String substring = list.substring(0, list.lastIndexOf("."));
            fileName = substring + "(异常原因)" + list.substring(list.lastIndexOf("."));
        }
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            inputStream = fileStorageService.downloadFileCloud(cloudName);
            response.setHeader("Content-disposition", "attachment; filename=" + new String(URLEncoder.encode(fileName, "utf-8")));
            response.setHeader("Content-Type", "application/octet-stream");
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[2048];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            inputStream.close();
            outputStream.flush();
            logger.info("下载成功， token：" + token);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("下载失败", e);
        }

    }

    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/batchSendSafetyVerify")
    public ModelAndView batchSendSafetyVerify(@RequestParam("token") String token) {
        ModelAndView view = new ModelAndView();
        view.setViewName("batchRemit/batchSendSafetyVerify");
        view.addObject("token", token);
        return view;

    }


    /**
     * 批量调用代付打款
     *
     * @param token
     * @param authCode
     * @param dealPassword
     * @param request
     * @return
     */
//    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/batchSendBac")
    @ResponseBody
    public ResponseMessage batchSendBac(HttpServletRequest request, @RequestParam("token") String token, @RequestParam("dealPassword") String dealPassword,
                                        @RequestParam("authCode") String authCode) {

        logger.info("验证打款提交参数：token:{}", token);
        ShiroUser user = super.getCurrentUser();
        logger.info("验证打款提交参数：customerNumber:{}", user.getCustomerNumber());
        //交易密码改为密文传输，需要解密
        String decryptPassWord = BACRsaUtil.privateDecrypt(dealPassword, ConfigUtils.getPrivateKey());
        //1.验证密码
        ResponseMessage responseMessage = new ResponseMessage(ResponseMsg.Status.SUCCESS);
        Long userId = user.getUserId();
        checkNotNull(userId);
        checkArgument(StringUtils.isNotBlank(dealPassword));
        checkArgument(StringUtils.isNotBlank(authCode));
        if (!userFacade.validateTradePassword(userId, decryptPassWord)) {
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg("交易密码不正确");
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            return responseMessage;
        }

        //短信验证
        try {
            SendSmsUtils.checkVaildFrequency(request, userId, authCode, RemitConstant.BATCH_REMIT_SMS_CODE_TYPE);
        } catch (ExceptionWrapper e) {
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg(e.getMessage());
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            return responseMessage;
        }

        //验证token
        Map<String, String> tokenValue = null;
        try {
            tokenValue = vaildToken(getCurrentUser().getLoginName(), token);
        } catch (Exception e) {
            logger.error("调用redis hgetall异常", e);
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
            return responseMessage;
        }

        RemitBatchReqDTO remitBatchReqDTO = new RemitBatchReqDTO();
        remitBatchReqDTO.setMerchantNo(getCurrentCustomerNumber());
        remitBatchReqDTO.setBatchNo(tokenValue.get("batchNo"));
        remitBatchReqDTO.setSource("mp");
        remitBatchReqDTO.setOperator(getCurrentUserSafe().getLoginName());
        try {
            RemitBatchRespDTO remitBatchRespDTO = remitFacade.batchInitiateRemitForMP(remitBatchReqDTO);
            if (remitBatchRespDTO != null) {
                String returnCode = remitBatchRespDTO.getReturnCode();
                if (!returnCode.equals(ErrorCode.SUCCESS)) {
                    logger.info("批量出款异常，bathNo={}", tokenValue.get("batchNo"));
                    responseMessage.setStatus(ResponseMessage.Status.ERROR);
                    responseMessage.setErrCode("9999");
                    responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
                    return responseMessage;
                }
            } else {
                logger.info("批量出款异常，bathNo={}", tokenValue.get("batchNo"));
                responseMessage.setStatus(ResponseMessage.Status.ERROR);
                responseMessage.setErrCode("9999");
                responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
                return responseMessage;
            }
        } catch (Throwable e) {
            logger.info("批量出款异常，bathNo={}", tokenValue.get("batchNo"));
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
            responseMessage.setErrCode("9999");
            responseMessage.setErrMsg("该出款批次已失效，请核实出资情况，避免重复出资风险！");
            return responseMessage;
        }
        boolean needAudit = false;
        try{
            CheckNeedAuditReqDTO reqDTO = new CheckNeedAuditReqDTO();
            reqDTO.setMerchantNo(user.getCustomerNumber());
            reqDTO.setAuditBizType(UserAuditBizTypeEnum.REMIT);
            reqDTO.setUserAuditRequestSourceType(UserAuditRequestSourceTypeEnum.SELF_SERVICE);
            logger.info("验证打款提交,查询复核请求参数:{}", JSONUtils.toJsonString(reqDTO));
            CheckNeedAuditRespDTO resp = userAuditConfigFacade.checkNeedAudit(reqDTO);
            logger.info("验证打款提交,查询复核返回参数:{}", JSONUtils.toJsonString(reqDTO));
            if("UA00000".equals(resp.getReturnCode())){
                needAudit = resp.getNeedAudit();
            }
        }catch (Exception ex){
            logger.error("获取商户是否需要复核异常！",ex);
        }
        responseMessage.put("needAudit",needAudit);
        responseMessage.put("batchNo",tokenValue.get("batchNo"));
        responseMessage.put("taskId", tokenValue.get("taskId"));
        logger.info("验证打款提交,返回参数:{}", JSONUtils.toJsonString(responseMessage));
        return responseMessage;
    }


    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/getFailResult")
    @ResponseBody
    public ResponseMessage getFailResult(HttpServletRequest request, @RequestParam("taskId") String taskId) {

        logger.info("查询批量打款任务结果： taskId：{}", taskId);
        ResponseMessage responseMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtil.isNotEmpty(taskId)) {

                RemitBatchInfoReqDTO remitBatchReqDto = new RemitBatchInfoReqDTO();
                remitBatchReqDto.setMerchantNo(getCurrentCustomerNumber());  //getCurrentCustomerNumber()
                remitBatchReqDto.setTaskId(Long.valueOf(taskId));
                RemitBatchRespDTO remitBatchRespDTO = remitFacade.queryBatchInfo(remitBatchReqDto);
                String returnCode = remitBatchRespDTO.getReturnCode();
                if(returnCode.equals(ErrorCode.SUCCESS)){
                    TaskStatus taskStatus = remitBatchRespDTO.getTaskStatus();
                    Integer validSuccessCount = (remitBatchRespDTO.getValidSuccessCount() == null?0:remitBatchRespDTO.getValidSuccessCount());
                    Integer validFailCount = (remitBatchRespDTO.getValidFailCount()== null?0:remitBatchRespDTO.getValidFailCount());
                    BigDecimal validFailAmount = (remitBatchRespDTO.getValidFailAmount()== null?BigDecimal.ZERO:remitBatchRespDTO.getValidFailAmount());
                    BigDecimal validSuccessAmount = (remitBatchRespDTO.getValidSuccessAmount()==null?BigDecimal.ZERO:remitBatchRespDTO.getValidSuccessAmount());
                    if(taskStatus == TaskStatus.SENDED){
                        responseMsg.put("status", "compete");

                    }else{
                        int processCount = remitBatchRespDTO.getTotalCount() - validSuccessCount;
                        responseMsg.put("processCount", processCount);
                        responseMsg.put("status", "process");
                    }

                    responseMsg.put("totalCount", remitBatchRespDTO.getTotalCount());
                    responseMsg.put("validSuccessCount", validSuccessCount);
                    responseMsg.put("validFailCount", validFailCount);
                    responseMsg.put("totalAmount", NumberUtils.formateNum(null,remitBatchRespDTO.getOrderAmount()));
                    responseMsg.put("validSuccessAmount",  NumberUtils.formateNum(null,validSuccessAmount));
                    responseMsg.put("validFailAmount",  NumberUtils.formateNum(null,validFailAmount));
                    responseMsg.put("batchNo",remitBatchRespDTO.getBatchNo());

                }else{
                    responseMsg.setStatus(ResponseMessage.Status.ERROR);
                    return responseMsg;
                }
                return responseMsg;
            } else {
                responseMsg.setStatus(ResponseMessage.Status.ERROR);
                return responseMsg;
            }
        } catch (Exception e) {
            logger.error("查询批量打款任务结果异常", e);
            responseMsg.setStatus(ResponseMessage.Status.ERROR);
            return responseMsg;
        }
    }


    /**
     * 跳转充值记录页
     *
     * @param request
     * @return
     */
//    @RequiresPermissions(RECHARGE_QUERY_PERMISSION)
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public ModelAndView queryView(HttpServletRequest request) {

        ModelAndView mav = new ModelAndView();
        Map<String, String> bankMap = queryAllHeadBank();
        mav.addObject("bankMap", bankMap);

        String customerNumber = super.getCurrentCustomerNumber();
        if(StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            mav.addObject("customerSign", customerSign);
        }

        mav.addObject("bankMap", bankMap);
        mav.setViewName("batchRemit/batchRemitQuery");
        return mav;
    }

    /**
     * 查询付款订单列表
     * @param remitQueryParam:
     * @Description: 查询付款订单列表（批量)
     */
    @RequestMapping(value = "/getFailResultPage")
    @ResponseBody
    public ResponseMessage queryOrderList(RemitQueryParam remitQueryParam,
                                          @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                          @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("查询付款订单，请求参数{}", JSONUtils.toJsonString(remitQueryParam));

        if (remitQueryParam.isEmptyCheck()) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询参数为空");
            return resMsg;
        }
        PreCheck.checkArgument(StringUtils.isNotBlank(remitQueryParam.getCustomerNumber()),"商户编号不能为空");
        String status = remitQueryParam.getStatus();
        if (status.equals(RemitStatusEnum.REQUEST_ACCEPT.name())) {
            status = RemitStatusEnum.REQUEST_ACCEPT.name() + "," + RemitStatusEnum.REQUEST_RECEIVE.name();
            remitQueryParam.setStatus(status);
        }
        QueryResult queryResult = null;
        try {
            //查询列表
            //处理收款账号
            String bankAccountNo = remitQueryParam.getBankAccountNo();
            if(StringUtils.isNotBlank(bankAccountNo)){
                remitQueryParam.setBankAccountNo(AESUtils.encryptDigest(bankAccountNo));
            }
            //处理收款方名称
            String receiverAccountName = remitQueryParam.getReceiverAccountName();
            if(StringUtils.isNotBlank(receiverAccountName)){
                remitQueryParam.setReceiverAccountName(AESUtils.encryptDigest(receiverAccountName));
            }
            queryResult = this.queryRemitOrderList(remitQueryParam, pageNo, pageSize);

        } catch (Exception e) {
            logger.error("queryRemitOrderList,查询异常,e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }

        if (queryResult != null) {
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                for (Map<String, Object> map : queryResult.getData()) {
                    //处理返回参数
                    adaptReturnResult(map);
                    String orderStatus = map.get("status").toString();
                    if(orderStatus.equals(RemitStatusEnum.REQUEST_ACCEPT.name()) || orderStatus.equals(RemitStatusEnum.REQUEST_RECEIVE.name())){
                        map.put("status",RemitStatusEnum.REQUEST_ACCEPT.getDesc());
                    }else{
                        String reversed = map.get("reversed").toString();
                        if(orderStatus.equals(RemitStatusEnum.SUCCESS.name()) && reversed.equals("1")){
                            map.put("status","银行冲退");
                        }else{
                            map.put("status",RemitStatusEnum.valueOf(orderStatus).getDesc());
                        }

                    }
                    if(map.get("trade_type")==null){
                        map.put("trade_type",Costants.ENTERPRISE_PAYMENT);
                    }
                }
            }
            resMsg.put("dataList", queryResult.getData());
        }
        //查询汇总信息
        resMsg = this.queryRemitOrderListSum(remitQueryParam, resMsg);
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        logger.info("查询充值订单列表返回，resMsg={}", JSONUtils.toJsonString(resMsg));
        return resMsg;
    }

    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/suspectedRepeatedBatch")
    @ResponseBody
    public String getSuspectedRepeatedBatch(@Param("token") String token) {
        logger.info("查看疑似重复入参 token:{}", token);
        String customerNumber = super.getCurrentCustomerNumber();
        Map<String, String> hgetall = RedisUtils.hgetall(token);
        String batchNo = hgetall.get("batchNo");
        String analyseResult = hgetall.get(getAnalyseRedisKey(customerNumber, batchNo));
        logger.info("返回解析结果：" + analyseResult);

        return analyseResult;
    }


    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/intoSuspectedDetail")
    public ModelAndView intoSuspectedDetail(HttpServletRequest request, @Param("batchNo") String batchNo) {
        ModelAndView mav = new ModelAndView();
        logger.info("跳转疑似明细页面 batchNo：" + batchNo);
        mav.addObject("batchNo", batchNo);
        mav.setViewName("batchRemit/suspectedRepeatedDetail");
        return mav;
    }

    @RequestMapping(value = "/queryRemitDetail")
    public ModelAndView queryRemitDetail(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        String batchNo = request.getParameter("batchNo");
        String requestNo = request.getParameter("requestNo");
        String customerNumber = request.getParameter("customerNumber");
        logger.info("查询订单明细 ：batchNo={},orderNo={},customerNumber={}",batchNo,orderNo,customerNumber);
        RemitOrderQueryReqDTO dto =new RemitOrderQueryReqDTO();
        PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber),"商户编号不能为空");
        dto.setInitiateMerchantNo(customerNumber);
        dto.setParentMerchantNo(customerNumber);

        dto.setOrderNo(orderNo);
        dto.setBatchNo(batchNo);
        dto.setRequestNo(requestNo);
        RemitOrderQueryRespDTO remitOrderQueryRespDTO = remitFacade.queryRemitOrder(dto);
        RemitResponseParam param = new RemitResponseParam();
        if(remitOrderQueryRespDTO != null){
            String returnCode = remitOrderQueryRespDTO.getReturnCode();
            if("UA00000".equals(returnCode)){
                BeanUtils.copyProperties(remitOrderQueryRespDTO,param);
                RemitOrderStatusEnum status = remitOrderQueryRespDTO.getStatus();
                if(status == RemitOrderStatusEnum.SUCCESS){
                    param.setStatus(RemitStatusEnum.SUCCESS.getDesc());
                }else if(status == RemitOrderStatusEnum.FAIL){
                    param.setStatus(RemitStatusEnum.FAIL.getDesc());
                }else if(status == RemitOrderStatusEnum.CANCELED){
                    param.setStatus(RemitStatusEnum.CANCELED.getDesc());
                }else if(status == RemitOrderStatusEnum.REMITING){
                    param.setStatus(RemitStatusEnum.REMITING.getDesc());
                }else{
                    param.setStatus(RemitStatusEnum.REQUEST_ACCEPT.getDesc());
                }
                if(!CheckUtils.isEmpty(remitOrderQueryRespDTO.getFinishTime())){
                    param.setFinishTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getFinishTime()));
                }
                param.setOrderAmount(remitOrderQueryRespDTO.getOrderAmount().toString());
                if(null != remitOrderQueryRespDTO.getFee()){
                    param.setFee(remitOrderQueryRespDTO.getFee().toString()+"元");
                }else{
                    param.setFee("--");
                }

                String receiverAccountNo = remitOrderQueryRespDTO.getReceiverAccountNo();
                /* 付款记录 跳转 查询明细*/
                HeadBankDTO headBankDTO = searchBankInfoFacade.searchHeadBankInfoByCode(remitOrderQueryRespDTO.getReceiverBankCode());
                if(headBankDTO != null){
                    String bankName = headBankDTO.getBankName();
                    if (StringUtils.isNotBlank(bankName) && StringUtils.isNotBlank(receiverAccountNo)) {
                        param.setReceiverAccountNo(bankName + "(" + receiverAccountNo.substring(receiverAccountNo.length() - 4, receiverAccountNo.length()) + ")");
                    }
                }
                param.setOrderInfo(remitOrderQueryRespDTO.getOrderInfo());
                param.setOrderTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getOrderTime()));
                RemitTypeEnum receiveType = remitOrderQueryRespDTO.getReceiveType();
                if(receiveType == RemitTypeEnum.REAL_TIME){
                    param.setReceiveType("实时到账");
                }else if(receiveType == RemitTypeEnum.TWO_HOUR){
                    param.setReceiveType("2小时到账");
                }else if(receiveType == RemitTypeEnum.NEXT_DAY){
                    param.setReceiveType("次日到账");
                }

                boolean isReversed = remitOrderQueryRespDTO.getIsReversed();
                if(isReversed){
                    param.setReverseTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getReverseTime()));
                    param.setStatus("银行冲退");
                }else{
                    param.setReverseTime("--");
                }

                if(StringUtils.isBlank(param.getFailReason())){
                    param.setFailReason("--");
                }
                if(StringUtils.isEmpty(param.getFinishTime())){
                    param.setFinishTime("--");
                }
                if(StringUtils.isEmpty(param.getComments())){
                    param.setComments("--");
                }
                if(StringUtils.isEmpty(param.getOrderInfo())){
                    param.setOrderInfo("--");
                }

                if(StringUtils.isEmpty(param.getBatchNo())){
                    param.setBatchNo("--");
                }

                if(StringUtils.isEmpty(param.getRemark())){
                    param.setRemark("--");
                }

                if (remitOrderQueryRespDTO.getTradeType() == null) {
                    param.setTradeType(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.ENTERPRISE_PAYMENT.getDesc());
                }else {
                    if(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType())!=null) {
                        param.setTradeType(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType()).getDesc());
                    }
                }

                param.setOrderNo(remitOrderQueryRespDTO.getRequestNo());
                param.setCapitalInfo(remitOrderQueryRespDTO.getCapitalInfo());
            }
        }
        logger.info("查询订单明细返回，remitResponseParam={}", JSON.toJSONString(param));
        mav.addObject("orderDetail", param);
        mav.setViewName("batchRemit/detail");
        return mav;
    }



    //    @RequiresPermissions(NewRemitPermission.BAC_REMITBANK)
    @RequestMapping(value = "/suspectedRepeatedDetail")
    @ResponseBody
    public ResponseMessage getSuspectedRepeatedDetail(@RequestParam("batchNo") String batchNo,
                                                      @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                                      @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {

        ResponseMessage responseMsg = new ResponseMessage("success");
        responseMsg.put("pageNo", pageNo);
        responseMsg.put("pageSize", pageSize);

        String customerNumber =  super.getCurrentCustomerNumber();
        String repeatedDetailRedisKey = getRepeatedDetailRedisKey(customerNumber, batchNo);
        String repeatedDetailOrderNo = RedisUtils.get(repeatedDetailRedisKey);
        List<String> orderNoList = new ArrayList<>();
        ArrayList<SuspectedRepeatedDetailDto> dtoArrayList = new ArrayList<>();
        if (repeatedDetailOrderNo != null) {
            StringBuffer sb = new StringBuffer();
            String[] orderArr = repeatedDetailOrderNo.split(",");
            for(int i = 0; i < orderArr.length;i++){
                sb.append(orderArr[i]);

            }
            logger.info("查询疑似重复明细参数: customerNumber{}, pageSize:{}, pageNo:{}", customerNumber, pageSize, pageNo);
//            result = remitFileDetailDao.getRemitDetailListOrderNo(customerNumber,batchNo, orderNoList, pageNo, pageSize);
            if(orderArr != null && orderArr.length>0){
                //查询列表
                QueryResult queryResult = this.queryRemitRepeatOrderList(batchNo,sb.toString(), pageNo, pageSize);
                if (!CheckUtils.isEmpty(queryResult.getData())) {
                    for (Map<String, Object> detail : queryResult.getData()) {

                        NumberFormat nf = NumberFormat.getNumberInstance();
                        nf.setMinimumFractionDigits(2);
                        nf.setMaximumFractionDigits(2);
                        nf.setGroupingUsed(false);

                        SuspectedRepeatedDetailDto dto = new SuspectedRepeatedDetailDto();
                        dto.setCreateTime(processTime(detail.get("create_time")));
                        dto.setBatchNo(detail.get("batch_no").toString());
                        dto.setOrderNo(detail.get("request_no").toString());
                        if(detail.get("order_amount") != null){
                            String order_amount = nf.format(new BigDecimal(detail.get("order_amount").toString()));
                            dto.setAmount(order_amount);
                        }
                        StringBuffer buffer = new StringBuffer();
                        if(null != detail.get("receiver_bank_code")){
                            String bankCode = detail.get("receiver_bank_code").toString();
                            if(StringUtil.isNotEmpty(bankCode)){
                                HeadBankDTO headBankDTO = queryBankInfo(bankCode);
                                if(headBankDTO != null){
                                    String bankName = headBankDTO.getBankName();
                                    buffer.append(bankName);
                                }
                            }
                        }

                        if(null !=  detail.get("receiver_account_no")){
                            String accountNo = detail.get("receiver_account_no").toString();
                            if(StringUtil.isNotEmpty(accountNo)){
                                accountNo = AESUtils.decryptWithBase64(accountNo);
                                buffer.append("("+accountNo.substring(accountNo.length() - 4, accountNo.length())+")");
                            }
                        }

                        if(null != detail.get("receiver_account_name")){
                            String receiverAccountName = detail.get("receiver_account_name").toString();
                            if(StringUtil.isNotEmpty(receiverAccountName)){
                                receiverAccountName = AESUtils.decryptWithBase64(receiverAccountName);
                                buffer.append(receiverAccountName);
                            }
                        }
                        dto.setAccountName(buffer.toString());

                        String status = detail.get("status").toString();
                        if(status.equals(RemitOrderStatusEnum.SUCCESS.name())){
                            Object reversed = detail.get("reversed");
                            if(null != detail.get("reversed")){
                                if(reversed.equals("1")){
                                    dto.setStatus("已到账,有退款");
                                }else{
                                    dto.setStatus(RemitStatusEnum.SUCCESS.getDesc());
                                }
                            }else{
                                dto.setStatus(RemitStatusEnum.SUCCESS.getDesc());
                            }

                        }else if(status.equals(RemitOrderStatusEnum.FAIL.name())){
                            dto.setStatus(RemitStatusEnum.FAIL.getDesc());
                        }else if(status.equals(RemitOrderStatusEnum.CANCELED.name())){
                            dto.setStatus(RemitStatusEnum.CANCELED.getDesc());
                        }else if(status.equals(RemitOrderStatusEnum.REMITING.name())){
                            dto.setStatus(RemitStatusEnum.REMITING.getDesc());
                        }else{
                            dto.setStatus(RemitStatusEnum.REQUEST_ACCEPT.getDesc());
                        }
                        dtoArrayList.add(dto);
                    }
                }
            }

        } else {
            logger.info("redis 中 无疑似重复数据的订单号,key= " + repeatedDetailRedisKey);
        }
        responseMsg.put("dataList", dtoArrayList);
        responseMsg.put("totalCount", orderNoList.size());
        logger.info("查询疑似重复明细返回：" + JSONUtils.toJsonString(responseMsg));
        return responseMsg;
    }


    @RequestMapping(value = "/downElectronicReceipt")
    @ResponseBody
    public void downElectronicReceipt(HttpServletRequest request, HttpServletResponse response) throws Exception{
        try{
            String orderNo = request.getParameter("orderNo");
            String customerNumber = request.getParameter("customerNumber");
            PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber),"商户编号不能为空");
            logger.info("代付下载电子回单 the orderNo=[{}],the customerNumber=[{}]",orderNo,customerNumber);
            Map<String,Object> map = new HashMap();
            map.put("customerNumber",customerNumber);
            map.put("orderNo",orderNo);
            RemitOrder remitOrder = remitOrderService.queryRemitOrderInfo(map);
            PdfUtils pdfUtils = new PdfUtils();
            long start = System.currentTimeMillis();
            OrderParam orderParam = handleResult(remitOrder);
            String data = pdfUtils.generateReceipt(orderParam, getCurrentCustomerNumber());
            BASE64Decoder decoder = new BASE64Decoder();
            //Base64解码
            byte[] len = decoder.decodeBuffer(data);
            for (int i = 0; i < len.length; ++i) {
                //调整异常数据
                if (len[i] < 0) {
                    len[i] += 256;
                }
            }
            /*在开始写入的那一刻再定义这些，不然影响异常的弹窗*/
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName="+orderNo+".pdf");
            OutputStream out = response.getOutputStream();
            out.write(len);
            out.flush();
            out.close();
            long end = System.currentTimeMillis();
            logger.info("代付下载电子回单耗时{}ms", (end - start));
        } catch (Throwable ex) {
            logger.error("代付下载电子回单异常 异常， 来个弹窗 ex={}", JSONObject.toJSONString(ex));
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * (红版商户后台)下载付款电子回单
     * @param remitDownloadReq
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/downLoadElectronicReceipt")
    @ResponseBody
    public void downLoadElectronicReceipt(RemitDownloadReq remitDownloadReq, HttpServletResponse response) throws Exception{
        try{
            logger.info("[红版商户后台]下载付款电子回单 the param=[{}]",JSONObject.toJSONString(remitDownloadReq));
            //商户编号
            String customerNumber = remitDownloadReq.getCustomerNumber();
            //订单编号
            String orderNo = remitDownloadReq.getOrderNo();
            //参数校验
            PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber),"商户编号不能为空");
            PreCheck.checkArgument(StringUtils.isNotBlank(orderNo),"订单编号不能为空");

            Map<String,Object> map = new HashMap();
            map.put("customerNumber",customerNumber);
            map.put("orderNo",orderNo);
            RemitOrder remitOrder = remitOrderService.queryRemitOrderInfo(map);
            PreCheck.checkArgument(Objects.nonNull(remitOrder),"付款订单查询结果为空");

            PdfUtils pdfUtils = new PdfUtils();
            long start = System.currentTimeMillis();
            OrderParam orderParam = handleResult(remitOrder);
            String data = pdfUtils.generateReceipt(orderParam, getCurrentCustomerNumber());
            BASE64Decoder decoder = new BASE64Decoder();
            //Base64解码
            byte[] len = decoder.decodeBuffer(data);
            for (int i = 0; i < len.length; ++i) {
                //调整异常数据
                if (len[i] < 0) {
                    len[i] += 256;
                }
            }
            /*在开始写入的那一刻再定义这些，不然影响异常的弹窗*/
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName="+orderNo+".pdf");
            OutputStream out = response.getOutputStream();
            out.write(len);
            out.flush();
            out.close();
            long end = System.currentTimeMillis();
            logger.info("代付下载电子回单耗时{}ms", (end - start));
        } catch (Throwable ex) {
            logger.error("代付下载电子回单异常 异常， 来个弹窗 ex={}", JSONObject.toJSONString(ex));
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }
    @RequestMapping(value = "/receipt", method = RequestMethod.GET)
    public ModelAndView receiptView(HttpServletRequest request) {
        String orderNo = request.getParameter("orderNo");
        String merchantNo = request.getParameter("merchantNo");
        PreCheck.checkArgument(StringUtils.isNotBlank(merchantNo),"商户编号不能为空");
        ModelAndView mav = new ModelAndView();
        mav.addObject("orderNo",orderNo);
        mav.addObject("merchantNo",merchantNo);
        mav.setViewName("receipt/receipt");
        return mav;
    }

    /**
     * @Description: 查询重复付款订单
     * @param batchNo
     * orderNoList:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     */
    private QueryResult queryRemitRepeatOrderList(String batchNo,String orderArr,int pageNo, int pageSize) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();
//        queryMap.put("batchNo",batchNo);
        queryMap.put("orderArr",orderArr);
        queryMap.put("customerNumber",getCurrentCustomerNumber());//getCurrentCustomerNumber()
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("queryRemitRepeatOrderList", queryParam);

    }


    private String processTime(Object time){
        try{
            if (time instanceof String) {
                String str = String.valueOf(time);
                if (StringUtils.isNotBlank(str)) {
                    SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    if (str.length() == 10) {
                        return smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY));
                    } else {
                        return smf.format(smf.parse(str));
                    }
                }
            } else if (time instanceof Timestamp) {
                return  DateUtils.toSqlTimestampString((Timestamp) time, DateUtils.DATE_FORMAT_DATETIME);
            }else if(time instanceof Date) {
                return DateUtil.date2String((Date) time, PATTERN_STANDARD19H);
            }
        } catch (Throwable e){
            logger.error("处理时间格式错误", e);
        }
        return null;
    }
    /**
     * 验证token令牌
     * @param loginName 登录名
     * @param token
     */
    private Map<String, String> vaildToken(String loginName, String token) {
        Map<String, String> hmget = null;
        try {
            hmget = RedisUtils.hgetall(token);
            logger.info("获取redis,hmget={},loginName={},token={}", JSON.toJSONString(hmget),loginName,token);
        } catch (Exception e) {
            logger.error("调用redis hgetall异常", e);
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        if (null == hmget) {
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        if (!loginName.equals(hmget.get("loginName"))) {
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        if (!"0".equals(hmget.get("validExceptionCount"))) {
            throw AccountPayException.TOKEN_ERROR.newInstance("该出款批次已失效，请核实出资情况，避免重复出资风险！");
        }
        RedisUtils.delete(token);
        return hmget;
    }


    public static class ResponseMsg implements Serializable {

        private static final long serialVersionUID = 1L;

        private Status status;
        private String token;
        private String redirectUrl;
        private Map<String, Object> retMsg;
        private String systemCode;

        public Status getStatus() {
            return status;
        }

        public void setStatus(Status status) {
            this.status = status;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getRedirectUrl() {
            return redirectUrl;
        }

        public void setRedirectUrl(String redirectUrl) {
            this.redirectUrl = redirectUrl;
        }

        public Map<String, Object> getRetMsg() {
            return retMsg;
        }

        public void setRetMsg(Map<String, Object> retMsg) {
            this.retMsg = retMsg;
        }

        public ResponseMsg(Status status) {
            this.status = status;
        }

        public ResponseMsg(String token, String redirectUrl) {
            this.status = Status.SUCCESS;
            this.token = token;
            this.redirectUrl = redirectUrl;
        }

        public String getSystemCode() {
            return systemCode;
        }

        public void setSystemCode(String systemCode) {
            this.systemCode = systemCode;
        }

        public ResponseMsg(Status status, String token, String redirectUrl, Map<String, Object> retMsg, String systemCode) {
            this.status = status;
            this.token = token;
            this.redirectUrl = redirectUrl;
            this.retMsg = retMsg;
            this.systemCode = systemCode;
        }

        public ResponseMsg() {
        }

        public enum Status {
            SUCCESS("SUCCESS"),
            FAILED("FAILED");

            private String value;

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }

            Status(String value) {
                this.value = value;
            }
        }

        public enum SystemCode {
            SYS_ERROR
        }

    }


    //参数校验逻辑处理
    private String validateQueryRequestDataParam(QueryRequestDataReqDto reqDto) {
        //校验商编
        if (reqDto.getCustomerId() == null
                && CollectionUtils.isEmpty(reqDto.getCustomerNumbers())
                && com.yeepay.g3.utils.log.utils.StringUtil.isEmpty(reqDto.getCustomerNumber())) {
            return AccountPayException.BATCH_QUERY_CUSTOMER_NOT_EXIST;
        }
        //校验分页数据
        if (com.yeepay.g3.utils.log.utils.StringUtil.isEmpty(reqDto.getPageNo() + "") && com.yeepay.g3.utils.log.utils.StringUtil.isEmpty(reqDto.getPageSize() + "")) {
            return AccountPayException.BATCH_QUERY_PAGE_ISNULL;
        }
        //商编长度不能超过50个
        if (CollectionUtils.isNotEmpty(reqDto.getCustomerNumbers()) && reqDto.getCustomerNumbers().size() > 50) {
            return AccountPayException.BATCH_QUERY_CUSTOMERS_TOO_LONG;
        }
        //数据限制校验 若订单号、批次号、订单号-批量、批次号-批量、求发起时间都未填，则为请求发起时间赋值 默认为结束日期为今天 开始日期为100天之前
        if (com.yeepay.g3.utils.log.utils.StringUtil.isEmpty(reqDto.getMctCreator()) && CollectionUtils.isEmpty(reqDto.getMctCreators())
                && com.yeepay.g3.utils.log.utils.StringUtil.isEmpty(reqDto.getBatchCode()) && CollectionUtils.isEmpty(reqDto.getBatchCodes())
                && reqDto.getCreateDateStart() == null && reqDto.getCreateDateEnd() == null) {
            try {
                Date endDate = new Date();
                Date startDate = DateUtils.SHORT_DATE_FORMAT.parse(DateUtils.SHORT_DATE_FORMAT.format(DateUtils.addDay(endDate, -100)));
                reqDto.setCreateDateStart(startDate);
                reqDto.setCreateDateEnd(endDate);
            } catch (ParseException e) {
                return AccountPayException.BATCH_QUERY_DATE_PARSE;
            }
        }
        //发起开始日期 和发起结束日期 必须同时都有值
        if (reqDto.getCreateDateStart() != null || reqDto.getCreateDateEnd() != null) {
            if (reqDto.getCreateDateStart() == null || reqDto.getCreateDateEnd() == null) {
                return AccountPayException.BATCH_QUERY_DATE_MUST_CONSISTENCY;
            }

        }
        //发起开始日期和 发起结束日期要再100天以内
        if (reqDto.getCreateDateStart() != null && reqDto.getCreateDateEnd() != null) {
            //开始日期 要早于结束日期
            if (reqDto.getCreateDateStart().compareTo(reqDto.getCreateDateEnd()) > 0) {
                return AccountPayException.BATCH_QUERY_ENDDATE_EARLYER_STARTDATE;
            }
            //开始日期和结束日期要在100天区间内
            if (DateUtil.daysOfTwo(reqDto.getCreateDateStart(), reqDto.getCreateDateEnd()) > 100) {
                return AccountPayException.BATCH_QUERY_CREATEDATE_LIMIT90DAY;
            }
        }
        //当批量编号 和 单次编号同时存在的时候 则以批量单号为准
        if (CollectionUtils.isNotEmpty(reqDto.getCustomerNumbers())) {
            reqDto.setCustomerNumber(null);
        }
        if (CollectionUtils.isNotEmpty(reqDto.getBatchCodes())) {
            reqDto.setBatchCode(null);
        }
        if (CollectionUtils.isNotEmpty(reqDto.getMctCreators())) {
            reqDto.setMctCreator(null);
        }
        //处理银行冲退状态
        if (com.yeepay.g3.utils.common.StringUtils.isNotEmpty(reqDto.getQueryBankRetreatType())) {
            if ("ONLY_RETREAT".equalsIgnoreCase(reqDto.getQueryBankRetreatType())) {
                reqDto.setQueryBankRetreatType("ONLY_RETREAT");
            } else if ("NOT_RETREAT".equalsIgnoreCase(reqDto.getQueryBankRetreatType())) {
                reqDto.setQueryBankRetreatType("NOT_RETREAT");
            } else {
                reqDto.setQueryBankRetreatType(null);
            }
        }
        return "";
    }


    private List<String> buildRemitProduct(String markProductCode,BusinessCheckRemoteService businessCheckRemoteService){

        List<String> secondProductList = new ArrayList<String>();
        String realtime="";
        String twohour="";
        String tomorrow="";

        MerchantProductQueryRespDTO merchantProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(getCurrentCustomerNumber(), "PAY", markProductCode, Costants.ENTERPRISE_PAYMENT, null);
        if("0000".equals(merchantProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductQueryRespDTO.getBaseProductList())) {
            for(BaseProductDTO baseProductDTO : merchantProductQueryRespDTO.getBaseProductList()) {
                if(!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode()))  {
                    if(RemitTypeEnum.REAL_TIME.getSecondProductCode().equals(baseProductDTO.getSecondBaseProductCode())){
                        realtime=baseProductDTO.getSecondBaseProductCode();
                    }else if(RemitTypeEnum.TWO_HOUR.getSecondProductCode().equals(baseProductDTO.getSecondBaseProductCode())){
                        twohour=baseProductDTO.getSecondBaseProductCode();
                    }else if(RemitTypeEnum.NEXT_DAY.getSecondProductCode().equals(baseProductDTO.getSecondBaseProductCode())){
                        tomorrow=baseProductDTO.getSecondBaseProductCode();
                    }
                }
            }
        }

        MerchantProductQueryRespDTO specialAccountProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(getCurrentCustomerNumber(), "PAY", markProductCode, Costants.REMIT_SPECIAL_ACCOUNT_FIRST_CODE, null);
        if("0000".equals(specialAccountProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(specialAccountProductQueryRespDTO.getBaseProductList())) {
            for(BaseProductDTO baseProductDTO : specialAccountProductQueryRespDTO.getBaseProductList()) {
                if(!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode()))  {
                    if(RemitTypeEnum.REAL_TIME.getSecondProductCode().equals(baseProductDTO.getSecondBaseProductCode())){
                        realtime=baseProductDTO.getSecondBaseProductCode();
                    }else if(RemitTypeEnum.TWO_HOUR.getSecondProductCode().equals(baseProductDTO.getSecondBaseProductCode())){
                        twohour=baseProductDTO.getSecondBaseProductCode();
                    }else if(RemitTypeEnum.NEXT_DAY.getSecondProductCode().equals(baseProductDTO.getSecondBaseProductCode())){
                        tomorrow=baseProductDTO.getSecondBaseProductCode();
                    }
                }
            }
        }

        if(StringUtils.isNotBlank(realtime)){
            secondProductList.add("URGENCY");
        }
        if(StringUtils.isNotBlank(twohour)){
            secondProductList.add("COMMON");
        }
        if(StringUtils.isNotBlank(tomorrow)){
            secondProductList.add("NEXT_DAY");
        }
        return secondProductList;
    }


    private boolean checkOpenProduct(String currentCustomerNumber,String markProductCode,BusinessCheckRemoteService businessCheckRemoteService){

        boolean hasRjtProduct =false;
        MerchantProductQueryRespDTO respDto = businessCheckRemoteService.queryMerchantProduct(currentCustomerNumber, "PAY", markProductCode, Costants.RJT, Costants.REALTIME);
        if("0000".equals(respDto.getRetCode())){
            hasRjtProduct = true;
        }
        return hasRjtProduct;
    }


    private AccountInfoRespDTO getRemitBalance(String currentCustomerNumber){
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        //查询余额
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.queryAccountBalance(currentCustomerNumber, AccountTypeEnum.FUND_ACCOUNT);
        return queryAccountResponseDto;
    }


    /**
     * 查询专款账户余额
     *
     * @param markProductCode
     * @param currentCustomerNumber
     * @return
     */
    private AccountInfoRespDTO getSpecialAccountBalance(String currentCustomerNumber, String markProductCode) {
        MerchantProductQueryRespDTO specialAccountProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(currentCustomerNumber, "PAY", markProductCode, Costants.REMIT_SPECIAL_ACCOUNT_FIRST_CODE, null);
        if ("0000".equals(specialAccountProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(specialAccountProductQueryRespDTO.getBaseProductList())) {
            BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
            //查询余额
            AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(currentCustomerNumber, AccountTypeEnum.SPECIAL_FUND_ACCOUNT);
            if (queryAccountResponseDto != null) {
                String accountStatus = queryAccountResponseDto.getAccountStatus();
                if ("AVAILABLE".equals(accountStatus) || "FROZEN_CREDIT".equals(accountStatus)) {
                    return queryAccountResponseDto;
                }
            }
        }
        return null;
    }


    public enum ERROR_CODE {
        FILETYPE_ERROR("FILETYPE_ERROR", "请上传.xls格式的批量付款文件"),
        PRODUCT_ERROR("PRODUCT_ERROR", "产品未开通"),
        FILEHEAD_ERROR("FILEHEAD_ERROR", "文件头不准确"),
        FILEPARSE_ERROR("FILEPARSE_ERROR", "文件解析异常，请将文件另存为.xls文件再试"),

        BATCHNO_FORMAT_ERROR("BATCHNO_FORMAT_ERROR", "批次号格式错误，请重新输入"),
        BATCHNO_REPEAT_ERROR("BATCHNO_REPEAT_ERROR", "批次号重复，请勿重复提交"),
        //        BATCHNO_ISBLANK_ERROR("BATCHNO_ISBLANK_ERROR", "批次号不能为空"),
        TOTAL_COUNT_MATCH_ERROR("TOTAL_COUNT_MATCH_ERROR", "总笔数与上传文件不符，可修正后提交"),
        TOTAL_COUNT_OVER("TOTAL_COUNT_OVER", "文件内容超过1000条限制，请拆分后提交"),
        TOTAL_COUNT_LESS("TOTAL_COUNT_LESS", "文件内容为空！请重新上传有效文件"),

        TOTAL_AMOUNT_MATCH_ERROR("TOTAL_AMOUNT_MATCH_ERROR", "总金额与上传文件不符，可修正后提交"),
        TOTAL_AMOUNT_ERROR("TOTAL_AMOUNT_ERROR", "总金额输入有误，请输入正确表达金额的数字"),
        TOTAL_AMOUNT_LESS("TOTAL_AMOUNT_LESS", "可用余额不足，无法出款"),
        ORDER_REPETE("ORDER_REPETE","订单已存在"),
        FILEEMPTY_ERROR("FILEEMPTY_ERROR", "上传文件为空，无法提交"),


        SYS_ERROR("SYS_ERROR", "系统异常"),


        ;
        private String name;
        private String value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        ERROR_CODE(String name, String value) {
            this.name = name;
            this.value = value;
        }
    }

    /**
     * 预批发送确认
     *
     * @param file            文件
     * @param batchNo         批号
     * @param totalCount      总计数
     * @param totalAmount     总额
     * @param shiroUser       shiro用户
     * @param token           代币
     * @param remitSendType   汇款发送类型
     * @param productType     产品类型
     * @param markProductCode 标记产品代码
     * @param ipAddress       ip地址
     * @param tradeType       交易类型
     * @return {@link Map}<{@link String}, {@link Object}>
     * @throws Exception 例外
     */
    public Map<String, Object> preBatchSendConfirm(
            MultipartFile file,
            String batchNo,
            Integer totalCount,
            String totalAmount,
            ShiroUser shiroUser,
            String token,
            String remitSendType,
            String productType,
            String markProductCode,
            String ipAddress,
            String tradeType
    ) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        String filename = file.getOriginalFilename();
        if (/* 校验文件名和版本 */ filename == null
//                || !(ExcelUtil.isExcel2003(filename))
        ) {
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.FILETYPE_ERROR.getName());
            errorInfoMap.put("description", ERROR_CODE.FILETYPE_ERROR.getValue());
            resultMap.put("file", errorInfoMap);
        }
        if (/* 批次号校验, 传了批次号 */ StringUtils.isNotBlank(batchNo)) {
            String batchNoRegx = "[a-z|A-Z|0-9]{1,15}";
            if (!batchNo.matches(batchNoRegx)) {
                Map<String, String> errorInfoMap = new HashMap<>();
                errorInfoMap.put("errorCode", ERROR_CODE.BATCHNO_FORMAT_ERROR.getName());
                errorInfoMap.put("description", ERROR_CODE.BATCHNO_FORMAT_ERROR.getValue());
                resultMap.put("batchNo", errorInfoMap);
            }
            // 查询批次信息
            RemitBatchInfoReqDTO remitBatchInfoRequestDTO = new RemitBatchInfoReqDTO();
            remitBatchInfoRequestDTO.setBatchNo(batchNo);
            remitBatchInfoRequestDTO.setMerchantNo(shiroUser.getCustomerNumber());
            RemitBatchRespDTO remitBatchRespDTO = remitFacade.queryBatchInfo(remitBatchInfoRequestDTO);
            String queriedBatchNo = remitBatchRespDTO.getBatchNo();
            if (/* 批次号存在, 说明提交过, 引导跳转到查询页面 */ StringUtils.isNotEmpty(queriedBatchNo)) {
                Map<String, String> errorInfoMap = new HashMap<>();
                errorInfoMap.put("errorCode", ERROR_CODE.BATCHNO_REPEAT_ERROR.getName());
                errorInfoMap.put("description", ERROR_CODE.BATCHNO_REPEAT_ERROR.getValue());
                errorInfoMap.put("url", "/batchRemit/getFileResultPageByBatch?batchNo=" + batchNo);
                resultMap.put("batchNo", errorInfoMap);
                return resultMap;
            }
        } else {
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.BATCHNO_FORMAT_ERROR.getName());
            errorInfoMap.put("description", ERROR_CODE.BATCHNO_FORMAT_ERROR.getValue());
            resultMap.put("batchNo", errorInfoMap);
        }
        if (/* 条数超限制 */ totalCount > 1000) {
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.TOTAL_COUNT_OVER.getName());
            errorInfoMap.put("description", ERROR_CODE.TOTAL_COUNT_OVER.getValue());
            resultMap.put("totalCount", errorInfoMap);
        }
        if (/* 空文件 */ totalCount <= 0) {
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.TOTAL_COUNT_LESS.getName());
            errorInfoMap.put("description", ERROR_CODE.TOTAL_COUNT_LESS.getValue());
            resultMap.put("totalCount", errorInfoMap);
        }
        String regx = "^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
        if (/* 金额格式问题 */ !totalAmount.matches(regx) || totalAmount.length() > 13) {
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.TOTAL_AMOUNT_ERROR.getName());
            errorInfoMap.put("description", ERROR_CODE.TOTAL_AMOUNT_ERROR.getValue());
            resultMap.put("totalAmount", errorInfoMap);
        }
        // 解析excel文件
        analyseFile(file, token, resultMap, batchNo, remitSendType, productType, shiroUser, markProductCode, ipAddress, tradeType);
        if (resultMap.containsKey("file") || resultMap.containsKey("batchNo")) {
            return resultMap;
        }
        Map<String, String> hmget = null;
        try {
            hmget = RedisUtils.hgetall(token);
        } catch (Exception e) {
            logger.error("调用redis hgetall异常", e);
        }
        if (null == hmget || hmget.size() < 1) {
            logger.error("redis取值失败，系统异常");
            throw new Exception("系统异常：读取redis失败");
        }

        //校验是否大于1000笔
        Integer after_totalCount = new Integer(hmget.get("totalCount"));
        if (after_totalCount > 1000) {
            Map<String, String> map1 = new HashMap<>();
            map1.put("errorCode", ERROR_CODE.TOTAL_COUNT_OVER.getName());
            map1.put("description",ERROR_CODE.TOTAL_COUNT_OVER.getValue());
            resultMap.put("totalCount", map1);
        }
        if (after_totalCount <= 0) {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("errorCode",ERROR_CODE.TOTAL_COUNT_LESS.getName());
            hashMap.put("description", ERROR_CODE.TOTAL_COUNT_LESS.getValue());
            resultMap.put("file", hashMap);
        }

        BigDecimal decimal_totalamount = new BigDecimal(totalAmount);
        if (decimal_totalamount.compareTo(new BigDecimal(hmget.get("totalAmount"))) != 0) {
            Map<String, String> map1 = new HashMap<>();
            map1.put("errorCode", ERROR_CODE.TOTAL_AMOUNT_MATCH_ERROR.getName());
            map1.put("description",ERROR_CODE.TOTAL_AMOUNT_MATCH_ERROR.getValue());
            resultMap.put("totalAmount", map1);
        }
        if (totalCount.compareTo(after_totalCount) != 0) {
            Map<String, String> map2 = new HashMap<>();
            map2.put("errorCode", ERROR_CODE.TOTAL_COUNT_MATCH_ERROR.getName());
            map2.put("description",ERROR_CODE.TOTAL_COUNT_MATCH_ERROR.getValue());
            resultMap.put("totalCount", map2);
        }
        return resultMap;
    }

    private void analyseFile(
            MultipartFile file,
            String token,
            Map<String, Object> map,
            String batchNo,
            String remitSendType,
            String productType,
            ShiroUser shiroUser,
            String markProductCode,
            String ipAddress,
            String tradeType
    ) {
        Workbook wb = null;
        try {
            wb = WorkbookFactory.create(file.getInputStream());
        } catch (Exception e) {
            // 不知道为啥文件都生成失败了, 然后就打了一个日志就结束了, 完事儿我也不敢逻辑, 打打日志吧
            // 空指针会被转化为出款批次重复有点吓人，还是具体的写清楚吧
            logger.error("批量代付, 生成excel失败, 然后下面应该会抛一个空指针, 不要被误导了, cased by ", e);
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", BacNewBatchRemitController.ERROR_CODE.FILEPARSE_ERROR.getName());
            errorInfoMap.put("description", BacNewBatchRemitController.ERROR_CODE.FILEPARSE_ERROR.getValue());
            map.put("file", errorInfoMap);
            return;
        }
        // 所以当文件生成有问题的时候 ↓ 这一行应该就空指针, 完事儿系统异常返回? 牛逼, 反正都是系统异常, 先这样吧
        Sheet sheet = wb.getSheetAt(0);
        Row head = sheet.getRow(0);
        // 校验文件头
        if (!checkHead(head)) {
            logger.error("文件头不准确");
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.FILEHEAD_ERROR.getName());
            errorInfoMap.put("description", ERROR_CODE.FILEHEAD_ERROR.getValue());
            map.put("file", errorInfoMap);
            return;
        }
        try {
            processBody(map, FileUtil.multipartFileToFile(file), token, batchNo, remitSendType, productType, shiroUser, markProductCode, ipAddress, tradeType);
        } catch (Exception e) {
            logger.error("token: " + token + " 文件解析异常", e);
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.FILEPARSE_ERROR.getName());
            errorInfoMap.put("description", ERROR_CODE.FILEPARSE_ERROR.getValue());
            map.put("file", errorInfoMap);
        }
    }

    /**
     * 先声明不是我写的, 我只是格式化了一下. add by zhaowei.zhang
     * 太庞大以至于不敢贸然重构
     *
     * @param hashMap
     * @param file
     * @param token
     * @param batchNo
     * @param remitSendType
     * @param productType
     * @param shiroUser
     * @param markProductCode
     * @param ipAddress
     * @param tradeType
     * @throws Exception
     */
    private void processBody(
            Map<String, Object> hashMap,
            File file,
            String token,
            String batchNo,
            String remitSendType,
            String productType,
            ShiroUser shiroUser,
            String markProductCode,
            String ipAddress,
            String tradeType
    ) throws Exception {
        // 文件复制
        File destFile = new File(System.getProperty("java.io.tmpdir") + token + ".xls");
        FileUtil.copyFile(file, destFile);
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(destFile);
        } catch (Exception e) {
            logger.error("复制后的文件读取失败", e);
            throw new Exception("复制后的文件读取失败");
        }
        //处理单条记录
        Workbook wb = WorkbookFactory.create(inputStream);
        List<RemitBatchDetailReqDTO> detailList = buildBatchDetailData(wb, ipAddress, tradeType);
        if (CollectionUtils.isNotEmpty(detailList)) {
            RemitBatchCheckReqDTO batchCheckReqDTO = new RemitBatchCheckReqDTO();
            batchCheckReqDTO.setBatchNo(batchNo);
            batchCheckReqDTO.setOperatorName(shiroUser.getLoginName());
            batchCheckReqDTO.setMerchantNo(shiroUser.getCustomerNumber());
            if (NewRemitSendType.NEXT_DAY.name().equals(remitSendType)) {
                batchCheckReqDTO.setRemitType(RemitTypeEnum.NEXT_DAY.name());
            } else if (NewRemitSendType.COMMON.name().equals(remitSendType)) {
                batchCheckReqDTO.setRemitType(RemitTypeEnum.TWO_HOUR.name());
            } else if (NewRemitSendType.URGENCY.name().equals(remitSendType)) {
                batchCheckReqDTO.setRemitType(RemitTypeEnum.REAL_TIME.name());
            }
            batchCheckReqDTO.setRemitBatchDetailList(detailList);
            batchCheckReqDTO.setSaleProductCode(markProductCode);
            batchCheckReqDTO.setFileName(file.getName());
            batchCheckReqDTO.setStorageFileName(token);
            RemitBatchCheckRespDTO remitBatchCheckRespDTO = remitFacade.batchCheckRemitParam(batchCheckReqDTO);
            logger.info("批量校验参数返回" + JSON.toJSONString(remitBatchCheckRespDTO));
            Integer validExceptionCount = remitBatchCheckRespDTO.getValidExceptionCount();
            if ("UA00000".equals(remitBatchCheckRespDTO.getReturnCode())) {
                List<RemitBatchDetailRespDTO> batchDetailList = remitBatchCheckRespDTO.getBatchDetailList();
                if (CollectionUtils.isNotEmpty(batchDetailList)) {
                    writeBackExcel(wb, batchDetailList);
                }
            } else if ("UA30013".equals(remitBatchCheckRespDTO.getReturnCode())) {
                Map<String, String> hashMap1 = new HashMap<>();
                hashMap1.put("errorCode", ERROR_CODE.ORDER_REPETE.getName());
                hashMap1.put("description", ERROR_CODE.ORDER_REPETE.getValue());
                hashMap.put("totalAmount", hashMap1);
            }
            FileOutputStream outputStream = null;
            try {
                if (validExceptionCount > 0) {
                    outputStream = new FileOutputStream(destFile);
                } else {
                    outputStream = new FileOutputStream(file);
                }
                wb.write(outputStream);
            } catch (Exception e) {
                logger.error("系统处理异常", e);
                throw new Exception("系统处理异常");
            } finally {
                if (null != outputStream) {
                    outputStream.close();
                }
                FileInputStream is = new FileInputStream(destFile);
                fileStorageService.uploadFileCloud(token + ".xls", is);
                is.close();
            }
            Map<String, String> cacheMap = new HashMap<>();
            Integer validSuccessCount = remitBatchCheckRespDTO.getValidSuccessCount();
            cacheMap.put("totalCount", remitBatchCheckRespDTO.getTotalCount().toString());
            cacheMap.put("totalAmount", remitBatchCheckRespDTO.getTotalAmount().toString());
            cacheMap.put("validExceptionCount", validExceptionCount.toString());
            cacheMap.put("validExceptionAmount", (remitBatchCheckRespDTO.getValidExceptionAmount() == null) ? "" : remitBatchCheckRespDTO.getValidExceptionAmount().toString());
            cacheMap.put("validSuccessCount", (validSuccessCount == null) ? "" : validSuccessCount.toString());
            cacheMap.put("validSuccessAmount", (remitBatchCheckRespDTO.getValidSuccessAmount() == null) ? "" : remitBatchCheckRespDTO.getValidSuccessAmount().toString());
            cacheMap.put("batchNo", batchNo);
            cacheMap.put("remitSendType", remitSendType);
            cacheMap.put("productType", "WTJS");
            cacheMap.put("loginName", shiroUser.getLoginName());
            cacheMap.put("fileName", file.getName());
            cacheMap.put("isRepeated", "false");
            if (remitBatchCheckRespDTO.getTaskId() != null) {
                cacheMap.put("taskId", remitBatchCheckRespDTO.getTaskId().toString());
            }
            logger.info("发往redis的值： " + JSONUtils.toJsonString(cacheMap));
            try {
                String detailRedisKey = getDetailRedisKey(shiroUser.getCustomerNumber(), batchNo);
                if (RedisUtils.exists(detailRedisKey)) {
                    RedisUtils.delete(detailRedisKey);
                }
                RedisUtils.hmset(token, cacheMap, ConfigUtils.getBatchSendExpire());
            } catch (Exception e) {
                logger.error("保存明细md5 到redis失败", e);
            }
        } else {
            Map<String, String> errorInfoMap = new HashMap<>();
            errorInfoMap.put("errorCode", ERROR_CODE.FILEEMPTY_ERROR.getName());
            errorInfoMap.put("description", ERROR_CODE.FILEEMPTY_ERROR.getValue());
            hashMap.put("file", errorInfoMap);
        }
    }


    private QueryResult query24SendedRemitOrderByCustomerNo(String customerNumber) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();

        queryMap.put("customerNumber", customerNumber);
//        queryMap.put("taskStatus",status);
//        queryMap.put("createEndDate",remitQueryParam.getCreateEndDate());
//        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
//        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
//        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("query24SendedRemitOrderByCustomerNo", queryParam);
    }

    private List<RemitFileDetailEntity> handleGet24SendedRemitOrderByCustomerNo(String customerNumber){
        List<RemitFileDetailEntity> list = new ArrayList<>();
        QueryResult result = query24SendedRemitOrderByCustomerNo(customerNumber);
        if (result != null) {
            if (!CheckUtils.isEmpty(result.getData())) {
                for (Map<String, Object> map : result.getData()) {
                    //处理返回参数
//                    adaptReturnResult(map);
                    RemitFileDetailEntity dto = new RemitFileDetailEntity();
                    try {
                        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        //下单时间
                        Object obj = map.get("create_time");
                        if (null != obj) {
                            if (obj instanceof String) {
                                String str = String.valueOf(obj);
                                if (StringUtils.isNotBlank(str)) {
                                    if (str.length() == 10) {
                                        map.put("create_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                                    } else {
                                        map.put("create_time", smf.format(smf.parse(str)));
                                    }
                                }
                            } else if (obj instanceof Timestamp) {
                                map.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                                dto.setCreateTime((Timestamp) obj);
                            }
                        }
                    }catch (Exception e){
                        logger.error("异常信息，e={}",e);
                    }

                    Object receiverAccountNo = map.get("receiver_account_no");
                    if (null != receiverAccountNo) {
                        String  accountNo = AESUtils.decryptWithBase64(receiverAccountNo.toString());
                        dto.setBankAccountNo(accountNo);
                    }

                    Object receiverAccountName = map.get("receiver_account_name");
                    if (null != receiverAccountName) {
                        String  accountName = AESUtils.decryptWithBase64(receiverAccountName.toString());
                        dto.setBankAccountName(accountName);
                    }
                    Object orderAmount = map.get("order_amount");
                    if (null != orderAmount) {
                        dto.setAmount(new BigDecimal(map.get("order_amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }

                    Object operator = map.get("operator");
                    if (null != operator) {
                        dto.setOperator(operator.toString());
                    }

                    Object batchNo = map.get("batch_no");
                    if (null != batchNo) {
                        dto.setBatchNo(batchNo.toString());
                    }

                    Object requestNo = map.get("request_no");
                    if (null != requestNo) {
                        dto.setRequestNo(requestNo.toString());
                    }

                    Object orderNo = map.get("order_no");
                    if (null != orderNo) {
                        dto.setOrderNo(orderNo.toString());
                    }

                    list.add(dto);
                }
            }
        }
        return list;
    }

    private QueryResult getListByBatchNoAndMerchantNo(String customerNumber, String batchNo) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();

        queryMap.put("customerNumber", customerNumber);
        queryMap.put("batchNo",batchNo);

        //查询组件查询
        QueryParam queryParam = new QueryParam();

        queryParam.setParams(queryMap);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("getListByBatchNoAndMerchantNo", queryParam);
    }

    private List<RemitFileDetailEntity>  handleGetListByBatchNoAndMerchantNo(String customerNumber, String batchNo){
        List<RemitFileDetailEntity> remitFileDetailList = new ArrayList<>();
        QueryResult result = getListByBatchNoAndMerchantNo(customerNumber, batchNo);
        if (result != null) {
            if (!CheckUtils.isEmpty(result.getData())) {
                for (Map<String, Object> map : result.getData()) {
                    //处理返回参数
//                    adaptReturnResult(map);
                    RemitFileDetailEntity dto = new RemitFileDetailEntity();
                    String accountNo = map.get("bank_account_no").toString();
                    accountNo = AESUtils.decryptWithBase64(accountNo);
                    String accountName= map.get("bank_account_name").toString();
                    accountName = AESUtils.decryptWithBase64(accountName);
                    String amount = map.get("amount").toString();
                    String orderNo = map.get("request_no").toString();
                    dto.setAmount(new BigDecimal(amount));
                    dto.setOrderNo(orderNo);
                    dto.setBankAccountName(accountName);
                    dto.setBankAccountNo(accountNo);
                    dto.setBatchNo( map.get("batch_no").toString());
                    remitFileDetailList.add(dto);
                }
            }
        }
        return remitFileDetailList;

    }

    /**
     * 校验是否有重复数据
     *
     * @param md5Map    当次提交的md5 <orderNo, md5>
     * @param shiroUser
     * @return 返回解析重复的list
     */
    private List<BatchRemitAnalyResult> validateIsRepeated(Map<String, String> md5Map, ShiroUser shiroUser) {

        String customerNumber = shiroUser.getCustomerNumber();
        //查询24小时批次列表
        List<RemitFileTaskEntity> remitFileTaskList = handleGet24SendedRemitFileTaskByCustomerNo(customerNumber, "SENDED");
        if(CollectionUtils.isEmpty(remitFileTaskList)){
            return null;
        }
        //查询商户24小时内发起的批量订单
        List<RemitFileDetailEntity> remitFileDetails = handleGet24SendedRemitOrderByCustomerNo(customerNumber);
        if(CollectionUtils.isEmpty(remitFileDetails)){
            return null;
        }
        List<BatchRemitAnalyResult> list = new ArrayList<>();
        Map<String, String> repeatedDetailMap = new HashMap<>();
        for(RemitFileTaskEntity entity:remitFileTaskList){
            String batchNo = entity.getBatchNo();
            BatchRemitAnalyResult result = new BatchRemitAnalyResult();
            result.setBatchNo(entity.getBatchNo());
            result.setCreateDate(entity.getCreateTime());
            result.setUrl("/batchRemit/intoSuspectedDetail?batchNo=" + entity.getBatchNo());
            result.setOperator(entity.getOperatorName());
            Map<String, String> taskDetailMap = new HashMap<>();
            for (int i = 0; i < remitFileDetails.size(); i++) {
                RemitFileDetailEntity remitFileDetail = remitFileDetails.get(i);
                String batchNo2 = remitFileDetail.getBatchNo();
                if(!batchNo.equals(batchNo2)){
                    continue;
                }
                String receiverAccountNo = remitFileDetail.getBankAccountNo();
                BigDecimal orderAmount = remitFileDetail.getAmount();
                String receiverAccountName = remitFileDetail.getBankAccountName();
                String requestNo = remitFileDetail.getRequestNo();
                if(StringUtils.isNotBlank(receiverAccountNo)&&StringUtils.isNotBlank(receiverAccountName) && orderAmount!= null){
                    String md5 = Digest.md5Digest(receiverAccountNo + receiverAccountName + NumberUtils.formateNum(null,orderAmount));
                    taskDetailMap.put(requestNo, md5);
                }


            }
            //取出被比较批次里重复的订单号。taskDetailMap 原先map， md5Map 新map
            //开始分析
            StringBuffer sb = new StringBuffer();
            int k = 0;
            for (Map.Entry<String, String> entry : taskDetailMap.entrySet()) {
                boolean b = md5Map.containsValue(entry.getValue());
                if (b) {
                    sb.append(entry.getKey() + ",");
                    k++;
                }
            }
            result.setSameCount(k);
//            result.setSameRate(NumberUtils.proportionInt(afterSize, totalSize));
            if (k > 0) {
                list.add(result);
                RedisUtils.set(getRepeatedDetailRedisKey(customerNumber, entity.getBatchNo()), sb.toString(), ConfigUtils.getBatchSendExpire());
                logger.info("疑似重复批次明细放入redis，batchNo:{}, map:{}", entity.getBatchNo(), JSONUtils.toJsonString(repeatedDetailMap));
            }

        }

        //返回校验结果
        return list;

    }

    private QueryResult get24SendedRemitFileTaskByCustomerNo(String customerNumber, String status) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();

        queryMap.put("customerNumber", customerNumber);
        queryMap.put("taskStatus",status);
//        queryMap.put("createEndDate",remitQueryParam.getCreateEndDate());
//        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
//        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
//        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("get24SendedRemitFileTaskByCustomerNo", queryParam);
    }

    private List<RemitFileTaskEntity> handleGet24SendedRemitFileTaskByCustomerNo(String customerNumber, String status){
        List<RemitFileTaskEntity> list = new ArrayList<>();
        QueryResult result = get24SendedRemitFileTaskByCustomerNo(customerNumber, status);
        if (result != null) {
            if (!CheckUtils.isEmpty(result.getData())) {
                for (Map<String, Object> map : result.getData()) {
                    //处理返回参数
//                    adaptReturnResult(map);
                    RemitFileTaskEntity dto = new RemitFileTaskEntity();
                    try {
                        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        //下单时间
                        Object obj = map.get("create_time");
                        if (null != obj) {
                            if (obj instanceof String) {
                                String str = String.valueOf(obj);
                                if (StringUtils.isNotBlank(str)) {
                                    if (str.length() == 10) {
                                        map.put("create_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                                    } else {
                                        map.put("create_time", smf.format(smf.parse(str)));
                                    }
                                }
                            } else if (obj instanceof Timestamp) {
                                map.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                            }
                            dto.setCreateTime(map.get("create_time").toString());
                        }
                    }catch (Exception e){
                        logger.error("异常信息，e={}",e);
                    }

                    Object operator_name = map.get("operator_name");
                    if(operator_name != null){
                        dto.setOperatorName(map.get("operator_name").toString());
                    }

                    if(map.get("batch_no") != null){
                        dto.setBatchNo( map.get("batch_no").toString());
                    }
                    list.add(dto);
                }
            }
        }
        return list;
    }

    /**
     * 获取存放在redis中的明细key
     *
     * @param customerNumber
     * @param batchNo
     * @return
     */
    private String getDetailRedisKey(String customerNumber, String batchNo) {
        logger.info("开始组装 detail 的Rediskey， customerNumber:{}, batchNO:{}", customerNumber, batchNo);
        return customerNumber + "_" + batchNo + "_detail";
    }


    /**
     * 组装存放分析结果的Rediskey
     *
     * @param customerNumber
     * @param batchNo
     * @return
     */
    public static String getAnalyseRedisKey(String customerNumber, String batchNo) {
        logger.info("开始组装 analyseResult 的Rediskey， customerNumber:{}, batchNO:{}", customerNumber, batchNo);
        return customerNumber + "_" + batchNo + "_analyseResult";
    }

    public static String getRepeatedDetailRedisKey(String customerNumber, String batchNo) {
        logger.info("开始组装 疑似重复明细 的Rediskey， customerNumber:{}, batchNO:{}", customerNumber, batchNo);
        return customerNumber + "_" + batchNo + "_repeatedDetail";
    }


    /**
     * @Description: 查询付款订单
     * @param remitQueryParam
     * orderNoList:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     */
    private QueryResult queryRemitOrderList(RemitQueryParam remitQueryParam,int pageNo, int pageSize) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("batchNo",remitQueryParam.getBatchNo());
        queryMap.put("bankCode",remitQueryParam.getBankCode());
        queryMap.put("bankAccountNo",remitQueryParam.getBankAccountNo());
        queryMap.put("receiverAccountName",remitQueryParam.getReceiverAccountName());
        queryMap.put("requestNo",remitQueryParam.getRequestNo());
        queryMap.put("remitType",remitQueryParam.getRemitType());
        queryMap.put("status",remitQueryParam.getStatus());
        queryMap.put("customerNumber", remitQueryParam.getCustomerNumber());
        queryMap.put("createStartDate",remitQueryParam.getCreateStartDate());
        queryMap.put("createEndDate",remitQueryParam.getCreateEndDate());
        queryMap.put("remark",remitQueryParam.getRemark());
        queryMap.put("notFirstProductCode","PAYMENT_SUPPLIER");
        queryMap.put("comments",remitQueryParam.getComments());
        if(StringUtils.isNotBlank(remitQueryParam.getTradeType())) {
            queryMap.put("tradeType", remitQueryParam.getTradeType());
        }
        if(StringUtils.isNotBlank(remitQueryParam.getCapitalInfo())) {
            queryMap.put("capitalInfo", remitQueryParam.getCapitalInfo());
        }
        if(!CheckUtils.isEmpty(remitQueryParam.getStatus())) {
            if("REVERSED".equals(remitQueryParam.getStatus())) {
                queryMap.put("status","SUCCESS");
                queryMap.put("reversed","1");
            }else {
                queryMap.put("reversednull","0");
            }
        }
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("queryRemitOrderList", queryParam);

    }
    @Autowired
    private RemitOrderService remitOrderService;

    /**
     * 下载
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(RemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("开始下载付款记录，请求参数{}", JSON.toJSONString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "下级商户编号不能空");
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant()) {
                param.setPlatformType(PlatformTypeEnum.RED_PLATFORM_MERCHANT.name());
            }
            param = dealParams(param);
            if(StringUtils.isEmpty(param.getCreateStartDate()) || StringUtils.isEmpty(param.getCreateEndDate())){
                if(StringUtils.isEmpty(param.getBatchNo()) && StringUtils.isEmpty(param.getRequestNo())){
                    //如果时间范围是空的,那么批次号和订单号必填
                    throw UnionAccountException.PARAM_REQUIRED_ERROR.newInstance("缺少必要的请求参数");
                }
            }
            StringBuilder desc = new StringBuilder();
            Boolean capitalManageOpen = getCapitalManage(param.getCustomerNumber());
            desc.append("付款订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            //查询的截止日期 大于 当前时间 则替换当前时间为结束时间
            if (StringUtils.isNotBlank(param.getCreateEndDate())) {
                Date current = new Date();
                if (DateUtils.parseDate(param.getCreateEndDate(), DateUtils.DATE_FORMAT_DATEONLY).compareTo(current) > 0) {
                    param.setCreateEndDate(DateUtils.toString(current, DateUtils.DATE_FORMAT_DATETIME));
                }
            }
            if ("appointSync".equals(param.getSyncType())) {
                new RemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-", remitOrderService, merchantRemoteService, capitalManageOpen).syncDownload(request, response);
            } else {
                new RemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-", remitOrderService, merchantRemoteService, capitalManageOpen).download(request, response);
            }
            logger.info("下载付款记录excel已完成");
        } catch (Throwable ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    @RequestMapping(value = "/batchDownloadElectronic")
    @ResponseBody
    public void batchDownloadElectronic(RemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("开始批量下载付款电子回单，请求参数{}", JSON.toJSONString(param));
            long start = System.currentTimeMillis();
            response.setCharacterEncoding("utf-8");
            String currentCustomerNumber = getCurrentCustomerNumber();
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "下级商户编号不能空");
            param = dealParams(param);
            String description = String.format("电子回单[%s]-%s~%s",currentCustomerNumber,param.getCreateStartDate(),param.getCreateEndDate());

            Map<String, Object> queryMap = com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils.toMapWithResourceType(param);
            if(!CheckUtils.isEmpty(param.getStatus())) {
                if("REVERSED".equals(param.getStatus())) {
                    queryMap.put("status","SUCCESS");
                    queryMap.put("reversed","1");
                }else {
                    queryMap.put("reversednull","0");
                }
            }

            new RemitElectronicReceiptDownloader(getCurrentUser(), queryMap, description, "付款订单查询-", merchantRemoteService,remitOrderService).download(request, response);
            long end = System.currentTimeMillis();
            logger.info("批量下载电子回单已完成,耗时{}s",(end-start)/1000);
        } catch (Throwable ex) {
            logger.error("批量下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    @RequestMapping(value = "/downloadBatchElectronic")
    @ResponseBody
    public void downloadBatchElectronic(RemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("开始下载付款电子回单，请求参数{}", JSON.toJSONString(param));
        try {
            String batchNo = param.getBatchNo();
            if (StringUtils.isBlank(batchNo)) {
                logger.error("下载[批量电子回单]  batchNo 为空 batchNo={}", batchNo);
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("请输入批次号后进行下载");
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "下级商户编号不能空");
            long start = System.currentTimeMillis();
            BatchReceiptRespDTO respDTO;
            try {
                BatchReceiptReqDTO reqDTO = new BatchReceiptReqDTO();
                reqDTO.setMerchantNo(param.getCustomerNumber());/*查询作为交易商编的数据*/
                reqDTO.setBizType(TradeTypeEnum.PAY);
                reqDTO.setBatchNo(batchNo);
                reqDTO.setGatherType(GatherTypeEnum.BATCH_NO);
                respDTO = receiptFacade.getBatchReceipt(reqDTO);
            } catch (Exception e) {
                logger.error("下载[批量电子回单] PDF异常", e);
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("下载PDF失败");
            }
            if (null == respDTO) {
                logger.error("下载[批量电子回单] 获取pdf为 null !respDTO={}", respDTO);
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("下载PDF失败");
            }
            if (!com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode.SUCCESS.equals(respDTO.getReturnCode())) {
                logger.error("下载[批量电子回单] 获取pdf失败!respDTO={}", respDTO);
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance(respDTO.getReturnMsg());
            }
            String data = respDTO.getData();
            BASE64Decoder decoder = new BASE64Decoder();
            //Base64解码
            byte[] len = decoder.decodeBuffer(data);
            for (int i = 0; i < len.length; ++i) {
                //调整异常数据
                if (len[i] < 0) {
                    len[i] += 256;
                }
            }
            /*在开始写入的那一刻再定义这些，不然影响异常的弹窗*/
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + param.getCustomerNumber()+"_"+batchNo + ".pdf");
            OutputStream out = response.getOutputStream();
            out.write(len);
            out.flush();
            out.close();
            long end = System.currentTimeMillis();
            logger.info("下载[批量电子回单]耗时{}ms", (end - start));
        } catch (Throwable ex) {
            logger.error("下载[批量电子回单] 异常， 来个弹窗 ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }

    }

    public static Map<String, Object> toMapWithResourceType(Object object) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                resultMap.put(field.getName(), field.get(object));
            } catch (Throwable e) {
                logger.error("toMapWithResourceType-error",e);
            }
        }
        return resultMap;
    }

    private RemitQueryParam dealParams(RemitQueryParam param){
        String status = param.getStatus();
        if (status.equals(RemitStatusEnum.REQUEST_ACCEPT.name())) {
            status = RemitStatusEnum.REQUEST_ACCEPT.name() + "," + RemitStatusEnum.REQUEST_RECEIVE.name();
            param.setStatus(status);
        }

        if(StringUtils.isNotBlank(param.getCreateEndDate())){
            param.setCreateEndDate(DateUtil.addDay(param.getCreateEndDate()));
        }
        return param;
    }


    /**
     * 查询付款汇总
     *
     * @param remitQueryParam
     * @param resMsg
     */
    private ResponseMessage queryRemitOrderListSum(RemitQueryParam remitQueryParam, ResponseMessage resMsg) {

        Map<String, Object> queryMap = com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils.toMapWithResourceType(remitQueryParam);
        queryMap.put("customerNumber", remitQueryParam.getCustomerNumber());
        queryMap.put("remark",remitQueryParam.getRemark());
        queryMap.put("comments",remitQueryParam.getComments());
        queryMap.put("notFirstProductCode","PAYMENT_SUPPLIER");
        if(!CheckUtils.isEmpty(remitQueryParam.getStatus())) {
            if("REVERSED".equals(remitQueryParam.getStatus())) {
                queryMap.put("status","SUCCESS");
                queryMap.put("reversed","1");
            }else {
                queryMap.put("reversednull","0");
            }
        }
        List<Map<String, Object>> withOrderListSum = null;
        try {
            withOrderListSum = QueryServiceUtil.query("accountTradeService", "queryRemitOrderListSum", queryMap);
        } catch (Exception e) {
            logger.error("queryRemitOrderListSum-参数异常", e);
            // 直接把异常信息返回
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }

        // 如果查询结果不为空的话
        if (withOrderListSum != null && !withOrderListSum.isEmpty()) {
            Map<String, Object> sumResult = withOrderListSum.get(0);
            String sum_amount = sumResult.get("sum_amount").toString();
            String sum_fee = sumResult.get("sum_fee").toString();
            if(StringUtils.isEmpty(sum_fee)){
                sum_fee = "0";
            }
            if(StringUtils.isEmpty(sum_amount)){
                sum_amount="0";
            }
            resMsg.getData().put("sum_count", sumResult.get("sum_count").toString());// 总笔数
            resMsg.getData().put("sum_amount", new BigDecimal(sum_amount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总金额
            resMsg.getData().put("sum_fee", new BigDecimal(sum_fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总手续费
            resMsg.getData().put("totalCount", sumResult.get("sum_count").toString());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", "0.00");// 总金额
            resMsg.getData().put("sum_fee", "0.00");// 总手续费
        }
        return resMsg;
    }

    private List<RemitBatchDetailReqDTO> buildBatchDetailData(Workbook wb, String ipAddress, String tradeType) {
        Sheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
        Row headRow = sheet.getRow(0);
        Cell addedHeadCell = headRow.createCell(10);
        addedHeadCell.setCellValue("异常原因");
        List<RemitBatchDetailReqDTO> batchDetailList = new ArrayList<>();
        logger.info("[批量代付] [明细校验] 待检查的行数为 {}", sheet.getLastRowNum());
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            RemitBatchDetailReqDTO detail = new RemitBatchDetailReqDTO();
            Row row = sheet.getRow(i);
            if (/* 去除row 无值的情况 */ row == null) {
                continue;
            } else {
                // 空数据过滤
                StrategyContext strategyContext = new StrategyContext(new BacNewBatchRemitCheckBlank(cellStyle, wb), row);
                String context = strategyContext.context();
                if (StringUtils.equals("true", context)) {
                    logger.info("[批量代付] [明细校验] 下标 {} 行为空行, 执行跳过.", i);
                    continue;
                }
            }
            detail.setRequestNo(POIUtil.getStrValue(row.getCell(0)));
            detail.setReceiverAccountNo(POIUtil.getStrValue(row.getCell(1)));
            detail.setReceiverAccountName(POIUtil.getStrValue(row.getCell(2)));
            detail.setBankName(POIUtil.getStrValue(row.getCell(3)));
            detail.setBankAccountType(POIUtil.getStrValue(row.getCell(4)));
            detail.setOrderAmount(POIUtil.getStrValue(row.getCell(5)));
            detail.setBankBranchName(POIUtil.getStrValue(row.getCell(6)));
            detail.setComments(POIUtil.getStrValue(row.getCell(7)));
            detail.setTradeType(tradeType);
            detail.setOrderDesc(POIUtil.getStrValue(row.getCell(8)));
            detail.setLineNumber(i);
            detail.setClientIp(ipAddress);
            detail.setBankCnapsCode(POIUtil.getStrValue(row.getCell(9)));
            batchDetailList.add(detail);
        }
        logger.info("[批量代付] [明细校验] 整合完毕, 生成明细 {}", batchDetailList.size());
        return batchDetailList;
    }


    private Map<String, Object> adaptReturnResult(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }

        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (null != detail.get("order_amount")) {
                detail.put("order_amount", nf.format(new BigDecimal(detail.get("order_amount").toString())));
            }
            if(null!= detail.get("receive_amount")){
                detail.put("receive_amount", nf.format(new BigDecimal(detail.get("receive_amount").toString())));
            }
            if (null != detail.get("fee")) {
                if (null != detail.get("fee_undertaker_merchant_no") && !detail.get("merchant_no").toString().equals(detail.get("fee_undertaker_merchant_no").toString())) {
                    detail.put("fee", "");
                }else{
                    detail.put("fee", nf.format(new BigDecimal(detail.get("fee").toString())));
                }
            }
            //下单时间
            Object obj = detail.get("create_time");
            if (null != obj) {
                if (obj instanceof String) {
                    String str = String.valueOf(obj);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("create_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("create_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (obj instanceof Timestamp) {
                    detail.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

            //完成时间
            Object finishTime = detail.get("finish_time");
            if (null != finishTime) {
                if (finishTime instanceof String) {
                    String str = String.valueOf(finishTime);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("finish_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("finish_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (finishTime instanceof Timestamp) {
                    detail.put("finish_time", DateUtils.toSqlTimestampString((Timestamp) finishTime, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

            //付款时间
            Object reverseTime = detail.get("reverse_time");
            if (null != reverseTime) {
                if (reverseTime instanceof String) {
                    String str = String.valueOf(reverseTime);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("reverse_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("reverse_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (reverseTime instanceof Timestamp) {
                    detail.put("reverse_time", DateUtils.toSqlTimestampString((Timestamp) reverseTime, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

            String receiver_account_no = (String)detail.get("receiver_account_no");

            receiver_account_no = AESUtils.decryptWithBase64(receiver_account_no);

            String receiver_account_name = detail.get("receiver_account_name").toString();
            receiver_account_name= AESUtils.decryptWithBase64(receiver_account_name);
            /* 付款记录查询 结果转化银行名称 */
            HeadBankDTO headBankDTO = searchBankInfoFacade.searchHeadBankInfoByCode(detail.get("receiver_bank_code").toString());
            StringBuffer buffer = new StringBuffer();
            if(headBankDTO != null){
                String bankName = headBankDTO.getBankName();
                if (StringUtils.isNotBlank(bankName)) {
                    buffer.append(NumberUtils.subStringWithMaxLength(bankName, 45));
                    buffer.append("—");
                }
            }
            if(StringUtil.isNotEmpty(receiver_account_name)){
                buffer.append(NumberUtils.subStringWithMaxLength(receiver_account_name, 24));
            }
            if(StringUtil.isNotEmpty(receiver_account_no)){
                buffer.append("(" + receiver_account_no.substring(receiver_account_no.length() - 4, receiver_account_no.length()) + ")");
            }
            detail.put("receiver_account_name",buffer);
            if(detail.get("batch_no") == null){
                detail.put("batch_no","");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return detail;
    }

    private void writeBackExcel(Workbook wb,List<RemitBatchDetailRespDTO> batchDetailList) {
        Sheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        //填充单元格
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        //填黄色
        cellStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
        //新文件追加一列
        Row headRow = sheet.getRow(0);
        Cell errorCellTitle = headRow.createCell(10);
        errorCellTitle.setCellValue("异常原因");
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            for (RemitBatchDetailRespDTO dto : batchDetailList) {
                Integer lineNumber = dto.getLineNumber();
                if (lineNumber == i) {
                    Row row = sheet.getRow(i);
                    String requestNo = dto.getRequestNo();
                    if (!(requestNo == null)) {
                        Cell cell = row.getCell(0);
                        cell.setCellStyle(cellStyle);
                    }
                    //账号
                    String bankAccountNo = dto.getReceiverAccountNo();
                    if (!(bankAccountNo == null)) {
                        Cell cell = row.getCell(1);
                        cell.setCellStyle(cellStyle);
                    }
                    //开户名
                    String bankAccountName = dto.getReceiverAccountName();
                    if (!(bankAccountName == null)) {
                        Cell cell = row.getCell(2);
                        cell.setCellStyle(cellStyle);
                    }
                    //银行名称
                    String bankName = dto.getBankName();
                    if (!(bankName == null)) {
                        Cell cell = row.getCell(3);
                        cell.setCellStyle(cellStyle);
                    }
                    //卡类型
                    String bankAccountType = dto.getBankAccountType();
                    if (!(bankAccountType == null)) {
                        Cell cell = row.getCell(4);
                        cell.setCellStyle(cellStyle);
                    }
                    //金额
                    String amountStr = dto.getOrderAmount();
                    if (!(amountStr == null)) {
                        Cell cell = row.getCell(5);
                        cell.setCellStyle(cellStyle);
                    }
                    //支行名称
                    String bankBranchName = dto.getBankBranchName();
                    if (!(bankBranchName == null)) {
                        Cell cell = row.getCell(6);
                        cell.setCellStyle(cellStyle);
                    }
                    //银行附言
                    String comments = dto.getComments();
                    if (!(comments == null)) {
                        Cell cell = row.getCell(7);
                        cell.setCellStyle(cellStyle);
                    }
                    //订单描述
                    String orderDesc = dto.getOrderDesc();
                    if (!(orderDesc == null)) {
                        Cell cell = row.getCell(8);
                        cell.setCellStyle(cellStyle);
                    }
                    String cnapsCode = dto.getCnapsCode();
                    if (!(cnapsCode == null)) {
                        Cell cell = row.getCell(9);
                        cell.setCellStyle(cellStyle);
                    }
                    //异常信息
                    String errorMsg = dto.getErrorMsg();
                    if (StringUtils.isNotBlank(errorMsg)) {
                        Cell cell = row.getCell(10);
                        if (null == cell) {
                            cell = row.createCell(10);
                        }
                        cell.setCellValue(errorMsg);
                    }
                }
            }
        }
    }

    private boolean checkHead(Row head) {
        if (head == null) {
            return false;
        }
        for (int index = 0; index < headStr.length; index++) {
            String strValue = POIUtil.getStrValue(head.getCell(index));
            // 兼容历史模版
            if (index == 3 && Objects.equals(strValue, "开户银行（必填）")) {
                continue;
            }
            if (index == 9 && org.springframework.util.StringUtils.isEmpty(strValue)) {
                continue;
            }
            if (!StringUtils.equals(strValue, headStr[index])) {
                return false;
            }
        }
        return true;
    }

    public Map<String, String> queryAllHeadBank(){
        try {
            if (!CheckUtils.isEmpty(SmartCacheUtils.get(RemitConstant.MP_BOSS_ALL_BANK_RESULT_CACHE))) {
                return (HashMap<String, String>)SmartCacheUtils.get(RemitConstant.MP_BOSS_ALL_BANK_RESULT_CACHE);
            }
        } catch (Exception e) {
            logger.error("获取所有银行信息缓存失败，异常信息为={}", e);
        }
        Map<String, String> returnMap = null;
        try {
            /*付款复核的查询列表 暂时先不改*/
            Map<String, String> bankMap = bankInfoQueryFacade.queryAllHeadBank();
            logger.debug("银行列表返回 bankMap={}",JSON.toJSONString(bankMap));
            // 根据Value值排序
            returnMap = sortMap(bankMap);
            logger.debug("银行列表排序返回 returnMap={}",JSON.toJSONString(returnMap));
        }catch (Exception e){
            logger.error("query BankInfoQueryFacade error ",e);
        }
        try {
            if(!CheckUtils.isEmpty(returnMap)) {
                SmartCacheUtils.set(RemitConstant.MP_BOSS_ALL_BANK_RESULT_CACHE, returnMap, 600);
            }
        }catch (Exception e){
            logger.error("设置所有银行信息缓存失败，异常信息为={}",e);
        }
        return returnMap;
    }

    /**
     * @param map
     * @return
     */
    private Map<String, String> sortMap(Map<String, String> map) {

        List<Map.Entry<String, String>> entries = new ArrayList<Map.Entry<String, String>>(map.entrySet());
        java.util.Collections.sort(entries, new Comparator<Map.Entry<String, String>>() {

            @Override
            public int compare(Map.Entry<String, String> obj1, Map.Entry<String, String> obj2) {
                int o1 = 0, o2 = 0;
                try {
                    o1 = getCnAscii(obj1.getValue().toCharArray()[0]);
                    o2 = getCnAscii(obj2.getValue().toCharArray()[0]);
                } catch (UnsupportedEncodingException e) {
                    o1 = 0;
                    o2 = 0;
                }
                if (o1 == o2) {
                    return 0;
                } else {
                    return o1 > o2 ? 1 : -1;
                }
            }
        });

        Map<String, String> map_link = new LinkedHashMap<String, String>();
        for (Map.Entry<String, String> entity : entries) {
            map_link.put(entity.getKey(), entity.getValue());
        }
        return map_link;

    }

    /**
     * @param cn
     * @throws UnsupportedEncodingException
     */
    private int getCnAscii(char cn) throws UnsupportedEncodingException {
        byte[] bytes = (String.valueOf(cn)).getBytes("GBK");
        if (bytes == null || bytes.length > 2 || bytes.length <= 0) {
            return 0;
        }
        if (bytes.length == 1) {
            return bytes[0];
        }
        if (bytes.length == 2) {
            int hightByte = 256 + bytes[0];
            int lowByte = 256 + bytes[1];

            return (256 * hightByte + lowByte) - 256 * 256;
        }

        return 0;
    }

    private HeadBankDTO queryBankInfo(String bankCode){
        HeadBankDTO headBankDTO = searchBankInfoFacade.searchHeadBankInfoByCode(bankCode);
        return headBankDTO;
    }


    /**
     * 处理数据
     * @param remitOrder
     * @return
     */
    private OrderParam handleResult(RemitOrder remitOrder) {
        OrderParam orderParam = new OrderParam();
        orderParam.setParentMerchantNo(remitOrder.getParentMerchantNo());
        orderParam.setOrderNo(remitOrder.getOrderNo());
        orderParam.setInitiateMerchantNo(remitOrder.getInitiateMerchantNo());
        orderParam.setTradeType(TradeTypeEnum.PAY);
        return orderParam;
    }


    @RequestMapping(value = "/queryHistoryOrder")
    public ModelAndView view(HttpServletRequest request,HttpServletResponse response) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("/batchRemit/remitQueryHistory");
        mav.addObject("UIWebRootUrl",WebPropertiesHolder.getUIWebRootUrl());
        cookieService.addCookie(request,response,"/bac-app");
        return mav;
    }

    /**
     * 是否有授信用款业务
     *
     * @param merchantNo
     * @return
     */
    private Boolean getCapitalManage(String merchantNo) {
        QueryMatchingUseFundReqDTO reqDTO = new QueryMatchingUseFundReqDTO();
        reqDTO.setMerchantNo(merchantNo);
        reqDTO.setUseWay("REMIT");
        QueryMatchingUseFundRespDTO respDTO = capitalManageService.queryUseFundConfig(reqDTO);
        if (AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(respDTO.getReturnCode())) {
            return false;
        } else {
            return true;
        }
    }


    @RequestMapping(value = "/query/remitDetail",method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryRedVersionRemitDetail(HttpServletRequest request) {
        try {
            String orderNo = request.getParameter("orderNo");
            String batchNo = request.getParameter("batchNo");
            String requestNo = request.getParameter("requestNo");
            String querySubMerchant = request.getParameter("querySubMerchant");
            String customerNumber = request.getParameter("customerNumber");
            logger.info("查询付款订单明细 ：batchNo={},orderNo={},querySubMerchant={},customerNumber={}", batchNo, orderNo,querySubMerchant,customerNumber);
            RemitOrderQueryReqDTO dto = new RemitOrderQueryReqDTO();
            PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber),"商户编号不能为空");
            dto.setInitiateMerchantNo(customerNumber);
            dto.setParentMerchantNo(customerNumber);

            dto.setOrderNo(orderNo);
            dto.setBatchNo(batchNo);
            dto.setRequestNo(requestNo);
            RemitOrderQueryRespDTO remitOrderQueryRespDTO = remitFacade.queryRemitOrder(dto);
            RemitResponseParam param = new RemitResponseParam();
            if (remitOrderQueryRespDTO != null) {
                String returnCode = remitOrderQueryRespDTO.getReturnCode();
                if ("UA00000".equals(returnCode)) {
                    BeanUtils.copyProperties(remitOrderQueryRespDTO, param);
                    RemitOrderStatusEnum status = remitOrderQueryRespDTO.getStatus();
                    if (status == RemitOrderStatusEnum.SUCCESS) {
                        param.setStatus(RemitStatusEnum.SUCCESS.getDesc());
                    } else if (status == RemitOrderStatusEnum.FAIL) {
                        param.setStatus(RemitStatusEnum.FAIL.getDesc());
                    } else if (status == RemitOrderStatusEnum.CANCELED) {
                        param.setStatus(RemitStatusEnum.CANCELED.getDesc());
                    } else if (status == RemitOrderStatusEnum.REMITING) {
                        param.setStatus(RemitStatusEnum.REMITING.getDesc());
                    } else {
                        param.setStatus(RemitStatusEnum.REQUEST_ACCEPT.getDesc());
                    }
                    if (!CheckUtils.isEmpty(remitOrderQueryRespDTO.getFinishTime())) {
                        param.setFinishTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getFinishTime()));
                    }
                    param.setOrderAmount(remitOrderQueryRespDTO.getOrderAmount().toString());
                    if (null != remitOrderQueryRespDTO.getFee()) {
                        param.setFee(remitOrderQueryRespDTO.getFee().toString() + "元");
                    } else {
                        param.setFee("--");
                    }

                    String receiverAccountNo = remitOrderQueryRespDTO.getReceiverAccountNo();
                    /* 付款记录 跳转 查询明细*/
                    HeadBankDTO headBankDTO = searchBankInfoFacade.searchHeadBankInfoByCode(remitOrderQueryRespDTO.getReceiverBankCode());
                    if (headBankDTO != null) {
                        String bankName = headBankDTO.getBankName();
                        if (StringUtils.isNotBlank(bankName) && StringUtils.isNotBlank(receiverAccountNo)) {
                            param.setReceiverAccountNo(bankName + "(" + receiverAccountNo.substring(receiverAccountNo.length() - 4, receiverAccountNo.length()) + ")");
                        }
                    }
                    param.setOrderInfo(remitOrderQueryRespDTO.getOrderInfo());
                    param.setOrderTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getOrderTime()));
                    RemitTypeEnum receiveType = remitOrderQueryRespDTO.getReceiveType();
                    if (receiveType == RemitTypeEnum.REAL_TIME) {
                        param.setReceiveType("实时到账");
                    } else if (receiveType == RemitTypeEnum.TWO_HOUR) {
                        param.setReceiveType("2小时到账");
                    } else if (receiveType == RemitTypeEnum.NEXT_DAY) {
                        param.setReceiveType("次日到账");
                    }

                    boolean isReversed = remitOrderQueryRespDTO.getIsReversed();
                    if (isReversed) {
                        param.setReverseTime(DateUtil.formatByDateTimePattern(remitOrderQueryRespDTO.getReverseTime()));
                        param.setStatus("银行冲退");
                    } else {
                        param.setReverseTime("--");
                    }

                    if (StringUtils.isBlank(param.getFailReason())) {
                        param.setFailReason("--");
                    }
                    if (StringUtils.isEmpty(param.getFinishTime())) {
                        param.setFinishTime("--");
                    }
                    if (StringUtils.isEmpty(param.getComments())) {
                        param.setComments("--");
                    }
                    if (StringUtils.isEmpty(param.getOrderInfo())) {
                        param.setOrderInfo("--");
                    }

                    if (StringUtils.isEmpty(param.getBatchNo())) {
                        param.setBatchNo("--");
                    }

                    if (StringUtils.isEmpty(param.getRemark())) {
                        param.setRemark("--");
                    }

                    if (remitOrderQueryRespDTO.getTradeType() == null) {
                        param.setTradeType(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.ENTERPRISE_PAYMENT.getDesc());
                    } else {
                        if (com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType()) != null) {
                            param.setTradeType(com.yeepay.g3.facade.unionaccount.trade.enumtype.TradeTypeEnum.valueOf(remitOrderQueryRespDTO.getTradeType()).getDesc());
                        }
                    }

                    param.setOrderNo(remitOrderQueryRespDTO.getRequestNo());
                    param.setCapitalInfo(remitOrderQueryRespDTO.getCapitalInfo());
                }
            }
            logger.info("查询订单明细返回，remitResponseParam={}", JSON.toJSONString(param));
            return BaseRespDTO.success(param);
        } catch (YeepayBizException e) {
            logger.warn("查询订单明细返回,业务异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("查询订单明细返回,异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }


    @RequestMapping(value = "/query/init/info",method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO initInfo(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        map.put("hasAvailableStatus", false);
        map.put("hasRemitProduct", false);
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            logger.info("初始化代付信息 请求参数为 ：" + currentCustomerNumber);
            String markProductCode = businessCheckRemoteService.queryMarketProduct(currentCustomerNumber);
            //查询开通产品
            List<String> secondProductList = buildRemitProduct(markProductCode, businessCheckRemoteService);
            if (!CollectionUtils.isEmpty(secondProductList)) {
                map.put("hasRemitProduct", true);
            }
            //查询可用余额
            BigDecimal balance = null;
            AccountInfoRespDTO responseDto = getRemitBalance(currentCustomerNumber);
            if (responseDto != null && "UA00000".equals(responseDto.getReturnCode())) {
                String accountStatus = responseDto.getAccountStatus();
                if ("AVAILABLE".equals(accountStatus) || "FROZEN_CREDIT".equals(accountStatus)) {
                    map.put("hasAvailableStatus", true);
                    balance = responseDto.getBalance();
                }
            } else {
                balance = BigDecimal.ZERO;
            }
            map.put("amount", NumberUtils.formateNum(null, balance));
            logger.info("初始化代付信息 请求参数为={},返回信息为={}", currentCustomerNumber, new Gson().toJson(map));
            return BaseRespDTO.success(map);
        } catch (Exception e) {
            logger.error("初始化代付信息,商编为" + currentCustomerNumber + "异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }
}
