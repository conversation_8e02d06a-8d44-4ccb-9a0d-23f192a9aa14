package com.yeepay.g3.app.account.pay.mboss.controller.file;

import com.yeepay.g3.app.account.pay.mboss.dto.TransferParamCheckDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.TransferParamDTO;
import com.yeepay.g3.app.account.pay.mboss.remote.FileStorageService;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.FileUtil;
import com.yeepay.g3.app.account.pay.mboss.utils.POIUtil;
import com.yeepay.g3.app.account.pay.mboss.utils.RedisUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Component
public class BatchTransferAccountFile extends FileAnalysis<TransferParamDTO> {
    private static final Logger LOGGER = LoggerFactory.getLogger(BatchTransferAccountFile.class);

    private FileStorageService fileStorageService = new FileStorageService();

    final static String[] headStr = {"转出方易宝商编（必填）", "转出签约名（必填）", "转入方易宝商编（必填）", "转入签约名（必填）", "金额（必填，单位：元）", "用途（必填）", "服务费承担方（选填）"};

    @Override
    public boolean checkHead(Row head) {
        if (head == null) {
            return false;
        }
        for (int i1 = 0; i1 < headStr.length; i1++) {
            String strValue = POIUtil.getStrValue(head.getCell(i1));
            if (!StringUtils.equals(strValue.trim(), headStr[i1].trim())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<TransferParamDTO> analysisData(Workbook wb) throws IllegalAccessException {
        Sheet sheet = wb.getSheetAt(0);
        List<TransferParamDTO> batchDetailList = new ArrayList<>();
        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            TransferParamDTO reqDto = new TransferParamDTO();
            for (Field declaredField : reqDto.getClass().getDeclaredFields()) {
                if (declaredField.isAnnotationPresent(FileHandle.class)) {
                    FileHandle fileHandle = declaredField.getAnnotation(FileHandle.class);
                    int dex = fileHandle.index();
                    String strValue1 = POIUtil.getStrValue(row.getCell(dex));
                    if(!StringUtils.isEmpty(strValue1)){
                        declaredField.setAccessible(true);
                        declaredField.set(reqDto, strValue1);
                    }
                }
            }
            batchDetailList.add(reqDto);
        }
        //这里只是因为 文件模板会产生相关的空行 暂时采用这个方法进行全空行过滤
        List<TransferParamDTO> collect = batchDetailList.stream().filter(item -> {
            return !(StringUtils.isBlank(item.getAmount()) && StringUtils.isBlank(item.getFromMerchantNo())
                    && StringUtils.isBlank(item.getFromMerchantName()) && StringUtils.isBlank(item.getToMerchantNo())
                    && StringUtils.isBlank(item.getToMerchantName()));
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<TransferParamDTO> dealFile(MultipartFile file) throws Exception {
        Workbook wb = null;
        try {
            wb = WorkbookFactory.create(file.getInputStream());
        } catch (Exception e) {
            LOGGER.error("生成异常", e);
            throw new IllegalArgumentException("文件打开异常");
        }
        Sheet sheet = wb.getSheetAt(0);
        Row head = sheet.getRow(0);
        //请求头校验
        if (!checkHead(head)) {
            throw new IllegalArgumentException("文件表头异常");
        }
        try {
            File fileSource = FileUtil.multipartFileToFile(file);
            File destFile = new File(System.getProperty("java.io.tmpdir") + 1 + ".xls");
            FileUtil.copyFile(fileSource, destFile);
            InputStream inputStream = null;
            inputStream = new FileInputStream(destFile);
            Workbook wbCopy = WorkbookFactory.create(inputStream);
            List<TransferParamDTO> transferParamDTOS = analysisData(wbCopy);
            return transferParamDTOS;
        } catch (Exception e) {
            LOGGER.error("复制后的文件读取失败");
            throw new Exception("复制后的文件读取失败");
        }
    }

    public void writeErrorFile(List<TransferParamCheckDTO> collect, String token, File file) throws Exception {
        //文件复制
        File destFile = new File(System.getProperty("java.io.tmpdir") + token + ".xls");
        InputStream inputStream = null;
        FileUtil.copyFile(file, destFile);
        try {
            inputStream = new FileInputStream(destFile);
        } catch (Exception e) {
            LOGGER.error("复制后的文件读取失败");
            throw new Exception("复制后的文件读取失败");
        }
        //处理单条记录
        Workbook wb = WorkbookFactory.create(inputStream);
        Sheet sheet = wb.getSheetAt(0);


        //新文件追加一列
        Row headRow = sheet.getRow(0);
        Cell cell1 = headRow.createCell(7);
        cell1.setCellValue("异常原因");

        for (int i = 1; i <= collect.size(); i++) {
            int index = i - 1;
            TransferParamCheckDTO transferParamCheckDTO = collect.get(index);
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            CellStyle cellStyle = wb.createCellStyle();
            if(StringUtils.isNotBlank( transferParamCheckDTO.getErrorMsg())){
                //填充单元格
                cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
                //填黄色
                cellStyle.setFillForegroundColor(HSSFColor.YELLOW.index);
            }
            for (Field declaredField : transferParamCheckDTO.getClass().getDeclaredFields()) {
                if (declaredField.isAnnotationPresent(FileHandle.class)) {
                    if (declaredField.isAnnotationPresent(FileHandle.class)) {
                        FileHandle fileHandle = declaredField.getAnnotation(FileHandle.class);
                        int dex = fileHandle.index();
                        declaredField.setAccessible(true);
                        Object o = declaredField.get(transferParamCheckDTO);
                        if (!(o == null)) {
                            Cell cell = row.getCell(dex);
                            if (cell == null) {
                                cell = row.createCell(dex);
                            }
                            cell.setCellStyle(cellStyle);
                            cell.setCellValue(String.valueOf(o));
                        }
                    }
                }
            }
        }
        //发往云存储
        FileOutputStream outputStream = null;
        try {
            //如果全部校验通过，则上传原文件； 有校验失败，则上传新文件
            outputStream = new FileOutputStream(destFile);
            wb.write(outputStream);
        } catch (Exception e) {
            LOGGER.error("系统处理异常", e);
            throw new Exception("系统处理异常");
        } finally {
            outputStream.close();
            LOGGER.info("开始发往云存储");
            Map<String, String> map = new HashMap<>();
            map.put("fileName", file.getName());
            RedisUtils.hmset(token, map, ConfigUtils.getBatchSendExpire());
            fileStorageService.uploadFileCloud(token + ".xls", new FileInputStream(destFile));
        }

    }
}
