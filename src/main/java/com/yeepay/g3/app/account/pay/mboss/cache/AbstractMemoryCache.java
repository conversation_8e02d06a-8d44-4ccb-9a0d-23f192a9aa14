package com.yeepay.g3.app.account.pay.mboss.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import java.util.concurrent.ExecutionException;


/**
 * @ClassName: AbstractMemoryCache
 * @Description: 缓存抽象类
 * <AUTHOR>
 * @Date 2022/11/9
 * @Version 1.0
 */
public abstract class AbstractMemoryCache<K, V> implements InitializingBean, DisposableBean {

    private LoadingCache<K, V> cache;

    /**
     * Get CacheBuilder
     *
     * @param cacheBuilder
     * @return
     */
    protected abstract CacheBuilder<Object, Object> getCacheBuilder(CacheBuilder<Object, Object> cacheBuilder);

    /**
     * Get CacheLoader
     *
     * @return
     */
    protected abstract CacheLoader<K, V> getCacheLoader();

    public V getCache(K k) throws ExecutionException {
        return this.cache.get(k);
    }

    public void putCache(K k, V v) {
        this.cache.put(k, v);
    }

    @Override
    public void destroy() throws Exception {
        this.cache.cleanUp();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        CacheLoader<K, V> cacheLoader = this.getCacheLoader();
        CacheBuilder<Object, Object> cacheBuilder = this.getCacheBuilder(CacheBuilder.newBuilder());
        this.cache = cacheBuilder.build(cacheLoader);
    }
}
