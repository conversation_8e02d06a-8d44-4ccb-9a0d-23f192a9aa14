package com.yeepay.g3.app.account.pay.mboss.dao;

import java.util.List;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorEntity;
import com.yeepay.g3.utils.persistence.GenericDao;

public interface DirectTransferOperatorDao extends GenericDao<DirectTransferOperatorEntity>  {
    
    
    List<DirectTransferOperatorEntity> queryByDebitCustomerNo(String debutCustomerNo);
    
    DirectTransferOperatorEntity queryByDebitCustomerNoAndLoginName(String debutCustomerNo,String loginName);
    
}
