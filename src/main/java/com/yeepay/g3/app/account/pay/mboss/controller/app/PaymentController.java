package com.yeepay.g3.app.account.pay.mboss.controller.app;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.app.req.PaymentReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.app.response.PaymentPageResultResponseDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.app.response.PaymentDetailResponseDTO;
import com.yeepay.g3.app.account.pay.mboss.service.PaymentService;
import com.yeepay.g3.app.account.pay.mboss.utils.PreCheck;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * title: 移动端付款查询<br>
 * description: 移动端付款查询<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 15:00
 */
@Controller
@RequestMapping("/app/payment/")
public class PaymentController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentController.class);

    @Autowired
    private PaymentService paymentService;


    /**
     * 查询付款订单列表
     *
     * @param param 请求参数
     * @return 分页请求结果
     */
    @RequestMapping(value = "queryPageList")
    @ResponseBody
    public BaseRespDTO<PaymentPageResultResponseDTO> queryPageList(PaymentReqDTO param) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[移动端]查询付款分页订单数据，param={},currentCustomerNumber={}", JSON.toJSONString(param), currentCustomerNumber);
        try {
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCreateStartDate()), "开始查询时间不能为空");
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCreateEndDate()), "结束查询时间不能为空");
            PreCheck.checkArgument(StringUtils.isNotBlank(currentCustomerNumber), "获取不到登录的商户信息,请重新登录");
        } catch (Exception e) {
            LOGGER.warn("[移动端]查询付款分页订单数据参数校验未通过，param={},currentCustomerNumber={},errorMsg={}", JSON.toJSONString(param), currentCustomerNumber, e.getMessage());
            return BaseRespDTO.failWith(e.getMessage());
        }
        param.setCustomerNumber(currentCustomerNumber);
        PaymentPageResultResponseDTO pageResult = paymentService.queryPageListByParam(param);
        return BaseRespDTO.successWith(pageResult);
    }

    /**
     * 查询付款详情
     *
     * @param orderId 请求参数
     * @return 分页请求结果
     */
    @RequestMapping(value = "queryDetail", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO<PaymentDetailResponseDTO> queryDetail(String orderId) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[移动端]查询付款详情 orderId={},currentCustomerNumber={}", orderId, currentCustomerNumber);
        try {
            PreCheck.checkArgument(StringUtils.isNotBlank(orderId), "订单id不能为空");
            PreCheck.checkArgument(StringUtils.isNotBlank(currentCustomerNumber), "获取不到登录的商户信息,请重新登录");
        } catch (Exception e) {
            LOGGER.warn("[移动端]查询付款详情参数校验未通过 orderId={},currentCustomerNumber={},errorMsg={}", orderId, currentCustomerNumber, e.getMessage());
            return BaseRespDTO.failWith(e.getMessage());
        }
        PaymentDetailResponseDTO result = paymentService.queryDetail(orderId, currentCustomerNumber);
        return BaseRespDTO.successWith(result);
    }

}
