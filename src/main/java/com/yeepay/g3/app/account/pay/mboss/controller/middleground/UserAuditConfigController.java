package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.yeepay.g3.app.account.pay.mboss.dto.UserAuditConfigSetReqDTO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.facade.mp.exception.ExceptionWrapper;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.QueryUserAuditConfigReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.SetUserAuditConfigReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.QueryUserAuditConfigRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.SetUserAuditConfigRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.UserAuditBizTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.UserAuditRequestSourceTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.facade.UserAuditConfigFacade;
import com.yeepay.g3.facade.unionaccount.manage.facade.UserAuditConfigManageFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-04-02 5:34 下午
 */
@Controller
@RequestMapping("/userAuditConfig")
public class UserAuditConfigController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserAuditConfigController.class);
    private static UserAuditConfigFacade userAuditConfigFacade = RemoteServiceFactory.getService(UserAuditConfigFacade.class);

    private static UserAuditConfigManageFacade userAuditConfigManageFacade = RemoteServiceFactory.getService(UserAuditConfigManageFacade.class);

    @RequestMapping(value = "/config", method = RequestMethod.GET)
    public ModelAndView configPage(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();
        ShiroUser user = getCurrentUser();
        QueryUserAuditConfigReqDTO queryReq = new QueryUserAuditConfigReqDTO();
        queryReq.setMerchantNo(user.getCustomerNumber());
        queryReq.setAuditBizType(UserAuditBizTypeEnum.REMIT);
        logger.info("userAuditConfig queryAuditConfig req={}", queryReq);
        QueryUserAuditConfigRespDTO resp = userAuditConfigFacade.queryAuditConfig(queryReq);
        logger.info("userAuditConfig queryAuditConfig resp={}", resp);
        if ("UA00000".equals(resp.getReturnCode())) {
            params.put("auditStatus", resp.getUserAuditConfig().getStatus());
        } else if ("UA30041".equals(resp.getReturnCode())) {
            params.put("auditStatus", "NONE");
        } else {
            throw new RuntimeException("查询复核配置异常！");
        }
        params.put("loginName", user.getLoginName());
        return new ModelAndView("/userAudit/config", params);
    }

    @ResponseBody
    @RequestMapping(value = "/config", method = RequestMethod.POST)
    public SetUserAuditConfigRespDTO config(HttpServletRequest request, @RequestBody UserAuditConfigSetReqDTO userAuditConfigSetReqDTO) {
        ShiroUser user = getCurrentUser();
        //短信验证
        try {
            SendSmsUtils.checkVaildFrequency(request, user.getUserId(), userAuditConfigSetReqDTO.getSmsCode(), "CONFIRM_SET_USER_AUDIT_CONFIG");
        } catch (ExceptionWrapper e) {
            SetUserAuditConfigRespDTO respDTO = new SetUserAuditConfigRespDTO();
            respDTO.setReturnCode("9999");
            respDTO.setReturnMsg(e.getMessage());
            return respDTO;
        }
        SetUserAuditConfigReqDTO reqDTO = new SetUserAuditConfigReqDTO();
        reqDTO.setUserAuditRequestSourceTypeEnum(UserAuditRequestSourceTypeEnum.SELF_SERVICE);
        reqDTO.setUserAuditBizTypeEnum(UserAuditBizTypeEnum.REMIT);
        reqDTO.setMerchantNo(getCurrentCustomerNumber());
        reqDTO.setAuditConfigStatus(userAuditConfigSetReqDTO.getRemitAuditStatus());
        SetUserAuditConfigRespDTO respDTO = userAuditConfigManageFacade.setAuditConfig(reqDTO);
        logger.info("userAuditConfig setAuditConfig reqDTO={},respDTO={}", reqDTO, respDTO);
        return respDTO;

    }
}
