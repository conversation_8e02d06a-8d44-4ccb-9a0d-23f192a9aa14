package com.yeepay.g3.app.account.pay.mboss.controller.middleground;


import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.helper.handler.BankAccountOperate;
import com.yeepay.g3.app.account.pay.mboss.helper.handler.BankAccountOperateFactory;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.AccountManageInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.BankAccountManageService;
import com.yeepay.g3.app.account.pay.mboss.service.CommonService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BankAccountManageDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.merchant_platform.dto.LegalPersonRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customerplatform.MerchantShuntQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.*;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.channel.MerchantSettleConfigRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.recharge.dto.response.QueryBankAccountRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.utils.lock.Lock;
import com.yeepay.utils.lock.impl.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @ClassName: BankAccountManageController
 * @Description: 银行账户管理
 * <AUTHOR>
 * @Date 2023/7/11
 * @Version 1.0
 */
@Controller
@Api(tags = "银行账户管理-API")
@RequestMapping("/bankAccount")
public class BankAccountManageController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BankAccountManageController.class);

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private BankAccountManageService bankAccountManageService;

    @Autowired
    private CommonService commonService;
    @Resource
    private BankAccountOperateFactory bankAccountOperateFactory;

    @Autowired
    private AccountManageInfoService accountManageInfoService;

    /**
     * 银行账户管理
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/manage")
    @ApiOperation(hidden = true,value="银行账户管理")
    public ModelAndView manage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("bankAccount/bankAccountManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("银行账户管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    /**
     * 银行进度查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/openProcess")
    @ApiOperation(hidden = true,value="银行进度查询")
    public ModelAndView openProcess(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("bankAccount/bankAccountOpen");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("银行进度查询页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 银行流水查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/bankTradeFlow")
    @ApiOperation(hidden = true,value="银行流水查询")
    public ModelAndView bankTradeFlow(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("bankAccount/bankTradeFlow");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("银行流水查询页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    /**
     * 查询银行开户有没有过
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openAccountAlready",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询当前银行是否开户")
    public ResponseMessage openAccountAlready(@RequestParam(value = "bankCode",required = false) String bankCode) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtils.isBlank(bankCode)) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("银行编码不能为空");
            }
            String merchantNo = getCurrentCustomerNumber();
            logger.info("查询当前银行有没有开户，商编为={},银行={}", merchantNo, bankCode);
            BankAccountOpenRecord resp = bankAccountManageService.getProcessBankAccountRecord(merchantNo, bankCode);
            if (resp != null) {
                resp.setOperatePermission(StringUtils.isNotBlank(resp.getOperateMerchantNo()) ? false : true);
            }
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询当前银行有没有开户,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询当前银行有没有开户,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 查询开户信息详情
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openDetail",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询开户信息详情")
    public ResponseMessage queryOpenDetail(@RequestParam(value = "requestNo",required = false) String requestNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtils.isBlank(requestNo)) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("开户请求号不能为空");
            }
            String merchantNo = getCurrentCustomerNumber();
            logger.info("查询开户信息详情，商编为={},请求号为={}", merchantNo, requestNo);
            MgBankAccountOpenDetailRespDTO resp = bankAccountManageService.mgQueryOpenDetail(merchantNo, requestNo);
            logger.info("查询开户信息详情，resp={}", JSONUtils.toJsonString(resp));
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询开户信息详情,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询开户信息详情,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 多渠道资金归集开户接口
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openAccount",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("多渠道银行开户接口")
    public ResponseMessage openAccount(HttpServletRequest request, @RequestBody BankAccountOpenParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("多渠道资金归集开户接口，请求参数{}", JSONUtils.toJsonString(param));
        //商户名称
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        try{
            //校验参数
            param.validateParam();
            BankAccountOperate bankAccountOperate = bankAccountOperateFactory.getBankAccountOperateByBankCode(param.getOpenBankCode());
            bankAccountOperate.validateBasicParams(request, param);
            if(!currentMerchant.getSignName().equals(param.getMerchantName())){
                throw UnionAccountException.PARAM_REQUIRED_ERROR.newInstance("商户名称不正确");
            }
        }catch (YeepayBizException e) {
            logger.info("多渠道资金归集开户接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        } catch (Exception e) {
            logger.error("多渠道资金归集开户接口,参数校验系统异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("参数校验处理异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;

        }
        Lock lock = new RedisLock("bank_account_open" + "_" + currentMerchant.getMerchantNo() + "_" + param.getCertificateNo()
                , 4);
        try {
            if (lock.tryLock(3)) {
                logger.info("多渠道资金归集开户接口拿到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                param.setMerchantNo(currentMerchant.getMerchantNo());
                param.setMerchantName(currentMerchant.getSignName());
                BankAccountOpenRespDTO resp = bankAccountManageService.mgOpenBankAccount(param,null);
                resMsg.put("data", resp);
            }else {
                logger.info("多渠道资金归集开户接口没有获取到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请勿重复开户，请稍候重试");
            }
        } catch (YeepayBizException e) {
            logger.warn("多渠道资金归集开户接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("多渠道资金归集开户接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                logger.error("释放分布式锁异常为={}", e);
            }
        }
        return resMsg;
    }

    /**
     * 页面保活
     *
     * @throws Exception
     */
    @RequestMapping(value = "/keepAlive",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("页面保活接口")
    public ResponseMessage keepAlive(HttpServletRequest request) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("开户信息查询，请求参数{}", JSONUtils.toJsonString(request));
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        if (currentMerchant == null || StringUtils.isBlank(currentMerchant.getMerchantNo())) {
            resMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("页面已失效，请重新登录");
        }else {
            resMsg.put("merchantName", currentMerchant.getSignName());
            resMsg.put("merchantNo", currentMerchant.getMerchantNo());
        }
        return resMsg;
    }

    /**
     * 开户信息分页查询
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openInfo",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("开户信息分页查询接口")
    public ResponseMessage queryOpenInfo(@RequestParam(value = "bankCode",required = false) String bankCode,
                                         @RequestParam(value = "pageNo",required = false) Integer pageNo,
                                         @RequestParam(value = "pageSize",required = false) Integer pageSize) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            logger.info("查询银行开户列表，商编为={},银行={}", merchantNo, bankCode);
            BankAccountRecordRespDTO resp = bankAccountManageService.getAllBankAccountRecord(null,merchantNo,bankCode,pageNo,pageSize);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询银行开户列表,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询银行开户列表,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 数字证书下载
     *
     * @throws Exception
     */
    @RequestMapping(value = "/download/caAuthorizationFile",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("数字证书下载接口")
    public void caAuthorizationFileDownload(HttpServletResponse response) throws IOException {
        try {
            String filePath = this.getClass().getResource("/static/数字证书及电子签名授权委托书.docx").getPath();
            commonService.downloadByPath(filePath, response);
        } catch (Throwable e) {
            logger.error("数字证书下载异常,异常信息为={}", e);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>");
        }
    }


    /**
     * 法人委托书下载
     *
     * @throws Exception
     */
    @RequestMapping(value = "/download/legalAuthorizationFile",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("法人委托书下载接口")
    public void download(HttpServletResponse response) throws IOException {
        try {
            String filePath = this.getClass().getResource("/static/授权委托书.doc").getPath();
            commonService.downloadByPath(filePath, response);
        } catch (Throwable e) {
            logger.error("法人委托书下载异常,异常信息为={}", e);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>");
        }
    }

    /**
     * 银行开户说明书
     *
     * @throws Exception
     */
    @RequestMapping(value = "/download/bankOpenIntroduce",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("银行开户说明书")
    public void bankOpenIntroduce(@RequestParam(value = "bankCode") String bankCode,
                                    HttpServletResponse response) throws IOException {
        logger.info("[下载银行开户说明书]bankCode={}", bankCode);
        try {
            BankAccountOperate bankAccountOperate = bankAccountOperateFactory.getBankAccountOperateByBankCode(bankCode);
            String filePath = this.getClass().getResource(bankAccountOperate.achieveIntroduceDownUrl()).getPath();
            commonService.downloadByPath(filePath, response);
        } catch (Throwable e) {
            logger.error("下载银行开户说明书异常,异常信息为={}", e);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>");
        }
    }

    /**
     * 短验申请
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openAccountAuthApply",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("银行开户-短验申请接口")
    public ResponseMessage openAccountAuthApply(@RequestBody OpenAccountConfirmParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if (StringUtils.isBlank(param.getRequestNo())) {
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("开户请求号不能为空");
            }
            String merchantNo = getCurrentCustomerNumber();
            logger.info("短验申请接口，商编为={},开户请求号={}", merchantNo, param.getRequestNo());
            BankAccountAuthApplyRespDTO respDTO = remoteService.openAccountAuthApply(getCurrentCustomerNumber(), param.getRequestNo());
            logger.info("短验申请接口，开户请求号={},返回信息为={}", param.getRequestNo(),JSONUtils.toJsonString(respDTO));
            if("FAIL".equals(respDTO.getStatus())){
                resMsg.setErrCode(respDTO.getReturnCode());
                resMsg.setErrMsg(respDTO.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        } catch (YeepayBizException e) {
            logger.warn("短验申请接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("短验申请接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 短验验证
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openAccountAuthConfirm",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("银行开户-短验验证接口")
    public ResponseMessage openAccountAuthConfirm(@RequestBody OpenAccountConfirmParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            param.validateParam();
            String merchantNo = getCurrentCustomerNumber();
            logger.info("短验验证接口，商编为={},请求信息为={}", merchantNo, JSONUtils.toJsonString(param));
            BankAccountAuthConfirmRespDTO respDTO =  remoteService.openAccountAuthConfirm(getCurrentCustomerNumber(), param.getRequestNo(), param.getAuthCode());
            logger.info("短验验证接口，开户请求号={},返回信息为={}", param.getRequestNo(),JSONUtils.toJsonString(respDTO));
            if("FAIL".equals(respDTO.getStatus())){
                resMsg.setErrCode(respDTO.getReturnCode());
                resMsg.setErrMsg(respDTO.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        } catch (YeepayBizException e) {
            logger.warn("短验验证接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("短验验证接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 查询开立成功银行账户信息
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/bankAccountInfo",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询开立成功银行账户信息接口")
    public ResponseMessage bankAccountInfo(@RequestParam(value = "bankCode",required = false) String bankCode) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            logger.info("查询开立成功银行账户信息，商编为={},银行={}", merchantNo, bankCode);
            List<BankAccountOpenRecord> resp = bankAccountManageService.getSuccessBankAccountRecord(null,merchantNo, bankCode);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询开立成功银行账户信息,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询开立成功银行账户信息,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询银行账户余额
     * 巨坑，企业号跟多渠道同一个入口，还不知道...
     * 自己的controller层搞接口，不要复用，或者加上注释
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/accountBalance", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询银行账户余额接口")
    public ResponseMessage accountBalance(@RequestParam(value = "bankCode", required = false) String bankCode,
                                          @RequestParam(value = "accountNo", required = false) String accountNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            logger.info("查询银行账户余额，商编为={},银行编码={},银行账户号={}", merchantNo, bankCode, accountNo);
            if (BankAccountBankCodeEnum.SUNINGBANK_ENTERPRISE.name().equals(bankCode)) {
                QueryBankAccountRespDTO respDTO = remoteService.queryBankAccountInfo(merchantNo, bankCode, accountNo);
                if (respDTO != null && "UA00000".equals(respDTO.getReturnCode())) {
                    resMsg.put("accountAmt", respDTO.getAccountAmt());
                    resMsg.put("useableAmt", respDTO.getUseableAmt());
                    resMsg.put("frozenAmt", respDTO.getFrozenAmt());
                }
            } else {
                QueryBankAccountRespDTO respDTO = remoteService.queryBankAccountInfo(merchantNo, bankCode, accountNo);
                if (respDTO != null && "UA00000".equals(respDTO.getReturnCode())) {
                    //总余额
                    resMsg.put("accountAmt", respDTO.getAccountAmt());
                }
                QueryBankAccountBalanceRespDTO accountRespDTO = remoteService.queryChannelBankAccountInfo(merchantNo, bankCode, accountNo);
                if (accountRespDTO != null && "UA00000".equals(accountRespDTO.getReturnCode())) {
                    resMsg.put("useableAmt", accountRespDTO.getUseableAmt());
                    //不可用余额
                    resMsg.put("frozenAmt", accountRespDTO.getFrozenAmt());
                    //自助充值可用金额
                    resMsg.put("rechargeUseAmt", accountRespDTO.getRechargeUseAmt());
                    //充值不可用金额
                    resMsg.put("rechargeNotUseAmt", accountRespDTO.getRechargeNotUseAmt());
                    //自动充值可用金额
                    resMsg.put("autoRechargeUseAmt", accountRespDTO.getAutoRechargeUseAmt());
                    //可用金额
                    BigDecimal rechargeUseAmt = (accountRespDTO.getRechargeUseAmt() == null) ? BigDecimal.ZERO : accountRespDTO.getRechargeUseAmt();
                    BigDecimal autoRechargeUseAmt = (accountRespDTO.getAutoRechargeUseAmt() == null) ? BigDecimal.ZERO : accountRespDTO.getAutoRechargeUseAmt();
                    resMsg.put("accountUseableAmt", rechargeUseAmt.add(autoRechargeUseAmt));
                }
            }
            return resMsg;
        } catch (YeepayBizException e) {
            logger.warn("查询银行账户余额,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询银行账户余额,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 入金通知和开户通知配置新增
     *
     * @param bankAccountNotifyConfigParam
     * @return
     */
    @ApiOperation("通知配置保存")
    @RequestMapping(value = "/notify/config/save", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage openAccountNotifyConfigSave(@RequestBody BankAccountNotifyConfigParam bankAccountNotifyConfigParam) {
        logger.info("保存银行开户通知配置 request={}", new Gson().toJson(bankAccountNotifyConfigParam));
        bankAccountNotifyConfigParam.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentCustomerNumber();
        String loginName = getCurrentUser().getLoginName();
        try {
            remoteService.addOpenAccountNotifyConfig(bankAccountNotifyConfigParam, merchantNo, loginName);
        }catch (Exception e){
            logger.error("新增通知配置异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 入金通知和开户通知配置修改
     *
     * @param
     * @return
     */
    @ApiOperation("通知配置更新")
    @RequestMapping(value = "/notify/config/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage merchantNotifyConfigUpdate(@RequestBody BankAccountNotifyConfigParam bankAccountNotifyConfigParam) {
        logger.info("更新银行开户通知配置 request={}", new Gson().toJson(bankAccountNotifyConfigParam));
        bankAccountNotifyConfigParam.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentCustomerNumber();
        String loginName = getCurrentUser().getLoginName();
        try {
            remoteService.updateOpenAccountNotifyConfig(bankAccountNotifyConfigParam, merchantNo, loginName);
        }catch (Exception e){
            logger.error("更新通知配置异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 入金通知配置修改
     *
     * @param depositNotifyModifyParam
     * @return
     */
    @ApiOperation("入金配置更新")
    @RequestMapping(value = "/deposit/notify/config/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage depositNotifyConfigUpdate(@RequestBody DepositNotifyModifyParam depositNotifyModifyParam) {
        logger.info("更新入金通知配置 request={}", new Gson().toJson(depositNotifyModifyParam));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentCustomerNumber();
        String loginName = getCurrentUser().getLoginName();
        try {
            remoteService.depositNotifyConfigUpdate(depositNotifyModifyParam, merchantNo, loginName);
        }catch (Exception e){
            logger.error("银行账户入金通知配置更新异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 入金通知和开户通知配置查询
     *
     * @param bankAccountNotifyConfigQuery
     * @return
     */
    @ApiOperation("通知配置查询")
    @RequestMapping(value = "/notify/config/query" , method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage notifyConfigQuery(@RequestBody BankAccountNotifyConfigQueryDTO bankAccountNotifyConfigQuery) {
        logger.info("查询开户通知配置 request={}", new Gson().toJson(bankAccountNotifyConfigQuery));
        bankAccountNotifyConfigQuery.validateParam();
        String merchantNo = getCurrentCustomerNumber();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            BankAccountNotifyConfigRespDTO resp = remoteService.queryBankOpenAccountNotifyConfig(bankAccountNotifyConfigQuery, merchantNo);
            resMsg.put("data", resp);
        }catch (Exception e){
            logger.error("银行账户通知查询异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    @ApiOperation("入金配置查询")
    @RequestMapping(value = "/deposit/config/query", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage queryDepositNotifyConfig(@RequestBody DepositNotifyConfigParam depositNotifyConfigParam) {
        logger.info("查询入金通知配置 request={}", new Gson().toJson(depositNotifyConfigParam));
        depositNotifyConfigParam.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentCustomerNumber();
        depositNotifyConfigParam.setMerchantNo(merchantNo);
        try {
            DepositNotifyConfigRespDTO depositNotifyConfigRespDTO = remoteService.queryDepositNotifyConfig(depositNotifyConfigParam);
            resMsg.put("data", depositNotifyConfigRespDTO);
        }catch (Exception e){
            logger.error("银行账户入金通知查询异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询开立账户类型
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/merchantType",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询商户开立账户类型")
    public ResponseMessage queryMerchantTypeDTO(@RequestParam(value = "bankCode") String bankCode,
                                                @RequestParam(value = "openMerchantNo",required = false) String openMerchantNo) {
        logger.info("查询商户开立账户类型请求,bankCode={},openMerchantNo={}", bankCode, openMerchantNo);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = openMerchantNo;
            if (StringUtils.isEmpty(merchantNo)) {
                merchantNo = getCurrentCustomerNumber();
                logger.info("查询商户开立账户类型,使用登录商编查询,登录商编为={}", merchantNo);
            }
            MerchantShuntQueryRespDTO respDTO = remoteService.queryMerchant(merchantNo);
            BankAccountOperate bankAccountOperate = bankAccountOperateFactory.getBankAccountOperateByBankCode(bankCode);
            MerchantTypeDTO merchantTypeDTO = bankAccountOperate.assembleOpenBankAccountMerchantType(respDTO.getSignType());
            merchantTypeDTO.setMerchantNo(merchantNo);
            resMsg.put("data", merchantTypeDTO);
            logger.info("查询商户开立账户类型bankCode={},响应前端内容={}", bankCode, JSONUtils.toJsonString(resMsg));
        } catch (YeepayBizException e) {
            logger.warn("查询商户开立账户类型,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询商户开立账户类型,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 查询产品是否开通
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getProductInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询产品是否开通")
    public ResponseMessage getProductInfo(@RequestParam(value = "bankCode") String bankCode) {
        String merchantNo = getCurrentCustomerNumber();
        logger.info("查询产品是否开通入参,bankCode={},merchantNo={}", bankCode, merchantNo);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            if(Objects.equals(bankCode, BankAccountBankCodeEnum.XWB_Z.name())){
                bankCode = BankAccountBankCodeEnum.XWB.getChannelBankCode();
            }
            Boolean productOpen = bankAccountManageService.getProductOpen(merchantNo, bankCode);
            resMsg.put("productOpen", productOpen);
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("获取产品开通异常,商户为=" + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("获取产品开通异常,商户为=" + merchantNo + ",异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }


    /**
     * 开户信息分页查询
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/flow",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("账户流水查询接口")
    public ResponseMessage queryBankAccountTrx(@RequestBody BankAccountTrxQueryDTO bankAccountTrxQuery) {
        logger.info("查询账户流水，请求参数={}", JSONUtils.toJsonString(bankAccountTrxQuery));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            bankAccountTrxQuery.validateParam();
            String merchantNo = bankAccountTrxQuery.getMerchantNo();
            if (StringUtils.isBlank(merchantNo)) {
                merchantNo = getCurrentCustomerNumber();
            }
            BankAccountTrxRecordRespDTO bankAccountTrxRecordResp = bankAccountManageService.queryBankAccountTrx(bankAccountTrxQuery, merchantNo);
            resMsg.put("data", bankAccountTrxRecordResp);
        } catch (AccountPayException e) {
            logger.warn("查询账户流水,业务异常为={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询账户流水,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        logger.info("查询账户流水，响应={}", JSONUtils.toJsonString(resMsg));
        return resMsg;
    }

    /**
     * 查询银行信息
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/queryOpenSuccessBankInfo",method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询银行账户信息映射")
    public ResponseMessage queryOpenSuccessBankInfo() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            List<BankAccountOpenRecord> bankAccountOpenRecords = bankAccountManageService.getSuccessBankAccountRecord(null, getCurrentCustomerNumber(), null);
            Set<String> bankCodeSet = bankAccountOpenRecords.stream()
                    .map(BankAccountOpenRecord::getBankCode)
                    .collect(Collectors.toSet());
            List<BankInfoDTO> bankInfoDTOS = bankCodeSet.stream().map(item -> {
                BankInfoDTO bankInfoDTO = new BankInfoDTO();
                BankAccountBankCodeEnum bankCodeEnum = BankAccountBankCodeEnum.valueOf(item);
                bankInfoDTO.setBankCode(bankCodeEnum.getChannelBankCode());
                bankInfoDTO.setBankName(bankCodeEnum.getDescription());
                return bankInfoDTO;
            }).collect(Collectors.toList());
            resMsg.put("data", bankInfoDTOS);
        } catch (YeepayBizException e) {
            logger.warn("查询银行账户信息映射,业务异常为={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询银行账户信息映射,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    @RequestMapping(value = "/queryMerchantLegalInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询法人信息, 法人名称:legalName，商户签约名:merchantName")
    public ResponseMessage queryLegalPersonInfo(@RequestParam(value = "merchantNo", required = false)
                                                        String merchantNo) {
        try {
            ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
            if (StringUtils.isBlank(merchantNo)) {
                merchantNo = getCurrentCustomerNumber();
            }
            logger.info("查询法人信息 merchantNo={}", merchantNo);
            LegalPersonRespDTO legalPersonInfo = remoteService.queryLegalPersonInfo(merchantNo);
            MerchantRespDTO merchantRespDTO = remoteService.queryMerchantInfo(merchantNo);
            resMsg.put("legalName", legalPersonInfo.getName());
            resMsg.put("merchantName", merchantRespDTO.getSignName());
            return resMsg;
        } catch (YeepayBizException e) {
            logger.error("查询法人信息业务异常 message={}", e.getMessage());
            ResponseMessage exceptionMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
            exceptionMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            exceptionMsg.setErrMsg(e.getMessage());
            return exceptionMsg;
        } catch (Throwable e) {
            logger.error("查询法人信息异常", e);
            ResponseMessage exceptionMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
            exceptionMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            exceptionMsg.setErrMsg("查询法人信息异常，请稍后重试");
            return exceptionMsg;
        }
    }

    /**
     * 查询当前商户的发起方式
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getSettleConfig", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询当前商户的结算周期")
    public ResponseMessage getSettleInfo() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentCustomerNumber();
        try {
            MerchantSettleConfigRespDTO merchantSettleConfigRespDTO = remoteService.querySettleConfig(merchantNo);
            if (merchantSettleConfigRespDTO == null || !"UA00000".equals(merchantSettleConfigRespDTO.getReturnCode())) {
                return resMsg;
            }
            resMsg.put("settleStartWay", merchantSettleConfigRespDTO.getSettleStartWay());
            resMsg.put("allowSwitch", merchantSettleConfigRespDTO.getAllowSwitch());
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("查询当前商户的结算周期,商户为={}," + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("查询当前商户的结算周期,商户为={}," + merchantNo + "异常信息为={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }

    @RequestMapping(value = "/query/downLoad")
    @ResponseBody
    @ApiOperation("账户流水下载接口")
    public ResponseMessage downLoad( BankAccountTrxDowloadQueryDTO bankAccountTrxQuery, HttpServletRequest request, HttpServletResponse response) {
        logger.info("账户流水下载接口，请求参数={}", JSONUtils.toJsonString(bankAccountTrxQuery));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            bankAccountTrxQuery.validateParam();
            bankAccountTrxQuery.setMerchantNo( getCurrentCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("银行流水");
            String type = new BankAccountManageDownloadService(getCurrentUser(), bankAccountTrxQuery, desc.toString(), "银行流水", remoteService).download(request, response);
            resMsg.put("downLoadType",type);
        } catch (AccountPayException e) {
            logger.warn("账户流水下载接口,业务异常为={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("账户流水下载接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        logger.info("账户流水下载接口，响应={}", JSONUtils.toJsonString(resMsg));
        return resMsg;
    }

    /**
     * 查询银行账户信息
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/queryOpenSuccessBankAccountInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询银行账户信息")
    public ResponseMessage queryOpenSuccessBankAccountInfo(@RequestParam(value = "bankCode") String bankCode,
                                                           @RequestParam(value = "merchantNo", required = false) String merchantNo) {
        logger.info("查询银行账户信息,req={}", bankCode);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        if (StringUtils.isBlank(merchantNo)) {
            merchantNo = getCurrentCustomerNumber();
        }
        try {
            List<BankAccountOpenRecord> bankAccountOpenRecords = bankAccountManageService.getSuccessBankAccountRecord(null, merchantNo, null);
            if (CollectionUtils.isEmpty(bankAccountOpenRecords)) {
                return resMsg;
            }
            List<BankAccountOpenRecord> bankAccountOpenRecordsFilterList = bankAccountOpenRecords
                    .stream()
                    .filter(item -> {
                        switch (bankCode) {
                            case "XWB":
                                return BankAccountBankCodeEnum.XWB.name().equals(item.getBankCode()) || BankAccountBankCodeEnum.XWB_Z.name().equals(item.getBankCode());
                            case "HXBXB":
                                return BankAccountBankCodeEnum.HXBXB_GATHER.name().equals(item.getBankCode());
                            case "SUNINGBANK":
                                return BankAccountBankCodeEnum.SUNINGBANK_MULTICHANNEL.name().equals(item.getBankCode());
                            default:
                                return bankCode.equals(item.getBankCode());
                        }
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bankAccountOpenRecordsFilterList)) {
                return resMsg;
            }
            List<BankInfoDTO> bankInfoDTOS = bankAccountOpenRecordsFilterList
                    .stream()
                    .map(item -> {
                        BankInfoDTO bankInfoDTO = new BankInfoDTO();
                        BankAccountBankCodeEnum bankCodeEnum = BankAccountBankCodeEnum.valueOf(item.getBankCode());
                        bankInfoDTO.setBankCode(bankCodeEnum.getChannelBankCode());
                        bankInfoDTO.setBankName(bankCodeEnum.getDescription());
                        bankInfoDTO.setBankAccountNo(item.getBankAccountNo());
                        return bankInfoDTO;
                    }).collect(Collectors.toList());
            logger.info("查询银行账户信息,resp={}", new Gson().toJson(bankInfoDTOS));
            resMsg.put("data", bankInfoDTOS);
        } catch (YeepayBizException e) {
            logger.warn("查询银行账户信息,业务异常为={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询银行账户信息,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    @RequestMapping(value = "/queryOpenBankByProduct", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询银行列表根据开通产品")
    public ResponseMessage queryOpenBankByProduct() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("查询银行列表根据开通产品,当前登录商编={}", getCurrentCustomerNumber());
            List<BankInfoDTO> bankInfoDTOS = bankAccountManageService.queryOpenBankByProduct(getCurrentCustomerNumber());
            resMsg.put("bankInfoDTOS", bankInfoDTOS);
            logger.info("查询银行列表根据开通产品,响应={}", JSONUtils.toJsonString(resMsg));
        } catch (AccountPayException e) {
            logger.error("查询银行列表根据开通产品,业务异常={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询银行列表根据开通产品,异常=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    @RequestMapping(value = "/queryOpenBankByRecord", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询银行列表根据开户记录")
    public ResponseMessage queryOpenBankByRecord(@RequestParam(value = "pageSource") String pageSource) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("查询银行列表根据开户记录,当前登录商编={},pageSource={}", getCurrentCustomerNumber(), pageSource);
            List<BankInfoDTO> bankInfoDTOS = bankAccountManageService.queryOpenBankByRecord(Lists.newArrayList(getCurrentCustomerNumber()), pageSource);
            resMsg.put("bankInfoDTOS", bankInfoDTOS);
            logger.info("查询银行列表根据开户记录,响应={}", JSONUtils.toJsonString(resMsg));
        } catch (AccountPayException e) {
            logger.error("查询银行列表根据开户记录,业务异常={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询银行列表根据开户记录,异常=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("系统异常");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }
}
