package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base;

import com.yeepay.g3.app.account.pay.mboss.dto.BaseDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 页面查询请求参数
 */
@ApiModel("页面查询请求参数")
public class BasePageReqDTO extends BaseDto {

    protected static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "查询页面编号")
    private Integer pageNo = 1;

    @ApiModelProperty(value = "页面数量")
    private Integer pageSize = 10;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
