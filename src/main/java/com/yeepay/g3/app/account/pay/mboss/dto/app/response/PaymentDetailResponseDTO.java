package com.yeepay.g3.app.account.pay.mboss.dto.app.response;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 19:29
 */
public class PaymentDetailResponseDTO {

    private String batchNo;
    /**
     * 商户请求号
     */
    private String requestNo;

    /**
     * 易宝流水号
     */
    private String orderNo;

    /**
     * 付款方商编
     */
    private String merchantNo;

    /**
     * 付款订单金额
     */
    private String orderAmount;

    /**
     * 收款金额
     */
    private String receiveAmount;

    /**
     * 扣账金额
     */
    private String debitAmount;


    /**
     * 创建时间
     */
    private String orderTime;

    /**
     * 付款完成时间
     */
    private String finishTime;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 手续费承担方编号
     */
    private String feeUndertakerMerchantNo;


    /**
     * 付款订单状态
     */
    private String status;

    /**
     * 失败或冲退原因
     */
    private String failReason;


    /**
     * 付款到账类型
     */
    private String receiveType;


    /**
     * 收款方账号
     */
    private String receiverAccountNo;

    /**
     * 收款方开户名
     */
    private String receiverAccountName;

    /**
     * 收款方银行编码
     */
    private String receiverBankCode;

    /**
     * 银行附言
     */
    private String comments;

    /**
     * 冲退标识
     */
    private boolean isReversed;

    /**
     * 冲退时间
     */
    private String reverseTime;

    /**
     * 订单描述
     */
    private String orderInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务类型
     */
    private String tradeType;

    /**
     * 操作员
     */
    private String operator;


    /**
     * 付款方名称
     */
    private String merchantName;

    /**
     * 资方信息
     */
    private String capitalInfo;


    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public String getReverseTime() {
        return reverseTime;
    }

    public void setReverseTime(String reverseTime) {
        this.reverseTime = reverseTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(String receiveType) {
        this.receiveType = receiveType;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getFeeUndertakerMerchantNo() {
        return feeUndertakerMerchantNo;
    }

    public void setFeeUndertakerMerchantNo(String feeUndertakerMerchantNo) {
        this.feeUndertakerMerchantNo = feeUndertakerMerchantNo;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getReceiveAmount() {
        return receiveAmount;
    }

    public void setReceiveAmount(String receiveAmount) {
        this.receiveAmount = receiveAmount;
    }

    public String getDebitAmount() {
        return debitAmount;
    }

    public void setDebitAmount(String debitAmount) {
        this.debitAmount = debitAmount;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getReceiverAccountNo() {
        return receiverAccountNo;
    }

    public void setReceiverAccountNo(String receiverAccountNo) {
        this.receiverAccountNo = receiverAccountNo;
    }

    public String getReceiverAccountName() {
        return receiverAccountName;
    }

    public void setReceiverAccountName(String receiverAccountName) {
        this.receiverAccountName = receiverAccountName;
    }

    public String getReceiverBankCode() {
        return receiverBankCode;
    }

    public void setReceiverBankCode(String receiverBankCode) {
        this.receiverBankCode = receiverBankCode;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public boolean isReversed() {
        return isReversed;
    }

    public void setReversed(boolean reversed) {
        isReversed = reversed;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCapitalInfo() {
        return capitalInfo;
    }

    public void setCapitalInfo(String capitalInfo) {
        this.capitalInfo = capitalInfo;
    }
}
