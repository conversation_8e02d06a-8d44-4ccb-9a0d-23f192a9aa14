package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base.BasePageRespDTO;
import com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.file.AliPayProxyTransferFile;
import com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req.*;
import com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp.*;
import com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.vo.BatchTransferVO;
import com.yeepay.g3.app.account.pay.mboss.entity.ChannelBookChargeRecordEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.ChannelBookInfoEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.ChannelBookProxyTransBatchInfoEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.ChannelBookProxyTransEntity;
import com.yeepay.g3.app.account.pay.mboss.service.alipay.AliPayChannelService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.utils.file.ExcelUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.transfer.facade.alipay.enumtype.BatchProxyTransferDetailStatusEnum;
import com.yeepay.g3.facade.unionaccount.transfer.facade.alipay.enumtype.BatchProxyTransferStatusEnum;
import com.yeepay.g3.facade.unionaccount.transfer.facade.alipay.enumtype.ChannelBookSignStatusEnum;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/alipay/manage")
public class AliPayChannelController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AliPayChannelController.class);
    @Resource
    private AliPayChannelService aliPayChannelService;

    @Resource
    private AliPayProxyTransferFile aliPayProxyTransferFile;

    private static final Gson GSON = new Gson();


    @RequestMapping(value = "/contractToken", method = RequestMethod.GET)
    public ModelAndView subView() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIAlipayRootUrl());
        logger.info("获取到url:"+WebPropertiesHolder.getUIAlipayRootUrl());
        mav.setViewName("alipay/contractToken");
        return mav;
    }

    @RequestMapping(value = "/bookkeepingManage", method = RequestMethod.GET)
    public ModelAndView bookkeepingManage() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIAlipayRootUrl());
        mav.setViewName("alipay/bookkeepingManage");
        return mav;
    }

    @RequestMapping(value = "/fundsTransferredIn", method = RequestMethod.GET)
    public ModelAndView fundsTransferredIn() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIAlipayRootUrl());
        mav.setViewName("alipay/fundsTransferredIn");
        return mav;
    }

    @RequestMapping(value = "/ledgerIssuance", method = RequestMethod.GET)
    public ModelAndView ledgerIssuance() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIAlipayRootUrl());
        mav.setViewName("alipay/ledgerIssuance");
        return mav;
    }

    @RequestMapping(value = "/dropshippingRecords", method = RequestMethod.GET)
    public ModelAndView dropshippingRecords() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIAlipayRootUrl());
        mav.setViewName("alipay/dropshippingRecords");
        return mav;
    }


    /**
     * 查询签约信息 用于查询当前是否签约
     */
    @RequestMapping(value = "/getAliContractInfo")
    @ResponseBody
    public Wrapper<AliPayContractRspDTO> getAliContractInfo() {
        try {
            ShiroUser shiroUser = super.getCurrentUser();
            AliPayContractRspDTO contractInfo = aliPayChannelService.getAliContractInfo(shiroUser.getCustomerNumber());
            if (contractInfo == null) {
                contractInfo = new AliPayContractRspDTO();
                contractInfo.setStatus(ChannelBookSignStatusEnum.FAIL.name());
                contractInfo.setSignName(shiroUser.getSignName());
            } else {
                contractInfo.setSignName(shiroUser.getSignName());
            }
            return WrapMapper.ok(contractInfo);
        } catch (Exception e) {
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 记账本签约
     */
    @RequestMapping(value = "/channelBook/sign")
    @ResponseBody
    public Wrapper<AliChannelBookContractRspDTO> channelBookSign() {
        try {
            ShiroUser shiroUser = super.getCurrentUser();
            AliChannelBookContractRspDTO rspDTO = aliPayChannelService.channelBookSign(shiroUser);
            return WrapMapper.ok(rspDTO);
        } catch (Exception e) {
            logger.error("签约异常",e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 记账本解约
     */
    @RequestMapping(value = "/channelBook/unsign")
    @ResponseBody
    public Wrapper<Void> channelBookUnSign(@RequestParam("orderNo") String orderNo) {
        try {
            ShiroUser shiroUser = super.getCurrentUser();
            aliPayChannelService.channelBookUnSign(shiroUser, orderNo);
            return WrapMapper.ok();
        } catch (Exception e) {
            logger.error("解约异常",e);

            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 请求记账本列表
     *
     * @param aliPayBankBookQueryParam
     * @return
     */
    @RequestMapping(value = "/queryChannelBookList")
    @ResponseBody
    public BasePageRespDTO<AliPayChannelBookRspDTO> queryChannelBookList(@RequestBody AliPayBankBookQueryDTO aliPayBankBookQueryParam) {
        logger.info("请求记账本列表:{}", JSONUtils.toJsonString(aliPayBankBookQueryParam));
        try {
            ShiroUser shiroUser = super.getCurrentUser();
            aliPayBankBookQueryParam.setMerchantNo(shiroUser.getCustomerNumber());
            Integer totalCount = aliPayChannelService.queryChannelBookListCount(aliPayBankBookQueryParam);
            List<ChannelBookInfoEntity> chargeRecordEntityList = aliPayChannelService.queryChannelBookList(aliPayBankBookQueryParam, aliPayBankBookQueryParam.getPageNo(), aliPayBankBookQueryParam.getPageSize());
            List<AliPayChannelBookRspDTO> channelBookList = chargeRecordEntityList.stream().map(item -> {
                AliPayChannelBookRspDTO aliPayChannelBookRspDTO = new AliPayChannelBookRspDTO();
                aliPayChannelBookRspDTO.setChannelBookName(item.getChannelBookName());
                aliPayChannelBookRspDTO.setChannelBookId(item.getChannelBookId());
                aliPayChannelBookRspDTO.setCreateTime(item.getCreateTime());
                return aliPayChannelBookRspDTO;
            }).collect(Collectors.toList());
            return BasePageRespDTO.successPage(channelBookList, totalCount == null ? 0L : Long.valueOf(totalCount));
        } catch (Exception e) {
            logger.error("请求记账本列表异常",e);
            return BasePageRespDTO.fail(e.getMessage());
        }

    }


    /**
     * 开立记账本
     */
    @RequestMapping(value = "/channelBook/open")
    @ResponseBody
    public Wrapper<AliChannelBookOpenRspDTO> channelBookOpen(@RequestBody AliChannelBookOpenReqDTO aliChannelBookOpen) {
        try {
            logger.info("记账本开立请求:{}", JSONUtils.toJsonString(aliChannelBookOpen));
            ShiroUser shiroUser = super.getCurrentUser();
            aliChannelBookOpen.setMerchantNo(shiroUser.getCustomerNumber());
            AliChannelBookOpenRspDTO rspDTO = aliPayChannelService.channelBookOpen(aliChannelBookOpen);
            return WrapMapper.ok(rspDTO);
        } catch (Exception e) {
            logger.error("记账本开立异常",e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 记账本详情
     */
    @RequestMapping(value = "/channelBook/detail")
    @ResponseBody
    public Wrapper<AliChannelBookDetailRspDTO> channelBookDetail(@RequestParam("channelBookId") String channelBookId) {
        try {
            logger.info("记账本详情请求:{}", JSONUtils.toJsonString(channelBookId));
            ShiroUser shiroUser = super.getCurrentUser();
            AliChannelBookDetailRspDTO rspDTO = aliPayChannelService.channelBookDetail(channelBookId, shiroUser);
            return WrapMapper.ok(rspDTO);
        } catch (Exception e) {
            logger.error("记账本详情异常",e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 资金拨入
     */
    @RequestMapping(value = "/capitalTransfer")
    @ResponseBody
    public Wrapper<AliCapitalTransferRspDTO> capitalTransfer(@RequestBody AliPayCapitalTransferDTO capitalTransferParam) {
        try {
            logger.info("资金拨入请求:{}", JSONUtils.toJsonString(capitalTransferParam));
            ShiroUser shiroUser = super.getCurrentUser();
            AliCapitalTransferRspDTO aliCapitalTransferRspDTO = aliPayChannelService.capitalTransfer(capitalTransferParam, shiroUser);
            return WrapMapper.ok(aliCapitalTransferRspDTO);
        } catch (Exception e) {
            logger.error("资金拨入异常",e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 资金拨入记录
     */
    @RequestMapping(value = "/capitalTransferList")
    @ResponseBody
    public BasePageRespDTO<ChannelBookChargeRecordDTO> capitalTransferList(@RequestBody AliPayCapitalTransferQueryDTO aliPayCapitalTransferQueryParam) {
        try {
            logger.info("资金拨入记录:{}", JSONUtils.toJsonString(aliPayCapitalTransferQueryParam));
            ShiroUser shiroUser = super.getCurrentUser();
            aliPayCapitalTransferQueryParam.setMerchantNo(shiroUser.getCustomerNumber());
            Integer totalCount = aliPayChannelService.capitalTransferListCount(aliPayCapitalTransferQueryParam);
            List<ChannelBookChargeRecordEntity> chargeRecordEntityList = aliPayChannelService.capitalTransferList(aliPayCapitalTransferQueryParam, aliPayCapitalTransferQueryParam.getPageNo(), aliPayCapitalTransferQueryParam.getPageSize());
            List<ChannelBookChargeRecordDTO> chanelRechargeList = chargeRecordEntityList.stream().map(item -> {
                ChannelBookChargeRecordDTO chargeRecordDTO = new ChannelBookChargeRecordDTO();
                chargeRecordDTO.setMerchantNo(item.getMerchantNo());
                chargeRecordDTO.setChannelBookId(item.getChannelBookId());
                chargeRecordDTO.setChannelBookName(item.getChannelBookName());
                chargeRecordDTO.setCreateTime(item.getCreateTime());
                chargeRecordDTO.setLastModifyTime(item.getLastModifyTime());
                chargeRecordDTO.setRemark(item.getRemark());
                chargeRecordDTO.setStatus(item.getStatus());
                chargeRecordDTO.setOrderNo(item.getOrderNo());
                chargeRecordDTO.setChannelNo(item.getChannelNo());
                chargeRecordDTO.setTransferAmount(item.getTransferAmount());
                chargeRecordDTO.setStatusDesc(BatchProxyTransferDetailStatusEnum.getDesByName(item.getStatus()));
                return chargeRecordDTO;
            }).collect(Collectors.toList());
            return BasePageRespDTO.successPage(chanelRechargeList, totalCount == null ? 0L : Long.valueOf(totalCount));
        } catch (Exception e) {
            return BasePageRespDTO.fail(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 资金拨入记录
     */
    @RequestMapping(value = "/capitalTransferListDownload")
    @ResponseBody
    public Wrapper<Void> capitalTransferListDownload(@RequestBody AliPayCapitalTransferQueryDTO aliPayCapitalTransferQueryParam, HttpServletRequest request, HttpServletResponse response) {
        try {
            logger.info("资金拨入记录:{}", JSONUtils.toJsonString(aliPayCapitalTransferQueryParam));
            ShiroUser shiroUser = super.getCurrentUser();
            aliPayCapitalTransferQueryParam.setMerchantNo(shiroUser.getCustomerNumber());
            List<ChannelBookChargeRecordEntity> chargeRecordEntityList = aliPayChannelService.capitalTransferListDownload(aliPayCapitalTransferQueryParam);
            List<ChannelBookChargeRecordDTO> chanelRechargeList = chargeRecordEntityList.stream().map(item -> {
                ChannelBookChargeRecordDTO chargeRecordDTO = new ChannelBookChargeRecordDTO();
                chargeRecordDTO.setMerchantNo(item.getMerchantNo());
                chargeRecordDTO.setChannelBookId(item.getChannelBookId());
                chargeRecordDTO.setChannelBookName(item.getChannelBookName());
                chargeRecordDTO.setCreateTime(item.getCreateTime());
                chargeRecordDTO.setLastModifyTime(item.getLastModifyTime());
                chargeRecordDTO.setRemark(item.getRemark());
                chargeRecordDTO.setStatus(item.getStatus());
                chargeRecordDTO.setOrderNo(item.getOrderNo());
                chargeRecordDTO.setChannelNo(item.getChannelNo());
                chargeRecordDTO.setTransferAmount(item.getTransferAmount());
                chargeRecordDTO.setStatusDesc(BatchProxyTransferDetailStatusEnum.getDesByName(item.getStatus()));
                return chargeRecordDTO;
            }).collect(Collectors.toList());
            ExcelUtils.exportFile(chanelRechargeList, ChannelBookChargeRecordDTO.class, response, "资金划入记录", aliPayCapitalTransferQueryParam.getDownType());
            return WrapMapper.ok();
        } catch (Exception e) {
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 代发明细
     */
    @RequestMapping(value = "/proxyTransferDetailList")
    @ResponseBody
    public Wrapper<List<ChannelBookProxyTransDTO>> proxyTransferDetailList(@Param("orderNo") String orderNo) {
        try {
            logger.info("代发明细请求:{}", JSONUtils.toJsonString(orderNo));
            ShiroUser shiroUser = super.getCurrentUser();
            ChannelBookProxyTransBatchInfoEntity batchProxyInfo = aliPayChannelService.getBatchProxyInfoByOrderNo(orderNo, shiroUser.getCustomerNumber());
            if (batchProxyInfo == null) {
                return WrapMapper.wrap(WrapMapper.ERROR, "未找到指定信息");
            }
            List<ChannelBookProxyTransEntity> channelBookProxyTransEntityList = aliPayChannelService.getBatchDetailByBatchNoAndMerchantNo(batchProxyInfo.getOrderNo(), shiroUser.getCustomerNumber());
            List<ChannelBookProxyTransDTO> channelProxyDetails = channelBookProxyTransEntityList.stream().map(item -> {
                ChannelBookProxyTransDTO channelBookProxyTransDTO = new ChannelBookProxyTransDTO();
                channelBookProxyTransDTO.setCreateTime(item.getCreateTime());
                channelBookProxyTransDTO.setLastModifyTime(item.getLastModifyTime());
                channelBookProxyTransDTO.setChannelBookId(item.getChannelBookId());
                channelBookProxyTransDTO.setChannelBookName(item.getChannelBookName());
                channelBookProxyTransDTO.setOrderNo(item.getOrderNo());
                channelBookProxyTransDTO.setFee(item.getFee());
                channelBookProxyTransDTO.setFeeStatus(item.getFeeStatus());
                channelBookProxyTransDTO.setBatchNo(item.getBatchNo());
                channelBookProxyTransDTO.setStatus(item.getStatus());
                channelBookProxyTransDTO.setStatusDesc(BatchProxyTransferDetailStatusEnum.getDesByName(item.getStatus()));
                channelBookProxyTransDTO.setTransferAmount(item.getTransferAmount());
                channelBookProxyTransDTO.setMerchantNo(item.getMerchantNo());
                channelBookProxyTransDTO.setReceiveName(item.getReceiveName());
                channelBookProxyTransDTO.setReceiveNo(item.getReceiveNo());
                channelBookProxyTransDTO.setRemark(item.getRemark());
                channelBookProxyTransDTO.setMessage(item.getMessage());
                return channelBookProxyTransDTO;
            }).collect(Collectors.toList());
            return WrapMapper.ok(channelProxyDetails);
        } catch (Exception e) {
            logger.error("代发明细请求异常",e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    @RequestMapping(value = "/proxyTransferDetailListDownload")
    @ResponseBody
    public Wrapper<Void> proxyTransferDetailListDownload(@Param("orderNo") String orderNo, @Param("downType") String downType, HttpServletRequest request, HttpServletResponse response) {
        try {
            logger.info("代发明细请求:{}", JSONUtils.toJsonString(orderNo));
            ShiroUser shiroUser = super.getCurrentUser();
            ChannelBookProxyTransBatchInfoEntity batchProxyInfo = aliPayChannelService.getBatchProxyInfoByOrderNo(orderNo, shiroUser.getCustomerNumber());
            if (batchProxyInfo == null) {
                return WrapMapper.wrap(WrapMapper.ERROR, "未找到指定信息");
            }
            List<ChannelBookProxyTransEntity> channelBookProxyTransEntityList = aliPayChannelService.getBatchDetailByBatchNoAndMerchantNo(batchProxyInfo.getOrderNo(), shiroUser.getCustomerNumber());
            List<ChannelBookProxyTransDTO> channelProxyDetails = channelBookProxyTransEntityList.stream().map(item -> {
                ChannelBookProxyTransDTO channelBookProxyTransDTO = new ChannelBookProxyTransDTO();
                channelBookProxyTransDTO.setCreateTime(item.getCreateTime());
                channelBookProxyTransDTO.setLastModifyTime(item.getLastModifyTime());
                channelBookProxyTransDTO.setChannelBookId(item.getChannelBookId());
                channelBookProxyTransDTO.setChannelBookName(item.getChannelBookName());
                channelBookProxyTransDTO.setOrderNo(item.getOrderNo());
                channelBookProxyTransDTO.setFee(item.getFee());
                channelBookProxyTransDTO.setFeeStatus(item.getFeeStatus());
                channelBookProxyTransDTO.setBatchNo(item.getBatchNo());
                channelBookProxyTransDTO.setStatus(item.getStatus());
                channelBookProxyTransDTO.setStatusDesc(BatchProxyTransferDetailStatusEnum.getDesByName(item.getStatus()));
                channelBookProxyTransDTO.setTransferAmount(item.getTransferAmount());
                channelBookProxyTransDTO.setMerchantNo(item.getMerchantNo());
                channelBookProxyTransDTO.setReceiveName(item.getReceiveName());
                channelBookProxyTransDTO.setReceiveNo(item.getReceiveNo());
                channelBookProxyTransDTO.setRemark(item.getRemark());
                channelBookProxyTransDTO.setMessage(item.getMessage());
                return channelBookProxyTransDTO;
            }).collect(Collectors.toList());
            ExcelUtils.exportFile(channelProxyDetails, ChannelBookProxyTransDTO.class, response, "代发明细记录", downType);
            return WrapMapper.ok();
        } catch (Exception e) {
            logger.error("代发请求异常",e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 单笔代发
     */
    @RequestMapping(value = "/singleProxyTransfer")
    @ResponseBody
    public Wrapper<BatchTransferVO> singleProxyTransfer(@RequestBody AliPaySingleProxyTransferDTO singleProxyTransferDTO) {
        try {
            logger.info("单笔代发:{}", JSONUtils.toJsonString(singleProxyTransferDTO));
            ShiroUser shiroUser = super.getCurrentUser();
            BatchTransferVO batchTransferVO = aliPayChannelService.singleProxyTransfer(singleProxyTransferDTO, shiroUser);
            return WrapMapper.ok(batchTransferVO);
        } catch (Exception e) {
            logger.error("单笔代发异常",e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }


    /**
     * 批量代发
     */
    @RequestMapping(value = "/batchProxyTransfer")
    @ResponseBody
    public Wrapper<BatchProxyTransferDTO> batchProxyTransfer(@RequestParam("file") MultipartFile file,
                                                             @RequestParam("totalCount") Integer totalCount,
                                                             @RequestParam("batchNo") String batchNo,
                                                             @RequestParam("totalAmount") String totalAmount, @RequestParam("channelBookId") String channelBookId,
                                                             @RequestParam("title") String title) {
        logger.info("单笔代发批次号:{}", JSONUtils.toJsonString(batchNo));
        try {
            BatchProxyTransferDTO batchProxyTransferDTO = new BatchProxyTransferDTO();
            ShiroUser shiroUser = super.getCurrentUser();
            String customerNumber = shiroUser.getCustomerNumber();
            //查询批次是否存在
            ChannelBookProxyTransBatchInfoEntity channelBookProxyTransBatchInfoEntity = aliPayChannelService.getBatchNoAndMerchant(customerNumber, batchNo);
            if (channelBookProxyTransBatchInfoEntity != null) {
                return WrapMapper.wrap(ResponseMessage.Status.ERROR.getValue(), "批次已存在");
            }
            //生成全局唯一标识
            List<AliPayProxyFileInfoDTO> transferParamDTOS = aliPayProxyTransferFile.dealFile(file);
            if (transferParamDTOS.size() > 500) {
                return WrapMapper.wrap(ResponseMessage.Status.ERROR.getValue(), "文件条数超过500 请拆分上传");
            }
            if (CollectionUtils.isEmpty(transferParamDTOS)) {
                return WrapMapper.wrap(ResponseMessage.Status.ERROR.getValue(), "文件为空");
            }
            //校验传入金额等数据
            BigDecimal totalSum = transferParamDTOS.stream()
                    .map(data -> Optional.ofNullable(data.getTotalAmount())
                            .map(BigDecimal::new)
                            .orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (!totalCount.equals(transferParamDTOS.size())
                    || totalSum.subtract(new BigDecimal(totalAmount)).compareTo(BigDecimal.ZERO) != 0) {
                return WrapMapper.wrap(ResponseMessage.Status.ERROR.getValue(), "传入条数和金额和对比数据不一致 请确认");
            }
            //处理数据校验
            List<AliPayProxyFileInfoCheckDTO> checkData = new ArrayList<>();
            for (AliPayProxyFileInfoDTO aliPayPoxyInfo : transferParamDTOS) {
                AliPayProxyFileInfoCheckDTO transferParamCheckDTO = new AliPayProxyFileInfoCheckDTO();
                org.springframework.beans.BeanUtils.copyProperties(aliPayPoxyInfo, transferParamCheckDTO);
                String msg = paramCheck(aliPayPoxyInfo);
                transferParamCheckDTO.setErrorMsg(msg);
                checkData.add(transferParamCheckDTO);
            }
            List<AliPayProxyFileInfoCheckDTO> collect = checkData.stream().filter(item -> !StringUtils.isBlank(item.getErrorMsg())).collect(Collectors.toList());
            String token = customerNumber + System.currentTimeMillis();
            batchProxyTransferDTO.setTotalCount(totalCount);
            batchProxyTransferDTO.setTotalAmount(totalAmount);
            batchProxyTransferDTO.setTotalCountCheck(transferParamDTOS.size());
            batchProxyTransferDTO.setTotalAmountCheck(String.valueOf(totalSum));
            if (collect.size() > 0) {
                try {
                    aliPayProxyTransferFile.writeErrorFile(checkData, token, FileUtil.multipartFileToFile(file));
                } catch (Exception e) {
                    return WrapMapper.wrap(ResponseMessage.Status.ERROR.getValue(), "错误文件写入异常 请确认");
                }
                String rediectUrl = "/subMerchant/downBatchDetail?token=";
                batchProxyTransferDTO.setErrCount(collect.size());
                batchProxyTransferDTO.setErrorToken(token);
                batchProxyTransferDTO.setRedirectUrl(rediectUrl + token);
                batchProxyTransferDTO.setCheckResult(Boolean.FALSE);
                return WrapMapper.ok(batchProxyTransferDTO);
            }
            AliPayBatchProxyTransferDTO aliPayBatchProxyTransferDTO = new AliPayBatchProxyTransferDTO();
            aliPayBatchProxyTransferDTO.setChannelBookId(channelBookId);
            aliPayBatchProxyTransferDTO.setTotalCount(totalCount);
            aliPayBatchProxyTransferDTO.setBatchNo(batchNo);
            aliPayBatchProxyTransferDTO.setTotalAmount(totalAmount);
            aliPayBatchProxyTransferDTO.setTitle(title);
            aliPayBatchProxyTransferDTO.setOperate(customerNumber);
            aliPayBatchProxyTransferDTO.setAliPaySingleProxyTransferDTOList(transferParamDTOS);
            //完全通过开始处理
            RedisUtils.set(customerNumber + aliPayBatchProxyTransferDTO.getBatchNo(), GSON.toJson(aliPayBatchProxyTransferDTO), 2 * 3600);
            RedisUtils.set(customerNumber + aliPayBatchProxyTransferDTO.getBatchNo() + "_data", GSON.toJson(aliPayBatchProxyTransferDTO), 2 * 3600);
            batchProxyTransferDTO.setBatchNo(aliPayBatchProxyTransferDTO.getBatchNo());
            batchProxyTransferDTO.setCheckResult(Boolean.TRUE);
            logger.error("批量代付处理, Cause By:{}", JSON.toJSONString(batchProxyTransferDTO));
            return WrapMapper.ok(batchProxyTransferDTO);
        } catch (Exception e) {
            logger.error("系统异常: " + batchNo, e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }

    private String paramCheck(AliPayProxyFileInfoDTO aliPayPoxyInfo) {
        StringBuilder stringBuilder = new StringBuilder();
        if (org.apache.commons.lang.StringUtils.isBlank(aliPayPoxyInfo.getLineNum())) {
            stringBuilder.append("序号未填写;");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(aliPayPoxyInfo.getReceiveNo())) {
            stringBuilder.append("收款方支付宝账号未填写;");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(aliPayPoxyInfo.getReceiveName())) {
            stringBuilder.append("收款方支付宝名称未填写;");
        }
        if (StringUtils.isBlank(aliPayPoxyInfo.getTotalAmount())) {
            stringBuilder.append("金额未填写;");
        }
        if (StringUtils.isBlank(aliPayPoxyInfo.getRemark())) {
            stringBuilder.append("备注未填写");
        }
        return stringBuilder.toString();
    }

    /**
     * 代发记录
     */
    @RequestMapping(value = "/proxyTransfer")
    @ResponseBody
    public BasePageRespDTO<BatchProxyTransferRecordDTO> proxyTransfer(@RequestBody AliPayProxyTransferQueryDTO aliPayCapitalTransferQueryParam) {
        try {
            logger.info("代发记录:{}", JSONUtils.toJsonString(aliPayCapitalTransferQueryParam));
            //文件处理
            ShiroUser shiroUser = super.getCurrentUser();
            String customerNumber = shiroUser.getCustomerNumber();
            logger.info("代发记录:{}", JSONUtils.toJsonString(customerNumber));
            if(customerNumber==null){
                return BasePageRespDTO.fail(ResponseMessage.Status.ERROR.getValue(), "请确认登陆");
            }
            aliPayCapitalTransferQueryParam.setMerchantNo(customerNumber);
            Integer totalCount = aliPayChannelService.proxyTransferCount(aliPayCapitalTransferQueryParam);
            List<ChannelBookProxyTransBatchInfoEntity> batchInfoEntities = aliPayChannelService.proxyTransfer(aliPayCapitalTransferQueryParam, aliPayCapitalTransferQueryParam.getPageNo(), aliPayCapitalTransferQueryParam.getPageSize());
            List<BatchProxyTransferRecordDTO> batchProxyTransferRecordDTOS = batchInfoEntities.stream().map(item -> {
                BatchProxyTransferRecordDTO batchProxyTransferDTO = new BatchProxyTransferRecordDTO();
                batchProxyTransferDTO.setId(item.getId());
                batchProxyTransferDTO.setCreateTime(item.getCreateTime());
                batchProxyTransferDTO.setLastModifyTime(item.getLastModifyTime());
                batchProxyTransferDTO.setChannelBookId(item.getChannelBookId());
                batchProxyTransferDTO.setChannelBookName(item.getChannelBookName());
                batchProxyTransferDTO.setBatchNo(item.getBatchNo());
                batchProxyTransferDTO.setStatus(item.getStatus());
                batchProxyTransferDTO.setStatusDes(BatchProxyTransferStatusEnum.getDesByName(item.getStatus()));
                batchProxyTransferDTO.setTotalAmount(item.getTotalAmount());
                batchProxyTransferDTO.setSuccessAmount(item.getSuccessAmount());
                batchProxyTransferDTO.setFailAmount(item.getFailAmount());
                batchProxyTransferDTO.setSuccessCount(item.getSuccessCount());
                batchProxyTransferDTO.setFailCount(item.getFailCount());
                batchProxyTransferDTO.setServiceCharge(item.getServiceCharge());
                batchProxyTransferDTO.setMerchantNo(item.getMerchantNo());
                batchProxyTransferDTO.setOrderNo(item.getOrderNo());
                return batchProxyTransferDTO;
            }).collect(Collectors.toList());
            return BasePageRespDTO.successPage(batchProxyTransferRecordDTOS, totalCount == null ? 0L : Long.valueOf(totalCount));
        } catch (Exception e) {
            logger.error("系统异常: ", e);
            return BasePageRespDTO.fail(WrapMapper.ERROR, e.getMessage());
        }
    }

    /**
     * 代发记录下载
     */
    @RequestMapping(value = "/proxyTransferDownload")
    @ResponseBody
    public Wrapper<Void> proxyTransferDownload(@RequestBody AliPayProxyTransferQueryDTO aliPayCapitalTransferQueryParam, HttpServletRequest request, HttpServletResponse response) {
        try {
            logger.info("代发记录:{}", JSONUtils.toJsonString(aliPayCapitalTransferQueryParam));
            //文件处理
            ShiroUser shiroUser = super.getCurrentUser();
            String customerNumber = shiroUser.getCustomerNumber();
            aliPayCapitalTransferQueryParam.setMerchantNo(customerNumber);
            List<ChannelBookProxyTransBatchInfoEntity> batchInfoEntities = aliPayChannelService.proxyTransferNopage(aliPayCapitalTransferQueryParam);
            List<BatchProxyTransferRecordDTO> batchProxyTransferRecordDTOS = batchInfoEntities.stream().map(item -> {
                BatchProxyTransferRecordDTO batchProxyTransferDTO = new BatchProxyTransferRecordDTO();
                batchProxyTransferDTO.setId(item.getId());
                batchProxyTransferDTO.setCreateTime(item.getCreateTime());
                batchProxyTransferDTO.setLastModifyTime(item.getLastModifyTime());
                batchProxyTransferDTO.setChannelBookId(item.getChannelBookId());
                batchProxyTransferDTO.setChannelBookName(item.getChannelBookName());
                batchProxyTransferDTO.setBatchNo(item.getBatchNo());
                batchProxyTransferDTO.setStatus(item.getStatus());
                batchProxyTransferDTO.setStatusDes(BatchProxyTransferStatusEnum.getDesByName(item.getStatus()));
                batchProxyTransferDTO.setTotalAmount(item.getTotalAmount());
                batchProxyTransferDTO.setSuccessAmount(item.getSuccessAmount());
                batchProxyTransferDTO.setFailAmount(item.getFailAmount());
                batchProxyTransferDTO.setSuccessCount(item.getSuccessCount());
                batchProxyTransferDTO.setFailCount(item.getFailCount());
                batchProxyTransferDTO.setServiceCharge(item.getServiceCharge());
                batchProxyTransferDTO.setMerchantNo(item.getMerchantNo());
                batchProxyTransferDTO.setOrderNo(item.getOrderNo());
                return batchProxyTransferDTO;
            }).collect(Collectors.toList());
            ExcelUtils.exportFile(batchProxyTransferRecordDTOS, BatchProxyTransferRecordDTO.class, response, "代发记录", aliPayCapitalTransferQueryParam.getDownType());
            return WrapMapper.ok();
        } catch (Exception e) {
            logger.error("系统异常: ", e);
            return WrapMapper.wrap(WrapMapper.ERROR, e.getMessage());
        }
    }


    @RequestMapping(value = "/confirmTransfer")
    @ResponseBody
    public Wrapper<BatchTransferVO> batchSendConfirm(@RequestParam("batchNo") String batchNo) {
        try {
            logger.info("ajaxBatchSendConfirm批量确认{}", batchNo);
            ShiroUser shiroUser = super.getCurrentUser();
            String dataString = RedisUtils.get(shiroUser.getCustomerNumber() + batchNo + "_data");
            if (org.springframework.util.StringUtils.isEmpty(dataString)) {
                logger.warn("batchNo=[{}],已过期", batchNo);
                return WrapMapper.wrap(ResponseMessage.Status.ERROR.getValue(), "数据已过期");
            }
            logger.debug("confirm dataString=[{}]", dataString);
            BatchTransferVO batchTransferVO = aliPayChannelService.batchSendConfirm(dataString, shiroUser);
            return WrapMapper.ok(batchTransferVO);
        } catch (Exception ex) {
            return WrapMapper.wrap(ResponseMessage.Status.ERROR.getValue(), ex.getMessage());
        }
    }

    //代发明细下载

    //代发批次信息下载


    /**
     * 下载模版
     *
     * @param request
     * @param response
     */
    @RequestMapping("/downloadBatchTemplate")
    public void downloadBatchTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = ResourceUtils.getFile("classpath:/fileTemplate/转账到支付宝批量代发模板.xls");
            Workbook wb = WorkbookFactory.create(file);
            POIUtil.downLoadData(response, file.getName(), (HSSFWorkbook) wb);
        } catch (Exception ex) {
            logger.error("下载模版异常", ex);
            try {
                response.getWriter().write("<script type='text/javascript'>parent.mpAlert('下载模版异常')</script>");
            } catch (IOException e) {

            }
        }
    }

    /**
     * 批量代发枚举
     */
    @RequestMapping("/batchStatusEnum")
    @ResponseBody
    public Wrapper<List<EnumNameValue<String>>> batchStatusEnum() {
        List<EnumNameValue<String>> collect = Arrays.stream(BatchProxyTransferStatusEnum.values()).map(item -> {
            EnumNameValue<String> data = new EnumNameValue<>();
            data.setName(item.name());
            data.setValue(item.getDescription());
            return data;
        }).collect(Collectors.toList());
        return WrapMapper.ok(collect);
    }

    /**
     * 资金拨入统计
     */
    @RequestMapping("/capitalTransferCount")
    @ResponseBody
    public Wrapper<CapitalTransferCountRspDTO> capitalTransferCount(@RequestBody AliPayCapitalTransferQueryDTO aliPayCapitalTransferQueryParam) {
        logger.info("资金拨入统计:{}", JSONUtils.toJsonString(aliPayCapitalTransferQueryParam));
        ShiroUser shiroUser = super.getCurrentUser();
        aliPayCapitalTransferQueryParam.setMerchantNo(shiroUser.getCustomerNumber());
        CapitalTransferCountRspDTO capitalTransferCount = aliPayChannelService.capitalTransferCount(aliPayCapitalTransferQueryParam);
        return WrapMapper.ok(capitalTransferCount);
    }

    /**
     * 批量代发统计
     */
    @RequestMapping("/proxyTransferCountSum")
    @ResponseBody
    public Wrapper<ProxyTransferCountRspDTO> proxyTransferCountSum(@RequestBody AliPayProxyTransferQueryDTO aliPayCapitalTransferQueryParam) {
        logger.info("批量代发统计:{}", JSONUtils.toJsonString(aliPayCapitalTransferQueryParam));
        ShiroUser shiroUser = super.getCurrentUser();
        aliPayCapitalTransferQueryParam.setMerchantNo(shiroUser.getCustomerNumber());
        ProxyTransferCountRspDTO proxyTransferCount = aliPayChannelService.proxyTransferCountSum(aliPayCapitalTransferQueryParam);
        return WrapMapper.ok(proxyTransferCount);
    }

    /**
     * 批量代发统计
     */
    @RequestMapping("/proxyTransferDetailCountSum")
    @ResponseBody
    public Wrapper<ProxyTransferDetailCountSumRspDTO> proxyTransferDetailCountSum(@Param("orderNo") String orderNo) {
        logger.info("代发明细统计:{}", JSONUtils.toJsonString(orderNo));
        ShiroUser shiroUser = super.getCurrentUser();
        ChannelBookProxyTransBatchInfoEntity batchProxyInfo = aliPayChannelService.getBatchProxyInfoByOrderNo(orderNo, shiroUser.getCustomerNumber());
        if (batchProxyInfo == null) {
            return WrapMapper.wrap(WrapMapper.ERROR, "未找到指定信息");
        }
        ProxyTransferDetailCountSumRspDTO channelBookProxyTransEntityList = aliPayChannelService.proxyTransferDetailCountSum(batchProxyInfo.getOrderNo(), shiroUser.getCustomerNumber());
        return WrapMapper.ok(channelBookProxyTransEntityList);
    }
}
