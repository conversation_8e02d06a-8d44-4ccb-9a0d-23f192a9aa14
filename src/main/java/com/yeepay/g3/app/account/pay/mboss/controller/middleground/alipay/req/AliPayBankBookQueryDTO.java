package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;

/**
 * @Description: 支付宝记账本信息查询
 */
@ApiModel(description = "支付宝记账本信息查询")
public class AliPayBankBookQueryDTO implements Serializable {
    /**
     * 记账本id
     */
    private String channelBookId;
    /**
     * 记账本名称
     */
    private String channelBookName;
    /**
     * 记账本名称
     */
    private String merchantNo;

    private Integer pageSize=10;

    private Integer pageNo=1;

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getChannelBookName() {
        return channelBookName;
    }

    public void setChannelBookName(String channelBookName) {
        this.channelBookName = channelBookName;
    }
}
