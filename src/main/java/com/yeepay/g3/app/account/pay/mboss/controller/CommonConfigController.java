package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.SettleRechargeStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.SettleWayEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.helper.BankCodeHelper;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * title: 通用配置
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/6/24 18:10
 */
@Controller
@RequestMapping("/commonConfig/")
public class CommonConfigController {

    /**
     * 查询银行下拉框选项
     *
     * @param type 业务场景类型
     * @return 下拉框选项map
     */
    @RequestMapping(value = "getConfig", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询通用配置")
    public ResponseMessage getConfig(@RequestParam(value = "type", required = true) String type) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        if (StringUtils.isBlank(type)) {
            throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("查询类型不能为空");
        }
        if (type.equals("rechargeOpt")) {
            Map<String, String> rechargeMap = BankCodeHelper.getRechargeMap();
            resMsg.getData().put("map", rechargeMap);
        }
        return resMsg;
    }

}
