package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import com.yeepay.g3.app.account.pay.mboss.utils.file.ExcelField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ChannelBookProxyTransDTO implements Serializable {

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date lastModifyTime;
    /**
     * 渠道记账本id
     */
    private String channelBookId;
    /**
     * 渠道记账本id
     */
    private String channelBookName;

    /**
     * 易宝订单号
     */
    @ExcelField(title = "易宝订单号",order = 1)
    private String orderNo;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 手续费状态
     * DEDUCT 扣除
     * REFUNDED 退回
     */
    private String feeStatus;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 状态
     */
    private String status;
    /**
     * 状态
     */
    @ExcelField(title = "代发状态",order = 5)
    private String statusDesc;
    /**
     * 金额
     */
    @ExcelField(title = "代发金额",order = 4)
    private BigDecimal transferAmount;

    /**
     * 商编
     */
    private String merchantNo;
    /**
     * 接收方
     */
    @ExcelField(title = "收款方支付宝账号",order = 2)
    private String receiveNo;

    /**
     * 接收方名称
     */
    @ExcelField(title = "收款方支付宝名称",order = 3)
    private String receiveName;

    /**
     * 备注
     */
    @ExcelField(title = "代发备注",order = 6)
    private String remark;

    @ExcelField(title = "失败原因",order = 7)
    private String message;

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getChannelBookName() {
        return channelBookName;
    }

    public void setChannelBookName(String channelBookName) {
        this.channelBookName = channelBookName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getTransferAmount() {
        return transferAmount;
    }

    public void setTransferAmount(BigDecimal transferAmount) {
        this.transferAmount = transferAmount;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getReceiveNo() {
        return receiveNo;
    }

    public void setReceiveNo(String receiveNo) {
        this.receiveNo = receiveNo;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
