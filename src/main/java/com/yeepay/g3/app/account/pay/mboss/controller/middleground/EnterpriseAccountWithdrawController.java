package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.EnterpriseWithdrawQueryReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.EnterpriseWithdrawReqDTO;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.service.EnterpriseAccountOrderService;
import com.yeepay.g3.app.account.pay.mboss.utils.BACRsaUtil;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.MpWithdrawBankRespDTO;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yibao.utils.json.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * @ClassName: EnterpriseAccountWithdrawController
 * @Description:
 * <AUTHOR>
 * @Date 2023/11/8
 * @Version 1.0
 */
@Controller
@Api(tags = "企业号账户提现管理-API")
@RequestMapping("/enterpriseWithdraw")
public class EnterpriseAccountWithdrawController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseAccountWithdrawController.class);


    @Resource
    private EnterpriseAccountOrderService enterpriseAccountWithdrawService;

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    @RequiresPermissions("***********")
    @RequestMapping("/orderManage")
    @ApiOperation(hidden = true, value = "订单管理")
    public ModelAndView bankPaymentManage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("enterpriseAccount/enterpriseOrderManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("订单管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 提现到银行获取初始化的信息
     * 余额
     * 密钥
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/init", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("企业号提现初始化信息")
    public ResponseMessage init(@RequestParam(value = "bankCode") String bankCode,
                                @RequestParam(value = "enterpriseAccountNo") String enterpriseAccountNo) {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Map<String, Object> map = enterpriseAccountWithdrawService.getWithdrawInitInfo(user.getCustomerNumber(), bankCode, enterpriseAccountNo);
            resMsg.put("map", map);
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("获取企业号提现初始化的信息异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("获取企业号提现初始化的信息异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("加载信息异常");
        }
        return resMsg;
    }


    /**
     * 企业号提现
     *
     * @param request
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("企业号提现下单")
    public ResponseMessage confirm(HttpServletRequest request, @RequestBody EnterpriseWithdrawReqDTO reqDTO) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("企业号提现下单参数：reqDTO:{}", JsonUtils.toJson(reqDTO));
            //校验参数
            reqDTO.validateParam();
            ShiroUser user = super.getCurrentUser();
            checkArgument(StringUtils.isNotBlank(reqDTO.getPasswd()));
            //交易密码改为密文传输，需要解密
            String decryptPassWord = BACRsaUtil.privateDecrypt(reqDTO.getPasswd(), ConfigUtils.getPrivateKey());
            //1.验证密码
            enterpriseAccountWithdrawService.queryTradePasswordValidateResult(user.getLoginName(),decryptPassWord);
            String merchantNo = getCurrentCustomerNumber();
            reqDTO.setMerchantNo(merchantNo);
            MpWithdrawBankRespDTO respDTO = enterpriseAccountWithdrawService.mpWithdraw(reqDTO);
            if (CheckUtils.isEmpty(respDTO) || !"UA00000".equals(respDTO.getReturnCode())) {
                logger.info("企业号提现失败,请求参数为={}，返回信息为={}", JSONUtils.toJsonString(reqDTO), JSONUtils.toJsonString(respDTO));
                resMsg.setErrCode(respDTO.getReturnCode());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg(respDTO.getReturnMsg());
            }
        } catch (NumberFormatException e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("提现金额格式不正确");
        } catch (YeepayBizException e) {
            logger.info("企业号提现业务异常,请求参数为={}", JSONUtils.toJsonString(reqDTO), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Throwable e) {
            logger.error("企业号提现异常,请求参数为={}", JSONUtils.toJsonString(reqDTO), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 企业号订单查询
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/queryOrderList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("企业号订单查询")
    public ResponseMessage queryOrderList(@RequestBody EnterpriseWithdrawQueryReqDTO reqDTO) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("企业号订单查询，请求参数{}", JSON.toJSONString(reqDTO));
            //参数校验
            reqDTO.setMerchantNo(getCurrentCustomerNumber());
            //查询列表
            List<Map<String, Object>> queryResult = enterpriseAccountWithdrawService.queryWithOrderList(reqDTO);
            resMsg.put("pageNo", reqDTO.getPageNo());
            resMsg.put("pageSize", reqDTO.getPageSize());
            resMsg.put("dataList", queryResult);
            Map<String, Object> map = enterpriseAccountWithdrawService.queryWithOrderSum(reqDTO);
            resMsg.put("sumAmount", map.get("sum_amount"));
            resMsg.put("sumCount", map.get("sum_count"));
            resMsg.put("sumFee", map.get("sum_fee"));
            resMsg.put("totalCount", map.get("totalCount"));
        } catch (Throwable e) {
            logger.error("企业号提现查询异常,请求参数为={}", JSONUtils.toJsonString(reqDTO), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 提现到银行查询产品是否开通
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getProductInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("企业号提现初始化信息")
    public ResponseMessage getProductInfo(@RequestParam(value = "bankCode", required = false) String bankCode) {
        ShiroUser user = super.getCurrentUser();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Boolean productOpen = enterpriseAccountWithdrawService.getProductOpen(user.getCustomerNumber(), bankCode);
            resMsg.put("productOpen", productOpen);
            return resMsg;
        } catch (AccountPayException e) {
            logger.error("获取企业号产品开通异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            logger.error("获取企业号产品开通异常,商户为={},异常信息为={}", user.getCustomerNo(), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统异常");
        }
        return resMsg;
    }

}
