package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.dto.req.DownloadElectronicReqDTO;
import com.yeepay.g3.app.account.pay.mboss.entity.TransferOrder;
import com.yeepay.g3.app.account.pay.mboss.enumtype.*;
import com.yeepay.g3.app.account.pay.mboss.enumtype.TransferTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.CapitalManageService;
import com.yeepay.g3.app.account.pay.mboss.service.CookieService;
import com.yeepay.g3.app.account.pay.mboss.service.TransferAuditService;
import com.yeepay.g3.app.account.pay.mboss.service.TransferOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.*;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.capital.manage.dto.req.QueryMatchingLoanReqDTO;
import com.yeepay.g3.capital.manage.dto.resp.QueryMatchingLoanRespDTO;
import com.yeepay.g3.facade.merchant.fee.dto.CalFeeRequestDto;
import com.yeepay.g3.facade.merchant.fee.dto.CalFeeResponseDto;
import com.yeepay.g3.facade.merchant.fee.facade.CalFeeFacade;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.BaseProductDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantProductQueryRespDTO;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.AccountInfoQueryRequestDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfo;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.TradeTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.exception.UnionAccountManageException;
import com.yeepay.g3.facade.unionaccount.manage.facade.AccountManageFacade;
import com.yeepay.g3.facade.unionaccount.transfer.dto.request.BgTransferReqDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.response.BgTransferOrderRespDTO;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.*;
import com.yeepay.g3.facade.unionaccount.transfer.facade.TransferExpandFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.unionaccount.base.service.untils.AccountTypeNameUtil;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CommonUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * 转账
 */
@Controller
@RequestMapping("/transfer")
public class MgTransferController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MgTransferController.class);

    //计费相关
    private CalFeeFacade calFeeFacade = RemoteServiceFactory.getService(CalFeeFacade.class);

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    // 账务
    private AccountManageFacade accountManageFacade = RemoteServiceFactory.getService(AccountManageFacade.class);

    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";

    // 默认当前
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private CookieService cookieService;

    private TransferExpandFacade transferExpandFacade = RemoteServiceFactory.getService(TransferExpandFacade.class);

    @Autowired
    private CapitalManageService capitalManageService;

    @Autowired
    private TransferAuditService transferAuditService;

    /**
     * 跳转到转账首页
     * @param request
     * @return
     */
    @RequestMapping(value = "/view", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView view(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("mgTransfer/index");
        return mav;
    }

    @RequestMapping(value = "/home", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView home(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("mgTransfer/singleTransferIndex");
        return mav;
    }

    /**
     * 转账首页
     * @param request
     * @return
     */
    @RequestMapping(value = "/index", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage index(HttpServletRequest request) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        //当前商编
        String currentCustomerNumber = getCurrentCustomerNumber();
        BigDecimal balance =null;
        try{
            //查询余额
           balance = queryBalance(AccountTypeEnum.FUND_ACCOUNT.name());
        }catch (Exception e){
            LOGGER.info("查询余额异常，e={}",e);
            List<String> productList = new ArrayList<String>();
            List<AccountInfoParam> accountList = new ArrayList<>();
            resMsg.put("accountTypeList",accountList);
            resMsg.put("productList",productList);
            return resMsg;

        }
        try{
            //判断是否开通转账产品
            resMsg = isDisplayProductAndUndertaker(currentCustomerNumber,resMsg);
            List<String> productList = (List)resMsg.getData().get("productList");
            //判断是否开通账户转账
            List<AccountInfoParam> accountList = getAccountList(currentCustomerNumber);
            resMsg.put("accountTypeList",accountList);
            if(CollectionUtils.isNotEmpty(accountList)){
                productList.add(TransferTypeEnum.ACCOUNT.name());
            }
            resMsg.put("balance",balance);
        }catch (Exception e){
            LOGGER.error("商户后台转账首页信息异常,currentCustomerNumber="+currentCustomerNumber,e);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        LOGGER.info("商户后台转账首页信息返回，resMsg={}",JSON.toJSON(resMsg));
        return resMsg;
    }

    /**
     * 校验交易密码
     * @param request
     * @param tradePassword
     * @return
     */
    @RequestMapping(value = "/checkPassWord", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage checkPassWord(HttpServletRequest request,@RequestParam("tradePassword") String tradePassword){
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String currentCustomerNumber = getCurrentCustomerNumber();
        ShiroUser currentUser = getCurrentUser();
        if(currentUser != null){
            String token = currentUser.getToken();
            Long userId = currentUser.getUserId();
            checkNotNull(userId);
            checkNotNull(token);
            LOGGER.info("当前商户，merchantNo={},token={}",currentCustomerNumber,token);
            if (!userFacade.validateTradePassword(userId, tradePassword)) {
                resMsg.put("isValidPassed", false);
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("交易密码不正确");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            } else {
                String key =currentCustomerNumber+"-"+token;
                resMsg.put("isValidPassed", true);
                resMsg.setStatus(ResponseMessage.Status.SUCCESS);
                RedisUtils.set(key,CheckResultTypeEnum.PASS.name(), ConfigUtils.getCheckPassWordExpire());
            }
        }else{
            LOGGER.info("获取当前用户失败");
        }
        LOGGER.info("密码校验返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    /**
     * 校验转入方信息
     * @param request
     * @param toMerchantNo
     * @param toMerchantName
     * @return
     */
    @RequestMapping(value = "/checkToMerchant", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage checkToMerchant(HttpServletRequest request, @RequestParam("toMerchantNo") String toMerchantNo,
                                           @RequestParam(value="toMerchantName") String toMerchantName) {
        toMerchantNo = toMerchantNo.trim();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("校验转入方信息，toMerchantNo=【{}】,toMerchantName=【{}】", toMerchantNo,toMerchantName);
        if(currentCustomerNumber.equals(toMerchantNo) ){
            resMsg.put("isMatch",false);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("商户转账时转出方与转入方不能相同,建议使用账户转账");
            return resMsg;
        }
        try {
            String merchantName = merchantRemoteService.getMerchantName(toMerchantNo);
            if(StringUtils.isNotBlank(merchantName)){
                if(toMerchantName.trim().equals(merchantName)){
                    resMsg.put("isMatch",true);
                }else{
                    resMsg.put("isMatch",false);
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("请核对转入方商户信息是否正确。");
                }
            }else{
                LOGGER.info("商户信息为空");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg("商户不存在或状态异常");
                return resMsg;
            }

        } catch (AccountPayException e) {
            resMsg.put("isMatch", false);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        } catch (Exception e) {
            LOGGER.error("查询异常，e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("商户查询异常");
            return resMsg;
        }

        LOGGER.info("转入方商户校验返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }



    /**
     * 跳转转账记录页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public ModelAndView queryView(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();

        String customerNumber = super.getCurrentCustomerNumber();
        if(StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            mav.addObject("customerSign", customerSign);
        }
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());

        mav.setViewName("mgTransfer/transferRecord");
        return mav;

    }

    /**
     * 转账记录页(迁移红版后台)
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryCustomerSign", method = RequestMethod.GET)
    public BaseRespDTO queryCustomerSign() {
        Map<String,Object> map=new HashMap<>();
        String customerNumber = super.getCurrentCustomerNumber();
        if (StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            map.put("customerSign", customerSign);
        }
        return BaseRespDTO.success(map);
    }


    @Autowired
    private TransferOrderService transferOrderService;

    /**
     * 跳转转账详情页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ResponseBody
    @Deprecated
    public ModelAndView queryOrderDetail(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        String customerNumber = request.getParameter("customerNumber");
        PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber), "商户编号不能为空");
        LOGGER.info("查询转账订单明细，订单号：orderNo={},customerNumber={}", orderNo, customerNumber);
        if (!CheckUtils.isEmpty(orderNo)) {
            TransferOrder dto = new TransferOrder();
            dto.setFromCustomerNo(customerNumber);
            dto.setOrderNo(orderNo);
            TransferOrder transferOrder = null;
            try {
                transferOrder = transferOrderService.queryTransferOrder(dto);
            } catch (Exception e) {
                LOGGER.error("查询转账订单异常", e);
            }
            TransferResponseParam transferResponseParam = new TransferResponseParam();
            if (transferOrder != null) {
                MgTransferAccountTypeEnum transferAccountType = transferOrder.getTransferAccountType();
                TransferStatusTypeEnum transferStatus = transferOrder.getStatus();
                transferResponseParam.setTransferStatus(transferStatus.getDesc());
                if (null != transferOrder.getCreateTime()) {
                    transferResponseParam.setCreateTime(DateUtil.formatDate(transferOrder.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                }

                if (null != transferOrder.getCompletedTime()) {
                    transferResponseParam.setFinishTime(DateUtil.formatDate(transferOrder.getCompletedTime(), "yyyy-MM-dd HH:mm:ss"));
                }

                if (null != transferOrder.getFee()) {
                    BigDecimal fee = transferOrder.getFee().setScale(2, BigDecimal.ROUND_HALF_UP);
                    transferResponseParam.setFee(fee+ "元");
                } else {
                    transferResponseParam.setFee("-");
                }

                if (null != transferOrder.getDeductionAmount()) {
                    transferResponseParam.setDebitAmount(transferOrder.getDeductionAmount().setScale(2).toPlainString());
                }
                if (null != transferOrder.getEnteredAmount()) {
                    transferResponseParam.setReceiveAmount(transferOrder.getEnteredAmount().setScale(2).toPlainString());
                }

                if (null != transferOrder.getTransferAmount()) {
                    transferResponseParam.setOrderAmount(transferOrder.getTransferAmount().setScale(2).toPlainString());
                }

                transferResponseParam.setFromMerchantNo(transferOrder.getFromCustomerNo());
                String fromMerchantName = transferOrder.getFromCustomerName();
                if (StringUtils.isNotBlank(fromMerchantName)) {
                    transferResponseParam.setFromMerchantName(transferOrder.getFromCustomerName());
                } else {
                    String merchantName = merchantRemoteService.getMerchantName(transferOrder.getFromCustomerNo());
                    if (StringUtils.isBlank(merchantName)) {
                        merchantName = "-";
                    }
                    transferResponseParam.setFromMerchantName(merchantName);
                }
                if (transferAccountType.name().equals(MgTransferAccountTypeEnum.MERCHANT.name())) {
                    String toMerchantNo = transferOrder.getToCustomerNo();
                    String toMerchantName = transferOrder.getToCustomerName();
                    if (StringUtils.isNotBlank(toMerchantName)) {
                        transferResponseParam.setToMerchantName(transferOrder.getToCustomerName() + "(" + toMerchantNo + ")");
                    } else {
                        String merchantName = merchantRemoteService.getMerchantName(toMerchantNo);
                        transferResponseParam.setToMerchantName(merchantName + "(" + toMerchantNo + ")");
                    }
                    transferResponseParam.setTransferAccountType(TransferTypeEnum.MERCHANT.getDesc());
                } else if (transferAccountType.name().equals(MgTransferAccountTypeEnum.ACCOUNT.name())) {
                    String toAccountType = transferOrder.getToAccountType();
                    transferResponseParam.setToMerchantName(AccountTypeNameUtil.getAccountTypeByName(toAccountType));
                    transferResponseParam.setTransferAccountType(TransferTypeEnum.ACCOUNT.getDesc());
                } else {
                    LOGGER.info("转账方式不存在");
                }
                String operator = transferOrder.getOperator();
                if (StringUtils.isNotBlank(operator)) {
                    transferResponseParam.setOperator(transferOrder.getOperator());
                } else {
                    transferResponseParam.setOperator("-");
                }

                transferResponseParam.setOrderNo(transferOrder.getOrderNo());
                transferResponseParam.setTransferStatus(transferOrder.getStatus().getDesc());
                transferResponseParam.setFromAccountType(AccountTypeEnum.valueOf(transferOrder.getFromAccountType()).getDesc());
                transferResponseParam.setToAccountType(AccountTypeEnum.valueOf(transferOrder.getToAccountType()).getDesc());
                transferResponseParam.setUsage(transferOrder.getUsage());

                if (transferStatus == TransferStatusTypeEnum.FAIL) {
                    transferResponseParam.setReturnMsg(transferOrder.getReturnMessage());
                } else {
                    transferResponseParam.setReturnMsg("-");
                }
                transferResponseParam.setBatchNo(transferOrder.getBatchNo());
            } else {
                LOGGER.info("转账订单信息不存在");
            }
            LOGGER.info("查询订单明细返回，transferResponseParam={}", JSON.toJSONString(transferResponseParam));
            mav.addObject("orderDetail", transferResponseParam);
        } else {
            LOGGER.info("订单号为空");
            throw new RuntimeException("订单号不能为空");
        }
        mav.setViewName("mgTransfer/detail");
        return mav;
    }



    /**
     * 转账下载电子回单
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/downElectronicReceipt")
    @ResponseBody
    public BaseRespDTO downElectronicReceipt(DownloadElectronicReqDTO param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("转账下载电子回单 请求参数{}",JSONObject.toJSONString(param));
            String orderNo = param.getOrderNo();
            if (StringUtils.isBlank(orderNo)) {
                LOGGER.error("转账下载[电子回单]  orderNo 为空 the customerNumber=[{}]", param.getCustomerNumber());
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("订单编号为空");
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            long start = System.currentTimeMillis();

            TransferOrder transferOrder = new TransferOrder();
            transferOrder.setFromCustomerNo(param.getCustomerNumber());
            transferOrder.setOrderNo(orderNo);
            TransferOrder dto = transferOrderService.queryTransferOrder(transferOrder);
            if (Objects.isNull(dto)) {
                LOGGER.error("转账下载[电子回单]  TransferOrder 为空 query={}", JSONObject.toJSONString(transferOrder));
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("转账订单详情为空");
            }
            //构建打印pdf
            PdfUtils pdfUtils = new PdfUtils();
            OrderParam orderParam = buildParam(dto);
            String data = pdfUtils.generateReceipt(orderParam, getCurrentCustomerNumber());

            BASE64Decoder decoder = new BASE64Decoder();
            //Base64解码
            byte[] len = decoder.decodeBuffer(data);
            for (int i = 0; i < len.length; ++i) {
                //调整异常数据
                if (len[i] < 0) {
                    len[i] += 256;
                }
            }
            /*在开始写入的那一刻再定义这些，不然影响异常的弹窗*/
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + "TRANSFER" + "_" + System.currentTimeMillis() + ".pdf");
            OutputStream out = response.getOutputStream();
            out.write(len);
            out.flush();
            out.close();
            long end = System.currentTimeMillis();
            LOGGER.info("转账电子回单耗时{}ms", (end - start));
            return BaseRespDTO.success();
        } catch (Throwable ex) {
            LOGGER.error("转账下载[电子回单] 异常， 来个弹窗 ex={}", ex);
            return BaseRespDTO.fail(ex.getMessage());
        }
    }




    /**
     * 确认转账
     *
     * @param amount
     * @param toMerchantNo
     * @param transferType
     * @param toMerchantName
     * @param toAccountType
     * @param feeChargeSide
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage confirm( @RequestParam("amount") String amount,
                                    @RequestParam(value="toMerchantNo",required = false) String toMerchantNo,
                                    @RequestParam("transferType") String transferType,
                                    @RequestParam(value ="toMerchantName",required = false) String toMerchantName,
                                    @RequestParam(value = "toAccountType",required = false) String toAccountType,
                                    @RequestParam(value = "toAccountBookNo", required = false) String toAccountBookNo,
                                    @RequestParam(value = "feeChargeSide",required = false) String feeChargeSide,
                                    @RequestParam(value = "usage") String usage,
                                    @RequestParam(value = "tradePassword") String tradePassword,
                                    @RequestParam(value = "auditFile",required = false) String auditFile) {
        toMerchantNo = toMerchantNo.trim();
        LOGGER.info("转账确认请求参数，amount={},toMerchantNo={},transferType={},toMerchantName={},toaccountType={},feeChargeSide={},usage={}",
                amount, toMerchantNo, transferType, toMerchantName, toAccountType, feeChargeSide,usage);


        // 交易密码需要解密
        tradePassword = BACRsaUtil.privateDecrypt(tradePassword, ConfigUtils.getPrivateKey());

        String currentCustomerNumber = getCurrentCustomerNumber();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);

        ShiroUser currentUser = getCurrentUser();
        try {
            checkNotNull(amount,"金额不能为空");
            checkNotNull(transferType,"转账方式不能为空");
            checkNotNull(usage,"转账备注不能为空");
            if(currentUser != null){
                if (!userFacade.validateTradePassword(currentUser.getUserId(), tradePassword)) {
                    resMsg.put("errorType", "passwordError");
                    resMsg.setErrCode("9999");
                    resMsg.setErrMsg("交易密码不正确");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    return resMsg;
                }
                boolean needAudit = false;
                //构建转账参数
                BgTransferReqDTO dto = buildMgTransferReqDTO(currentCustomerNumber, amount, usage);
                dto.setAuditFile(auditFile);
                if(TransferTypeEnum.ACCOUNT.name().equals(transferType)){
                    dto.setToMerchantNo(currentCustomerNumber);
                    checkNotNull(toAccountType,"转入账户不能为空");
                    dto.setToAccountType(AccountTypeEnum.valueOf(toAccountType));
                    if (AccountTypeEnum.VCC_ACCOUNT == dto.getToAccountType()) {
                        dto.setNotifyType(TransferCrossNotifyTypeEnum.SMS);
                        dto.setNotifyUrl(currentUser.getBindedMobile());
                    }
                }else if(TransferTypeEnum.MERCHANT.name().equals(transferType)){
                    checkNotNull(feeChargeSide,"手续费承担方不能为空");
                    dto.setToMerchantNo(toMerchantNo);
                    dto.setToMerchantName(toMerchantName);
                    if (StringUtils.isBlank(toAccountType)) {
                        dto.setToAccountType(AccountTypeEnum.FUND_ACCOUNT);
                    } else {
                        dto.setToAccountType(AccountTypeEnum.valueOf(toAccountType));
                        dto.setToAccountNo(toAccountBookNo);
                    }
                    if(FeeTypeEnum.PAYER.name().equals(feeChargeSide)){
                        //转出方承担
                        dto.setFeeChargeSide(FeeChargeTypeEnum.OUTSIDE);
                    }else if(FeeTypeEnum.PAYEE.name().equals(feeChargeSide)){
                        //转入方承担
                        dto.setFeeChargeSide(FeeChargeTypeEnum.INSIDE);
                    }else{
                        LOGGER.info("手续承担方为平台方");
                    }
                    try{
                        needAudit = transferAuditService.dealMpSourceAuditIdentify(currentUser.getCustomerNumber());
                    }catch (Exception ex){
                        LOGGER.error("获取商户是否需要复核异常！",ex);
                    }
                }else{
                    LOGGER.info("转账方式不支持");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("转账方式不支持");
                    return resMsg;
                }

                resMsg.put("needAudit",needAudit);

                LOGGER.info("转账确认请求参数，dto={}", JSON.toJSONString(dto));
                BgTransferOrderRespDTO respDTO = transferExpandFacade.bgTransfer(dto);
                LOGGER.info("转账确认返回参数，respDTO={}", JSON.toJSONString(respDTO));
                TransferResponseParam response = new TransferResponseParam();
                if(respDTO != null){
                    String returnCode = respDTO.getReturnCode();
                    if("UA00000".equals(returnCode)){
                        if(needAudit){
                            resMsg.put("requestNo",respDTO.getRequestNo());
                        }else{
                            response.setTransferStatus(respDTO.getTransferStatus().name());
                            response.setFinishTime(respDTO.getFinishTime());
                            response.setOrderNo(respDTO.getOrderNo());
                            response.setOrderAmount(respDTO.getOrderAmount());
                            response.setReceiveAmount(respDTO.getReceiveAmount());
                            response.setFee(respDTO.getFee());
                        }
                        resMsg.put("transferResult",response);
                    }else{
                        resMsg.setStatus(ResponseMessage.Status.ERROR);
                        resMsg.setErrMsg(respDTO.getReturnMsg());
                    }
                }else{
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("转账异常，请稍后重试");
                }

            }else{
                LOGGER.info("获取当前用户信息为空");
            }
        } catch (Exception e) {
            LOGGER.error("转账异常，e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常，请稍后再试");
        }

        LOGGER.info("转账返回，resMsg={}", JSON.toJSONString(resMsg));

        return resMsg;
    }
    /**
     * 查询转账订单列表
     *
     * @param transferQueryParam:
     * @Description: 查询转账订单列表（批量)
     */
    @RequestMapping(value = "/queryOrderList")
    @ResponseBody
    public ResponseMessage queryOrderList(TransferQueryParam transferQueryParam,
                                          @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                          @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("查询转账订单，请求参数{}", JSON.toJSONString(transferQueryParam));
        PreCheck.checkArgument(StringUtils.isNotBlank(transferQueryParam.getCustomerNumber()), "商户编号不能为空");
        if (transferQueryParam.isEmptyCheck()) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询参数为空");
            return resMsg;
        }

        QueryResult queryResult = null;
        try {
            //查询列表
            queryResult = this.queryTransferOrderList(transferQueryParam, pageNo, pageSize);

        } catch (Exception e) {
            LOGGER.error("queryTransferOrderList,查询异常,e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }

        if (queryResult != null) {
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                for (Map<String, Object> map : queryResult.getData()) {
                    //处理返回参数
                    adaptReturnResult(map);
                }
            }
            resMsg.put("dataList", queryResult.getData());
        }
        //查询汇总信息
        resMsg = this.queryTransferOrderListSum(transferQueryParam, resMsg);
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        LOGGER.info("查询转账订单列表返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    /**
     * 下载转账记录
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(TransferQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("开始下载转账记录，请求参数{}", JSON.toJSONString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant()) {
                LOGGER.info("[红版商户后台]下载转账记录列表 customerNumber=[{}]",param.getCustomerNumber());
                param.setPlatformType(PlatformTypeEnum.RED_PLATFORM_MERCHANT.name());
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            String merchantName = merchantRemoteService.getMerchantName(param.getCustomerNumber());
            param.setFromCustomerName(merchantName);
            Boolean capitalManageOpen = getCapitalManage(param.getCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("转账订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant() && StringUtils.isNotBlank(param.getSyncType())
                    && "appointSync".equals(param.getSyncType())) {

                new TransferOrderDownloadService(getCurrentUser(), param, desc.toString(), "转账订单查询-",capitalManageOpen).syncDownload(request, response);
            } else {
                new TransferOrderDownloadService(getCurrentUser(), param, desc.toString(), "转账订单查询-",capitalManageOpen).download(request, response);
            }
            LOGGER.info("下载转账记录excel已完成");
        } catch (Throwable ex) {
            LOGGER.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * 转账记录下载excel/csv,红绿一个
     * @param param
     * @param request
     * @param response
     * @return
     * @throws Exception
     */

    @RequestMapping(value = "/downloadTransferRecord")
    @ResponseBody
    public BaseRespDTO<String> downloadTransferRecord(TransferQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("开始下载转账记录，请求参数{}", JSON.toJSONString(param));
            long start = System.currentTimeMillis();
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            String merchantName = merchantRemoteService.getMerchantName(param.getCustomerNumber());
            param.setFromCustomerName(merchantName);
            Boolean capitalManageOpen = getCapitalManage(param.getCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("转账订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");

            BaseRespDTO result = new TransferOrderDownloadService(getCurrentUser(), param, desc.toString(), "转账订单查询-", capitalManageOpen).downloadReturnDTO(request, response);
            if(AccountPayException.ASYNC_DOWNLOAD_FILE.getDefineCode().equals(result.getCode())){
                result.setCode("000000");
                result.setData(AccountPayException.ASYNC_DOWNLOAD_FILE.getDefineCode());
            }
            long end = System.currentTimeMillis();
            LOGGER.info("批量下载 [转账] 记录已完成,耗时{}s", (end - start) / 1000);
            return result; // 添加返回值
        } catch (Throwable ex) {
            LOGGER.error("下载异常，ex={}", ex);
            return BaseRespDTO.fail(ex.getMessage());
        }
    }

    /**
     * 批量下载电子回单 -- 绿版使用-不能加返回值，否则弹窗jsp会失效
     * downElectronicReceipt
     * 加上返回参数是为了 红版商户后台场景，需要获取一下码 判断是异步下载
     */
    @RequestMapping(value = "/batchDownloadElectronic")
    @ResponseBody
    @Deprecated
    public void batchDownloadElectronic(TransferQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        LOGGER.info("开始批量下载 [转账] 电子回单，请求参数 param={}", JSON.toJSONString(param));
        try {
            long start = System.currentTimeMillis();
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            if(/**/StringUtils.isBlank(param.getPlatformType())){
                param.setPlatformType(PlatformTypeEnum.GREEN_PLATFORM_MERCHANT.name());
            }
            /*异步下载时的描述*/
            String description = String.format("转账电子回单[%s]-%s~%s", param.getCustomerNumber(), param.getCreateStartDate(), param.getCreateEndDate());
            BaseRespDTO result = new TransferElectronicReceiptDownloader(getCurrentUser(), param, description).download(request, response);
            long end = System.currentTimeMillis();
            LOGGER.info("批量下载 [转账] 电子回单已完成,耗时{}s", (end - start) / 1000);
//            return result;
        } catch (Throwable ex) {
            LOGGER.error("批量下载异常， param=" + JSON.toJSONString(param) + "， cased by", ex);
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
//            return BaseRespDTO.fail(ex.getMessage());
        }
    }


    /**
     * 批量下载电子回单 红绿版使用，根据返回码弹弹窗
     * downElectronicReceipt
     * 加上返回参数是为了 红版商户后台场景，需要获取一下码 判断是异步下载
     * 因为红版框架限制了code 这个字段不是 000000 会额外再弹数据,....所以再加一个其他的字段
     */
    @RequestMapping(value = "/batchDownloadElectronic-red")
    @ResponseBody
    public BaseRespDTO<String> batchDownloadElectronicRed(TransferQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        LOGGER.info("开始批量下载 [转账] 电子回单，请求参数 param={}", JSON.toJSONString(param));
        try {
            long start = System.currentTimeMillis();
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            if(/**/StringUtils.isBlank(param.getPlatformType())){
                param.setPlatformType(PlatformTypeEnum.GREEN_PLATFORM_MERCHANT.name());
            }
            /*异步下载时的描述*/
            String description = String.format("转账电子回单[%s]-%s~%s", param.getCustomerNumber(), param.getCreateStartDate(), param.getCreateEndDate());

            BaseRespDTO result = new TransferElectronicReceiptDownloader(getCurrentUser(), param, description).download(request, response);
            if(AccountPayException.ASYNC_DOWNLOAD_FILE.getDefineCode().equals(result.getCode())){
                /*因为红版后台 如果不返回000000 会有一个弹窗提示，如果想去掉，必须得这个返回*/
                result.setCode("000000");
                result.setData(AccountPayException.ASYNC_DOWNLOAD_FILE.getDefineCode());
            }
            long end = System.currentTimeMillis();
            LOGGER.info("批量下载 [转账] 电子回单已完成,耗时{}s", (end - start) / 1000);
            return result;
        } catch (Throwable ex) {
            LOGGER.error("批量下载异常， param=" + JSON.toJSONString(param) + "， cased by", ex);
//            response.setHeader("Content-type", "text/html;charset=UTF-8");
//            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
            return BaseRespDTO.fail(ex.getMessage());
        }
    }


    /**
     *  转账预计费
     * @param request
     * @param tradeAmount
     * @param productType
     * @param feeChargeSide
     * @return
     */
    @RequestMapping(value = "/preCalFee", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage preCallfee(HttpServletRequest request,
                                      @RequestParam("tradeAmount") String tradeAmount,
                                      @RequestParam(value = "productType") String productType,
                                      @RequestParam(value = "feeChargeSide") String feeChargeSide){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("转账预计费请求参数，tradeAmount={},productType={},feeChargeSide={}",tradeAmount,productType,feeChargeSide);
        String merchant = getCurrentCustomerNumber();
        CalFeeRequestDto calFeeRequestDto = buildCalFeeRequestDto(merchant, tradeAmount, productType);
        //基础产品三级
        LOGGER.info("调用账务预计费接口，入参{}", JSON.toJSONString(calFeeRequestDto));
        BigDecimal fee = new BigDecimal(0.00);
        CalFeeResponseDto calFeeResponseDto;
        try{
            calFeeResponseDto = calFeeFacade.preCalFee(calFeeRequestDto);
            LOGGER.info("调用账务预计费接口，返回参数{}", JSON.toJSONString(calFeeResponseDto));
            if("FAIL".equals(calFeeResponseDto.getStatus())){
                String errorCode = calFeeResponseDto.getErrorCode();
                if(FeeCodeEnum.CODE_300002.getCode().equals(errorCode) || FeeCodeEnum.CODE_300005.getCode().equals(errorCode)){
                    LOGGER.error("预计费接口错误，返回错误");
                    resMsg.setErrCode("9999");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("产品费率配置有误，请联系销售经理或客服");
                    return resMsg;
                }

               if(FeeCodeEnum.CODE_300007.getCode().equals(errorCode)){
                   resMsg.put("fee", fee.toString());
                   resMsg.put("deductAmount", new BigDecimal(tradeAmount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                   return resMsg;
               }

                if(FeeCodeEnum.CODE_399999.getCode().equals(errorCode)){
                    resMsg.setErrCode("9999");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("系统异常，请联系销售经理或客服处理");
                    return resMsg;
                }
            }
        }catch (Exception e) {
            LOGGER.error("调用账务预计费接口错误", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统调用异常，请稍后再试");
            return resMsg;
        }

        if(null != calFeeResponseDto.getFee()) {
            fee = calFeeResponseDto.getFee().setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        resMsg.put("fee", fee.toString());

        if (FeeTypeEnum.PAYER.name().equals(feeChargeSide)) {
            resMsg.put("fee", fee.toString());
            resMsg.put("deductAmount", new BigDecimal(tradeAmount).add(fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());

        } else if (FeeTypeEnum.PAYEE.name().equals(feeChargeSide)) {
            resMsg.put("fee", fee.toString());
            resMsg.put("deductAmount", new BigDecimal(tradeAmount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());

        } else if (FeeTypeEnum.PLATFORM.name().equals(feeChargeSide)) {
            resMsg.put("fee", BigDecimal.ZERO.toString());

        } else {
            throw new RuntimeException("不支持此交易类型");
        }

        return resMsg;
    }

    /**
     * 构建参数
     * @param merchant
     * @param tradeAmount
     * @param productType
     * @return
     */
    private CalFeeRequestDto buildCalFeeRequestDto(String merchant,String tradeAmount,String productType){
        CalFeeRequestDto calFeeRequestDto = new CalFeeRequestDto();
        //易宝流水号
        calFeeRequestDto.setYeepayTrxFlowNo(CommonUtils.getUUID());
        //业务方
        calFeeRequestDto.setBizCode(Costants.ACCOUNT_BIZ_CODE);
        //商户订单请求号
        calFeeRequestDto.setMerchantOrderNo(CommonUtils.getUUID());
        //商户编号
        calFeeRequestDto.setMerchantNo(merchant);
        //手续费承担商编
        calFeeRequestDto.setFeeMerchantNo(merchant);
        //算费金额
        calFeeRequestDto.setAmount(new BigDecimal(tradeAmount));
        //交易时间
        calFeeRequestDto.setTrxTime(new Date());
        //营销产品码
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        calFeeRequestDto.setMarketingProduct(businessCheckRemoteService.queryMarketProduct(merchant));
        //基础产品二级
        calFeeRequestDto.setBasicsProductFirst(Costants.TRANSFER_BASICSPRODUCTFIRST);
        if(TransferTypeEnum.MERCHANT.name().equals(productType)){
            calFeeRequestDto.setBasicsProductSecond(TransferSecondProductCodeEnum.B2B.name());
        }
        //基础产品三级
        calFeeRequestDto.setBasicsProductThird("-");
        return calFeeRequestDto;
    }

    /**
     * 构建转账参数
     * @param currentCustomerNumber
     * @param amount
     * @param usage
     * @return
     */
    private BgTransferReqDTO buildMgTransferReqDTO(String currentCustomerNumber,String amount,String usage){
        BgTransferReqDTO dto = new BgTransferReqDTO();
        dto.setClientIp(NetUtils.getIpAddress());
        dto.setReqSource(ReqSourceEnum.MP);
        dto.setInitiateMerchantNo(currentCustomerNumber);
        dto.setFromMerchantNo(currentCustomerNumber);
        dto.setParentMerchantNo(currentCustomerNumber);
        dto.setOrderAmount(amount);
        dto.setUsage(usage);
        dto.setFromAccountType(AccountTypeEnum.FUND_ACCOUNT);
        dto.setOperator(getCurrentUser().getLoginName());
        Long requestNo = SnowflakeIdFactory.generateId();
        dto.setRequestNo("ZZ" + requestNo);
        String merchantName = merchantRemoteService.getMerchantName(currentCustomerNumber);
        dto.setFromMerchantName(merchantName);
        dto.setSalesProductCode(querySaleProductCode(currentCustomerNumber));
        dto.setTransferType(com.yeepay.g3.facade.unionaccount.transfer.enumtype.TransferTypeEnum.B2B);
        return dto;
    }

    private ResponseMessage isDisplayProductAndUndertaker(String currentCustomerNumber,ResponseMessage resMsg){
        List<String> productList = new ArrayList<String>();
        List<String> feeTypeList = new ArrayList<String>();
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();

        String saleProductCode = businessCheckRemoteService.queryMarketProduct(currentCustomerNumber);
        try {
            MerchantProductQueryRespDTO merchantProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(currentCustomerNumber, ProductTypeEnum.ACCOUNT.toString(), saleProductCode, Costants.TRANSFER_BASICSPRODUCTFIRST, Costants.TRANSFER_BASICSPRODUCTSECOND);
            LOGGER.info("查询产品开通，merchantProductQueryRespDTO={}", JSON.toJSONString(merchantProductQueryRespDTO));
            if ("0000".equals(merchantProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductQueryRespDTO.getBaseProductList())) {
                for (BaseProductDTO baseProductDTO : merchantProductQueryRespDTO.getBaseProductList()) {
                    if (!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode())) {
                        String secondBaseProductCode = baseProductDTO.getSecondBaseProductCode();
                        if (TransferSecondProductCodeEnum.B2B.name().equals(secondBaseProductCode)) {
                            productList.add(TransferTypeEnum.MERCHANT.name());
                            Map map = baseProductDTO.getProductAttributeMap();
                            String feeCustomerNo = map.get("FEE_MERCHANT_NO").toString();
                            if (currentCustomerNumber.equals(feeCustomerNo)) {
                                feeTypeList.add(FeeTypeEnum.PAYER.name());
                                feeTypeList.add(FeeTypeEnum.PAYEE.name());
                            } else {
                                feeTypeList.add(FeeTypeEnum.PLATFORM.name());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("查询产品开通发生异常，异常信息为: ", e);
        }
        resMsg.put("feeUndertakerList",feeTypeList);
        resMsg.put("productList",productList);
        return resMsg;
    }


    /**
     * 查询余额
     */
    private BigDecimal queryBalance(String accountType){
        //查询资金账户余额
        BigDecimal balance = new BigDecimal(BigInteger.ZERO);
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(getCurrentCustomerNumber(), AccountTypeEnum.valueOf(accountType));
        BigDecimal queryBalance = queryAccountResponseDto.getBalance();
        if (queryBalance != null) {
            balance = queryBalance;
        }
        return balance;
    }

    /**
     * 查询转账汇总
     *
     * @param param
     * @param resMsg
     */
    private ResponseMessage queryTransferOrderListSum(TransferQueryParam param, ResponseMessage resMsg) {
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getCustomerNumber());
        List<Map<String, Object>> withOrderListSum = null;
        try {
            withOrderListSum = QueryServiceUtil.query("accountTradeService", "queryTransferOrderListSum", queryMap);
        } catch (Exception e) {
            LOGGER.error("queryTransferOrderListSum-参数异常", e);
            // 直接把异常信息返回
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }
        // 如果查询结果不为空的话
        if (withOrderListSum != null && !withOrderListSum.isEmpty()) {
            Map<String, Object> sumResult = withOrderListSum.get(0);

            String sum_amount = sumResult.get("sum_amount").toString();
            String sum_fee = sumResult.get("sum_fee").toString();
            if(StringUtils.isEmpty(sum_amount)){
                sum_amount ="0";
            }
            if(StringUtils.isEmpty(sum_fee)){
                sum_fee ="0";
            }
            resMsg.getData().put("sum_count", sumResult.get("sum_count").toString());// 总笔数
            resMsg.getData().put("sum_amount", new BigDecimal(sum_amount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总金额
            resMsg.getData().put("sum_fee", new BigDecimal(sum_fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总手续费
            resMsg.getData().put("totalCount", sumResult.get("sum_count").toString());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", "0.00");// 总金额
            resMsg.getData().put("sum_fee", "0.00");// 总手续费
        }
        return resMsg;
    }

    /**
     * @param param:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     * @Description: 查询转账列表
     */
    private QueryResult queryTransferOrderList(TransferQueryParam param, int pageNo, int pageSize) {
        //构造查询参数
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getCustomerNumber());
        Integer startIndex = (pageNo - 1) * pageSize + 1;

        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        QueryResult queryTransferOrderList = queryService.query("queryTransferOrderList", queryParam);
        return queryTransferOrderList;

    }


    /**
     * 适配返回结果
     *
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }

        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (null != detail.get("transfer_amount")) {
                detail.put("transfer_amount", new BigDecimal(detail.get("transfer_amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }
            if (null != detail.get("fee")) {

                if (null != detail.get("fee_customer_no") && !detail.get("to_customer_no").toString().equals(detail.get("fee_customer_no").toString()) && !detail.get("from_customer_no").toString().equals(detail.get("fee_customer_no").toString())) {
                    detail.put("fee","-");
                }else{
                    detail.put("fee", new BigDecimal(detail.get("fee").toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                }

            }else{
                detail.put("fee","-");
            }

            Object transfer_account_type = detail.get("transfer_account_type");

            Object to_account_type = detail.get("to_account_type");

            if(null != transfer_account_type){
                transfer_account_type = transfer_account_type.toString();
                if(TransferTypeEnum.MERCHANT.name().equals(transfer_account_type)){
                    detail.put("transfer_account_type",TransferTypeEnum.MERCHANT.getDesc());
                    Object toCustomerName = detail.get("to_customer_name");
                    Object toCustomerNo = detail.get("to_customer_no");
                    if(null != toCustomerNo){
                        if(null !=toCustomerName){//+"("+toCustomerNo.toString()+")"    +"("+toCustomerNo.toString()+")"
                            detail.put("to_customer_name",toCustomerName.toString());
                        }else{
                            String merchantName = merchantRemoteService.getMerchantName(toCustomerNo.toString());
                            detail.put("to_customer_name",merchantName);
                        }

                    }else{
                        detail.put("to_customer_name","-");
                    }
                }else {
                    detail.put("transfer_account_type",TransferTypeEnum.ACCOUNT.getDesc());
                    if(null != to_account_type){
                        detail.put("to_account_type", AccountTypeNewEnum.valueOf(to_account_type.toString()).getDesc());
                        detail.put("to_customer_name",AccountTypeNewEnum.valueOf(to_account_type.toString()).getDesc());
                    }else {
                        detail.put("to_account_type","-");
                    }
                    detail.put("to_customer_no","");

                }
            }

            //下单时间
            Object obj = detail.get("create_time");
            if (null != obj) {
                if (obj instanceof String) {
                    String str = String.valueOf(obj);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("create_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("create_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (obj instanceof Timestamp) {
                    detail.put("createTime", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

        } catch (Exception e) {
            LOGGER.error("这都能错..擦....", e);
        }
        return detail;
    }


    /**
     * 查询账户列表
     * @param currentCustomerNumber
     * @return
     */
    private List<AccountInfoParam> getAccountList(String currentCustomerNumber){
        //判断是否开通账户转账
        AccountInfoQueryRequestDTO queryRequestDto = new AccountInfoQueryRequestDTO();
        queryRequestDto.setMerchantNo(currentCustomerNumber);
        List<AccountInfoParam> accountInfoParamList = new ArrayList<>();
        try{
            AccountInfoQueryRespDTO accountInfoQueryRespDTO = accountManageFacade.queryAccountInfo(queryRequestDto);
            LOGGER.info("返回账户列表，accountInfoQueryRespDTO={}",JSON.toJSONString(accountInfoQueryRespDTO));
            if(accountInfoQueryRespDTO != null){
                List<AccountInfo> accountInfoList = accountInfoQueryRespDTO.getAccountInfoList();
                if (CollectionUtils.isNotEmpty(accountInfoList)) {
                    for (AccountInfo accountInfo : accountInfoList) {
                        String accountType = accountInfo.getAccountType();
                        //获取配置中的账户类型
                        String value = ConfigUtils.getAccountType(accountType);
                        if (StringUtils.isNotBlank(value)) {
                            AccountInfoParam param = new AccountInfoParam();
                            param.setAccountNo(accountInfo.getAccountNo());
                            param.setAccountType(accountType);
                            param.setAccountBalance(accountInfo.getBalance());
                            param.setAccountTypeDesc(value);
                            accountInfoParamList.add(param);
                        }
                    }
                }
            }else{
                LOGGER.info("查询商户账户列表异常");
            }
        }catch (Exception e){
            LOGGER.error("查询账户列表异常，异常信息为: ", e);
        }
        return accountInfoParamList;
    }

    /**
     * 查询营销产品码
     */
    private String querySaleProductCode(String currentCustomerNumber){
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        String saleProductCode = businessCheckRemoteService.queryMarketProduct(currentCustomerNumber);
        return saleProductCode;

    }

    /**
     * 跳转到查询历史订单
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/queryHistoryOrder")
    public ModelAndView queryHistoryOrder(HttpServletRequest request,HttpServletResponse response) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("mgTransfer/transferQueryHistory");
        mav.addObject("UIWebRootUrl",WebPropertiesHolder.getUIWebRootUrl());
        cookieService.addCookie(request,response,"/bac-app");
        return mav;
    }

    /**
     * 查询转账历史订单（迁移红版后台）
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/queryTransferHistoryOrder")
    public BaseRespDTO queryTransferHistoryOrder(HttpServletRequest request, HttpServletResponse response) {
        cookieService.addCookie(request, response, "/bac-app");
        return BaseRespDTO.success();
    }



    /**
     * 是否有授信放款业务
     *
     * @param merchantNo
     * @return
     */
    private Boolean getCapitalManage(String merchantNo) {
        QueryMatchingLoanReqDTO reqDTO = new QueryMatchingLoanReqDTO();
        reqDTO.setFromMerchantNo(merchantNo);
        reqDTO.setRequestBiz("TRANSFER");
        QueryMatchingLoanRespDTO respDTO = capitalManageService.queryLoanConfig(reqDTO);
        if (AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(respDTO.getReturnCode())) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 跳转转账详情页（红绿用一个）
     *
     * @param orderNo 订单编号
     * @return
     */
    @RequestMapping(value = "/queryTransferDetail", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryTransferDetail(@RequestParam("orderNo") String orderNo,@RequestParam("customerNumber") String customerNumber) {
        Map<String,Object> map=new HashMap<>();
        LOGGER.info("[商户后台]查询转账订单明细，the orderNo={},the currentCustomerNumber={}", orderNo,customerNumber);
        PreCheck.checkArgument(StringUtils.isNotBlank(orderNo));
        PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber));

        TransferOrder dto = new TransferOrder();
        dto.setFromCustomerNo(customerNumber);
        dto.setOrderNo(orderNo);
        TransferOrder transferOrder;
        try {
            transferOrder = transferOrderService.queryTransferOrder(dto);
        } catch (Exception e) {
            LOGGER.error("[商户后台]查询转账订单异常", e);
            return BaseRespDTO.fail("查询转账订单异常");
        }
        TransferResponseParam transferResponseParam = new TransferResponseParam();
        if (transferOrder != null) {
            MgTransferAccountTypeEnum transferAccountType = transferOrder.getTransferAccountType();
            TransferStatusTypeEnum transferStatus = transferOrder.getStatus();
            transferResponseParam.setTransferStatus(transferStatus.getDesc());
            if (null != transferOrder.getCreateTime()) {
                transferResponseParam.setCreateTime(DateUtil.formatDate(transferOrder.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            }

            if (null != transferOrder.getCompletedTime()) {
                transferResponseParam.setFinishTime(DateUtil.formatDate(transferOrder.getCompletedTime(), "yyyy-MM-dd HH:mm:ss"));
            }

            if (null != transferOrder.getFee()) {
                BigDecimal fee = transferOrder.getFee().setScale(2, BigDecimal.ROUND_HALF_UP);
                transferResponseParam.setFee(fee + "元");
            } else {
                transferResponseParam.setFee("-");
            }

            if (null != transferOrder.getDeductionAmount()) {
                transferResponseParam.setDebitAmount(transferOrder.getDeductionAmount().setScale(2).toPlainString());
            }
            if (null != transferOrder.getEnteredAmount()) {
                transferResponseParam.setReceiveAmount(transferOrder.getEnteredAmount().setScale(2).toPlainString());
            }

            if (null != transferOrder.getTransferAmount()) {
                transferResponseParam.setOrderAmount(transferOrder.getTransferAmount().setScale(2).toPlainString());
            }

            transferResponseParam.setFromMerchantNo(transferOrder.getFromCustomerNo());
            String fromMerchantName = transferOrder.getFromCustomerName();
            if (StringUtils.isNotBlank(fromMerchantName)) {
                transferResponseParam.setFromMerchantName(transferOrder.getFromCustomerName());
            } else {
                String merchantName = merchantRemoteService.getMerchantName(customerNumber);
                if (StringUtils.isBlank(merchantName)) {
                    merchantName = "-";
                }
                transferResponseParam.setFromMerchantName(merchantName);
            }
            if (transferAccountType.name().equals(MgTransferAccountTypeEnum.MERCHANT.name())) {
                String toMerchantNo = transferOrder.getToCustomerNo();
                String toMerchantName = transferOrder.getToCustomerName();
                if (StringUtils.isNotBlank(toMerchantName)) {
                    transferResponseParam.setToMerchantName(transferOrder.getToCustomerName() + "(" + toMerchantNo + ")");
                } else {
                    String merchantName = merchantRemoteService.getMerchantName(toMerchantNo);
                    transferResponseParam.setToMerchantName(merchantName + "(" + toMerchantNo + ")");
                }
                transferResponseParam.setTransferAccountType(TransferTypeEnum.MERCHANT.getDesc());
            } else if (transferAccountType.name().equals(MgTransferAccountTypeEnum.ACCOUNT.name())) {
                String toAccountType = transferOrder.getToAccountType();
                transferResponseParam.setToMerchantName(AccountTypeNameUtil.getAccountTypeByName(toAccountType));
                transferResponseParam.setTransferAccountType(TransferTypeEnum.ACCOUNT.getDesc());
            } else {
                LOGGER.info("[商户后台]转账方式不存在");
            }
            String operator = transferOrder.getOperator();
            if (StringUtils.isNotBlank(operator)) {
                transferResponseParam.setOperator(transferOrder.getOperator());
            } else {
                transferResponseParam.setOperator("-");
            }

            transferResponseParam.setOrderNo(transferOrder.getOrderNo());
            transferResponseParam.setTransferStatus(transferOrder.getStatus().getDesc());
            transferResponseParam.setFromAccountType(AccountTypeEnum.valueOf(transferOrder.getFromAccountType()).getDesc());
            transferResponseParam.setToAccountType(AccountTypeEnum.valueOf(transferOrder.getToAccountType()).getDesc());
            transferResponseParam.setUsage(transferOrder.getUsage());

            if (transferStatus == TransferStatusTypeEnum.FAIL) {
                transferResponseParam.setReturnMsg(transferOrder.getReturnMessage());
            } else {
                transferResponseParam.setReturnMsg("-");
            }
            transferResponseParam.setBatchNo(transferOrder.getBatchNo());
        } else {
            LOGGER.info("[商户后台]转账订单信息不存在");
        }
        LOGGER.info("[商户后台]查询订单明细返回，transferResponseParam={}", JSON.toJSONString(transferResponseParam));
        map.put("orderDetail", transferResponseParam);
        return BaseRespDTO.success(map);
    }


    /**
     * 处理数据
     * @param transferOrder
     * @return
     */
    private OrderParam buildParam(TransferOrder transferOrder) {
        OrderParam orderParam = new OrderParam();
        Pair<String, String> merchantNo = remoteService.queryFirstAndSecondMerchant(transferOrder.getFromCustomerNo());
        orderParam.setParentMerchantNo(merchantNo.getRight());
        orderParam.setOrderNo(transferOrder.getOrderNo());
        orderParam.setInitiateMerchantNo(merchantNo.getLeft());
        orderParam.setTradeType(TradeTypeEnum.TRANSFER);
        return orderParam;
    }
    
}
