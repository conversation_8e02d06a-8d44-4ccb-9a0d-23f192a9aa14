package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Mr.yin
 * @date: 2024/7/29  20:27
 */
@ApiModel(description = "提现确认返回参数")

public class WithdrawConfirmResp implements Serializable {
    private static final long serialVersionUID = -1L;
    @ApiModelProperty(value = "易宝订单号")
    private String orderNo;

    @ApiModelProperty(value = "请求号")
    private String requestNo;

    @ApiModelProperty(value = "请求时间")
    private String createTime;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
