package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @author: wen
 * @date: 2024/7/28  17:27
 */
@ApiModel(description = "账户总余额")
public class AccountTotalBalanceModel implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "账户总余额")
    private String tips;

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }
}
