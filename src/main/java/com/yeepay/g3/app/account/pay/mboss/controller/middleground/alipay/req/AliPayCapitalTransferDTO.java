package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 支付宝记账本拨入
 */
@ApiModel(description = "支付宝记账本拨入")
public class AliPayCapitalTransferDTO implements Serializable {
    /**
     * 记账本id
     */
    private String channelBookId;


    /**
     * 划拨金额
     */
    private String transferAmount;

    /**
     * 备注
     * @return
     */
    private  String remark;

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getTransferAmount() {
        return transferAmount;
    }

    public void setTransferAmount(String transferAmount) {
        this.transferAmount = transferAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
