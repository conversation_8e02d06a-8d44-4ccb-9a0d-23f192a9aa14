package com.yeepay.g3.app.account.pay.mboss.controller;

import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.AccountBalanceAlarmRuleModifyReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.AccountBalanceAlarmRuleQueryReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountBalanceAlarmQueryRespDTO;

import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountBalanceAlarmRuleDetailRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountBalanceAlarmRuleModifyRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.facade.AccountBalanceAlarmRuleFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


@Controller
@RequestMapping("/account/alarm/MG")
public class MGAccountBalanceAlarmController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MGAccountBalanceAlarmController.class);

    private static final String QUERY_PERMISSION_CONSTANT = "21402";
    private static final AccountBalanceAlarmRuleFacade accountBalanceAlarmRuleFacade = RemoteServiceFactory.getService(AccountBalanceAlarmRuleFacade.class);


    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/query-account-list")
    @ResponseBody
    public AccountBalanceAlarmQueryRespDTO queryAccountList() {

        String customerNo = getCurrentCustomerNumber();
        LOGGER.info("MG queryAccountList customerNo={}", customerNo);
        AccountBalanceAlarmRuleQueryReqDTO reqDTO = new AccountBalanceAlarmRuleQueryReqDTO();
        reqDTO.setMerchantNo(customerNo);
        AccountBalanceAlarmQueryRespDTO resp = accountBalanceAlarmRuleFacade.queryRuleList(reqDTO);
        LOGGER.info("MG queryAccountList resp = {}", resp);
        return resp;
    }

    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/setting-rule", method = RequestMethod.POST)
    @ResponseBody
    public AccountBalanceAlarmRuleModifyRespDTO settingRule(@RequestBody AccountBalanceAlarmRuleModifyReqDTO req) {
        LOGGER.info("设置余额预警规则:req={},操作人={}",req,getCurrentUser().getLoginName());
        return accountBalanceAlarmRuleFacade.modifyRule(req);
    }

    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/query-rule")
    @ResponseBody
    public AccountBalanceAlarmRuleDetailRespDTO queryRule(@RequestParam(value = "id", required = true) Long id) {
        String customerNo = getCurrentCustomerNumber();
        LOGGER.info("queryRule customerNo={},id={}", customerNo, id);
        return accountBalanceAlarmRuleFacade.getRule(id);
    }


    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/close-rule", method = RequestMethod.POST)
    @ResponseBody
    public AccountBalanceAlarmRuleModifyRespDTO closeRule(@RequestBody AccountBalanceAlarmRuleModifyReqDTO reqDTO) {
        LOGGER.info("设置余额预警规则:closeRule 操作人={},req={}",  getCurrentUser().getLoginName(), reqDTO);
        AccountBalanceAlarmRuleDetailRespDTO detail = accountBalanceAlarmRuleFacade.getRule(reqDTO.getRuleId());
        if(!"UA00000".equals(detail.getReturnCode())){
            throw new RuntimeException("未查询余额预警规则异常！returnMsg="+detail.getReturnMsg());
        }
        return  accountBalanceAlarmRuleFacade.modifyRule(reqDTO);
    }

}
