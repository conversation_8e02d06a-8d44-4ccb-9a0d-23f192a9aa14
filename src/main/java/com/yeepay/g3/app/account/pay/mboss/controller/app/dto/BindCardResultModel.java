package com.yeepay.g3.app.account.pay.mboss.controller.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @author: wen
 * @date: 2024/7/24  17:27
 */
@ApiModel(description = "银行绑卡结果")
public class BindCardResultModel implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "绑卡结果")
    private String status;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
