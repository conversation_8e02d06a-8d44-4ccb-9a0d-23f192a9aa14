package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base.BasePageRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.AccountBookTradeInfoModel;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.AccountBookRechargeRefundReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.AccountBookTradeQueryListReqDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.AccountBookRechargeStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.AccountBookRefundStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountBookTradeDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.unionaccount.recharge.dto.request.MpAccountBookRefundReqDTO;
import com.yeepay.g3.facade.unionaccount.recharge.dto.response.MpAccountBookRefundRespDTO;
import com.yeepay.g3.facade.unionaccount.recharge.exception.UnionAccountRechargeException;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/account-book/trade")
public class AccountBookTradeController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountBookTradeController.class);

    @Autowired
    private RemoteService remoteService;

    public static final String PERMISSION = "**************";


    /**
     * 跳转记账簿交易查询页面
     *
     * @param request
     * @return
     */
    @RequiresPermissions(PERMISSION)
    @RequestMapping("/page")
    @ApiOperation(value = "记账簿交易查询页面")
    public ModelAndView page(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("accountBook/trade");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl_ACCOUNTBOOK());
        LOGGER.info("【记账簿流水列表查询】查询页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "记账簿流水列表查询")
    @ResponseBody
    public BasePageRespDTO<AccountBookTradeInfoModel> list(@RequestBody AccountBookTradeQueryListReqDTO reqDTO) {
        String customerNumber = getCurrentCustomerNumber();
        LOGGER.info("[记账簿流水列表] [查询] 商户={} reqDTO={}", customerNumber, JSON.toJSONString(reqDTO));
        try {
            reqDTO.validateParam();
            reqDTO.setCustomerNumber(customerNumber);
            Date startTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getCreateStartTime(), "yyyy-MM-dd HH:mm:ss");
            Date endTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getCreateEndTime(), "yyyy-MM-dd HH:mm:ss");
            Assert.isTrue(LocalDateTimeUtils.getDayDiff(startTime, endTime) <= 92, "查询时间间隔不能超过3个月");
            Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(reqDTO);
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            queryParam.setStartIndex((reqDTO.getPageNo() - 1) * reqDTO.getPageSize() + 1);
            queryParam.setMaxSize(reqDTO.getPageSize());
            queryParam.setDoSum(true);
            if ("RECHARGE".equals(reqDTO.getTradeType())) {
                return pageQueryAccountBookRechargeOrder(queryParam);
            } else {
                return pageQueryAccountBookRefundOrder(queryParam);
            }
        } catch (IllegalArgumentException e) {
            LOGGER.warn("[记账簿流水列表] [查询] 参数异常，商编=" + customerNumber + ",异常为={}", e.getMessage());
            throw UnionAccountRechargeException.PARAM_REQUIRED_ERROR.newInstance(e.getMessage());
        } catch (YeepayBizException e) {
            LOGGER.warn("[记账簿流水列表] [查询] 业务异常，商编=" + customerNumber + ",异常为={}", e);
            return BasePageRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[记账簿流水列表] [查询] 异常，商编=" + customerNumber + ",异常为={}", e);
            return BasePageRespDTO.systemError("系统异常");
        }
    }

    @RequestMapping(value = "/download")
    @ResponseBody
    public BaseRespDTO<String> downloadRecord(@RequestBody AccountBookTradeQueryListReqDTO reqDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String customerNumber = getCurrentCustomerNumber();
        LOGGER.info("[记账簿流水列表] [下载] 商户={} reqDTO={}", customerNumber, JSON.toJSONString(reqDTO));
        try {
            reqDTO.validateParam();
            reqDTO.setCustomerNumber(customerNumber);
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            Date startTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getCreateStartTime(), "yyyy-MM-dd HH:mm:ss");
            Date endTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getCreateEndTime(), "yyyy-MM-dd HH:mm:ss");
            Assert.isTrue(LocalDateTimeUtils.getDayDiff(startTime, endTime) <= 92, "查询时间间隔不能超过3个月");
            CheckUtils.notEmpty(reqDTO.getFileType(), "fileType");
            Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(reqDTO);
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            StringBuilder desc = new StringBuilder();
            desc.append("记账簿流水列表,").append(reqDTO.getCreateEndTime()).append("至").append(reqDTO.getCreateEndTime()).append("数据");
            BaseRespDTO result = new AccountBookTradeDownloadService(getCurrentUser(), reqDTO, desc.toString(), "记账簿流水列表-").downloadReturnDTO(request, response);
            if (AccountPayException.ASYNC_DOWNLOAD_FILE.getDefineCode().equals(result.getCode())) {
                LOGGER.info("[记账簿流水列表] [下载] 异步任务发起");
                return BaseRespDTO.success(AccountPayException.ASYNC_DOWNLOAD_FILE.getDefineCode());
            }
            LOGGER.info("[记账簿流水列表] [下载] excel已完成");
            return result; // 添加返回值
        } catch (Throwable ex) {
            LOGGER.error("下载异常，ex={}", ex);
            return BaseRespDTO.fail(ex.getMessage());
        }
    }

    @RequestMapping(value = "/recharge-refund", method = RequestMethod.POST)
    @ApiOperation(value = "记账簿入金退回")
    @ResponseBody
    public BaseRespDTO rechargeRefund(@RequestBody AccountBookRechargeRefundReqDTO reqDTO) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[记账簿入金退回] 请求参数 = {}", JSON.toJSONString(reqDTO));
        try {
            reqDTO.validateParam();
            //调用union-account-recharge 商户后台入金退回
            MpAccountBookRefundReqDTO requestDto = new MpAccountBookRefundReqDTO();
            requestDto.setMerchantNo(currentCustomerNumber);
            Long requestNo = SnowflakeIdFactory.generateId();
            requestDto.setMerchantRefundRequestNo("JZPTK" + requestNo);
            requestDto.setOriginalOrderNo(reqDTO.getOriginalOrderNo());
            requestDto.setBankId(reqDTO.getReceiverBankCode());
            requestDto.setReceiverAccountNo(reqDTO.getReceiverAccountNo());
            requestDto.setRefundAmount(new BigDecimal(reqDTO.getRefundAmount()));
            MpAccountBookRefundRespDTO respDTO = remoteService.mpAccountBookRefund(requestDto);
            if ("UA00000".equals(respDTO.getReturnCode())) {
                return BaseRespDTO.success(respDTO.getRefundStatus());
            } else {
                return BaseRespDTO.fail(respDTO.getReturnMsg());
            }
        } catch (Throwable e) {
            LOGGER.error("[记账簿入金退回] 失败", e);
            return BaseRespDTO.fail("记账簿入金退回失败");
        }
    }

    private BasePageRespDTO<AccountBookTradeInfoModel> pageQueryAccountBookRechargeOrder(QueryParam queryParam) {
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("bacRechargeService", QueryService.class);
        QueryResult queryResult = queryService.query("queryAccountBookRechargeOrderList", queryParam);
        if (queryResult != null) {
            if (queryResult.getTotalCount() == 0L) {
                return BasePageRespDTO.successPage(Lists.newArrayList(), 0L);
            }
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                List<AccountBookTradeInfoModel> list = new ArrayList<>();
                for (Map<String, Object> map : queryResult.getData()) {
                    list.add(convertRechargeOrderModel(map));
                }
                return BasePageRespDTO.successPage(list, queryResult.getTotalCount());
            }
        }
        return BasePageRespDTO.successPage(Lists.newArrayList(), 0L);
    }

    private BasePageRespDTO<AccountBookTradeInfoModel> pageQueryAccountBookRefundOrder(QueryParam queryParam) {
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("bacRechargeService", QueryService.class);
        QueryResult queryResult = queryService.query("queryAccountBookRefundOrderList", queryParam);
        if (queryResult != null) {
            if (queryResult.getTotalCount() == 0L) {
                return BasePageRespDTO.successPage(Lists.newArrayList(), 0L);
            }
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                List<AccountBookTradeInfoModel> list = new ArrayList<>();
                for (Map<String, Object> map : queryResult.getData()) {
                    list.add(convertRefundOrderModel(map));
                }
                return BasePageRespDTO.successPage(list, queryResult.getTotalCount());
            }
        }
        return BasePageRespDTO.successPage(Lists.newArrayList(), 0L);
    }


    private AccountBookTradeInfoModel convertRechargeOrderModel(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return null;
        }
        AccountBookTradeInfoModel model = new AccountBookTradeInfoModel();
        model.setOrderNo(QueryMapperUtil.convertObjectToString(detail.get("order_no")));
        model.setCreateTime(QueryMapperUtil.convertTimeToString(detail.get("create_time")));
        model.setTradeType("RECHARGE");
        model.setAccountBookNo(QueryMapperUtil.convertObjectToString(detail.get("sub_account_no")));
        model.setPayerAccountName(QueryMapperUtil.convertBankAccountName(detail.get("payer_account_name")));
        model.setPayerAccountNo(QueryMapperUtil.convertBankAccountNo(detail.get("payer_account_no")));
        model.setPayerBankName(QueryMapperUtil.convertObjectToString(detail.get("bank_name")));
        model.setPayerBankCode(QueryMapperUtil.convertObjectToString(detail.get("bank_id")));
        model.setPostScrip(QueryMapperUtil.convertObjectToString(detail.get("remit_comment")));
        model.setOrderAmount(NumberUtils.formateNum(null, new BigDecimal(detail.get("order_amount").toString())));
        model.setReceiverAccountNo(QueryMapperUtil.convertBankAccountNo(detail.get("receiver_account_no")));
        model.setReceiverAccountName(QueryMapperUtil.convertBankAccountName(detail.get("receiver_account_name")));
        if (AccountBookRechargeStatusEnum.valueOf(detail.get("status").toString()) == AccountBookRechargeStatusEnum.FAIL) {
            model.setFailReason(QueryMapperUtil.convertObjectToString(detail.get("return_msg")));
        }
        model.setStatus(AccountBookRechargeStatusEnum.valueOf(detail.get("status").toString()).name());
        model.setStatusName(AccountBookRechargeStatusEnum.valueOf(detail.get("status").toString()).getDesc());
        return model;
    }

    private AccountBookTradeInfoModel convertRefundOrderModel(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return null;
        }
        AccountBookTradeInfoModel model = new AccountBookTradeInfoModel();
        model.setOrderNo(QueryMapperUtil.convertObjectToString(detail.get("refund_order_no")));
        model.setCreateTime(QueryMapperUtil.convertTimeToString(detail.get("create_time")));
        model.setTradeType("REFUND");
        model.setAccountBookNo(QueryMapperUtil.convertObjectToString(detail.get("original_sub_account_no")));
        model.setPostScrip(QueryMapperUtil.convertObjectToString(detail.get("bank_postscrip")));
        model.setOrderAmount(NumberUtils.formateNum(null, new BigDecimal(detail.get("refund_amount").toString())));
        model.setReceiverAccountNo(QueryMapperUtil.convertBankAccountNo(detail.get("receiver_account_no")));
        model.setReceiverAccountName(QueryMapperUtil.convertBankAccountName(detail.get("receiver_account_name")));
        if (AccountBookRefundStatusEnum.valueOf(detail.get("refund_status").toString()) == AccountBookRefundStatusEnum.FAIL) {
            model.setFailReason(QueryMapperUtil.convertObjectToString(detail.get("return_msg")));
        }
        model.setStatus(AccountBookRefundStatusEnum.valueOf(detail.get("refund_status").toString()).name());
        model.setStatusName(AccountBookRefundStatusEnum.valueOf(detail.get("refund_status").toString()).getDesc());
        return model;
    }


}
