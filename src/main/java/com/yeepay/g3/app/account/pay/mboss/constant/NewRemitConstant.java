package com.yeepay.g3.app.account.pay.mboss.constant;

import com.google.gson.Gson;

import java.util.Calendar;

/**
 * Created by yp-tc-m-2797 on 18/1/26.
 */
public class NewRemitConstant {

    public static final String BAC_STORAGE_ENDPOINT = "bac.storage_endpoint"; //分布式存储地址

    public static final String BAC_STORAGE_SECRET = "bac.storage_secret";//分布式存储secret

    public static final String BAC_STORAGE_BUCKET = "bac.storage_bucket"; //分布式存储桶

    public static final String PRE_SEND_REMIT_KEY = "preSendRemit_";

    public static final String MAP_KEY_BATCH_REMIT_ERROR = "BATCH_REMIT_ERROR_LIST";

    public static final String MAP_KEY_BATCH_REMIT_RESULT = "BATCH_REMIT_RESULT_LIST";

    //产品码
    public static final String WTJS_PRODUCT = "WTJS";
    public static final String RJT_PRODUCT = "RJT";
    public static final String CASH_PRODUCT = "CASH";

    public static final String CASH_SECOND_PRODUCT_T1 = "T1";
    public static final String CASH_SECOND_PRODUCT_D0 = "D0";
    public static final String CASH_SECOND_PRODUCT_D1 = "D1";



    //代付  出款方式
    public static final String URGENCY_TYPE_URGENCY = "URGENCY";
    public static final String URGENCY_TYPE_COMMON = "COMMON";
    public static final String URGENCY_TYPE_NEXT_DAY = "NEXT_DAY";

    public static final String BAC_SECONDPRODUCT = "BAC_REMIT";

    public static final String AUTH_CODE_SMS = "SMS";

    public static final String AUTH_CODE_TOTP = "TOTP";

    private static final String REDIS_ANYC_BATCH_TOTALCOUNT="bac-app.redis_anyc_batch_totalcount";

    private static final String REDIS_ANYC_BATCH_PROCESSCOUNT="bac-app.redis_anyc_batch_processcount";

    private static final String REDIS_ANYC_BATCH_PROCESSEDEND="bac-app.redis_anyc_batch_processedend";

    public static final String YJ_AUDIT_PROCESS_NUM="balance.yj_audit_process_num";

    // T1提现短信验证码校验key
    public static final String CONFIRM_SEND_T1WITHDRAW = "CONFIRM_SEND_T1WITHDRAW";

    public static String getRedisAnycBatchProcesscount(String customerNumber, String fileName){
        return REDIS_ANYC_BATCH_PROCESSCOUNT + "_"+customerNumber+"_"+fileName;
    }

    public static String getRedisAnycTotalCountKey(String customerNumber, String fileName){
        return REDIS_ANYC_BATCH_TOTALCOUNT+"_"+customerNumber+"_"+fileName;
    }

    public static String getReditAnycProcessedKey(String customerNumber, String fileName){
        return REDIS_ANYC_BATCH_PROCESSEDEND+"_"+customerNumber+"_"+fileName;
    }

    public static String getRemitRequestBatchDtoKey(Long userId, String bacBatchNo){
        return PRE_SEND_REMIT_KEY + userId + "_" + bacBatchNo + "_remitRequestBatchDto";
    }

    public static String getRemitRequestTimestamp(Long userId) {
        return PRE_SEND_REMIT_KEY + userId + "_" + Calendar.getInstance().getTimeInMillis();

    }

    // 默认pageSize
    public static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认当前
    public static final String PAGE_NO_DEFAULT_VAL = "1";



    public static final String UNION_CARD_PREFIX = "62";

    public static final Gson GSON = new Gson();

    public static final String MP_SUB_MERCHANT_CACHE = "mp_all_sub_merchant_cache";

    public static final String MP_MERCHANT_CACHE = "mp_merchant_cache";
    public static final String MP_MERCHANT_CACHE_V2 = "accountPayMBoss:SUB_MER:%s";

    public static final String MP_SUB_AGENT_MERCHANT_CACHE = "mp_all_sub_agent_merchant_cache";

}
