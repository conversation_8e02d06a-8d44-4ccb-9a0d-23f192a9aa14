package com.yeepay.g3.app.account.pay.mboss.controller.app;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.AccountWithdrawArrivalTypeModel;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.BindCardModel;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.DicCodeDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.EnumHelper;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.BankInfoRemoteService;
import com.yeepay.g3.app.account.pay.mboss.remote.BindCardFacadeRemoteService;
import com.yeepay.g3.app.account.pay.mboss.remote.MerchantProductRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.Costants;
import com.yeepay.g3.app.account.pay.mboss.utils.HiddenCodeUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.SmartCacheUtilsHelper;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.shiro.helper.ShiroSecurityHelper;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BankCardAccount;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BindCardQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BankCardTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 老板管账app 企业账户 提现
 *
 * @author: Mr.yin
 * @date: 2024/7/24  15:11
 */
@Controller
@Api(tags = "app-企业账户-提现")
@RequestMapping("/app/withdraw")
public class WithdrawAppController extends BaseController {

    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());


    @Resource
    private BindCardFacadeRemoteService bindCardFacadeRemoteService;

    @Resource
    private MerchantProductRemoteService merchantProductRemoteService;

    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;

    @Resource
    private BankInfoRemoteService bankInfoRemoteService;


    @RequestMapping(value = "/cardList", method = RequestMethod.GET)
    @ApiOperation(value = "查询绑定卡")
    @ResponseBody
    public BaseRespDTO<List<BindCardModel>> queryBindCardList() {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[/cardList 查询提现绑定卡] 商户={}", currentCustomerNumber);
        try {
            /*查询三个月最后一条提现卡记录*/
//            Date endTime = new Date();
//            WithdrawOrderEntity lastWithdrawOrder = withdrawOrderService.selectLastSuccessWithdrawOrderByMerchantNo(currentCustomerNumber, LocalDateTimeUtils.getToDayOfFewMonth(endTime, 3), endTime);
            String cacheKey = SmartCacheUtilsHelper.structureKey(SmartCacheUtilsHelper.SmartCacheKeyConstants.MERCHANT_LAST_WITHDRAW_CARD, currentCustomerNumber);
            String bindId = SmartCacheUtilsHelper.getWithOutException(cacheKey);
            LOGGER.info("[查询提现绑定卡] 商户={} 查询到3个月成功的订单信息 BankAccountNo =  {}", bindId);
            BindCardQueryRespDTO bindCardQueryRespDTO = bindCardFacadeRemoteService.queryBindCardV2(currentCustomerNumber);
            List<BankCardAccount> bankCardAccountList = bindCardQueryRespDTO.getBankCardAccountList();
            LinkedList<BindCardModel> bindCardModelList = new LinkedList<>();
            if (!CheckUtils.isEmpty(bankCardAccountList)) {
                LOGGER.info("[查询提现绑定卡] 商户={} 查询绑定银行卡成功，卡数量为= {}", bindCardQueryRespDTO.getMerchantNo(), bankCardAccountList.size());
                for (BankCardAccount bankCardAccount : bankCardAccountList) {
                    if (StringUtils.isNotBlank(bindId) && bindId.equals(bankCardAccount.getBindCardId())) {
                        bindCardModelList.addFirst(covertBindCardModel(bankCardAccount));
                    } else {
                        bindCardModelList.add(covertBindCardModel(bankCardAccount));
                    }
                }
            }
            LOGGER.info("[查询提现绑定卡] 组合商户绑卡信息，商编为={}，卡信息为={}", currentCustomerNumber, JSONUtils.toJsonString(bindCardModelList));
            return BaseRespDTO.success(bindCardModelList);
        } catch (YeepayBizException e) {
            LOGGER.warn("[查询提现绑定卡] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("查询绑卡信息异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    private BindCardModel covertBindCardModel(BankCardAccount bankCardAccount) {
        BindCardModel bindCardModel = new BindCardModel();
        //账户/商户名
        if (BankCardTypeEnum.DEBIT_CARD == bankCardAccount.getBankCardType()) {
            bindCardModel.setName(HiddenCodeUtils.hiddenName(bankCardAccount.getAccountName()));
        } else {
            bindCardModel.setName(bankCardAccount.getAccountName());
        }
        bindCardModel.setBankCode(bankCardAccount.getBankCode());
        bindCardModel.setLogoCode(bankInfoRemoteService.queryBankLogoCodeByBankCode(bankCardAccount.getBankCode()));
        //银行名称
        bindCardModel.setBankName(bankCardAccount.getBankName());
        //掩码银行卡号
        bindCardModel.setCardNo(HiddenCodeUtils.hiddenBankCardNo(bankCardAccount.getAccountNo()));
        //正常提现卡只有（DEBIT_CARD， ENTERPRISE_ACCOUNT）两个类型
        bindCardModel.setCardType(String.valueOf(bankCardAccount.getBankCardType()));
        bindCardModel.setCardTypeDesc(EnumHelper.getWithdrawCardTypeDesc(bankCardAccount.getBankCardType()));
        bindCardModel.setBindId(bankCardAccount.getBindCardId());

        return bindCardModel;
    }

    /**
     * 1.不同的账户类型 规则上定义的可支持的到账时间不同，例：手续费账户，只支持次日到账
     * 2.一些账户类型提现不需要校验产品，或者校验的不是一般的产品 ENTERPRISE_WITHDRAW_STANDARD 例：手续费不校验，不计费，营销账户提现是这个：MARKET_WITHDRAW
     *
     * @return
     */
    @RequestMapping(value = "/arrive-time", method = RequestMethod.GET)
    @ApiOperation(value = "查询可支持的提现账户 及 到账时效")
    @ResponseBody
//    @RequiresPermissions(Costants.CONFIRM_WITHDRAW_RULE)
    public BaseRespDTO<List<AccountWithdrawArrivalTypeModel>> queryWithDrawArrivalTime() {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[/arrive-time 查询可支持的提现账户到账时效] 商户={}", currentCustomerNumber);
        List<AccountWithdrawArrivalTypeModel> list = Lists.newArrayList();
        try {
            /*首先校验权限*/
            if (!ShiroSecurityHelper.hasPermission(Logical.AND, Costants.CONFIRM_WITHDRAW_RULE)) {
                LOGGER.info("[/arrive-time] 当前操作员无提现发起权限 operator={} ,商户={}",getCurrentUser().getLoginName(), currentCustomerNumber);
                throw AccountPayException.NO_PERMISSION_ERROR.newInstance("暂无权限,请联系管理员或咨询客服申请权限");
            }
            /*1.校验一下资金账户存不存在?*/
            List<AccountDTO> accountList = businessCheckRemoteService.queryAccountInfos(currentCustomerNumber);
            Map<String, String> allowAccountTypeAndFirstProductCode = ConfigUtils.getAppWithdrawAllowAccountTypeMap();
            List<AccountDTO> allowAccountType = accountList.stream().filter(e -> allowAccountTypeAndFirstProductCode.containsKey(e.getAccountType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allowAccountType)) {
                LOGGER.info("[查询可支持的提现账户到账时效] 不存在支持的账户类型 商编为={}，result={}", currentCustomerNumber, JSONUtils.toJsonString(list));
                return BaseRespDTO.success(list);
            }
            /*2.为各个账户类型找到账产品开通的时间*/
            Map<String, List<DicCodeDTO>> arriveTypeMap = Maps.newHashMap();
            for (AccountDTO accountDTO : allowAccountType) {
                /*2.1确认好是不是这个一级*/
                String firstProduct = allowAccountTypeAndFirstProductCode.get(accountDTO.getAccountType());
                /*2.2 获取一下一级下的产品开通列表*/
                List<DicCodeDTO> arriveTypeList = arriveTypeMap.get(firstProduct);
                if (/*没查过就查一下*/null == arriveTypeList) {
                    arriveTypeList = merchantProductRemoteService.queryWithdrawArriveTypeMerchant(currentCustomerNumber, firstProduct);
                    /*如果为空底下是空list*/
                    arriveTypeMap.put(firstProduct, arriveTypeList);
                }
                /*2.3存在对应的产品开通，则加进去*/
                if (!CollectionUtils.isEmpty(arriveTypeList)) {
                    AccountWithdrawArrivalTypeModel arrivalTypeModel = new AccountWithdrawArrivalTypeModel();
                    arrivalTypeModel.setAccountType(accountDTO.getAccountType());
                    arrivalTypeModel.setAccountTypeDesc(EnumHelper.getAccountTypeDesc(accountDTO.getAccountType()));
                    arrivalTypeModel.setArriveTypeModelList(arriveTypeList);
                    list.add(arrivalTypeModel);
                }
            }
            LOGGER.info("[查询可支持的提现账户到账时效] 商编为={}，result={}", currentCustomerNumber, JSONUtils.toJsonString(list));
            return BaseRespDTO.success(list);
        } catch (YeepayBizException e) {
            LOGGER.warn("[查询可支持的提现账户到账时效] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[查询可支持的提现账户到账时效] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }


}
