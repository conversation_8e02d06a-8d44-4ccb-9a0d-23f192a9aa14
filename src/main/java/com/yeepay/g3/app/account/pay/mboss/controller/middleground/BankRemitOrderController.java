package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.BankRemitModel;
import com.yeepay.g3.app.account.pay.mboss.dto.BankRemitQueryParam;
import com.yeepay.g3.app.account.pay.mboss.dto.BankRemitRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankRemitStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.service.BankRemitOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BankRemitOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.bank.management.facade.SearchBankInfoFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.recharge.exception.UnionAccountRechargeException;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.yeepay.g3.app.account.pay.mboss.constant.NewRemitConstant.PAGE_NO_DEFAULT_VAL;
import static com.yeepay.g3.app.account.pay.mboss.constant.NewRemitConstant.PAGE_SIZE_DEFAULT_VAL;

/**
 * 订单付款
 */
@Controller
@RequestMapping(value = "/bankRemit")
public class BankRemitOrderController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(BankRemitOrderController.class);
    private SearchBankInfoFacade searchBankInfoFacade = RemoteServiceFactory.getService(SearchBankInfoFacade.class);

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    @Autowired
    private BankRemitOrderService bankRemitOrderService;

    @RequestMapping("/query")
    public ModelAndView view() {
        return new ModelAndView("bankRemit/queryBankRemit");
    }

    @RequestMapping(value = "/getFailResultPage", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "订单付款订单查询")
    public BaseRespDTO<BankRemitRespDTO> queryOrderList(BankRemitQueryParam bankRemitQueryParam,
                                                        @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                                        @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        logger.info("查询订单付款订单，请求参数{}", JSONUtils.toJsonString(bankRemitQueryParam));
        String currentCustomerNumber = getCurrentCustomerNumber();
        bankRemitQueryParam.setCustomerNumber(currentCustomerNumber);
        logger.info("[/cardList 订单付款记录查询] 商户={}", currentCustomerNumber);
        PreCheck.checkArgument(StringUtils.isNotBlank(bankRemitQueryParam.getCustomerNumber()),"商户编号不能为空");
        String status = bankRemitQueryParam.getStatus();
        if (status.equals(BankRemitStatusEnum.REMITTING.name())) {
            String ll=  BankRemitStatusEnum.BANK_DEDUCT.name()+ "," + BankRemitStatusEnum.CLEARING.name() + "," + BankRemitStatusEnum.REMITTING.name();
            bankRemitQueryParam.setStatus(ll);
        }
        if(status.equals(BankRemitStatusEnum.FAIL.name())){
            status=BankRemitStatusEnum.FAIL.name()+","+BankRemitStatusEnum.CANCELED.name();
            bankRemitQueryParam.setStatus(status);
        }
        BankRemitRespDTO respDTO =new BankRemitRespDTO();
        try{
            bankRemitQueryParam.validateParam();
            LocalDate start = LocalDate.parse(bankRemitQueryParam.getCreateStartDate());
            LocalDate end = LocalDate.parse(bankRemitQueryParam.getCreateEndDate());
            // 计算两个日期之间的天数差异
            long daysBetween = ChronoUnit.DAYS.between(start, end);
            // 判断是否超过92天
            Assert.isTrue(daysBetween <= 92, "查询时间间隔不能超过3个月");
            String receiverAccountNo = bankRemitQueryParam.getReceiverAccountNo();
            if(StringUtils.isNotBlank(receiverAccountNo)){
                bankRemitQueryParam.setReceiverAccountNo(AESUtils.encryptDigest(receiverAccountNo));
            }
            //处理收款方名称
            String receiverAccountName = bankRemitQueryParam.getReceiverAccountName();
            if(StringUtils.isNotBlank(receiverAccountName)){
                bankRemitQueryParam.setReceiverAccountName(AESUtils.encryptDigest(receiverAccountName));
            }
            List<Map<String,Object>> sumQueryResult = this.queryBankRemitOrderListSum(bankRemitQueryParam, pageSize, pageNo);
            if(sumQueryResult != null && !sumQueryResult.isEmpty()) {
                Map<String, Object> sumResult = sumQueryResult.get(0);
                String sum_amount = sumResult.get("sum_amount").toString();
                String sum_fee = sumResult.get("sum_fee").toString();
                if(StringUtils.isEmpty(sum_fee)){
                    sum_fee = "0";
                }
                if(StringUtils.isEmpty(sum_amount)){
                    sum_amount="0";
                }
                respDTO.setSumCount(sumResult.get("sum_count").toString());// 总笔数
                respDTO.setSumAmount(new BigDecimal(sum_amount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总金额
                respDTO.setSumFee(new BigDecimal(sum_fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总手续费
                respDTO.setTotalCount(sumResult.get("sum_count").toString());// 总数
            } else {
                respDTO.setSumCount("0");// 总笔数
                respDTO.setSumAmount("0.00");// 总金额
                respDTO.setSumFee("0.00");// 总手续费
            }
            QueryResult queryResult = this.queryBankRemitOrderList(bankRemitQueryParam, pageNo, pageSize);
            List<BankRemitModel> bankRemitModelList = convertToBankRemitModelList(queryResult);
            respDTO.setPageNo(new BigDecimal(pageNo));
            respDTO.setBankRemitModelList(bankRemitModelList);
            return BaseRespDTO.success(respDTO);
        }catch(IllegalArgumentException e){
            logger.warn("[订单付款列表查询] 参数异常，商编=" + currentCustomerNumber +"异常为={}", e.getMessage());
            throw UnionAccountRechargeException.PARAM_REQUIRED_ERROR.newInstance(e.getMessage());
        } catch (YeepayBizException e) {
            logger.warn("[查询可支持的提现账户到账时效] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("[查询可支持的提现账户到账时效] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }
    public List<BankRemitModel> convertToBankRemitModelList(QueryResult queryResult) {
        List<Map<String, Object>> queryResultList = Lists.newArrayList(queryResult.getData());
        List<BankRemitModel> resultList = Lists.newArrayList();
        for (Map<String, Object> resultMap : queryResultList) {
            BankRemitModel bankRemitModel = convertToBankRemitModelList(resultMap);
            resultList.add(bankRemitModel);
        }
        return resultList;
    }

    private BankRemitModel convertToBankRemitModelList(Map<String, Object> resultMap) {
        BankRemitModel bankRemitModel = new BankRemitModel();
        bankRemitModel.setId((Long) resultMap.get("id"));
        bankRemitModel.setRequestNo((String) resultMap.get("request_no"));
        bankRemitModel.setOrderNo((String) resultMap.get("order_no"));
        bankRemitModel.setMerchantNo((String) resultMap.get("merchant_no"));
        BigDecimal fee = (BigDecimal) resultMap.get("fee");
        if(fee!=null){
            bankRemitModel.setFee((fee.setScale(2, BigDecimal.ROUND_HALF_UP)).toString());
        }
        BigDecimal orderAmount = (BigDecimal) resultMap.get("order_amount");
        if(orderAmount!=null){
            bankRemitModel.setOrderAmount(orderAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        }
        BigDecimal receiveAmount = (BigDecimal) resultMap.get("receive_amount");
        if(receiveAmount!=null){
            bankRemitModel.setReceiveAmount(receiveAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        }
        BigDecimal debitAmount = (BigDecimal) resultMap.get("debit_amount");
        if(debitAmount!=null){
            bankRemitModel.setDebitAmount(debitAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        }

        String status = (String) resultMap.get("status");
        bankRemitModel.setStatus((String) resultMap.get("status"));
        if(status.equals(BankRemitStatusEnum.BANK_DEDUCT.name()) ||
                status.equals(BankRemitStatusEnum.CLEARING.name()) || status.equals(BankRemitStatusEnum.REMITTING.name())){
            bankRemitModel.setStatus(BankRemitStatusEnum.REMITTING.name());
        }
        if(status.equals(BankRemitStatusEnum.FAIL.name()) || status.equals(BankRemitStatusEnum.CANCELED.name())){
            bankRemitModel.setStatus(BankRemitStatusEnum.FAIL.name());
        }
        bankRemitModel.setCreateTime(((Date) resultMap.get("create_time")));
        bankRemitModel.setFinishTime(((Date) resultMap.get("finish_time")));
        bankRemitModel.setReverseTime(((Date) resultMap.get("reverse_time")));
        bankRemitModel.setReturnMsg((String) resultMap.get("return_msg"));
        bankRemitModel.setComments((String) resultMap.get("comments"));
        bankRemitModel.setRelationOrderNo((String) resultMap.get("relation_order_no"));
        //收款账号和户号名称解密
        String receiverAccountNo = (String) resultMap.get("receiver_account_no");
        receiverAccountNo = AESUtils.decryptWithBase64(receiverAccountNo);
        bankRemitModel.setReceiverAccountNo(receiverAccountNo);
        String receiverAccountName = (String) resultMap.get("receiver_account_name");
        receiverAccountName= AESUtils.decryptWithBase64(receiverAccountName);
        bankRemitModel.setReceiverAccountName(receiverAccountName);
        //付款账号和户号名称解密
        String payerAccountNo = (String)resultMap.get("payer_account_no");
        payerAccountNo = AESUtils.decryptWithBase64(payerAccountNo);
        bankRemitModel.setPayerAccountNo(payerAccountNo);
        String payerAccountName = (String) resultMap.get("payer_account_name");
        payerAccountName= AESUtils.decryptWithBase64(payerAccountName);
        bankRemitModel.setPayerAccountName(payerAccountName);
        bankRemitModel.setRemitType((String) resultMap.get("remit_type"));
        return bankRemitModel;
    }

    /**
     * @Description: 查询付款订单
     * @param bankRemitQueryParam
     * orderNoList:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     */
    private QueryResult queryBankRemitOrderList(BankRemitQueryParam bankRemitQueryParam,int pageNo, int pageSize) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("createStartDate", bankRemitQueryParam.getCreateStartDate());
        queryMap.put("createEndDate", bankRemitQueryParam.getCreateEndDate());
        queryMap.put("remitCreateStartDate", bankRemitQueryParam.getCreateStartDate());
        queryMap.put("remitCreateEndDate", bankRemitQueryParam.getCreateEndDate());
        queryMap.put("customerNumber", bankRemitQueryParam.getCustomerNumber());
        queryMap.put("status", bankRemitQueryParam.getStatus());
        queryMap.put("orderNo", bankRemitQueryParam.getOrderNo());
        queryMap.put("remitType", bankRemitQueryParam.getRemitType());
        queryMap.put("receiverAccountNo", bankRemitQueryParam.getReceiverAccountNo());
        queryMap.put("receiverAccountName", bankRemitQueryParam.getReceiverAccountName());
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("queryBankRemitOrderList", queryParam);
    }

    private List<Map<String, Object>> queryBankRemitOrderListSum(BankRemitQueryParam bankRemitQueryParam,int pageNo, int pageSize) {
        //构造查询参数
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("createStartDate", bankRemitQueryParam.getCreateStartDate());
        queryMap.put("createEndDate", bankRemitQueryParam.getCreateEndDate());
        queryMap.put("remitCreateStartDate", bankRemitQueryParam.getCreateStartDate());
        queryMap.put("remitCreateEndDate", bankRemitQueryParam.getCreateEndDate());
        queryMap.put("customerNumber", bankRemitQueryParam.getCustomerNumber());
        queryMap.put("status", bankRemitQueryParam.getStatus());
        queryMap.put("orderNo", bankRemitQueryParam.getOrderNo());
        queryMap.put("remitType", bankRemitQueryParam.getRemitType());
        queryMap.put("receiverAccountNo", bankRemitQueryParam.getReceiverAccountNo());
        queryMap.put("receiverAccountName", bankRemitQueryParam.getReceiverAccountName());
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        List<Map<String, Object>> bankRemitOrderListSum = QueryServiceUtil.query("accountTradeService", "queryBankRemitOrderListSum", queryMap);
        return bankRemitOrderListSum;

    }

    /**
     * 下载
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/download")
    @ResponseBody
    @ApiOperation(value = "下载")
    public void downloadRecord(BankRemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("开始下载订单付款记录，请求参数{}", JSON.toJSONString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            String currentCustomerNumber = getCurrentCustomerNumber();
            param.setCustomerNumber(currentCustomerNumber);
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "下级商户编号不能空");
            param = dealParams(param);
            if(StringUtils.isEmpty(param.getCreateStartDate()) || StringUtils.isEmpty(param.getCreateEndDate())){
                throw UnionAccountException.PARAM_REQUIRED_ERROR.newInstance("查询时间范围必填");
            }
            StringBuilder desc = new StringBuilder();
            desc.append("查询订单付款记录,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            //查询的截止日期 大于 当前时间 则替换当前时间为结束时间
            if (StringUtils.isNotBlank(param.getCreateEndDate())) {
                Date current = new Date();
                if (DateUtils.parseDate(param.getCreateEndDate(), DateUtils.DATE_FORMAT_DATEONLY).compareTo(current) > 0) {
                    param.setCreateEndDate(DateUtils.toString(current, DateUtils.DATE_FORMAT_DATETIME));
                }
            }
            if ("appointSync".equals(param.getSyncType())) {
                new BankRemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-", bankRemitOrderService, merchantRemoteService).syncDownload(request, response);
            } else {

                new BankRemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-", bankRemitOrderService, merchantRemoteService).download(request, response);
            }
            logger.info("下载付款记录excel已完成");
        } catch (Throwable ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    private BankRemitQueryParam dealParams(BankRemitQueryParam param){

        String status = param.getStatus();
        if (status.equals(BankRemitStatusEnum.REMITTING.name())) {
            String orderStatus=  BankRemitStatusEnum.BANK_DEDUCT.name()+ "," + BankRemitStatusEnum.CLEARING.name() + "," + BankRemitStatusEnum.REMITTING.name();
            param.setStatus(orderStatus);
        }
        if(status.equals(BankRemitStatusEnum.FAIL.name())) {
            status = BankRemitStatusEnum.FAIL.name() + "," + BankRemitStatusEnum.CANCELED.name();
            param.setStatus(status);
        }
        String receiverAccountNo = param.getReceiverAccountNo();
        if(StringUtils.isNotBlank(receiverAccountNo)){
            param.setReceiverAccountNo(AESUtils.encryptDigest(receiverAccountNo));
        }
        //处理收款方名称
        String receiverAccountName = param.getReceiverAccountName();
        if(StringUtils.isNotBlank(receiverAccountName)){
            param.setReceiverAccountName(AESUtils.encryptDigest(receiverAccountName));
        }
        return param;
    }


}
