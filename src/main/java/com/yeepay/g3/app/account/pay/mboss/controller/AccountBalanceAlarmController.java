package com.yeepay.g3.app.account.pay.mboss.controller;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.yeepay.g3.facade.account.alarm.dto.AccountBalAlarmNotifyIntervalRespDto;
import com.yeepay.g3.facade.account.alarm.dto.AccountBalAlarmQueryAccountsRespDto;
import com.yeepay.g3.facade.account.alarm.dto.AccountBalAlarmRuleReqDto;
import com.yeepay.g3.facade.account.alarm.dto.AccountBalAlarmRuleRespDto;
import com.yeepay.g3.facade.account.alarm.facade.AccountBalAlarmRuleFacade;
import com.yeepay.g3.facade.account.pay.enums.RequestStatusEnum;
import com.yeepay.g3.utils.common.CommonUtils;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.app.account.pay.mboss.dao.DirectTransferOperatorCreditNoRelaDao;
import com.yeepay.g3.app.account.pay.mboss.dao.DirectTransferOperatorDao;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorCreditNoRelaEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorEntity;
import com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DataFormater;
import com.yeepay.g3.app.account.pay.mboss.utils.QueryServiceUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.response.ResponseMessage.Status;
import com.yeepay.g3.component.platform.facade.CustomerQueryFacade;
import com.yeepay.g3.core.member.util.ConfigureUtil;
import com.yeepay.g3.facade.account.pay.exception.AccountPayException;
import com.yeepay.g3.facade.account.pay.facade.AccountPayTransferFacade;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.facade.MerchantFacade;
import com.yeepay.g3.facade.mp.facade.OperateFacade;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


@Controller
@RequestMapping("/account/alarm")
public class AccountBalanceAlarmController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountBalanceAlarmController.class);
    private AccountBalAlarmRuleFacade ruleFacade = RemoteServiceFactory.getService(AccountBalAlarmRuleFacade.class);
    private static final String QUERY_PERMISSION_CONSTANT = "21402";

    /**
     * 查询商户账户
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/accountList")
    public ModelAndView accountList(HttpServletRequest request, HttpServletResponse response) {
        String customerNumber = getCurrentCustomerNumber();
        ModelAndView mav = new ModelAndView();
        if(isA3g()){
            //中台页面
            mav.setViewName("accountAlarmMG/accountList");
        }else{
            //非中台
            mav.setViewName("accountAlarm/accountList");
        }

        return mav;
    }

    /**
     * 规则设置页面
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/rulePage")
    public ModelAndView rulePage(HttpServletRequest request,
                                 HttpServletResponse response) {
        ModelAndView mav = new ModelAndView();
        if(isA3g()){
            //中台页面
            mav.setViewName("accountAlarmMG/rulePage");
        }else{
            //非中台
            mav.setViewName("accountAlarm/rulePage");
        }
        return mav;
    }


    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/query-notify-interval")
    @ResponseBody
    public AccountBalAlarmNotifyIntervalRespDto queryNotifyInterval() {
        String requestNo = CommonUtils.getUUID();
        LOGGER.info("queryNotifyInterval requestNo={},customerNo={}", requestNo,getCurrentCustomerNumber());
        try {
            AccountBalAlarmNotifyIntervalRespDto resp = ruleFacade.queryNotifyIntervals(requestNo);
            return resp;
        }catch(Throwable e) {
            LOGGER.error("queryNotifyInterval-error",e);
            AccountBalAlarmNotifyIntervalRespDto resp = new AccountBalAlarmNotifyIntervalRespDto();
            resp.setStatus(RequestStatusEnum.FAIL);
            resp.setErrorMsg(e.getMessage());
            return resp;
        }
    }

    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/query-account-list")
    @ResponseBody
    public AccountBalAlarmQueryAccountsRespDto queryAccountList() {
        String requestNo = CommonUtils.getUUID();
        String customerNo = getCurrentCustomerNumber();
        LOGGER.info("queryAccountList requestNo={},customerNo={}", requestNo,customerNo);
        try {
            AccountBalAlarmQueryAccountsRespDto accountList = ruleFacade.queryAccountByCustomerNo(requestNo, customerNo);
            return accountList;
        }catch(Throwable e) {
            LOGGER.error("queryAccountList-error",e);
            AccountBalAlarmQueryAccountsRespDto accountList = new  AccountBalAlarmQueryAccountsRespDto();
            accountList.setErrorMsg(e.getMessage());
            accountList.setStatus(RequestStatusEnum.FAIL);
            return accountList;
        }
    }

    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/setting-rule",method = RequestMethod.POST)
    @ResponseBody
    public AccountBalAlarmRuleRespDto settingRule(@RequestBody AccountBalAlarmRuleReqDto req) {
        String requestNo = CommonUtils.getUUID();
        String customerNo = getCurrentCustomerNumber();
        req.setRequestNo(requestNo);
        req.setCustomerNo(customerNo);
        LOGGER.info("settingRule requestNo={},customerNo={},req={}", requestNo, customerNo, req.toString());
        try {
            AccountBalAlarmRuleRespDto resp = ruleFacade.setRule(req);
            return resp;
        } catch (Throwable e) {
            LOGGER.error("settingRule-error", e);
            return returnFailResp(e,requestNo);
        }
    }

    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/query-rule")
    @ResponseBody
    public AccountBalAlarmRuleRespDto queryRule(@RequestParam(value = "id",required = true) Long id) {
        String requestNo = CommonUtils.getUUID();
        String customerNo = getCurrentCustomerNumber();
        LOGGER.info("queryRule requestNo={},customerNo={},id={}", requestNo,customerNo,id);
        try {
            AccountBalAlarmRuleRespDto resp = ruleFacade.queryRuleByAcccountId(requestNo,id,customerNo);
            return resp;
        }catch(Throwable e) {
            LOGGER.error("queryRule-error",e);
            return returnFailResp(e,requestNo);
        }
    }

    private AccountBalAlarmRuleRespDto returnFailResp(Throwable e,String requestNo) {
        AccountBalAlarmRuleRespDto resp  = new  AccountBalAlarmRuleRespDto();
        resp.setErrorMsg(e.getMessage());
        resp.setStatus(RequestStatusEnum.FAIL);
        resp.setRequestNo(requestNo);
        return resp;
    }

    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/close-rule",method = RequestMethod.POST)
    @ResponseBody
    public AccountBalAlarmRuleRespDto closeRule(@RequestBody AccountBalAlarmRuleReqDto req) {
        String requestNo = CommonUtils.getUUID();
        String customerNo = getCurrentCustomerNumber();
        req.setCustomerNo(customerNo);
        LOGGER.info("closeRule requestNo={},customerNo={},id={}", requestNo,customerNo,req.getId());
        try {
            CheckUtils.notEmpty(req.getId(),"id");
            AccountBalAlarmRuleRespDto resp = ruleFacade.closeRule(requestNo,req.getId(),customerNo);
            return resp;
        }catch(Throwable e) {
            LOGGER.error("closeRule-error",e);
            return returnFailResp(e,requestNo);
        }
    }

}
