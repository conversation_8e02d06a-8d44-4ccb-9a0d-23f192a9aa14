package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import com.yeepay.g3.app.account.pay.mboss.controller.file.FileHandle;

public class AliPayProxyFileInfoDTO {
    /**
     * 编号
     */
    @FileHandle(index=0,desc="编号（必填）")
    private  String lineNum;

    /**
     * 收款账号
     */
    @FileHandle(index=1,desc="收款方支付宝账号（必填）")
    private String receiveNo;
    /**
     * 收款账号
     */
    @FileHandle(index=2,desc="收款方姓名（必填）")
    private String receiveName;

    /**
     * 金额
     */
    @FileHandle(index=3,desc="金额（必填，单位：元）")
    private String totalAmount;

    /**
     * 备注
     */
    @FileHandle(index=4,desc="备注（选填）")
    private String remark;

    public String getLineNum() {
        return lineNum;
    }

    public void setLineNum(String lineNum) {
        this.lineNum = lineNum;
    }

    public String getReceiveNo() {
        return receiveNo;
    }

    public void setReceiveNo(String receiveNo) {
        this.receiveNo = receiveNo;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
