package com.yeepay.g3.app.account.pay.mboss.databaseScan.manage.dao;

import com.yeepay.g3.app.account.pay.mboss.databaseScan.manage.entity.BankAccountOpenRecordEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
//@Repository
public interface BankAccountOpenRecordDao {

    /**
     * 查询总条数
     *
     */
    Long queryRecordCount(@Param("merchantNoList") List<String> merchantNoList,
                          @Param("bankIdList") List<String> bankIdList, @Param("statusList") List<String> statusList);


    /**
     * 查询开户请求数据
     *
     * @param merchantNoList
     * @param bankIdList
     * @param statusList
     * @param pageIndex
     * @param pageEnd
     * @return
     */
    List<BankAccountOpenRecordEntity> queryRecordPage(@Param("merchantNoList") List<String> merchantNoList,
                                                      @Param("bankIdList") List<String> bankIdList, @Param("statusList") List<String> statusList,@Param("accountStatus") String accountStatus,
                                                      @Param("pageIndex") Integer pageIndex, @Param("pageEnd") Integer pageEnd);

    /**
     * 查询不能再次发起的银行编码
     * @param merchantNoList
     * @return
     */
    List<BankAccountOpenRecordEntity> selectNotRetryBankIdentityCode(@Param("merchantNoList") List<String> merchantNoList);
}