package com.yeepay.g3.app.account.pay.mboss.dto.app.response;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 19:29
 */
public class PaymentAggregateResponseDTO implements Serializable {
    /**
     * 总金额
     */
    private String sumAmount;
    /**
     * 总数量
     */
    private String sumCount;
    /**
     * 总服务费
     */
    private String sumFee;
    /**
     * 总数量
     */
    private String totalCount;


    public String getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(String totalCount) {
        this.totalCount = totalCount;
    }

    public String getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(String sumAmount) {
        this.sumAmount = sumAmount;
    }

    public String getSumCount() {
        return sumCount;
    }

    public void setSumCount(String sumCount) {
        this.sumCount = sumCount;
    }

    public String getSumFee() {
        return sumFee;
    }

    public void setSumFee(String sumFee) {
        this.sumFee = sumFee;
    }

}
