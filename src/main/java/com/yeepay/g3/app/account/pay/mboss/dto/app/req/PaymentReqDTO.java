package com.yeepay.g3.app.account.pay.mboss.dto.app.req;

import com.yeepay.g3.app.account.pay.mboss.dto.app.PageReqBase;

/**
 * title:
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6 15:14
 */
public class PaymentReqDTO extends PageReqBase {
    /**
     * 开始查询时间
     */
    private String createStartDate;
    /**
     * 开始查询结束时间
     */
    private String createEndDate;
    /**
     * 状态(REQUEST_ACCEPT:已受理、SUCCESS:已到账、FAIL:付款失败、CANCELED:撤销成功、REVERSED:银行冲退)
     */
    private String status;

    private String customerNumber;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(String createStartDate) {
        this.createStartDate = createStartDate;
    }

    public String getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(String createEndDate) {
        this.createEndDate = createEndDate;
    }

    public String getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
    }
}
