package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.account.pay.mboss.constant.RemitConstant;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.WithdrawListQueryReqDTO;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.WithdrawReqDTO;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base.BasePageRespDTO;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.WithdrawConfirmResp;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.WithdrawDetailModel;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.WithdrawOrderModel;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.dto.req.DownloadElectronicReqDTO;
import com.yeepay.g3.app.account.pay.mboss.entity.WithdrawOrderBase;
import com.yeepay.g3.app.account.pay.mboss.enumtype.EnumHelper;
import com.yeepay.g3.app.account.pay.mboss.enumtype.PlatformTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.ProductTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.BindCardFacadeRemoteService;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.CookieService;
import com.yeepay.g3.app.account.pay.mboss.service.WithdrawOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.ProvinceCityService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.WithdrawOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.handler.RemoteFacadeProxyFactory;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.facade.account.adapter.facade.AccountFrontFacade;
import com.yeepay.g3.facade.bank.management.facade.SearchBankInfoFacade;
import com.yeepay.g3.facade.bank.management.facade.dto.BranchBankDTO;
import com.yeepay.g3.facade.bank.management.facade.dto.DistrictInfoDTO;
import com.yeepay.g3.facade.bank.management.facade.dto.DistrictNameAndCode;
import com.yeepay.g3.facade.merchant_platform.dto.AreaBaseDTO;
import com.yeepay.g3.facade.merchant_platform.dto.AreaCodeReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.AreaRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.*;
import com.yeepay.g3.facade.merchant_platform.enumtype.BankCardTag;
import com.yeepay.g3.facade.merchant_platform.facade.MerchantFacade;
import com.yeepay.g3.facade.merchant_platform.facade.customermanagement.CustomerMerchantInfoFacade;
import com.yeepay.g3.facade.merchant_platform.facade.customerplatform.CusBankCardFacade;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.BindCardQueryReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.BindCardReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.ModifyBindCardReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.SingleBindCardQueryReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.*;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BankCardOperateTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BankCardTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.BindSourceEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.TradeTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.manage.exception.UnionAccountManageException;
import com.yeepay.g3.facade.unionaccount.manage.facade.BindCardFacade;
import com.yeepay.g3.facade.unionaccount.manage.facade.ReceiptFacade;
import com.yeepay.g3.facade.unionaccount.recharge.exception.UnionAccountRechargeException;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.AutoWithdrawRuleCancelReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.AutoWithdrawRuleQueryReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.AutoWithdrawRuleSetReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.request.WithdrawStandardReqDTO;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.*;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.*;
import com.yeepay.g3.facade.unionaccount.trade.facade.MGWithdrawFacade;
import com.yeepay.g3.facade.unionaccount.trade.params.AccountBasicParam;
import com.yeepay.g3.facade.unionaccount.trade.params.ProductCodeParam;
import com.yeepay.g3.facade.unionaccount.trade.utils.WithdrawProductBasicConfigUtils;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.RuleStatusEnum;
import com.yeepay.g3.unionaccount.base.service.untils.OrderNoUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yeepay.g3.app.account.pay.mboss.utils.DateUtil.PATTERN_STANDARD19H;

/**
 * 提现
 */
@Controller
@RequestMapping("/withdraw")
@Api(tags = "app、mp提现管理")
public class WithdrawController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WithdrawController.class);

    /**
     * 允许跳转到的地址
     */
    private static final List<String> allowRedirectUrls = Lists.newArrayList("/withdraw/view/FUND_ACCOUNT","/withdraw/manageCardView","/withdraw/showAutoWithdrawRuleSet");

    //提现相关接口
    private MGWithdrawFacade MGWithdrawFacade = RemoteServiceFactory.getService(MGWithdrawFacade.class);
    //提现前置
    private AccountFrontFacade accountFrontFacade = RemoteFacadeProxyFactory.getService(AccountFrontFacade.class);


    //客户中心，银行账户
    private CusBankCardFacade cusBankCardFacade = RemoteFacadeProxyFactory.getService(CusBankCardFacade.class);

    //客户中心
    private MerchantFacade merchantFacade = RemoteFacadeProxyFactory.getService(MerchantFacade.class);

    //查询客户信息
    private CustomerMerchantInfoFacade customerMerchantInfoFacade = RemoteFacadeProxyFactory.getService(CustomerMerchantInfoFacade.class);


    //中台管理系统manage
    private BindCardFacade bindCardFacade = RemoteServiceFactory.getService(BindCardFacade.class);

    private SearchBankInfoFacade searchBankInfoFacade = RemoteServiceFactory.getService(SearchBankInfoFacade.class);

    private ReceiptFacade receiptFacade = RemoteFacadeProxyFactory.getService(ReceiptFacade.class);

    @Autowired
    private BusinessCheckRemoteService businessCheckRemoteService;

    private final ExecutorService executorService = ThreadUtil.getThreadPool();


    // 银行卡号保留后四位
    private static final String REMAIN_4 = "REMAIN_4";

    //提现状态
    private static final Map<String, String> WITHDRAW_STATUS_MAP = Maps.newLinkedHashMap();

    //到账类型
    private static final Map<String, String> ARRIVE_TYPE_MAP = Maps.newLinkedHashMap();

    //到账类型
    private static final Map<String, String> ACCOUNT_TYPE_MAP = Maps.newLinkedHashMap();

    //标准提现产品一级码
    private static final Set<String> MG_FIRST_PRODUCT_CODE_SET = Sets.newHashSet();

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private CookieService cookieService;

    @Resource
    private BindCardFacadeRemoteService bindCardFacadeRemoteService;

    @Resource
    private WithdrawOrderService withdrawOrderService;

    static {
        WITHDRAW_STATUS_MAP.put("REQUEST_RECEIVE","提现已接收");
        WITHDRAW_STATUS_MAP.put("REQUEST_ACCEPT","提现已受理");
        WITHDRAW_STATUS_MAP.put("REMITING,DEBIT_EXCEPTION,REMIT_EXCEPTION,REFUND_EXCEPTION,REFUND_FAIL","银行处理中");
        WITHDRAW_STATUS_MAP.put("SUCCESS","提现成功");
        WITHDRAW_STATUS_MAP.put("REVERSED","银行冲退");
        WITHDRAW_STATUS_MAP.put("FAIL","提现失败");

        ARRIVE_TYPE_MAP.put("REAL_TIME","实时到账");
        ARRIVE_TYPE_MAP.put("TWO_HOUR","2小时到账");
        ARRIVE_TYPE_MAP.put("NEXT_DAY","次日到账");

        getConfigAccountTypeList();

        MG_FIRST_PRODUCT_CODE_SET.add("-");//手续费账户无产品码，默认为 -
        getConfigProductCodeList();


    }

    private static void getConfigAccountTypeList(){
        List< AccountBasicParam> basicList = WithdrawProductBasicConfigUtils.getAccountConfigList();
        for(AccountBasicParam basic : basicList){
          ACCOUNT_TYPE_MAP.put(basic.getAccountType(),AccountTypeEnum.valueOf(basic.getAccountType()).getDesc());
        }
    }

    private static void getConfigProductCodeList(){
        List<ProductCodeParam> productList = WithdrawProductBasicConfigUtils.getProductConfigList();
        for(ProductCodeParam param : productList){
            MG_FIRST_PRODUCT_CODE_SET.add(param.getFirstProductCode());
        }
    }

    class AccountInfo implements Serializable, Comparable<AccountInfo>{
        private String accountType;
        private String serviceType;
        private String createTime;
        private BigDecimal balance;
        private Integer sort;
        private List<DicCodeDTO> dicCodeDTOS;
        public String getAccountType() {return accountType;}
        public void setAccountType(String accountType) {this.accountType = accountType;}
        public String getServiceType() {return serviceType;}
        public void setServiceType(String serviceType) {this.serviceType = serviceType;}
        public String getCreateTime() {return createTime;}
        public void setCreateTime(String createTime) {this.createTime = createTime;}
        public BigDecimal getBalance() {return balance;}
        public void setBalance(BigDecimal balance) {this.balance = balance;}
        public Integer getSort() {return sort;}
        public void setSort(Integer sort) {this.sort = sort;}
        public List<DicCodeDTO> getDicCodeDTOS() {return dicCodeDTOS;}
        public void setDicCodeDTOS(List<DicCodeDTO> dicCodeDTOS) {this.dicCodeDTOS = dicCodeDTOS;}
        @Override
        public int compareTo(AccountInfo o) {
            return this.getSort() - o.getSort();
        }
    }

    class ProductInfo implements Serializable{
      private String firstProductCode;
      private String secondProductCode;
      private String thirdProductCode;
      private String productType;
      public String getFirstProductCode() {
        return this.firstProductCode;
      }
      public void setFirstProductCode(String firstProductCode) {
        this.firstProductCode = firstProductCode;
      }
      public String getSecondProductCode() {
        return this.secondProductCode;
      }
      public void setSecondProductCode(String secondProductCode) {
        this.secondProductCode = secondProductCode;
      }
      public String getThirdProductCode() {
        return this.thirdProductCode;
      }
      public void setThirdProductCode(String thirdProductCode) {
        this.thirdProductCode = thirdProductCode;
      }
      public String getProductType() { return productType; }
      public void setProductType(String productType) { this.productType = productType; }
    }

    public class CheckProductTask implements Runnable {
        private final Logger LOGGER = LoggerFactory.getLogger(CheckProductTask.class);

        private BusinessCheckRemoteService service;
        private String productType;
        private String markProductCode;
        private String customerNumber;
        private String firstCode;
        private String secondCode;
        private CountDownLatch latch;
        private List<BaseProductDTO> productList;

        public CheckProductTask(BusinessCheckRemoteService service,String productType,String markProductCode,String customerNumber,String firstCode,String secondCode,CountDownLatch latch,List<BaseProductDTO> productList){
            this.markProductCode = markProductCode;
            this.productType = productType;
            this.service = service;
            this.customerNumber = customerNumber;
            this.firstCode = firstCode;
            this.secondCode = secondCode;
            this.latch = latch;
            this.productList = productList;
        }

        @Override
        public void run(){
            try {
                LOGGER.info("调用客户中心查询提现产品开通参数：customerNumber={},productType={},markProductCode={}, firstCode={}, secondCode={}",customerNumber, productType, markProductCode, firstCode, secondCode);
                MerchantProductQueryRespDTO merchantProductQueryRespDTO = service.queryMerchantProduct(customerNumber, productType, markProductCode, firstCode, secondCode);
                if("0000".equals(merchantProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductQueryRespDTO.getBaseProductList())) {
                    productList.addAll(merchantProductQueryRespDTO.getBaseProductList());
                    LOGGER.info("调用客户中心查询提现产品开通结果："+JSONUtils.toJsonString(merchantProductQueryRespDTO.getBaseProductList()));
                }
            }catch (Exception e){
                LOGGER.error("线程池查询产品开通情况异常");
            }finally {
                latch.countDown();
            }
        }
    }


    public List<AccountInfo> getWithdrawProductView(Map<String, AccountBasicParam> basicConfig,List<AccountInfo> accountInfoList) throws InterruptedException {
        //先获取有多少个产品需要校验开通状态
        List<ProductInfo> productInfos = new ArrayList<>();
        for(AccountInfo info : accountInfoList){
            AccountBasicParam param = basicConfig.get(info.accountType);
            //如果需要收费
            if(AccountServiceEnum.CHARGE.equals(AccountServiceEnum.valueOf(param.getServiceType()))){
                Iterator<Map.Entry<String, ProductCodeParam>> entries = param.getPayTypeMap().entrySet().iterator();
                while(entries.hasNext()){
                    ProductInfo product = new ProductInfo();
                    Map.Entry<String, ProductCodeParam> entry = entries.next();
                    product.setFirstProductCode(entry.getValue().getFirstProductCode());
                    product.setSecondProductCode(entry.getValue().getSecondProductCode());
                    product.setThirdProductCode(entry.getValue().getThirdProductCode());
                    product.setProductType(param.getProductType());
                    productInfos.add(product);
                }
            }
        }
        LOGGER.info("提现计费产品校验结果集：productInfos={}",JSONUtils.toJsonString(productInfos));
        //通过多线程请求客户中心，校验产品开通情况
        String customerNumber = getCurrentCustomerNumber();
        String markProductCode = businessCheckRemoteService.queryMarketProduct(customerNumber);
        List<BaseProductDTO> productList = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(productInfos.size());
        for(ProductInfo info : productInfos){
            CheckProductTask task = new CheckProductTask(businessCheckRemoteService,info.getProductType(),markProductCode,customerNumber,info.getFirstProductCode(),info.getSecondProductCode(),latch,productList);
            executorService.submit(task);
        }
        latch.await(500, TimeUnit.MILLISECONDS);
        LOGGER.info("提现计费产品客户中心校验结果集：productList={}",JSONUtils.toJsonString(productList));
        //通过产品开通情况，处理账户列表及到账方式
        Iterator<AccountInfo> accountIterator = accountInfoList.iterator();
        while(accountIterator.hasNext()){
            AccountInfo info = accountIterator.next();
            AccountBasicParam paramConfig = basicConfig.get(info.getAccountType());
            Iterator<Map.Entry<String, ProductCodeParam>> entries = paramConfig.getPayTypeMap().entrySet().iterator();
            List<DicCodeDTO> dicCodeDTOS = new ArrayList<>();
            //该账户类型计费
            if(AccountServiceEnum.CHARGE.equals(AccountServiceEnum.valueOf(paramConfig.getServiceType()))){
                int flag = 0;
                while(entries.hasNext()){
                    Map.Entry<String, ProductCodeParam> entry = entries.next();
                    String key = entry.getKey();
                    ProductCodeParam productCodeParam = entry.getValue();
                    //此处目前通过三级产品码校验
                    List<BaseProductDTO> result = productList.stream()
                        .filter(e ->   StringUtils.defaultIfEmpty(productCodeParam.getFirstProductCode(),"").equals(StringUtils.defaultIfEmpty(e.getFirstBaseProductCode(),""))
                                    && StringUtils.defaultIfEmpty(productCodeParam.getSecondProductCode(),"").equals(StringUtils.defaultIfEmpty(e.getSecondBaseProductCode(),""))
                                    && StringUtils.defaultIfEmpty(productCodeParam.getThirdProductCode(),"").equals(StringUtils.defaultIfEmpty(e.getThirdBaseProductCode(),"")))
                        .collect(Collectors.toList());
                    if(result!=null&&result.size()>0){
                        //匹配到则加加
                        flag++;
                        BaseProductDTO dto = result.get(0);
                        if(!CheckUtils.isEmpty(dto.getSecondBaseProductCode()))  {
                            Object feeType = null ;
                            if(!CollectionUtils.isEmpty(dto.getProductAttributeMap())){
                                feeType = dto.getProductAttributeMap().get("FEE_MERCHANT_TYPE");
                            }
                            dicCodeDTOS.add(new DicCodeDTO(WithdrawTypeEnum.valueOf(entry.getKey()).name(),WithdrawTypeEnum.valueOf(key).getMqDesc(),feeType==null?"":feeType.toString()));
                        }
                    }
                }
                info.setDicCodeDTOS(dicCodeDTOS);
                //没有匹配到产品，则去除
                if(flag==0){
                    accountIterator.remove();
                }
            }else{
                //非计费，则从配置获取到账方式
                while(entries.hasNext()){
                    Map.Entry<String, ProductCodeParam> entry = entries.next();
                    dicCodeDTOS.add(new DicCodeDTO(WithdrawTypeEnum.valueOf(entry.getKey()).name(),WithdrawTypeEnum.valueOf(entry.getKey()).getMqDesc(),""));
                }
                info.setDicCodeDTOS(dicCodeDTOS);
            }
        }
        return accountInfoList;
    }

    /**
     * 通过配置文件对账户类型进行排序并转换
     * @param accountList
     * @return
     */
    private List<AccountInfo> sortAccountInfo(Map<String, AccountBasicParam> basicConfig,List<AccountDTO> accountList){
        List<AccountInfo> list = new ArrayList<>();
        for(AccountDTO dto:accountList){
            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setAccountType(dto.getAccountType());
            accountInfo.setBalance(dto.getBalance());
            AccountBasicParam basicParam = basicConfig.get(accountInfo.getAccountType());
            if(basicParam!=null){
                accountInfo.setServiceType(basicParam.getServiceType());
                accountInfo.setSort(basicParam.getSort());
                list.add(accountInfo);
            }
        }
        Collections.sort(list);
        return CollectionUtils.isEmpty(list) ? Collections.EMPTY_LIST : list;
    }

    private void buildAccountView(List<AccountInfo> accountInfoList, ModelAndView mav) {
        List<Map<String,Object>> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(accountInfoList)){
            accountInfoList.stream().forEach(e->{
                Map<String,Object> map = new HashMap<>();
                map.put("accountType",e.getAccountType());
                map.put("balance",e.getBalance());
                map.put("accountTypeName",AccountTypeEnum.valueOf(e.getAccountType()).getDesc());
                map.put("serviceType",e.getServiceType());
                Collections.sort(e.getDicCodeDTOS());
                map.put("arriveType",JSONUtils.toJsonString(e.getDicCodeDTOS()));
                list.add(map);
            });
        }
        mav.addObject("accountTypeList",list);
    }

    /**
     * 跳转到提现页面
     * <AUTHOR>
     * @date 2020-03-11 11:35
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequiresPermissions(Costants.WITHDRAW_PERMISSION)
    @RequestMapping(value = "/view/{accountType}", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView withdrawView(HttpServletRequest request, @PathVariable("accountType") String accountType){
        //提现账户基本信息配置
        Map<String, AccountBasicParam> basicConfig = WithdrawProductBasicConfigUtils.getAccountBasicConfig();
        ModelAndView mav = new ModelAndView();
        mav.setViewName("/middleground/withdrawView");
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        mav = buildBindCardListView(mav,null);
        List<AccountDTO> accountList = businessCheckRemoteService.queryAccountInfos(getCurrentCustomerNumber());
        List<AccountInfo> accountInfoList = sortAccountInfo(basicConfig,accountList);
        try {
            accountInfoList = getWithdrawProductView(basicConfig,accountInfoList);
            mav.addObject("hasWithdrawProduct", !accountInfoList.isEmpty());
            buildAccountView(accountInfoList,mav);
            mav.addObject("accountType",accountType);
            mav = buildModelView(mav, request);
        } catch (Exception e) {
            LOGGER.error("提现卡页面错误", e);
            mav.addObject("exception",e.getMessage());
            mav.addObject("path","/middleground/withdrawView");
            mav.setViewName("/common/error");
            return mav;
        }

        return mav;
    }

    /**
     * 确认提现
     * <AUTHOR>
     * @date 2020-03-31 15:22
     * @param request:
     * @param accountType:
     * @param remark:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/confirm")
    @ResponseBody
    public ResponseMessage confirm(HttpServletRequest request,
                                   @RequestParam("accountType") String accountType,
                                   @RequestParam("remark") String remark){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String amount = request.getParameter("amount");
            String bindId = request.getParameter("bindId");
            String arriveType = request.getParameter("arriveType");
            if(CheckUtils.isEmpty(amount)){
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("提现金额不能为空");
                return resMsg;
            }

            if(CheckUtils.isEmpty(bindId)) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("请选择或添加提现卡");
                return resMsg;
            }
            if(CheckUtils.isEmpty(arriveType)) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("请选择提现到账类型");
                return resMsg;
            }
            WithdrawStandardReqDTO withdrawStandardReqDTO = assembleWithdrawStandardReqDTO(bindId, arriveType, getCurrentCustomerNumber(), accountType, amount, remark);
            //ip地址获取
            withdrawStandardReqDTO.setClientIp(NetUtils.getRemoteIP(request));
            withdrawStandardReqDTO.setInitiateMode(InitiateModeEnum.MP);
            WithdrawOrderRespDTO withdrawOrderRespDTO = startWithdrawOrderRespDTO(withdrawStandardReqDTO);
            if(CheckUtils.isEmpty(withdrawOrderRespDTO) || !"UA00000".equals(withdrawOrderRespDTO.getReturnCode())) {
                LOGGER.error("提现异常");
                resMsg.setErrCode(withdrawOrderRespDTO.getReturnCode());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg(withdrawOrderRespDTO.getReturnMsg());
            }
            String cacheKey = SmartCacheUtilsHelper.structureKey(SmartCacheUtilsHelper.SmartCacheKeyConstants.MERCHANT_LAST_WITHDRAW_CARD, getCurrentCustomerNumber());
            SmartCacheUtilsHelper.setWithOutException(cacheKey, bindId, 90 * 60 * 24);
        }catch (Throwable e) {
            LOGGER.error("提现异常", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }

    /**
     * 组装调用提现参数
     *
     * @param bindId
     * @param arriveType
     * @param CurrentCustomerNumber
     * @param accountType
     * @param amount
     * @param remark
     * @return
     */
    private WithdrawStandardReqDTO assembleWithdrawStandardReqDTO(String bindId, String arriveType, String CurrentCustomerNumber, String accountType, String amount, String remark) {
        WithdrawStandardReqDTO withdrawStandardReqDTO = new WithdrawStandardReqDTO();
        //请求号
        withdrawStandardReqDTO.setRequestNo(SnowflakeIdFactory.generateId().toString());
        //提现卡标识
        withdrawStandardReqDTO.setBankCardId(bindId);
        //到账类型
        withdrawStandardReqDTO.setArriveType(WithdrawTypeEnum.valueOf(arriveType));
        withdrawStandardReqDTO.setMerchantNo(CurrentCustomerNumber);
        withdrawStandardReqDTO.setAccountType(AccountTypeEnum.valueOf(accountType));
        //提现金额（输入的金额）
        withdrawStandardReqDTO.setWithdrawAmount(amount);
        String markProductCode = businessCheckRemoteService.queryMarketProduct(CurrentCustomerNumber);
        withdrawStandardReqDTO.setSalesProductCode(markProductCode);
        withdrawStandardReqDTO.setRemark(remark);
//        withdrawStandardReqDTO.setInitiateMode(InitiateModeEnum.MP);/*在外面写吧*/
        withdrawStandardReqDTO.setOperator( getCurrentUser().getLoginName());
        return withdrawStandardReqDTO;
    }

    /**
     * 有两种提现发起
     * @param withdrawStandardReqDTO
     * @return
     */
    private WithdrawOrderRespDTO startWithdrawOrderRespDTO( WithdrawStandardReqDTO withdrawStandardReqDTO) {
        String currentCustomerNumber = withdrawStandardReqDTO.getMerchantNo();
        WithdrawOrderRespDTO withdrawOrderRespDTO;
        List<String> frontMerchantNos = ConfigUtils.getFrontMerchantNos();
        if (!CollectionUtils.isEmpty(frontMerchantNos) && frontMerchantNos.contains(currentCustomerNumber)) {
            LOGGER.info("提现发起方商编属于配置走前置层商编, 调用前置层, merchantNo:{}", currentCustomerNumber);
            withdrawOrderRespDTO = accountFrontFacade.withdraw(withdrawStandardReqDTO);
        } else {
            withdrawOrderRespDTO = MGWithdrawFacade.withdraw(withdrawStandardReqDTO);

        }
        LOGGER.info("调用提现接口返回参数{}", JSON.toJSONString(withdrawOrderRespDTO));
        return withdrawOrderRespDTO;
    }

    @RequestMapping(value = "/verify-code-confirm", method = RequestMethod.POST)
    @ApiOperation(value = "APP 确认提现 返回结果为 订单号 ")
    @ResponseBody
    /*这个权限交由APP自己控制了入口实现了*/
//    @RequiresPermissions(Costants.CONFIRM_WITHDRAW_RULE)
    public BaseRespDTO<WithdrawConfirmResp> confirmWithdraw(@RequestBody WithdrawReqDTO reqDTO, HttpServletRequest request) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[确认提现] 商户={} reqDTO={}", currentCustomerNumber, JSONUtils.toJsonString(reqDTO));
        /*1.首先验证短信验证码*/
        try {
            SendSmsUtils.checkVaildFrequencyV2(request, this.getCurrentUser().getUserId(), reqDTO.getVerifyCode(), RemitConstant.APP_WITHDRAW_SMS_CODE_TYPE);
        } catch (Exception e) {
            LOGGER.warn("[确认提现] 商编为=" + currentCustomerNumber + "，验证短信验证码异常 cased by", e);
            return BaseRespDTO.fail(AccountPayException.SMS_VERIFICATION_ERROR.getDefineCode(), e.getMessage());
        }
        /*2.发起提现*/
        WithdrawStandardReqDTO withdrawStandardReqDTO = assembleWithdrawStandardReqDTO(reqDTO, currentCustomerNumber);
        /*无论成功失败都有请求号展示*/
        BaseRespDTO failResult;
        WithdrawConfirmResp confirmRespDTO = new WithdrawConfirmResp();
        confirmRespDTO.setRequestNo(withdrawStandardReqDTO.getRequestNo());
        confirmRespDTO.setCreateTime(LocalDateTimeUtils.getDateString(new Date()));
        try {
            reqDTO.validateParam();
            WithdrawOrderRespDTO withdrawOrderRespDTO = startWithdrawOrderRespDTO(withdrawStandardReqDTO);
            if (CheckUtils.isEmpty(withdrawOrderRespDTO) || !"UA00000".equals(withdrawOrderRespDTO.getReturnCode())) {
                LOGGER.warn("[确认提现] 提现发起异常  商编为={}, code={},message={}", currentCustomerNumber, withdrawOrderRespDTO.getReturnCode(), withdrawOrderRespDTO.getReturnMsg());
                throw new AccountPayException(withdrawOrderRespDTO.getReturnCode(), withdrawOrderRespDTO.getReturnMsg());
            }
            String cacheKey = SmartCacheUtilsHelper.structureKey(SmartCacheUtilsHelper.SmartCacheKeyConstants.MERCHANT_LAST_WITHDRAW_CARD, currentCustomerNumber);
            SmartCacheUtilsHelper.setWithOutException(cacheKey, reqDTO.getBindId(), 90 * 60 * 24);
            confirmRespDTO.setOrderNo(withdrawOrderRespDTO.getOrderNo());
            LOGGER.info("[确认提现] 商编为={}，提现发起成功 orderNo={}", currentCustomerNumber, JSONUtils.toJsonString(confirmRespDTO));
            return BaseRespDTO.success(confirmRespDTO);
        } catch (YeepayBizException e) {
            LOGGER.warn("[确认提现] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            failResult = BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[确认提现] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            failResult =  BaseRespDTO.fail(e.getMessage());
        }
        /*失败也返回请求号*/
        failResult.setData(confirmRespDTO);
        LOGGER.info("[确认提现] 商编为={}，提现发起失败 failResult={}", currentCustomerNumber, JSONUtils.toJsonString(failResult));
        return failResult;
    }

    private WithdrawStandardReqDTO assembleWithdrawStandardReqDTO(WithdrawReqDTO reqDTO, String currentCustomerNumber) {
        WithdrawStandardReqDTO reqDTO1 =  assembleWithdrawStandardReqDTO(reqDTO.getBindId(), reqDTO.getArriveType(), currentCustomerNumber, reqDTO.getAccountType(), reqDTO.getAmount(), reqDTO.getRemark());
        /*app提现如果是实收都走外扣*/
        reqDTO1.setFeeDeductType(FeeChargeTypeEnum.OUTSIDE);
        reqDTO1.setTerminalType(TerminalTypeEnum.PHONE);
        reqDTO1.setInitiateMode(InitiateModeEnum.APP);
        return reqDTO1;
    }

    /**
     * 跳转提现查询页面
     * <AUTHOR>
     * @date 2020-03-16 18:17
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping
    @ResponseBody
    @RequiresPermissions(Costants.WITHDRAW_QUERY_PERMISSION)
    public ModelAndView forwardWithQuery() {
        // 直接跳转vm
        ModelAndView mav = new ModelAndView("middleground/withdrawQuery");
        mav.addObject("statusMap", WITHDRAW_STATUS_MAP);
        mav.addObject("arriveTypeMap", ARRIVE_TYPE_MAP);
        mav.addObject("accountTypeMap", ACCOUNT_TYPE_MAP);


        String customerNumber = super.getCurrentCustomerNumber();
        if(StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            mav.addObject("customerSign", customerSign);
        }

        return mav;
    }

    /**
     *  提现订单查询
     * <AUTHOR>
     * @date 2020-03-11 11:36
     * @param withdrawQueryParam:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/queryOrderList")
    @ResponseBody
    public ResponseMessage queryOrderList( WithdrawQueryParam withdrawQueryParam,
                                           @RequestParam(value = "pageSize", defaultValue = Costants.PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                           @RequestParam(value = "pageNo", defaultValue = Costants.PAGE_NO_DEFAULT_VAL) int pageNo){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("查询提现订单，请求参数{}", JSON.toJSONString(withdrawQueryParam));
        PreCheck.checkArgument(StringUtils.isNotBlank(withdrawQueryParam.getCustomerNumber()),"商户编号不能为空");
        //参数校验
        checkInputParam(withdrawQueryParam);
        withdrawQueryParam.setFirstCodes(StringUtils.join(MG_FIRST_PRODUCT_CODE_SET.toArray(), ","));
        //查询列表
        QueryResult queryResult = this.queryWithOrderList(withdrawQueryParam, pageNo, pageSize);
        if (!CheckUtils.isEmpty(queryResult.getData())) {
            for (Map<String, Object> detail : queryResult.getData()) {
                adaptReturnResult(detail);
            }
        }
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        resMsg.put("dataList", queryResult.getData());
        this.queryWithOrderListSum(withdrawQueryParam, resMsg);
        return resMsg;
    }


    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "app提现订单列表查询")
    @ResponseBody
    public BasePageRespDTO<WithdrawOrderModel> withdrawListQuery(@RequestBody WithdrawListQueryReqDTO reqDTO) {
        String customerNumber = getCurrentCustomerNumber();
        LOGGER.info("[提现订单列表查询] 商户={} reqDTO={}", customerNumber, JSON.toJSONString(reqDTO));
        try {
            reqDTO.validateParam();
            Date startTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getCreateTime(), "yyyy-MM-dd HH:mm:ss");
            Date endTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss");
            Assert.isTrue(LocalDateTimeUtils.getDayDiff(startTime, endTime) <= 92, "查询时间间隔不能超过3个月");
            List<String> statusList = EnumHelper.getWithdrawQueryStatusCode(reqDTO.getOrderStatus());
            String reverseStatus = "REVERSED".equals(reqDTO.getOrderStatus()) ? "REVERSED" : null;
            List<String> firstCodes = Lists.newArrayList(MG_FIRST_PRODUCT_CODE_SET);
            Long total = withdrawOrderService.selectWithdrawOrderListSum(customerNumber, statusList, reverseStatus, startTime, endTime,firstCodes);
            if (total == 0) {
                LOGGER.info("[提现订单列表查询] 商编为={} 总数量为0,", customerNumber);
                return BasePageRespDTO.successPage(Lists.newArrayList(), 0L);
            }
            List<WithdrawOrderBase> resultList = withdrawOrderService.selectWithdrawOrderList(customerNumber, statusList, reverseStatus, startTime, endTime,firstCodes, reqDTO.getPageNo(), reqDTO.getPageSize());
            List<WithdrawOrderModel> list = resultList.stream().map(e -> {
                WithdrawOrderModel orderModel = new WithdrawOrderModel();
                orderModel.setOrderNo(e.getOrderNo());
                orderModel.setCreateTime(LocalDateTimeUtils.getDateString(e.getCreateTime()));
                javafx.util.Pair<String, String> showStatus = EnumHelper.getWithdrawShowStatusCode(e.getStatus(),e.getRemitStatus());
                orderModel.setOrderStatus(showStatus.getKey());
                orderModel.setOrderAmount(e.getOrderAmount().setScale(2).toPlainString());
                return orderModel;
            }).collect(Collectors.toList());
            LOGGER.info("[提现订单列表查询] 商编为={}，成功,", customerNumber);
            return BasePageRespDTO.successPage(list, total);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("[提现订单列表查询] 参数异常，商编=" + customerNumber + ",异常为={}", e.getMessage());
            throw UnionAccountRechargeException.PARAM_REQUIRED_ERROR.newInstance(e.getMessage());
        } catch (YeepayBizException e) {
            LOGGER.warn("[提现订单列表查询] 业务异常，商编=" + customerNumber + ",异常为={}", e);
            return BasePageRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[提现订单列表查询] 异常，商编=" + customerNumber + ",异常为={}", e);
            return BasePageRespDTO.systemError("系统异常");
        }
    }

    /**
     * 跳转提现详情页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView queryOrderDetail(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        String customerNumber = request.getParameter("customerNumber");
        PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber), "商户编号不能为空");
        LOGGER.info("查询提现订单明细，订单号：orderNo={},customerNumber={}", orderNo, customerNumber);
        if (!CheckUtils.isEmpty(orderNo)) {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("customerNumber", customerNumber);
            queryMap.put("orderNo", orderNo);
            //查询组件查询
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
            QueryResult queryResult = queryService.query("queryWithdrawOrderList", queryParam);
            if(!CheckUtils.isEmpty(queryResult.getData())){
                Map<String, Object> map = (Map)((List)queryResult.getData()).get(0);

                String accountType = (String)map.get("account_type");
                map.put("account_type", AccountTypeEnum.valueOf(accountType).getDesc());

                String bank_name = (String)map.get("bank_name");
                String bank_account_no = (String)map.get("bank_account_no");
                bank_name = (bank_name == null ? "" : bank_name);
                bank_account_no = (bank_account_no == null ? "" : "(" + processBankNoRemain4Numbers(bank_account_no) +")");
                map.put("bank_name", bank_name + bank_account_no);

                String status = (String)map.get("status");
                String remitStatus = (String)map.get("remit_status");
                if ("SUCCESS".equals(status) || "REVERSED".equals(remitStatus)) {
                    if("REVERSED".equals(remitStatus)){
                        map.put("status","银行冲退");
                        map.put("finish_time",map.get("debit_back_time"));
                    }else{
                        map.put("status","提现成功");
                    }
                } else if ("REMITING".equals(status) || "DEBIT_EXCEPTION".equals(status) || "REMIT_EXCEPTION".equals(status)
                        ||"REFUND_EXCEPTION".equals(status) ||"REFUND_FAIL".equals(status)) {
                    map.put("status","银行处理中");
                } else if ("REQUEST_RECEIVE".equals(status)) {
                    map.put("status","提现已接收");
                } else if ("REQUEST_ACCEPT".equals(status)) {
                    map.put("status","提现已受理");
                } else if ("FAIL".equals(status)){
                    map.put("status","提现失败");
                }

                if(AccountTypeEnum.FEE_ACCOUNT.equals(accountType)){
                    map.put("payer_fee","无");
                }
                if(map.get("fee_undertaker_merchant_no")!=null && !ObjectUtils.equals(map.get("from_customer_no"),map.get("fee_undertaker_merchant_no"))){
                    map.put("payer_fee","无");
                }

                String arriveType = (String)map.get("arrive_type");
                if ("REAL_TIME".equals(arriveType)){
                    map.put("arrive_type","实时到账");
                } else if("TWO_HOUR".equals(arriveType)){
                    map.put("arrive_type","2小时到账");
                } else {
                    map.put("arrive_type","次日到账");
                }
                Object received_amount = map.get("received_amount");
                if(received_amount!=null){
                    map.put("received_amount",dealAmount(received_amount));
                }
                Object payer_fee = map.get("payer_fee");
                if(payer_fee!=null){
                    map.put("payer_fee", dealAmount(payer_fee));
                }
                Object deduct_amount = map.get("deduct_amount");
                if(deduct_amount!=null){
                    map.put("deduct_amount",dealAmount(deduct_amount));
                }
                Object withdraw_amount = map.get("withdraw_amount");
                if(withdraw_amount!=null){
                    map.put("withdraw_amount",dealAmount(withdraw_amount));
                }

                LOGGER.info("查询订单明细返回，transferResponseParam={}", JSON.toJSONString(map));
                mav.addObject("data", map);
            } else {
                LOGGER.info("通过订单号查询提现记录不存在");
                throw new RuntimeException("通过订单号查询提现记录不存在");
            }
        } else {
            LOGGER.info("订单号为空");
            throw new RuntimeException("订单号不能为空");
        }
        mav.setViewName("middleground/detail");
        return mav;
    }

    private Object dealAmount(Object o){
        if(o==null){
            return null;
        }
        if(o instanceof BigDecimal){
            return ((BigDecimal)o).setScale(2,BigDecimal.ROUND_DOWN);
        }else if(StringUtils.isNumeric(o.toString())){
            return new BigDecimal(o.toString()).setScale(2,BigDecimal.ROUND_DOWN);
        }
        return o;
    }

    /**
     * 提现下载电子回单
     *
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/downElectronicReceipt")
    @ResponseBody
    public void downElectronicReceipt(DownloadElectronicReqDTO param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("提现下载电子回单 请求参数{}",JSONObject.toJSONString(param));
            String orderNo = param.getOrderNo();
            if (StringUtils.isBlank(orderNo)) {
                LOGGER.error("提现下载[电子回单]  orderNo 为空");
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("订单编号为空");
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            long start = System.currentTimeMillis();

            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("customerNumber", param.getCustomerNumber());
            queryMap.put("orderNo", orderNo);
            //查询组件查询
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
            QueryResult queryResult = queryService.query("queryWithdrawOrderList", queryParam);
            if (CheckUtils.isEmpty(queryResult.getData())) {
                LOGGER.error("提现下载[电子回单]  查询结果为空 query={}", JSONObject.toJSONString(queryMap));
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("提现订单详情为空");
            }

            Map<String, Object> map = (Map) ((List) queryResult.getData()).get(0);
            //构建打印pdf
            PdfUtils pdfUtils = new PdfUtils();
            OrderParam orderParam = buildParam(map);
            String data = pdfUtils.generateReceipt(orderParam, getCurrentCustomerNumber());

            BASE64Decoder decoder = new BASE64Decoder();
            //Base64解码
            byte[] len = decoder.decodeBuffer(data);
            for (int i = 0; i < len.length; ++i) {
                //调整异常数据
                if (len[i] < 0) {
                    len[i] += 256;
                }
            }
            /*在开始写入的那一刻再定义这些，不然影响异常的弹窗*/
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + "WITHDRAW" + "_" + System.currentTimeMillis() + ".pdf");
            OutputStream out = response.getOutputStream();
            out.write(len);
            out.flush();
            out.close();
            long end = System.currentTimeMillis();
            LOGGER.info("提现电子回单耗时{}ms", (end - start));
        } catch (Throwable ex) {
            LOGGER.error("提现下载[电子回单] 异常， 来个弹窗 ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }


    /**
     * 处理数据
     * @param map
     * @return
     */
    private OrderParam buildParam(Map<String, Object> map) {
        OrderParam orderParam = new OrderParam();
        Pair<String, String> merchantNo = remoteService.queryFirstAndSecondMerchant(map.get("from_customer_no").toString());

        orderParam.setParentMerchantNo(merchantNo.getRight());
        orderParam.setOrderNo(map.get("order_no").toString());
        orderParam.setInitiateMerchantNo(merchantNo.getLeft());
        orderParam.setTradeType(TradeTypeEnum.WITHDRAW);

        return orderParam;
    }


    /**
     * 提现订单汇总信息
     * <AUTHOR>
     * @date 2020-03-11 11:36
     * @param withdrawQueryParam:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/queryOrderSum")
    @ResponseBody
    public ResponseMessage queryOrderSum(WithdrawQueryParam withdrawQueryParam){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        //查询汇总信息
        withdrawQueryParam.setFirstCodes(StringUtils.join(MG_FIRST_PRODUCT_CODE_SET.toArray(), ","));
        this.queryWithOrderListSum(withdrawQueryParam, resMsg);
        return resMsg;
    }

    /**
     * 管理提现卡页面(返回提现卡列表)
     * <AUTHOR>
     * @date 2020-03-11 11:35
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     */
//    @RequiresPermissions(Costants.WITHDRAW_MANAGE_PERMISSION)
//    @RequestMapping(value = "/manageCardView")
//    @ResponseBody
//    public ModelAndView manageCardView(HttpServletRequest request){
//        ModelAndView mav = new ModelAndView();
//        mav.setViewName("/middleground/manageWithdraw");
//        buildBindCardListView(mav, null);
//        return mav;
//    }

    /**
     * 添加提现卡页面(返回开户名称，账户类型，所有总行)
     * <AUTHOR>
     * @date 2020-03-11 11:35
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequiresPermissions(Costants.WITHDRAW_MANAGE_PERMISSION)
    @RequestMapping(value = "/manageCardView", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView addCardView(HttpServletRequest request){
        ModelAndView mav = new ModelAndView();
        try {
            mav.setViewName("/middleground/manageWithdraw");
            mav = buildModelView(mav, request);
        }catch (Throwable e) {
            LOGGER.error("提现卡页面错误", e);
            mav.addObject("exception",e.getMessage());
            mav.addObject("path","/withdraw/manageCardView");
            mav.setViewName("/common/error");
            return mav;
        }
        buildBindCardListView(mav,null);
        return mav;
    }

    private ModelAndView buildModelView(ModelAndView mav,HttpServletRequest request) throws Exception {
        String productType = "";
        String merchantName = "";
        MerchantinfoRespDTO merchantinfoRespDTO = businessCheckRemoteService.queryMerchantInfo(getCurrentCustomerNumber());
        if(!CheckUtils.isEmpty(merchantinfoRespDTO) && "0000".equals(merchantinfoRespDTO.getRetCode())) {
            LOGGER.info("调用客户中心查询客户信息，返回的签约类型为{}", merchantinfoRespDTO.getSignType());
            merchantName = merchantinfoRespDTO.getSignedName();
            Pair<String, String> info = businessCheckRemoteService.convertWithdrawCheckInfo(merchantinfoRespDTO);
            if (StringUtils.isNotEmpty(info.getLeft())) {
                productType = info.getLeft();
            }
            if (StringUtils.isNotEmpty(info.getRight())) {
                mav.addObject("corporationName", info.getRight());
            }
        } else{
            LOGGER.error("调用客户中心查询客户信息，异常，返回参数：{}", JSONUtils.toJsonString(merchantinfoRespDTO));
        }
        mav.addObject("bankMap", ProvinceCityService.queryBankMap());
        mav.addObject("provinceList", ProvinceCityService.getAreasV2(null));
        mav.addObject("productType", productType);
        mav.addObject("merchantName", merchantName);
        mav.addObject("notSupportBankMap", JSON.toJSONString(UniformConfigUtils.getNotSupportBankMap()));
        String redirectUrl = request.getParameter("redirectUrl");

        if(!CheckUtils.isEmpty(redirectUrl)) {
            if(allowRedirectUrls.contains(redirectUrl)){
                mav.addObject("redirectUrl", redirectUrl);
            }else{
                throw new RuntimeException("不允许跳转的地址,redirectUrl="+redirectUrl);
            }
        }
        return mav;
    }


    /**
     * 页面异步查询省对应的区
     * <AUTHOR>
     * @date 2020-03-11 11:36
     * @param request:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/queryAreas")
    @ResponseBody
    public ResponseMessage queryAreas(HttpServletRequest request,
                                      @RequestParam("parentCode") String parentCode) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            List<DistrictInfoDTO> areaBaseDTOList = ProvinceCityService.getAreasV2(parentCode);
            if (!CheckUtils.isEmpty(areaBaseDTOList)) {
                resMsg.put("subAreaList", areaBaseDTOList);
            }
        }catch (Throwable e) {
            LOGGER.error("查询省对应区错误", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询省对应区错误，请稍后重试");
        }
        return resMsg;
    }

    /**
     * 页面异步查询支行列表
     * <AUTHOR>
     * @date 2020-03-13 13:14
     * @param request:
     * @param headBankCode:总行编码
     * @param bankProvinceCode:省份编码
     * @param bankCityCode:城市编码
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/queryBranchBank")
    @ResponseBody
    public ResponseMessage queryBranchBank(HttpServletRequest request,
                                           @RequestParam("headBankCode") String headBankCode,
                                           @RequestParam("bankProvinceCode") String bankProvinceCode,
                                           @RequestParam("bankCityCode") String bankCityCode) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        //返回支行信息
        try {

            Map<String, String> branchBankMap = ProvinceCityService.getBranchBankMap(headBankCode, bankProvinceCode, bankCityCode);
            if(!CheckUtils.isEmpty(branchBankMap)) {
                resMsg.put("branchBankMap",  branchBankMap);
            }
        }catch (Throwable e) {
            LOGGER.error("查询支行列表错误", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询支行列表错误，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 确认添加提现卡
     * <AUTHOR>
     * @date 2020-03-11 11:36
     * @param settleInput:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/firmAddCard")
    @ResponseBody
    public ResponseMessage confirmAddCard(SettleInput settleInput){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String cardType = settleInput.getProductType();
            BindCardReqDTO bindCardReqDTO = new BindCardReqDTO();
            String returnCardType = "";
            if("person".equals(cardType) || "individual".equals(cardType)) {
                bindCardReqDTO.setBankCardType(BankCardTypeEnum.DEBIT_CARD);
                returnCardType = "借记卡";
            }else if("personBusiness".equals(cardType) || "business".equals(cardType)) {
                bindCardReqDTO.setBankCardType(BankCardTypeEnum.ENTERPRISE_ACCOUNT);
                //开户支行编码
                returnCardType = "对公卡";
            }else if("settle_account".equals(cardType)) {
                returnCardType = "单位结算卡";
                bindCardReqDTO.setBankCardType(BankCardTypeEnum.UNIT_SETTLEMENT_CARD);
                //开户支行编码
            }
            bindCardReqDTO.setInitiateMerchantNo(getCurrentCustomerNumber());
            bindCardReqDTO.setMerchantNo(getCurrentCustomerNumber());
            //银行账号/卡号
            bindCardReqDTO.setAccountNo(settleInput.getCardNo());
            //开户总行编码
            bindCardReqDTO.setBankCode(settleInput.getHeadBankCode());
            bindCardReqDTO.setBranchCode(settleInput.getBankCode());
            bindCardReqDTO.setBindSource(BindSourceEnum.MP);
            bindCardReqDTO.setBasicProductCode(Costants.WITHDRAW_BASICSPRODUCTFIRST);
            BindCardRespDTO bindCardRespDTO = bindCardFacade.bindCard(bindCardReqDTO);
            LOGGER.info("添加提现卡返回结果{}", JSON.toJSONString(bindCardRespDTO));
            if("UA00000".equals(bindCardRespDTO.getReturnCode())) {
                return resMsg;
            }else {
                if("UA30001".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("系统异常，请稍后再试");
                }else if("UA40008".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("商户信息有误，请核对后再试");
                }else if("UA40013".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("银行账户信息有误，请核对");
                }else if("UA40011".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("卡片无法识别，请检查后再试");
                }else if("UA40015".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("开户银行有误，请核对");
                }else if("UA40012".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("请添加" + returnCardType);
                }else if("UA40014".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("省市关系对应有误，请核对");
                }else if("UA40006".equals(bindCardRespDTO.getReturnCode())){
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("该银行账户已存在，请勿重复添加");
                }else {
                    resMsg.setErrCode(bindCardRespDTO.getReturnCode());
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg(null == bindCardRespDTO.getReturnMsg() ? "添加提现卡失败" : bindCardRespDTO.getReturnMsg());
                }
            }
        }catch (Throwable e) {
            LOGGER.error("添加提现卡失败", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("添加提现卡失败");
        }

        LOGGER.error("添加提现卡信息返回,resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }



    /**
     * 展示自动提现规则
     * <AUTHOR>
     * @date 2020-07-28 14:37
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(value = "/showAutoWithdrawRule")
    @ResponseBody
    @RequiresPermissions(Costants.AUTO_WITHDRAW_RULE)
    public ModelAndView showAutoWithdrawRule(HttpServletRequest request){
        ModelAndView mav = new ModelAndView();

        boolean hasAutoWithdrawRule = false;
        boolean hasEffectiveAutoWithdrawRule = false;
        AutoWithdrawRuleQueryReqDTO autoWithdrawRuleQueryReqDTO = new AutoWithdrawRuleQueryReqDTO();
        autoWithdrawRuleQueryReqDTO.setMerchantNo(getCurrentCustomerNumber());
        try{
            AutoWithdrawRuleQueryRespDTO autoWithdrawRuleQueryRespDTO = MGWithdrawFacade.queryAutoWithdrawRule(autoWithdrawRuleQueryReqDTO);
            if (!CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO)&&!CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList())) {
                hasAutoWithdrawRule = true;
                for (AutoWithdrawRule rule : autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList()) {
                    if(RuleStatusEnum.EFFECTIVE.equals(rule.getStatus())){
                        hasEffectiveAutoWithdrawRule = true;
                    }
                }
            }
        } catch (Throwable e){
            LOGGER.error("查询自动提现规则失败， ",e);
            mav.addObject("hasAutoWithdrawRule",hasAutoWithdrawRule);
            mav.addObject("hasEffectiveAutoWithdrawRule",hasEffectiveAutoWithdrawRule);
        }
        mav = buildWithdrawProductView(mav);
        if(hasAutoWithdrawRule){
            mav.addObject("hasAutoWithdrawRule",true);
            mav.addObject("hasEffectiveAutoWithdrawRule",hasEffectiveAutoWithdrawRule);
            mav.setViewName("/middleground/autoWithdrawRuleView");
        } else {
            mav = buildBindCardListView(mav, REMAIN_4);
            mav.setViewName("/middleground/showAutoWithdrawRuleSet");
            try {
                mav = buildModelView(mav,request);
            } catch (Exception e) {
                LOGGER.error("查询自动提现规则失败， ",e);
            }
        }

        return mav;
    }

    /**
     * 查询自动提现规则
     * @return
     */
    @RequestMapping(value = "/queryAutoWithdrawRules")
    @ResponseBody
    public ResponseMessage queryAutoWithdrawRules(){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        AutoWithdrawRuleQueryReqDTO autoWithdrawRuleQueryReqDTO = new AutoWithdrawRuleQueryReqDTO();
        autoWithdrawRuleQueryReqDTO.setMerchantNo(getCurrentCustomerNumber());
        List<Map<String,Object>> dataList = new ArrayList<Map<String, Object>>();
        try{
            AutoWithdrawRuleQueryRespDTO autoWithdrawRuleQueryRespDTO = MGWithdrawFacade.queryAutoWithdrawRule(autoWithdrawRuleQueryReqDTO);
            if (!CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO)&&!CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList())) {
                for (AutoWithdrawRule rule : autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList()) {
                    Map<String,Object> data = new HashMap<String, Object>();
                    data.put("createTime",processTime(rule.getCreateTime()));
                    data.put("triggerTime",rule.getTriggerTime());
                    if(null!=rule.getRemainAmount()){
                        data.put("remainAmount",rule.getRemainAmount());
                    }
                    data.put("bankAccountNo",HiddenCodeUtils.hiddenBankCardNo4End(rule.getBankAccountNo()));
                    data.put("bankName",rule.getBankName());
                    data.put("operator",rule.getOperator());
                    data.put("status",rule.getStatus().name());
                    data.put("receiveType",rule.getReceiveType().getMqDesc());
                    data.put("remark",rule.getRemark());
                    dataList.add(data);
                }
            }
            resMsg.put("autoWithdrawRules",dataList);
        } catch (Throwable e){
            LOGGER.error("查询自动提现规则失败， ",e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常，请稍后重试");
        }
        return resMsg;
    }


    @RequestMapping(value = "/showAutoWithdrawRuleSet")
    @ResponseBody
    @RequiresPermissions(Costants.AUTO_WITHDRAW_RULE)
    public ModelAndView showAutoWithdrawRuleSet(HttpServletRequest request){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("/middleground/showAutoWithdrawRuleSet");
        mav = buildBindCardListView(mav,REMAIN_4);
        mav = buildWithdrawProductView(mav);
        try {
            mav = buildModelView(mav,request);
        } catch (Exception e) {
            LOGGER.error("查询自动提现规则失败， ",e);
        }
        return mav;
    }

    /**
     * 作废自动提现规则
     * <AUTHOR>
     * @date 2020-07-27 18:22
     * @param request:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequestMapping(value = "/cancelAutoWithdrawRule")
    @ResponseBody
    public ResponseMessage cancelWithdrawRule(HttpServletRequest request){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            AutoWithdrawRuleCancelReqDTO autoWithdrawRuleCancelReqDTO = new AutoWithdrawRuleCancelReqDTO();
            autoWithdrawRuleCancelReqDTO.setMerchantNo(getCurrentCustomerNumber());
            AutoWithdrawRuleCancelRespDTO autoWithdrawRuleCancelRespDTO = MGWithdrawFacade.cancelAutoWithdrawRule(autoWithdrawRuleCancelReqDTO);
            LOGGER.info("调用作废自动提现接口返回参数{}", JSON.toJSONString(autoWithdrawRuleCancelRespDTO));
            if(CheckUtils.isEmpty(autoWithdrawRuleCancelRespDTO) || !"UA00000".equals(autoWithdrawRuleCancelRespDTO.getReturnCode())) {
                LOGGER.error("作废自动提现规则异常");
                resMsg.setErrCode(autoWithdrawRuleCancelRespDTO.getReturnCode());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg(autoWithdrawRuleCancelRespDTO.getReturnMsg());
            }
        }catch (Throwable e) {
            LOGGER.error("作废自动提现规则异常", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 设置提现规则
    * @param request
    * @param accountType
    * @param remark
    * @return com.yeepay.g3.app.newframe.response.ResponseMessage
    * <AUTHOR>
    * @Date 2020-07-27 18:48
    */
    @RequestMapping(value = "/setAutoWithdrawRule")
    @ResponseBody
    public ResponseMessage setAutoWithdrawRule(HttpServletRequest request){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String remainAmount = request.getParameter("remainAmount");
            String bindId = request.getParameter("bindId");
            String receiveType = request.getParameter("receiveType");
            String remark = request.getParameter("remark");
            String triggerTime = request.getParameter("triggerTime");
            String bankName = request.getParameter("bankName");

            if(CheckUtils.isEmpty(bindId)) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("请选择或添加提现卡");
                return resMsg;
            }
            if(CheckUtils.isEmpty(receiveType)) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("请选择提现到账类型");
                return resMsg;
            }
            AutoWithdrawRuleSetReqDTO autoWithdrawRuleSetReqDTO = new AutoWithdrawRuleSetReqDTO();
            autoWithdrawRuleSetReqDTO.setMerchantNo(getCurrentCustomerNumber());
            autoWithdrawRuleSetReqDTO.setBindId(bindId);
            autoWithdrawRuleSetReqDTO.setRemark(getRemarkAutoWithdraw(remark));
            autoWithdrawRuleSetReqDTO.setTriggerTime(triggerTime);
            autoWithdrawRuleSetReqDTO.setOperator(getCurrentUser().getLoginName());
            autoWithdrawRuleSetReqDTO.setBankName(bankName);
            BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
            String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
            if(CheckUtils.isEmpty(markProductCode)){
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrCode("9999");
                resMsg.setErrMsg("系统繁忙，请稍后再试");
                return resMsg;
            }
            autoWithdrawRuleSetReqDTO.setMarketProductCode(markProductCode);
            //迁移提现卡后方法
            BindCardQueryRespDTO bindCardQueryRespDTO = this.queryBindCardV2();
            if(!CheckUtils.isEmpty(bindCardQueryRespDTO.getBankCardAccountList())) {
                LOGGER.info("商户{}进入管理提现卡查询绑定银行卡成功，卡数量为{}", bindCardQueryRespDTO.getMerchantNo(), bindCardQueryRespDTO.getBankCardAccountList().size());
                for(BankCardAccount bankCardAccount : bindCardQueryRespDTO.getBankCardAccountList()) {
                    if(bankCardAccount.getBindCardId().equals(bindId)){
                        autoWithdrawRuleSetReqDTO.setBankAccountNo(bankCardAccount.getAccountNo());
                        break;
                    }
                }
            }
            if(!CheckUtils.isEmpty(remainAmount)){
                autoWithdrawRuleSetReqDTO.setRemainAmount(new BigDecimal(remainAmount));
            }
            //到账类型
            if(!CheckUtils.isEmpty(receiveType)) {
                if("REALTIME".equals(receiveType)) {
                    autoWithdrawRuleSetReqDTO.setReceiveType(WithdrawTypeEnum.REAL_TIME);
                }else if("TWOHOURS".equals(receiveType)){
                    autoWithdrawRuleSetReqDTO.setReceiveType(WithdrawTypeEnum.TWO_HOUR);
                }else if("TOMORROW".equals(receiveType)){
                    autoWithdrawRuleSetReqDTO.setReceiveType(WithdrawTypeEnum.NEXT_DAY);
                }
            }
            AutoWithdrawRuleSetRespDTO autoWithdrawRuleSetRespDTO = MGWithdrawFacade.setAutoWithdrawRule(autoWithdrawRuleSetReqDTO);
            LOGGER.info("调用设置自动提现规则接口返回参数{}", JSON.toJSONString(autoWithdrawRuleSetRespDTO));
            if(CheckUtils.isEmpty(autoWithdrawRuleSetRespDTO) || !"UA00000".equals(autoWithdrawRuleSetRespDTO.getReturnCode())) {
                LOGGER.warn("调用设置自动提现规则接口, 调用不成功 resp = {} ,autoWithdrawRuleSetReqDTO={}",JSON.toJSONString(autoWithdrawRuleSetRespDTO),JSON.toJSONString(autoWithdrawRuleSetReqDTO));
                resMsg.setErrCode(autoWithdrawRuleSetRespDTO.getReturnCode());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg(autoWithdrawRuleSetRespDTO.getReturnMsg());
            }
        }catch (Throwable e) {
            LOGGER.error("设置自动提现规则异常", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 自动提现规则转换
     * @param remark
     * @return
     */
    public String getRemarkAutoWithdraw(String remark) {
        if(remark==null){
            return null;
        }
        try {
            StringBuffer remarkResult=new StringBuffer();
            if(StringUtils.isNotBlank(remark)&&remark.contains("YYYYMMDD")){
                if(remark.indexOf("YYYYMMDD")>0 && StringUtils.isNotBlank(remark.substring(0, remark.indexOf("YYYYMMDD")))) {
                    remarkResult.append("'").append(remark.substring(0, remark.indexOf("YYYYMMDD"))).append("'+");
                }
                remarkResult.append("#today_yyyyMMdd");
                Integer afterLength=remark.length()-remark.indexOf("YYYYMMDD");
                if(afterLength>8 && StringUtils.isNotBlank(remark.substring(remark.indexOf("YYYYMMDD")+8))) {
                    remarkResult.append("+'").append(remark.substring(remark.indexOf("YYYYMMDD")+8)).append("'");
                }
            }else {
                remarkResult.append(remark);
            }
            return remarkResult.toString();
        }catch (Exception e){
            LOGGER.error("设置自动提现规则时转换备注异常", e);
        }
        return remark;
    }


    /**
     * 提现卡查询
     * @return
     */
    @RequestMapping(value = "/withdrawCardDetail",method = RequestMethod.GET)
    @ApiOperation(value = "提现卡查询")
    @ResponseBody
    public ResponseMessage queryWithdrawCardDetail(HttpServletRequest request){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String bindId = request.getParameter("bindId");
        if(CheckUtils.isEmpty(bindId)){
            LOGGER.info("绑卡id为空");
            throw new RuntimeException("绑卡id不能为空");
        }

        SingleBindCardQueryReqDTO dto = new SingleBindCardQueryReqDTO();
        String currentCustomerNumber = getCurrentCustomerNumber();
        dto.setInitiateMerchantNo(currentCustomerNumber);
        dto.setMerchantNo(currentCustomerNumber);
        dto.setBindId(Long.valueOf(bindId));
        BindCardParam bindCardParam = new BindCardParam();
        try{
            SingleBindCardQueryRespDTO bindCardQueryRespDto = bindCardFacade.querySingleBankCard(dto);
            if(ErrorCode.SUCCESS.equals(bindCardQueryRespDto.getReturnCode())){
                BankCardAccount bankCardAccount = bindCardQueryRespDto.getBankCardAccount();
                if(bankCardAccount != null){
                    String branchBankCode = bankCardAccount.getBranchBankCode();
                    String bankName = bankCardAccount.getBankName();
                    bindCardParam.setBankName(bankName);
                    bindCardParam.setBankCardType(bankCardAccount.getBankCardType().name());
                    bindCardParam.setBankCode(bankCardAccount.getBankCode());
                    bindCardParam.setAccountName(bankCardAccount.getAccountName());
                    bindCardParam.setCardNo(HiddenCodeUtils.hiddenBankCardNo(bankCardAccount.getAccountNo()));
                    if(StringUtils.isNotBlank(branchBankCode)){
                        BranchBankDTO branchBankDTO = searchBankInfoFacade.searchBranchBankInfoByCnapsCode(branchBankCode);
                        if(branchBankDTO != null){
                            String branchBankName = branchBankDTO.getBranchBankName();
                            bindCardParam.setBranchCode(branchBankCode);
                            bindCardParam.setBranchCodeName(branchBankName);
                            Map<String, DistrictNameAndCode> districtNameAndCodeMap = branchBankDTO.getDistrictNameAndCodeMap();
                            if(districtNameAndCodeMap != null){
                                DistrictNameAndCode province = districtNameAndCodeMap.get("province");
                                if(province != null){
                                    String districtCode = province.getDistrictCode();
                                    String districtName = province.getDistrictName();
                                    bindCardParam.setBankProvinceCode(districtCode);
                                    bindCardParam.setBankProvinceName(districtName);
                                }else{
                                    bindCardParam.setBankProvinceCode(null);
                                    bindCardParam.setBankProvinceName(null);
                                }
                                DistrictNameAndCode city = districtNameAndCodeMap.get("city");
                                if(city != null){
                                    String districtCode = city.getDistrictCode();
                                    String districtName = city.getDistrictName();
                                    bindCardParam.setBankCityCode(districtCode);
                                    bindCardParam.setBankCityName(districtName);
                                }else{
                                    bindCardParam.setBankCityCode(null);
                                    bindCardParam.setBankCityName(null);
                                }
                            }else{
                                bindCardParam = buildDefaultValue(bindCardParam);
                            }

                        }
                    }else{
                        bindCardParam = buildDefaultValue(bindCardParam);
                    }
                }
            }else{
                throw new RuntimeException("查询提现卡异常");
            }

            LOGGER.error("提现卡信息返回,bindCardParam={}",JSON.toJSONString(bindCardParam));
            resMsg.put("bindCardParam",bindCardParam);
        } catch (Throwable e){
            LOGGER.error("查询提现卡异常， ",e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常，请稍后重试");
        }
        return resMsg;
    }

    /**
     * 更新提现卡
     * @param bindCardParam
     * @return
     */
    @RequestMapping(value = "/updateOrCancelCard")
    @ResponseBody
    public ResponseMessage updateOrCancelCard(BindCardParam bindCardParam){
        ResponseMessage resMsg  = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        if (StringUtils.isNotBlank(bindCardParam.getBindIdCharacter())) {
            bindCardParam.setBindId(Long.valueOf(bindCardParam.getBindIdCharacter()));
        }
        String bankCardOperateType = bindCardParam.getBankCardOperateType();
        ModifyBindCardReqDTO modifyBindCardReqDTO = new ModifyBindCardReqDTO();
        String currentCustomerNumber = getCurrentCustomerNumber();
        modifyBindCardReqDTO.setMerchantNo(currentCustomerNumber);
        modifyBindCardReqDTO.setInitiateMerchantNo(currentCustomerNumber);
        modifyBindCardReqDTO.setBindId(bindCardParam.getBindId());
        if(bankCardOperateType.equals(BankCardOperateTypeEnum.MODIFY.name())){
            modifyBindCardReqDTO.setBankCode(bindCardParam.getBankCode());
            modifyBindCardReqDTO.setBranchCode(bindCardParam.getBranchCode());
            modifyBindCardReqDTO.setBankCardOperateType(BankCardOperateTypeEnum.MODIFY);
        }else if(bankCardOperateType.equals(BankCardOperateTypeEnum.CANCELLED.name())){
            modifyBindCardReqDTO.setBankCardOperateType(BankCardOperateTypeEnum.CANCELLED);
        }else{
            throw new RuntimeException("操作类型有误");
        }

        try {
            ModifyBindCardRespDTO respDTO = bindCardFacade.modifyBindCard(modifyBindCardReqDTO);
        }catch (Throwable e) {
            LOGGER.error("更新提现卡异常", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("9999");
            resMsg.setErrMsg("系统调用异常，请稍后重试");
        }
        return resMsg;
    }


    /**
     * 查询提现订单
     * <AUTHOR>
     * @date 2020-03-13 13:58
     * @param param:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     */
    private QueryResult queryWithOrderList(WithdrawQueryParam param, int pageNo, int pageSize) {
        if("REVERSED".equals(param.getStatus())){
            param.setStatus(null);
            param.setRemitStatus("REVERSED");
        }
        //构造查询参数
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getCustomerNumber());
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        //查询组件查询
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
        return queryService.query("queryWithdrawOrderList", queryParam);

    }

    /**
     * 查询提现汇总
     * <AUTHOR>
     * @date 2020-03-13 14:05
     * @param param:
     * @param resMsg:
     * @return void
     */
    private void queryWithOrderListSum(WithdrawQueryParam param, ResponseMessage resMsg) {
        if("REVERSED".equals(param.getStatus())){
            param.setStatus(null);
            param.setRemitStatus("REVERSED");
        }
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getCustomerNumber());

        List<Map<String, Object>> withOrderListSum = QueryServiceUtil.query("accountTradeService", "queryWithdrawOrderListSum", queryMap);
        // 如果查询结果不为空的话
        if (!CheckUtils.isEmpty(withOrderListSum)) {
            Map<String, Object> sumResult = withOrderListSum.get(0);
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumFractionDigits(2);
            nf.setMaximumFractionDigits(2);
            resMsg.getData().put("sum_count", sumResult.get("sum_count").toString());// 总笔数
            resMsg.getData().put("sum_amount", CheckUtils.isEmpty(sumResult.get("sum_amount"))? "0.00" : nf.format(new BigDecimal(sumResult.get("sum_amount").toString())));// 总金额
            resMsg.getData().put("sum_fee", CheckUtils.isEmpty(sumResult.get("sum_fee"))? "0.00" : nf.format(new BigDecimal(sumResult.get("sum_fee").toString())));// 总手续费
            resMsg.getData().put("totalCount", sumResult.get("sum_count").toString());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", 0.00);// 总金额
            resMsg.getData().put("sum_fee", 0.00);// 总手续费
        }
    }

    /**
     * 提现订单下载
     * <AUTHOR>
     * @date 2020-03-16 17:45
     * @param param:
     * @param request:
     * @param response:
     * @return void
     */
    @RequestMapping(value = "/download")
    public void downloadRecord(WithdrawQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception{
        try{
            this.checkInputParam(param);
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant()) {
                LOGGER.info("[红版商户后台]下载下级提现订单 the customerNumber=[{}]", param.getCustomerNumber());
                param.setPlatformType(PlatformTypeEnum.RED_PLATFORM_MERCHANT.name());
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()),"商户编号不能为空");
            if("REVERSED".equals(param.getStatus())){
                param.setStatus(null);
                param.setRemitStatus("REVERSED");
            }
            CheckUtils.notEmpty(param.getFileType(),"fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            param.setFirstCodes(StringUtils.join(MG_FIRST_PRODUCT_CODE_SET.toArray(), ","));
            StringBuilder desc = new StringBuilder();
            desc.append("提现订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            if ("appointSync".equals(param.getSyncType())) {
                new WithdrawOrderDownloadService(getCurrentUser(), param, desc.toString(), "提现订单查询-").syncDownload(request, response);
            } else {
                new WithdrawOrderDownloadService(getCurrentUser(), param, desc.toString(), "提现订单查询-").download(request, response);
            }
        }catch (Throwable ex){
            LOGGER.error("downloadRecord-error",ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('"+ex.getMessage()+"')</script>");
        }
    }

    /**
     * 适配返回结果
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }
        Map<String, AccountBasicParam> config = WithdrawProductBasicConfigUtils.getAccountBasicConfig();
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);
        try {
            if (null != detail.get("received_amount")) {
                detail.put("received_amount", nf.format(new BigDecimal(detail.get("received_amount").toString())));
            }
            if (null != detail.get("withdraw_amount") && StringUtils.isNotBlank(detail.get("withdraw_amount").toString())) {
                detail.put("withdraw_amount", nf.format(new BigDecimal(detail.get("withdraw_amount").toString())));
            }
            if (null != detail.get("payer_fee")) {
                detail.put("payer_fee", nf.format(new BigDecimal(detail.get("payer_fee").toString())));
            }
            if (null != detail.get("account_type")) {
                detail.put("service_type", config.get(detail.get("account_type").toString()).getServiceType());
                detail.put("account_type", AccountTypeEnum.valueOf(detail.get("account_type").toString()).getDesc());
            }

            //下单时间
            Object obj = detail.get("create_time");
            if (null != obj) {
                detail.put("create_time", processTime(obj));
            }

            //处理卡号后四位
            Object banCard = detail.get("bank_account_no");
            if (null != banCard) {
                detail.put("bank_account_no",processBankNoRemain4Numbers(banCard));
            }

        } catch (Throwable e) {
            LOGGER.error("这都能错..擦....", e);
        }
        return detail;
    }

    private String processTime(Object time){
        try{
            if (time instanceof String) {
                String str = String.valueOf(time);
                if (StringUtils.isNotBlank(str)) {
                    SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    if (str.length() == 10) {
                        return smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY));
                    } else {
                        return smf.format(smf.parse(str));
                    }
                }
            } else if (time instanceof Timestamp) {
                return  DateUtils.toSqlTimestampString((Timestamp) time, DateUtils.DATE_FORMAT_DATETIME);
            }else if(time instanceof Date) {
                return DateUtil.date2String((Date) time, PATTERN_STANDARD19H);
            }
        } catch (Throwable e){
            LOGGER.error("处理时间格式错误", e);
        }
        return null;
    }

    private String processBankNoRemain4Numbers(Object bankNo){
        if (bankNo instanceof String) {
            String str = String.valueOf(bankNo);
            if (StringUtils.isNotBlank(str)) {
                return HiddenCodeUtils.hiddenBankCardNo4End(AESUtils.decryptWithBase64(str));

            }
        }
        return null;
    }

    /**
     * 输入参数校验
     * @param param
     */
    private void checkInputParam(WithdrawQueryParam param) {

        if (param == null || param.isEmptyCheck()) {
            LOGGER.error("入参为空!无法进行数据查询!");
            throw new RuntimeException("入参为空!无法进行数据查询!");
        }

        // 请求开始时间不能查过结束时间
        if (StringUtils.isNotEmpty(param.getCreateStartDate())
                && StringUtils.isNotEmpty(param.getCreateEndDate())) {
            try {
                Date startDate = DateUtils.parseDate(
                        param.getCreateStartDate(),
                        DateUtils.DATE_FORMAT_DATEONLY);
                Date endDate = DateUtils.parseDate(param.getCreateEndDate(),
                        DateUtils.DATE_FORMAT_DATEONLY);
                if (DateUtils.compareDate(startDate, endDate, Calendar.DATE) > 0) {
                    LOGGER.error(
                            "请求开始时间大于请求结束时间!入参格式有误! createStartDate={},createEndDate={}",
                            param.getCreateStartDate(),
                            param.getCreateEndDate());
                    throw new RuntimeException("请求开始时间大于请求结束时间!");
                }
                // 请求开始时间和请求结束时间不能超过100天
                if (CheckParamUtils.isOver100DaysInterval(startDate, endDate)) {
                    LOGGER.error(
                            "请求开始时间和请求结束时间间隔超过100天!入参格式有误! createStartDate={},createEndDate={}",
                            param.getCreateStartDate(),
                            param.getCreateEndDate());
                    throw new RuntimeException("请求开始时间和请求结束时间间隔超过100天!");
                }
            } catch (ParseException e) {
                LOGGER.error("这不可能报错,别闹了...");
            }
        }
    }

    /**
     * @Description: 查询绑卡
     * <AUTHOR>
     * @date 2020-03-17 14:19
     * @return com.yeepay.g3.facade.merchant_platform.dto.customermanagement.QueryBankAccountRespDTO
     */
    private QueryBankAccountRespDTO queryBindCard() {
        QueryBankAccountReqDTO queryBankAccountReqDTO = new QueryBankAccountReqDTO();
        queryBankAccountReqDTO.setUid(UniformConfigUtils.getSystemUidForReqCusCenter());
        queryBankAccountReqDTO.setSystem(Costants.SYSTEM_CODE);
        queryBankAccountReqDTO.setCharSet("UTF-8");
        queryBankAccountReqDTO.setReqTime(new Date());
        queryBankAccountReqDTO.setMerchantNo(getCurrentCustomerNumber());
        //查询提现卡
        queryBankAccountReqDTO.setBankCardTag(BankCardTag.WITHDRAW_DEPOSIT);
        QueryBankAccountRespDTO queryBankAccountRespDTO;
        try {
            queryBankAccountRespDTO = cusBankCardFacade.queryBankAccount(queryBankAccountReqDTO);
        }catch (Throwable e) {
            LOGGER.error("调用客户中心查询提现卡列表失败", e);
            throw new RuntimeException("查询提现卡列表失败");
        }
        return queryBankAccountRespDTO;
    }

    private BindCardParam buildDefaultValue(BindCardParam bindCardParam){
        bindCardParam.setBankCityCode(null);
        bindCardParam.setBankCityName(null);
        bindCardParam.setBankProvinceCode(null);
        bindCardParam.setBankProvinceName(null);
        return bindCardParam;
    }

    /**
     * @Description: 查询提现卡--迁移版本
     * <AUTHOR>
     * @date 2020-12-03 17:40
     * @return com.yeepay.g3.facade.unionaccount.manage.dto.response.BindCardQueryRespDTO
     */
    public BindCardQueryRespDTO queryBindCardV2() {
        String merchantNo = getCurrentCustomerNumber();
        BindCardQueryReqDTO bindCardQueryReqDTO = new BindCardQueryReqDTO();
        bindCardQueryReqDTO.setInitiateMerchantNo(merchantNo);
        bindCardQueryReqDTO.setMerchantNo(merchantNo);
        //根据产品码 只查出提现卡
        bindCardQueryReqDTO.setBasicProductCode(Costants.WITHDRAW_BASICSPRODUCTFIRST);
        BindCardQueryRespDTO bindCardQueryRespDTO;
        try{
            bindCardQueryRespDTO = bindCardFacade.queryBankCards(bindCardQueryReqDTO);
            if("UA00000".equals(bindCardQueryRespDTO.getReturnCode())) {
                return bindCardQueryRespDTO;
            }else {
                throw new RuntimeException("查询提现卡列表失败");
            }
        }catch (Exception e) {
            LOGGER.warn("查询中台管理系统提现卡异常" + e);
            throw new RuntimeException("查询提现卡列表失败");
        }
    }

    /**
     * 获取省市地区信息
     * @param parentCode
     * @return
     */
    private List<AreaBaseDTO> getAreas(String parentCode) {
        AreaCodeReqDTO reqDTO = new AreaCodeReqDTO();
        reqDTO.setSystem(Costants.SYSTEM_CODE);
        reqDTO.setCharSet("UTF-8");
        reqDTO.setUid(UniformConfigUtils.getSystemUidForReqCusCenter());
        reqDTO.setReqTime(new Date());
        reqDTO.setCode(parentCode);
        AreaRespDTO dto = merchantFacade.getAllArea(reqDTO);
        if (dto != null && "0000".equals(dto.getRetCode())) {
            return dto.getAreaList();
        } else {
            return null;
        }
    }


    private ModelAndView buildWithdrawProductView(ModelAndView modelAndView){
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        boolean hasWithdrawProduct = false;
        List<DicCodeAutoWithdrawDTO> secondProductList = new ArrayList<>();

        String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
        MerchantProductQueryRespDTO merchantProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(getCurrentCustomerNumber(), ProductTypeEnum.ACCOUNT.toString(), markProductCode, Costants.WITHDRAW_BASICSPRODUCTFIRST, null);
        if("0000".equals(merchantProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductQueryRespDTO.getBaseProductList())) {
            for(BaseProductDTO baseProductDTO : merchantProductQueryRespDTO.getBaseProductList()) {
                if(!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode()))  {
                    Object feeType = null ;
                    if(!CollectionUtils.isEmpty(baseProductDTO.getProductAttributeMap())){
                        feeType = baseProductDTO.getProductAttributeMap().get("FEE_MERCHANT_TYPE");
                    }
                    if(WithdrawTypeEnum.REAL_TIME.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())){
                        secondProductList.add(new DicCodeAutoWithdrawDTO(baseProductDTO.getSecondBaseProductCode(),WithdrawTypeEnum.REAL_TIME.getDesc(),feeType==null?"":feeType.toString()));
                    }else if(WithdrawTypeEnum.TWO_HOUR.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())){
                        secondProductList.add(new DicCodeAutoWithdrawDTO(baseProductDTO.getSecondBaseProductCode(),WithdrawTypeEnum.TWO_HOUR.getDesc(),feeType==null?"":feeType.toString()));
                    }else if(WithdrawTypeEnum.NEXT_DAY.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())){
                        secondProductList.add(new DicCodeAutoWithdrawDTO(baseProductDTO.getSecondBaseProductCode(),WithdrawTypeEnum.NEXT_DAY.getDesc(),feeType==null?"":feeType.toString()));
                    }
                    hasWithdrawProduct = true;
                }
            }

        }
        modelAndView.addObject("hasWithdrawProduct", hasWithdrawProduct);
        modelAndView.addObject("secondProductList",secondProductList);
        return modelAndView;
    }


    /**
     *
     * @param modelAndView
     * @param bankCardNoHiddenStyle 卡号掩码格式 REMAIN_4 保留4位 其他值前4后4
     * @return
     */
    private ModelAndView buildBindCardListView(ModelAndView modelAndView, String bankCardNoHiddenStyle){

        List<BindcardMsg> bindCardMsgList = new ArrayList<BindcardMsg>();
        BindCardQueryRespDTO bindCardQueryRespDTO = this.queryBindCardV2();
        if(!CheckUtils.isEmpty(bindCardQueryRespDTO.getBankCardAccountList())) {
            LOGGER.info("商户{}进入管理提现卡查询绑定银行卡成功，卡数量为{}", bindCardQueryRespDTO.getMerchantNo(), bindCardQueryRespDTO.getBankCardAccountList().size());
            for(BankCardAccount bankCardAccount : bindCardQueryRespDTO.getBankCardAccountList()) {
                BindcardMsg bindcardMsg = new BindcardMsg();
                //账户/商户名
                bindcardMsg.setName(bankCardAccount.getAccountName());
                //银行名称
                bindcardMsg.setBankName(bankCardAccount.getBankName());
                //掩码银行卡号
                bindcardMsg.setCardNo(HiddenCodeUtils.hiddenBankCardNo(bankCardAccount.getAccountNo()));
                //正常提现卡只有（DEBIT_CARD， ENTERPRISE_ACCOUNT）两个类型
                bindcardMsg.setCardType(bankCardAccount.getBankCardType().toString());
                bindcardMsg.setBindId(Long.valueOf(bankCardAccount.getBindCardId()));
                bindCardMsgList.add(bindcardMsg);
            }
        }
        modelAndView.addObject("bankCardList", bindCardMsgList);
        modelAndView.addObject("bindCardList", bindCardMsgList);
        return modelAndView;
    }


    @RequestMapping(value = "/queryHistoryOrder")
    public ModelAndView view(HttpServletRequest request,HttpServletResponse response) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("middleground/withdrawQueryHistory");
        mav.addObject("UIWebRootUrl",WebPropertiesHolder.getUIWebRootUrl());
        cookieService.addCookie(request,response,"/bac-app");
        return mav;
    }


    /**
     * 跳转提现详情页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/query/detail", method = RequestMethod.GET)
    @ApiOperation(value = "提现订单详情查询")
    @ResponseBody
    public BaseRespDTO queryDetail(HttpServletRequest request) {
        String orderNo = request.getParameter("orderNo");
        if (StringUtils.isBlank(orderNo)) {
            return BaseRespDTO.success("订单号不能为空");
        }
        LOGGER.info("查询提现订单明细，订单号：orderNo={}", orderNo);
        try {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("customerNumber", getCurrentCustomerNumber());
            queryMap.put("orderNo", orderNo);
            //查询组件查询
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
            QueryResult queryResult = queryService.query("queryWithdrawOrderList", queryParam);
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                Map<String, Object> map = (Map) ((List) queryResult.getData()).get(0);
                String accountType = (String) map.get("account_type");
                map.put("account_type", AccountTypeEnum.valueOf(accountType).getDesc());
                String bank_name = (String) map.get("bank_name");
                String bank_account_no = (String) map.get("bank_account_no");
                bank_name = (bank_name == null ? "" : bank_name);
                bank_account_no = (bank_account_no == null ? "" : "(" + processBankNoRemain4Numbers(bank_account_no) + ")");
                map.put("bank_name", bank_name + bank_account_no);

                String status = (String) map.get("status");
                String remitStatus = (String) map.get("remit_status");
                if ("SUCCESS".equals(status) || "REVERSED".equals(remitStatus)) {
                    if ("REVERSED".equals(remitStatus)) {
                        map.put("status", "银行冲退");
                        map.put("finish_time", map.get("debit_back_time"));
                    } else {
                        map.put("status", "提现成功");
                    }
                } else if ("REMITING".equals(status) || "DEBIT_EXCEPTION".equals(status) || "REMIT_EXCEPTION".equals(status)
                        || "REFUND_EXCEPTION".equals(status) || "REFUND_FAIL".equals(status)) {
                    map.put("status", "银行处理中");
                } else if ("REQUEST_RECEIVE".equals(status)) {
                    map.put("status", "提现已接收");
                } else if ("REQUEST_ACCEPT".equals(status)) {
                    map.put("status", "提现已受理");
                } else if ("FAIL".equals(status)) {
                    map.put("status", "提现失败");
                }

                String arriveType = (String) map.get("arrive_type");
                if ("REAL_TIME".equals(arriveType)) {
                    map.put("arrive_type", "实时到账");
                } else if ("TWO_HOUR".equals(arriveType)) {
                    map.put("arrive_type", "2小时到账");
                } else {
                    map.put("arrive_type", "次日到账");
                }
                Object received_amount = map.get("received_amount");
                if (received_amount != null) {
                    map.put("received_amount", dealAmount(received_amount) + "元");
                }
                Object payer_fee = map.get("payer_fee");
                if (payer_fee != null) {
                    map.put("payer_fee", dealAmount(payer_fee) + "元");
                }
                if (AccountTypeEnum.FEE_ACCOUNT.equals(accountType)) {
                    map.put("payer_fee", "无");
                }
                if (map.get("fee_undertaker_merchant_no") != null && !ObjectUtils.equals(map.get("from_customer_no"), map.get("fee_undertaker_merchant_no"))) {
                    map.put("payer_fee", "无");
                }
                Object deduct_amount = map.get("deduct_amount");
                if (deduct_amount != null) {
                    map.put("deduct_amount", dealAmount(deduct_amount) + "元");
                }
                Object withdraw_amount = map.get("withdraw_amount");
                if (withdraw_amount != null) {
                    map.put("withdraw_amount", dealAmount(withdraw_amount) + "元");
                }

                LOGGER.info("查询订单明细返回，transferResponseParam={}", JSON.toJSONString(map));
                return BaseRespDTO.success(map);

            } else {
                return BaseRespDTO.success("暂无数据");
            }
        } catch (Exception e) {
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    @RequestMapping(value = "/withdraw-detail", method = RequestMethod.GET)
    @ApiOperation(value = "app提现订单详情查询")
    @ResponseBody
    public BaseRespDTO<WithdrawDetailModel> detailWithdraw(@RequestParam("orderNo") String orderNo) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[提现 详情查询] 商户={} orderNo={}", currentCustomerNumber, orderNo);
        try {
            Assert.isTrue(StringUtils.isNotBlank(orderNo), "orderNo 不能为空");
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("customerNumber", getCurrentCustomerNumber());
            queryMap.put("orderNo", orderNo);
            Date createTime = OrderNoUtils.getOrderTime(orderNo);
            if(null!= createTime) {
                queryMap.put("createStartDate", LocalDateTimeUtils.getStartOfToDay(createTime));
                queryMap.put("createEndDate", LocalDateTimeUtils.getStartOfDayTomorrow(createTime));
            }
            //查询组件查询
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
            QueryResult queryResult = queryService.query("queryWithdrawOrderList", queryParam);
            if (CheckUtils.isEmpty(queryResult.getData())) {
                LOGGER.info("[提现 详情查询] 商户={} orderNo={} 未查询到数据", currentCustomerNumber, orderNo);
                return BaseRespDTO.success();
            }
            Map<String, Object> map = (Map) ((List) queryResult.getData()).get(0);
            WithdrawDetailModel detailModel = assembleWithdrawDetailModel(map);
            LOGGER.info("[提现 详情查询] 商编为={}，detailModel={}", currentCustomerNumber,JSON.toJSONString(detailModel));
            return BaseRespDTO.success(detailModel);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("[提现 详情查询] 参数异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(AccountPayException.PARAM_ERROR.getDefineCode(), e.getMessage());
        } catch (YeepayBizException e) {
            LOGGER.warn("[提现 详情查询] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[提现 详情查询] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    private WithdrawDetailModel assembleWithdrawDetailModel(Map<String, Object> map) {
        WithdrawDetailModel detailModel = new WithdrawDetailModel();
        /*提现账户类型*/
        String accountType = (String) map.get("account_type");
        detailModel.setAccountType(accountType);
        detailModel.setAccountTypeDesc(EnumHelper.getAccountTypeDesc(accountType));

        /*状态*/
        String status = (String) map.get("status");
        String remitStatus = (String) map.get("remit_status");
        javafx.util.Pair<String, String> statusPair =  EnumHelper.getWithdrawShowStatusCode(status,remitStatus);
        detailModel.setStatus(statusPair.getKey());
        /*到账时间*/
        detailModel.setFinishTime(dealDateString(map.get("finish_time")));
        if ("REVERSED".equals(remitStatus)) {
            detailModel.setFinishTime(dealDateString(map.get("debit_back_time")));
        }

        /*到账时效*/
        String arriveType = (String) map.get("arrive_type");
        detailModel.setArriveType(arriveType);

        /*手续费*/
        Object payer_fee = map.get("payer_fee");
        detailModel.setFeeChargeType((String) map.get("fee_real_type"));
        if (payer_fee != null) {
            detailModel.setFee(dealAmount(payer_fee)+"");
        }
        if (AccountTypeEnum.FEE_ACCOUNT.equals(accountType)) {
            detailModel.setFee(null);
        }
        if (map.get("fee_undertaker_merchant_no") != null && !ObjectUtils.equals(map.get("from_customer_no"), map.get("fee_undertaker_merchant_no"))) {
            detailModel.setFee(null);
        }

        /*账号掩码*/
        String bank_name = (String) map.get("bank_name");
        String bank_account_no = (String) map.get("bank_account_no");
        bank_name = (bank_name == null ? "" : bank_name);
        bank_account_no = (bank_account_no == null ? "" : "(" + processBankNoRemain4Numbers(bank_account_no) + ")");
        map.put("bank_name", bank_name + bank_account_no);
        detailModel.setBankNameAndCardNo(bank_name + bank_account_no);

        detailModel.setOrderNo((String) map.get("order_no"));
        detailModel.setFromCustomerNo((String) map.get("from_customer_no"));
        MerchantRespDTO merchantInfo = remoteService.queryMerchantInfo(detailModel.getFromCustomerNo());
        detailModel.setFromCustomerName(null != merchantInfo ? merchantInfo.getShortName() : null);
        detailModel.setOperator((String) map.get("operator"));
        detailModel.setRemark((String) map.get("remark"));
        detailModel.setCreateTime(dealDateString( map.get("create_time")));
        detailModel.setWithdrawAmount(dealAmountString(map.get("withdraw_amount")));
        detailModel.setDeductAmount(dealAmountString(map.get("deduct_amount")));
        detailModel.setReceivedAmount(dealAmountString(map.get("received_amount")));
        detailModel.setReturnCode((String) map.get("return_code"));
        detailModel.setReturnMessage((String) map.get("return_message"));
        return detailModel;
    }

    private String dealAmountString(Object o){
        if(o==null){
            return null;
        }
        if(o instanceof BigDecimal){
            return ((BigDecimal)o).setScale(2,BigDecimal.ROUND_DOWN).toPlainString();
        }else if(StringUtils.isNumeric(o.toString())){
            return new BigDecimal(o.toString()).setScale(2,BigDecimal.ROUND_DOWN).toPlainString();
        }
        return o.toString();
    }

    private String dealDateString(Object o){
        if (o == null) {
            return null;
        }
        if (o instanceof Date) {
            return LocalDateTimeUtils.getDateString((Date) o);
        } else if (o instanceof Timestamp) {
            return LocalDateTimeUtils.getDateString((Date) o);
        }
        return o.toString();
    }


    @RequestMapping(value = "/cardManage/init", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO cardManageInit(HttpServletRequest request) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        Map<String, Object> map = new HashMap<>();
        try {
            map = buildBindCardListView(map, currentCustomerNumber);
            map = buildModelView(map, request);
            LOGGER.info("提现绑卡页面初始化，商编={}，返回为={}", currentCustomerNumber, JSONUtils.toJsonString(map));
            return BaseRespDTO.success(map);
        } catch (Exception e) {
            LOGGER.error("提现绑卡页面初始化异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    /**
     * 提现页面初始化
     * @param request
     * @return
     */
    @RequestMapping(value = "/order/init", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO withdrawInit(HttpServletRequest request) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        //提现账户基本信息配置
        Map<String, AccountBasicParam> basicConfig = WithdrawProductBasicConfigUtils.getAccountBasicConfig();
        Map<String, Object> map = new HashMap<>();
        try {
            map = buildBindCardListView(map, currentCustomerNumber);
            List<AccountDTO> accountList = businessCheckRemoteService.queryAccountInfos(getCurrentCustomerNumber());
            List<AccountInfo> accountInfoList = sortAccountInfo(basicConfig, accountList);
            accountInfoList = getWithdrawProductView(basicConfig, accountInfoList);
            map.put("hasWithdrawProduct", !accountInfoList.isEmpty());
            buildAccountView(accountInfoList, map);
            map = buildModelView(map, request);
            LOGGER.info("提现页面初始化，商编={}，返回为={}", currentCustomerNumber, JSONUtils.toJsonString(map));
            return BaseRespDTO.success(map);
        } catch (Exception e) {
            LOGGER.error("提现页面初始化异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    /**
     * 初始化自动提现规则
     * @param request
     * @return
     */
    @RequestMapping(value = "/auto/rule/init", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO autoRuleInit(HttpServletRequest request) {
        String merchantNo = getCurrentCustomerNumber();
        try {
            LOGGER.info("初始化自动提现规则,商编为 = {}", merchantNo);
            Map<String, Object> map = new HashMap<>();
            boolean hasAutoWithdrawRule = false;
            boolean hasEffectiveAutoWithdrawRule = false;
            AutoWithdrawRuleQueryReqDTO autoWithdrawRuleQueryReqDTO = new AutoWithdrawRuleQueryReqDTO();
            autoWithdrawRuleQueryReqDTO.setMerchantNo(merchantNo);
            AutoWithdrawRuleQueryRespDTO autoWithdrawRuleQueryRespDTO = MGWithdrawFacade.queryAutoWithdrawRule(autoWithdrawRuleQueryReqDTO);
            if (!CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO) && !CheckUtils.isEmpty(autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList())) {
                hasAutoWithdrawRule = true;
                for (AutoWithdrawRule rule : autoWithdrawRuleQueryRespDTO.getAutoWithdrawRuleList()) {
                    if (RuleStatusEnum.EFFECTIVE.equals(rule.getStatus())) {
                        hasEffectiveAutoWithdrawRule = true;
                    }
                }
            }
            map.put("hasAutoWithdrawRule", hasAutoWithdrawRule);
            map.put("hasEffectiveAutoWithdrawRule", hasEffectiveAutoWithdrawRule);
            map = buildWithdrawProductMap(map);
            return BaseRespDTO.success(map);
        } catch (Exception e) {
            LOGGER.error("初始化自动提现规则,商编为=" + merchantNo + "异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }


    private Map<String, Object> buildWithdrawProductMap(Map<String, Object> map) {
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        boolean hasWithdrawProduct = false;
        List<DicCodeAutoWithdrawDTO> secondProductList = new ArrayList<>();
        String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
        MerchantProductQueryRespDTO merchantProductQueryRespDTO = businessCheckRemoteService.queryMerchantProduct(getCurrentCustomerNumber(), ProductTypeEnum.ACCOUNT.toString(), markProductCode, Costants.WITHDRAW_BASICSPRODUCTFIRST, null);
        if ("0000".equals(merchantProductQueryRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductQueryRespDTO.getBaseProductList())) {
            for (BaseProductDTO baseProductDTO : merchantProductQueryRespDTO.getBaseProductList()) {
                if (!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode())) {
                    Object feeType = null;
                    if (!CollectionUtils.isEmpty(baseProductDTO.getProductAttributeMap())) {
                        feeType = baseProductDTO.getProductAttributeMap().get("FEE_MERCHANT_TYPE");
                    }
                    if (WithdrawTypeEnum.REAL_TIME.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())) {
                        secondProductList.add(new DicCodeAutoWithdrawDTO(baseProductDTO.getSecondBaseProductCode(), WithdrawTypeEnum.REAL_TIME.getMqDesc(), feeType == null ? "" : feeType.toString()));
                    } else if (WithdrawTypeEnum.TWO_HOUR.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())) {
                        secondProductList.add(new DicCodeAutoWithdrawDTO(baseProductDTO.getSecondBaseProductCode(), WithdrawTypeEnum.TWO_HOUR.getMqDesc(), feeType == null ? "" : feeType.toString()));
                    } else if (WithdrawTypeEnum.NEXT_DAY.getPayWay().equals(baseProductDTO.getSecondBaseProductCode())) {
                        secondProductList.add(new DicCodeAutoWithdrawDTO(baseProductDTO.getSecondBaseProductCode(), WithdrawTypeEnum.NEXT_DAY.getMqDesc(), feeType == null ? "" : feeType.toString()));
                    }
                    hasWithdrawProduct = true;
                }
            }

        }
        map.put("hasWithdrawProduct", hasWithdrawProduct);
        map.put("secondProductList", secondProductList);
        return map;
    }


    private void buildAccountView(List<AccountInfo> accountInfoList, Map<String, Object> reqMap) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountInfoList)) {
            accountInfoList.stream().forEach(e -> {
                Map<String, Object> map = new HashMap<>();
                map.put("accountType", e.getAccountType());
                map.put("balance", e.getBalance());
                map.put("accountTypeName", AccountTypeEnum.valueOf(e.getAccountType()).getDesc());
                map.put("serviceType", e.getServiceType());
                Collections.sort(e.getDicCodeDTOS());
                map.put("arriveType", JSONUtils.toJsonString(e.getDicCodeDTOS()));
                list.add(map);
            });
        }
        reqMap.put("accountTypeList", list);
    }


    private Map<String, Object> buildBindCardListView(Map<String, Object> map, String merchantNo) {
        List<BindcardMsg> bindCardMsgList = new ArrayList<BindcardMsg>();
        BindCardQueryRespDTO bindCardQueryRespDTO = bindCardFacadeRemoteService.queryBindCardV2(merchantNo);
        if (!CheckUtils.isEmpty(bindCardQueryRespDTO.getBankCardAccountList())) {
            LOGGER.info("商户{}进入管理提现卡查询绑定银行卡成功，卡数量为{}", bindCardQueryRespDTO.getMerchantNo(), bindCardQueryRespDTO.getBankCardAccountList().size());
            for (BankCardAccount bankCardAccount : bindCardQueryRespDTO.getBankCardAccountList()) {
                BindcardMsg bindcardMsg = new BindcardMsg();
                //账户/商户名
                bindcardMsg.setName(bankCardAccount.getAccountName());
                //银行名称
                bindcardMsg.setBankName(bankCardAccount.getBankName());
                //掩码银行卡号
                bindcardMsg.setCardNo(HiddenCodeUtils.hiddenBankCardNo(bankCardAccount.getAccountNo()));
                //正常提现卡只有（DEBIT_CARD， ENTERPRISE_ACCOUNT）两个类型
                bindcardMsg.setCardType(bankCardAccount.getBankCardType().toString());
                bindcardMsg.setBindId(Long.valueOf(bankCardAccount.getBindCardId()));
                bindcardMsg.setBindIdCharacter(bankCardAccount.getBindCardId());
                bindCardMsgList.add(bindcardMsg);
            }
        }
        LOGGER.info("初始化绑卡信息，商编为={}，卡信息为={}", merchantNo, JSONUtils.toJsonString(bindCardMsgList));
        map.put("bindCardList", bindCardMsgList);
        return map;
    }


    private Map<String, Object> buildModelView(Map<String, Object> map, HttpServletRequest request) throws Exception {
        String productType = "";
        String merchantName = "";
        MerchantinfoRespDTO merchantinfoRespDTO = businessCheckRemoteService.queryMerchantInfo(getCurrentCustomerNumber());
        if (!CheckUtils.isEmpty(merchantinfoRespDTO) && "0000".equals(merchantinfoRespDTO.getRetCode())) {
            LOGGER.info("调用客户中心查询客户信息，返回的签约类型为{}", merchantinfoRespDTO.getSignType());
            merchantName = merchantinfoRespDTO.getSignedName();
            Pair<String, String> info = businessCheckRemoteService.convertWithdrawCheckInfo(merchantinfoRespDTO);
            if (StringUtils.isNotEmpty(info.getLeft())) {
                productType = info.getLeft();
            }
            if (StringUtils.isNotEmpty(info.getRight())) {
                map.put("corporationName", info.getRight());
            }
        } else {
            LOGGER.error("调用客户中心查询客户信息，异常，返回参数：{}", JSONUtils.toJsonString(merchantinfoRespDTO));
        }
        map.put("bankMap", ProvinceCityService.queryBankMap());
        map.put("provinceList", ProvinceCityService.getAreasV2(null));
        map.put("productType", productType);
        map.put("merchantName", merchantName);
        map.put("notSupportBankMap", JSON.toJSONString(UniformConfigUtils.getNotSupportBankMap()));
        String redirectUrl = request.getParameter("redirectUrl");

        if (!CheckUtils.isEmpty(redirectUrl)) {
            if (allowRedirectUrls.contains(redirectUrl)) {
                map.put("redirectUrl", redirectUrl);
            } else {
                throw new RuntimeException("不允许跳转的地址,redirectUrl=" + redirectUrl);
            }
        }
        return map;
    }

    /**
     * 查询提现详情页（二合一接口）
     * @param orderNo
     * @param customerNumber
     * @return
     */
    @RequestMapping(value = "/queryWithdrawDetail", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryWithdrawDetail(@RequestParam("orderNo") String orderNo,@RequestParam(value = "customerNumber") String customerNumber) {
        LOGGER.info("[红版商户后台]查询提现详情页 the orderNo=[{}],the customerNumber=[{}]",orderNo,customerNumber);
        if (StringUtils.isBlank(orderNo)) {
            return BaseRespDTO.success("订单号不能为空");
        }
        if (StringUtils.isBlank(customerNumber)) {
            return BaseRespDTO.success("商户编号不能为空");
        }
        try {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("customerNumber", customerNumber);
            queryMap.put("orderNo", orderNo);
            //查询组件查询
            QueryParam queryParam = new QueryParam();
            queryParam.setParams(queryMap);
            QueryService queryService = (QueryService) QueryServiceUtil.getBean("accountTradeService", QueryService.class);
            QueryResult queryResult = queryService.query("queryWithdrawOrderList", queryParam);
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                Map<String, Object> map = (Map) ((List) queryResult.getData()).get(0);
                String accountType = (String) map.get("account_type");
                map.put("account_type", AccountTypeEnum.valueOf(accountType).getDesc());
                String bank_name = (String) map.get("bank_name");
                String bank_account_no = (String) map.get("bank_account_no");
                bank_name = (bank_name == null ? "" : bank_name);
                bank_account_no = (bank_account_no == null ? "" : "(" + processBankNoRemain4Numbers(bank_account_no) + ")");
                map.put("bank_name", bank_name + bank_account_no);

                String status = (String) map.get("status");
                String remitStatus = (String) map.get("remit_status");
                if ("SUCCESS".equals(status) || "REVERSED".equals(remitStatus)) {
                    if ("REVERSED".equals(remitStatus)) {
                        map.put("status", "银行冲退");
                        map.put("finish_time", map.get("debit_back_time"));
                    } else {
                        map.put("status", "提现成功");
                    }
                } else if ("REMITING".equals(status) || "DEBIT_EXCEPTION".equals(status) || "REMIT_EXCEPTION".equals(status)
                        || "REFUND_EXCEPTION".equals(status) || "REFUND_FAIL".equals(status)) {
                    map.put("status", "银行处理中");
                } else if ("REQUEST_RECEIVE".equals(status)) {
                    map.put("status", "提现已接收");
                } else if ("REQUEST_ACCEPT".equals(status)) {
                    map.put("status", "提现已受理");
                } else if ("FAIL".equals(status)) {
                    map.put("status", "提现失败");
                }

                String arriveType = (String) map.get("arrive_type");
                if ("REAL_TIME".equals(arriveType)) {
                    map.put("arrive_type", "实时到账");
                } else if ("TWO_HOUR".equals(arriveType)) {
                    map.put("arrive_type", "2小时到账");
                } else {
                    map.put("arrive_type", "次日到账");
                }
                Object received_amount = map.get("received_amount");
                if (received_amount != null) {
                    map.put("received_amount", dealAmount(received_amount) + "元");
                }
                Object payer_fee = map.get("payer_fee");
                if (payer_fee != null) {
                    map.put("payer_fee", dealAmount(payer_fee) + "元");
                }
                if (AccountTypeEnum.FEE_ACCOUNT.equals(accountType)) {
                    map.put("payer_fee", "无");
                }
                if (map.get("fee_undertaker_merchant_no") != null && !ObjectUtils.equals(map.get("from_customer_no"), map.get("fee_undertaker_merchant_no"))) {
                    map.put("payer_fee", "无");
                }
                Object deduct_amount = map.get("deduct_amount");
                if (deduct_amount != null) {
                    map.put("deduct_amount", dealAmount(deduct_amount) + "元");
                }
                Object withdraw_amount = map.get("withdraw_amount");
                if (withdraw_amount != null) {
                    map.put("withdraw_amount", dealAmount(withdraw_amount) + "元");
                }

                LOGGER.info("查询订单明细返回，transferResponseParam={}", JSON.toJSONString(map));
                return BaseRespDTO.success(map);

            } else {
                return BaseRespDTO.success("暂无数据");
            }
        } catch (Exception e) {
            return BaseRespDTO.fail(e.getMessage());
        }
    }




    public static class BindcardMsg{
        //商户名称
        private String name;
        //卡类型
        private String cardType;
        private String bankName;
        private String cardNo;
        private Long bindId;
        private String city;
        private String province;
        private String bindIdCharacter;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }


        public String getCardType() {
            return cardType;
        }

        public void setCardType(String cardType) {
            this.cardType = cardType;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getCardNo() {
            return cardNo;
        }

        public void setCardNo(String cardNo) {
            this.cardNo = cardNo;
        }

        public Long getBindId() {
            return bindId;
        }

        public void setBindId(Long bindId) {
            this.bindId = bindId;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getBindIdCharacter() {
            return bindIdCharacter;
        }

        public void setBindIdCharacter(String bindIdCharacter) {
            this.bindIdCharacter = bindIdCharacter;
        }
    }


    public static class SettleInput{
        //卡号
        private String cardNo;
        private String headBankCode;
        private String headBankName;
        private String bankCode;
        private String bankName;
        private String bankProvinceName;
        private String bankCityName;
        private String bankProvinceCode;
        private String bankCityCode;
        //开户名称
        private String name;
        //账户类型
        private String productType;

        public String getCardNo() {
            return cardNo;
        }

        public void setCardNo(String cardNo) {
            this.cardNo = cardNo;
        }

        public String getHeadBankCode() {
            return headBankCode;
        }

        public void setHeadBankCode(String headBankCode) {
            this.headBankCode = headBankCode;
        }

        public String getHeadBankName() {
            return headBankName;
        }

        public void setHeadBankName(String headBankName) {
            this.headBankName = headBankName;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getBankProvinceName() {
            return bankProvinceName;
        }

        public void setBankProvinceName(String bankProvinceName) {
            this.bankProvinceName = bankProvinceName;
        }

        public String getBankCityName() {
            return bankCityName;
        }

        public void setBankCityName(String bankCityName) {
            this.bankCityName = bankCityName;
        }

        public String getBankProvinceCode() {
            return bankProvinceCode;
        }

        public void setBankProvinceCode(String bankProvinceCode) {
            this.bankProvinceCode = bankProvinceCode;
        }

        public String getBankCityCode() {
            return bankCityCode;
        }

        public void setBankCityCode(String bankCityCode) {
            this.bankCityCode = bankCityCode;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }
    }
}
