package com.yeepay.g3.app.account.pay.mboss.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.yeepay.g3.app.account.pay.mboss.dao.DirectTransferOperatorCreditNoRelaDao;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorCreditNoRelaEntity;
import com.yeepay.g3.utils.persistence.mybatis.GenericDaoDefault;


public class DirectTransferOperatorCreditNoRelaDaoImpl  extends GenericDaoDefault<DirectTransferOperatorCreditNoRelaEntity> implements DirectTransferOperatorCreditNoRelaDao {

    @Override
    public List<DirectTransferOperatorCreditNoRelaEntity> queryByOperatorId(Long operatorId) {
        return (List<DirectTransferOperatorCreditNoRelaEntity>)super.getSqlSession().selectList("DirectTransferOperatorCreditNoRelaEntity.queryByOperatorId", operatorId);
    }

    @Override
    public void deleteByOperatorId(Long operatorId) {
        this.delete("deleteByOperatorId", operatorId);
    }

    @Override
    public List<DirectTransferOperatorCreditNoRelaEntity> queryByDebitCustomerAndLoginName(
            String debitCustomerNo, String loginName) {
        Map<String,String> parameter = new HashMap<String,String>();
        parameter.put("debitCustomerNo",debitCustomerNo);
        parameter.put("loginName", loginName);
        return (List<DirectTransferOperatorCreditNoRelaEntity>)super.getSqlSession().selectList("DirectTransferOperatorCreditNoRelaEntity.queryByDebitCustomerAndLoginName", parameter);
        
    }
    
}
