package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base;
import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页列表查询基类
 */
@NoArgsConstructor
@ApiModel(description = "分页列表查询 结果")
public class BasePageRespDTO<T> implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "返回码")
    private String code;

    @ApiModelProperty(value = "返回信息")
    private String message;

    @ApiModelProperty(value = "查询总条数")
    private Long total = 0L;

    @ApiModelProperty(value = "查询当前页面数据")
    private List<T> list;

    public static <T> BasePageRespDTO<T> successPage(List<T> list, Long total) {

        BasePageRespDTO<T> result = new BasePageRespDTO();
        result.setCode("000000");
        result.setMessage("请求成功");
        result.setList(list);
        result.setTotal(total);
        return result;
    }

    public static <T> BasePageRespDTO fail(String code,String message){
        BasePageRespDTO<T> respDTO = new BasePageRespDTO<>();
        respDTO.setCode(code);
        respDTO.setMessage(message);
        respDTO.setTotal(0L);
        respDTO.setList(Lists.newArrayList());
        return respDTO;
    }

    public static <T> BasePageRespDTO fail(String message){
        BasePageRespDTO<T> respDTO = new BasePageRespDTO<>();
        respDTO.setCode("400399");
        respDTO.setMessage(message);
        respDTO.setTotal(0L);
        respDTO.setList(Lists.newArrayList());
        return respDTO;
    }

    public static BasePageRespDTO systemError(String message){
        return fail("509998",message);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
