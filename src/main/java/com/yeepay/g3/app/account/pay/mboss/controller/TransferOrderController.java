package com.yeepay.g3.app.account.pay.mboss.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.omg.CORBA.PRIVATE_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ibm.db2.jcc.am.ne;
import com.yeepay.g3.app.account.pay.mboss.utils.AccountPayRefundDownLoad;
import com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DataExportUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DataFormater;
import com.yeepay.g3.app.account.pay.mboss.utils.ExportExcelParam;
import com.yeepay.g3.app.account.pay.mboss.utils.HistoryTradeManagementDownLoad;
import com.yeepay.g3.app.account.pay.mboss.utils.QueryServiceUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.response.ResponseMessage.Status;
import com.yeepay.g3.facade.mp.PermissionAndFunctionConstant;
import com.yeepay.g3.facade.mp.dto.UserDTO;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;


@Controller
@RequestMapping("/boss/accountpay/transferorder")
public class TransferOrderController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(TransferOrderController.class);
	private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

	
	protected final DataFormater dataFormater = new DataFormater();
	
	 // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认pageNo
    private static final String PAGE_NO_DEFAULT_VAL = "1";
    
    private static final String QUERY_PERMISSION_CONSTANT = "1004";

    // 金额格式
    private static final String AMOUT_FORMAT = "^([1-9]\\d*\\.\\d*|0\\.\\d+|[1-9]\\d*|0)$";

    // 产品类型集合
    private static final Map<String, List<String>> PRODUCT_TYPE_MAP = Maps.newHashMap();
    //状态集合
    private static final Map<String, List<String>> STATUS_MAP = Maps.newHashMap();
    //剩余可退款金额展示为'-'
    private static final List<String> NOT_SHOW_SURPLUS_REFUND_AMOUNT = Arrays.asList(new String[]{"INIT", "WAIT_PAY", "CLOSE", "REJECT", "TIME_OUT", "FULLY_PAY", "CS_ACCEPT", "CS_SUCCESS", "REPEAL_ACCEPT", "REPEALED"});

    // 网银历史库时间范围配置
    //private List<String> hisDsList = ConfigureSetting.getHisDataSourceRange();
    

    /**
     * 查询账户支付请求订单
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/query")
    public ModelAndView queryPayOrder(HttpServletRequest request, HttpServletResponse response){
    	logger.info("refundquerySuccess");

        ModelAndView mav = new ModelAndView();
        //String orderString = request.getParameter("orderNo");
        mav.setViewName("transfer/queryTransferOrder");
        //mav.addObject("orderNo",orderString);
        return mav;
    }
    


    
    /**
     * 订单列表查询(ajax)
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping(value = "/list")
    @ResponseBody
    public ResponseMessage queryHistoryOrderList(HttpServletRequest request, HttpServletResponse response,
    		QueryInputParam param,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param), pageSize, pageNo);
        ResponseMessage resMsg = new ResponseMessage("success");
        param.setOrderNo(request.getParameter("orderNo"));
        param.setInnerOrderNo(request.getParameter("innerOrderNo"));
        param.setStartTime(request.getParameter("startTime"));
        param.setEndTime(request.getParameter("endTime"));
        param.setRequestSys(request.getParameter("requestSys"));
        param.setStatus(request.getParameter("status"));
        
        param.setDebitCustomerNo( getCurrentCustomerNumber());
        param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
        param.setTrxStartTime(request.getParameter("trxStartTime"));
        param.setTrxEndTime(request.getParameter("trxEndTime"));
        param.setTransferWay(request.getParameter("transferWay"));
        param.setAccountNo(request.getParameter("accountNo"));
        param.setRequestNo(request.getParameter("requestNo"));
        param.setTransferType(request.getParameter("transferType"));

        String merchant = getCurrentCustomerNumber();
        String transferWay = request.getParameter("transferWay");
        String queryKey = "";
        String querySumKey = "";
        if(StringUtils.isNotBlank(transferWay)){
        	if(transferWay.equals("S0")){
        		queryKey = "queryTransferOrder";
        		querySumKey = "queryTransferOrderSum";
        	}else if(transferWay.equals("T0")){
        		queryKey = "queryTimingRecord";
        		querySumKey = "queryTimingRecordSum";

        	}
        }else{
        	queryKey = "queryTransferOrder";
    		querySumKey = "queryTransferOrderSum";
        }
        if(StringUtils.isNotBlank(request.getParameter("mark"))){
           param.setDebitCustomerNo(merchant);
        }
       /* param.setCustomerNo(merchant);
        param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));*/
        
       
  
      
        
        logger.info("..."+ToStringBuilder.reflectionToString(param), pageSize, pageNo);

        
       /* try {
            checkInputParam(param);

        } catch (RuntimeException e) {
            resMsg.setStatus(Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }*/
        String path = request.getContextPath();

        try {
			QueryResult result = this.queryOrderList(param, pageNo, pageSize,queryKey);

			if (result != null && !CheckUtils.isEmpty(result.getData())) {
			    for (Map<String, Object> detail : result.getData()) {
			        adaptReturnResult(detail,pageNo,pageSize,path);
			    }
			    resMsg.put("pageNo", pageNo);
			    resMsg.put("pageSize", pageSize);
			    resMsg.put("dataList", result.getData());

			}

      List<Map<String, Object>> list =  this.queryOrderListSum(param,querySumKey);
			// 如果查询结果不为空的话
			if (!CheckUtils.isEmpty(list)) {
			    // int sumCount = Integer.parseInt((String) list.get(0).get("SUM_COUNT"));
				int sumCount = (Integer) mergeCount(list, "SUM_COUNT", "int");
			    resMsg.put("totalCount", sumCount);// 这是啥

			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();

		}

        return resMsg;
    }
    
    
   @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
   @RequestMapping(value = "/listsum")
    @ResponseBody
    public ResponseMessage queryHistoryOrderListSum(HttpServletRequest request, HttpServletResponse response,QueryInputParam param) {
    	 logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param));
         ResponseMessage resMsg = new ResponseMessage("success");
         param.setOrderNo(request.getParameter("orderNo"));
         param.setInnerOrderNo(request.getParameter("innerOrderNo"));
         param.setStartTime(request.getParameter("startTime"));
         param.setEndTime(request.getParameter("endTime"));
         param.setRequestSys(request.getParameter("requestSys"));
         param.setStatus(request.getParameter("status"));
         
         param.setDebitCustomerNo( getCurrentCustomerNumber());
         param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
         param.setTrxStartTime(request.getParameter("trxStartTime"));
         param.setTrxEndTime(request.getParameter("trxEndTime"));
         param.setTransferWay(request.getParameter("transferWay"));
         param.setTransferType(request.getParameter("transferType"));
         param.setAccountNo(request.getParameter("accountNo"));
         param.setRequestNo(request.getParameter("requestNo"));
         String merchant = getCurrentCustomerNumber();
         String transferType = request.getParameter("transferType");
         String transferWay = request.getParameter("transferWay");

         String queryKey = "";
         String querySumKey = "";
         if(StringUtils.isNotBlank(transferWay)){
         	if(transferWay.equals("S0")){
         		queryKey = "queryTransferOrder";
         		querySumKey = "queryTransferOrderSum";
         	}else if(transferWay.equals("T0")){
         		queryKey = "queryTimingRecord";
         		querySumKey = "queryTimingRecordSum";

         	}
         }else{
        	 queryKey = "queryTransferOrder";
      		querySumKey = "queryTransferOrderSum"; 
         }
         
         
         if(StringUtils.isNotBlank(request.getParameter("mark"))){
            param.setDebitCustomerNo(merchant);
         }

         logger.info("..."+ToStringBuilder.reflectionToString(param));

        List<Map<String, Object>> list = this.queryOrderListSum(param,querySumKey);


        // 如果查询结果不为空的话
        if (list != null && !list.isEmpty()) {
            Map<String, Object> sumResult = list.get(0);
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumFractionDigits(2);
            nf.setMaximumFractionDigits(2);

            /*resMsg.getData().put("sum_count", sumResult.get("SUM_COUNT").toString());// 总笔数
            resMsg.getData().put("sum_amount", nf.format(new BigDecimal(sumResult.get("SUM_AMOUNT").toString())));// 总金额
            resMsg.getData().put("sum_fee", nf.format(new BigDecimal(sumResult.get("SUM_FEE").toString())));// 总手续费
*/      
           try {
				resMsg.getData().put("sum_count", (Integer) mergeCount(list, "SUM_COUNT", "int")+"");// 总笔数
				resMsg.getData().put("sum_amount", nf.format((BigDecimal)mergeCount(list, "SUM_AMOUNT", "float")));// 总金额
				resMsg.getData().put("sum_fee", nf.format((BigDecimal)mergeCount(list, "SUM_FEE", "float")));// 总金额
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", 0.00);// 总金额
            resMsg.getData().put("sum_fee", 0.00);// 总手续费
        }

        return resMsg;
    }
    
   
    
    
    /**
     * 适配返回结果,例如FULLY_PAY之后的状态需要变更可退款金额为-等
     *
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail, int pageNo, int pageSize, String path) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }

        Map<String, Object> operation = new HashMap<String, Object>();
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        nf.setGroupingUsed(false);

        // 判断当前状态是否是支付成功,推送清算中心,成功和已退款,如果是则展示对应的可退款余额,否则展示'-'
        String operatorString = (String) detail.get("operator");
		if(StringUtils.isNotBlank(operatorString)){
	        UserDTO userDTO = userFacade.getUserByLoginName(operatorString);
	        if(userDTO != null)
	        detail.put("mobile", userDTO.getMobile());

		}
		
		 String sms_verify =  detail.get("sms_verify") + "";
	        if(com.yeepay.g3.utils.common.StringUtils.equals(sms_verify, "0")){
	        	sms_verify = "否";
	        }else if(com.yeepay.g3.utils.common.StringUtils.equals(sms_verify, "1")){
	        	sms_verify = "是";
	        }
	        detail.put("sms_verify", sms_verify); 
	        
	        String transfer_type = (String) detail.get("transfer_type");
	        if(com.yeepay.g3.utils.common.StringUtils.equals(transfer_type, "OUTER")){
	        	transfer_type = "外部转账";
	        }else {
	        	transfer_type = "内部转账";
	        }
	        detail.put("transfer_type", transfer_type); 
	      
	        String fee_type = (String) detail.get("fee_type");
	        if(com.yeepay.g3.utils.common.StringUtils.equals(fee_type, "CREDIT")){
	        	fee_type = "收款方";
	        }else {
	        	fee_type = "付款方";
	        }
	        detail.put("fee_type", fee_type);

        
        String status = (String) detail.get("status");
        String markWord = (String) detail.get("markWord");
        if(StringUtils.equals(markWord,"zhengyakun")){
        	status = "";
        }else{
        	if(com.yeepay.g3.utils.common.StringUtils.equals(status, "SEND")){
          	   status = "已发送";
             }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "INIT")){
           	   status = "初始";
              }
             else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "VERIFIED")){
          	   status = "已验证";
             }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "PROCESSING")){
          	   status = "账务处理中";
             }else if(com.yeepay.g3.utils.common.StringUtils.equals(status, "SUCCESS")){
          	   status = "转账成功";
             }else{
          	   status = "转账失败";
             }
        }
        
        detail.put("status", status); 
      
        detail.put("orgorderno","---");

        if (null != detail.get("amount")) {
            detail.put("amount", nf.format(new BigDecimal(detail.get("amount").toString())));
        }
        if (null != detail.get("fee")) {
            detail.put("fee", nf.format(new BigDecimal(detail.get("fee").toString())));
        }
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
          
            Object obj = detail.get("trx_time");
            if (null != obj) {
                    detail.put("trx_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            
             obj = detail.get("create_time");
            if (null != obj) {
                    detail.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            obj = detail.get("last_modify_time");
            if (null != obj) {
                    detail.put("last_modify_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));

            }
            
            

        } catch (Exception e) {
            logger.error("这都能错..擦....", e);
        }
        
       /* String orderNo = (String) detail.get("orderNo");
        QueryInputParam param = new QueryInputParam();
        param.setOrderNo(orderNo);
		QueryResult result = this.queryOrderListHelp(param, pageNo, pageSize);
		List<Map<String, Object>> list = (List<Map<String, Object>>) result.getData();
		if(list != null && list.size() > 0){
			Map<String, Object> map=list.get(0);
			detail.put("innerOrderNo", map.get("innerOrderNo")+"");
		}*/
		

		return operation;

    }
   public void exportExcelWithData(HttpServletResponse response, HttpServletRequest request, ExportExcelParam excelParam,List<Map<String, Object>> data)
			throws IOException {
		DataExportUtils dataEcportUtil = new DataExportUtils();
		dataEcportUtil.setTextResource(textResource);
		dataEcportUtil.setMessageFormater(messageFormater);
		dataEcportUtil.setDataFormater(dataFormater);
		dataEcportUtil.exportExcelWithData(response, request, excelParam,data);
	}
  
   /**
    * 历史交易入参绑定dto
    */
   @SuppressWarnings("unused")
   @Data
   @ToString
   @AllArgsConstructor
   @NoArgsConstructor
   private static class QueryInputParam {

	   public String getTransferWay() {
			return transferWay;
		}

		public void setTransferWay(String transferWay) {
			this.transferWay = transferWay;
		}


	private String transferWay;
	   public String getOrderNo() {
			return orderNo;
		}




		public void setOrderNo(String orderNo) {
			this.orderNo = orderNo;
		}




		public String getInnerOrderNo() {
			return innerOrderNo;
		}




		public void setInnerOrderNo(String innerOrderNo) {
			this.innerOrderNo = innerOrderNo;
		}




		public String getRequestSys() {
			return requestSys;
		}




		public void setRequestSys(String requestSys) {
			this.requestSys = requestSys;
		}




		public String getStatus() {
			return status;
		}




		public void setStatus(String status) {
			this.status = status;
		}




		public String getDebitCustomerNo() {
			return debitCustomerNo;
		}




		public void setDebitCustomerNo(String debitCustomerNo) {
			this.debitCustomerNo = debitCustomerNo;
		}




		public String getCreditCustomerNo() {
			return creditCustomerNo;
		}




		public void setCreditCustomerNo(String creditCustomerNo) {
			this.creditCustomerNo = creditCustomerNo;
		}




		public String getTransferType() {
			return transferType;
		}




		public void setTransferType(String transferType) {
			this.transferType = transferType;
		}




		public String getStartTime() {
			return startTime;
		}




		public void setStartTime(String startTime) {
			this.startTime = startTime;
		}




		public String getEndTime() {
			return endTime;
		}




		public void setEndTime(String endTime) {
			this.endTime = endTime;
		}




		public String getTrxStartTime() {
			return trxStartTime;
		}




		public void setTrxStartTime(String trxStartTime) {
			this.trxStartTime = trxStartTime;
		}




		public String getTrxEndTime() {
			return trxEndTime;
		}




		public void setTrxEndTime(String trxEndTime) {
			this.trxEndTime = trxEndTime;
		}




		public String getAccountNo() {
			return accountNo;
		}




		public void setAccountNo(String accountNo) {
			this.accountNo = accountNo;
		}




		public String getRequestNo() {
			return requestNo;
		}




		public void setRequestNo(String requestNo) {
			this.requestNo = requestNo;
		}




		private String orderNo;
	    private String innerOrderNo;
	    private String requestSys;

	    private String status;
	    private String debitCustomerNo;
	    private String creditCustomerNo;
	    private String transferType;
	    private String startTime;
	    private String endTime;
	    private String trxStartTime;
	    private String trxEndTime;
	    private String accountNo;
	    private String requestNo;
   

   }
   private QueryResult queryOrderList(QueryInputParam param, int pageNo, int pageSize, String queryKey) {
       QueryResult result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
       //queryMap.put("customerNumber", getCurrentCustomerNumber());

       


       Integer startIndex = (pageNo - 1) * pageSize + 1;

       QueryParam queryParam = new QueryParam();
       queryParam.setParams(queryMap);
       queryParam.setStartIndex(startIndex);
       queryParam.setMaxSize(pageSize);
       queryParam.setDoSum(true);

       logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
      result = queryService.query(queryKey, queryParam);

       return result;
   }
   private List<Map<String, Object>> queryOrderListSum(QueryInputParam param, String queryKey) {
	   List<Map<String, Object>> result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
       //queryMap.put("customerNumber", getCurrentCustomerNumber());

      // logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
       result = QueryServiceUtil.query("queryAccntPayService", queryKey, queryMap);

       //result = (List<Map<String, Object>>) queryService.query("queryPayOrderSum", queryParam);

       return result;
   }
    Object mergeCount(List<Map<String, Object>> list, String key, String clazz){
    	if (clazz.equals("int")) {
    		int result = 0;
    		for(Map<String, Object> map:list ){
    			result +=Integer.parseInt((String) map.get(key));
    		}
    		 return result;
			
		}else{
			BigDecimal result = new BigDecimal("0.00").setScale(2, RoundingMode.DOWN);
    		for(Map<String, Object> map:list ){
    			result = result.add(new BigDecimal((String) map.get(key)).setScale(2, RoundingMode.DOWN));
    			//result +=Float.parseFloat((String) map.get(key));
    		}
    		 return result;
			
		}
	   
   }
    
    private QueryResult queryOrderListHelp(QueryInputParam param, int pageNo, int pageSize) {
        QueryResult result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        //queryMap.put("customerNumber", getCurrentCustomerNumber());

        


        Integer startIndex = (pageNo - 1) * pageSize + 1;

        QueryParam queryParam = new QueryParam();
        queryParam.setParams(queryMap);
        queryParam.setStartIndex(startIndex);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);

        logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

        QueryService queryService = (QueryService) QueryServiceUtil.getBean(
                "queryAccntPayService", QueryService.class);
       result = queryService.query("queryPayOrder", queryParam);

        return result;
    }
   

   
   
}
