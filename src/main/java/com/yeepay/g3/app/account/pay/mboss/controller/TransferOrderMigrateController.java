package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.MigrateTransferOrderReqDTO;
import com.yeepay.g3.app.account.pay.mboss.service.ParamTransferService;
import com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.PublicUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.QueryServiceUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.List;
import java.util.Map;


/**
 * 中台迁移商户的非中台转账
 */
@Controller
@RequestMapping("/transferMigrate")
public class TransferOrderMigrateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(TransferOrderMigrateController.class);
    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认pageNo
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    @Autowired
    private ParamTransferService paramTransferService;

    /**
     * 订单列表查询
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/queryTransferHistory")
    @ResponseBody
    public ResponseMessage queryHistoryOrderList(HttpServletRequest request, HttpServletResponse response,
                                                 MigrateTransferOrderReqDTO param,
                                                 @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                                 @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        logger.info("查询迁移商户的非中台转账列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param), pageSize, pageNo);
        ResponseMessage resMsg = new ResponseMessage("success");
        param.setOrderNo(request.getParameter("orderNo"));
        param.setInnerOrderNo(request.getParameter("innerOrderNo"));
        param.setStartTime(request.getParameter("startTime"));
        param.setEndTime(request.getParameter("endTime"));
        param.setRequestSys(request.getParameter("requestSys"));
        param.setStatus(request.getParameter("status"));

        param.setDebitCustomerNo(getCurrentCustomerNumber());
        param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
        param.setTrxStartTime(request.getParameter("trxStartTime"));
        param.setTrxEndTime(request.getParameter("trxEndTime"));
        param.setTransferWay(request.getParameter("transferWay"));
        param.setAccountNo(request.getParameter("accountNo"));
        param.setRequestNo(request.getParameter("requestNo"));
        param.setTransferType(request.getParameter("transferType"));

        String merchant = getCurrentCustomerNumber();
        String transferWay = request.getParameter("transferWay");
        String queryKey = "queryTransferOrder";
        String querySumKey = "queryTransferOrderSum";
        if (StringUtils.isNotBlank(request.getParameter("mark"))) {
            param.setDebitCustomerNo(merchant);
        }
        logger.info("查询迁移商户的非中台转账列表转换后的入参为" + JSONUtils.toJsonString(param), pageSize, pageNo);
        String path = request.getContextPath();
        try {
            paramTransferService.checkQueryParam(param.getStartTime(),param.getEndTime());
            QueryResult result = this.queryOrderList(param, pageNo, pageSize, queryKey);
            if (result != null && !CheckUtils.isEmpty(result.getData())) {
                for (Map<String, Object> detail : result.getData()) {
                    paramTransferService.adaptTransferReturnResult(detail, pageNo, pageSize, path);
                }
                resMsg.put("pageNo", pageNo);
                resMsg.put("pageSize", pageSize);
                resMsg.put("dataList", result.getData());
            }
            List<Map<String, Object>> list = this.queryOrderListSum(param, querySumKey);
            // 如果查询结果不为空的话
            if (list != null && !list.isEmpty()) {
                Map<String, Object> sumResult = list.get(0);
                NumberFormat nf = NumberFormat.getNumberInstance();
                nf.setMinimumFractionDigits(2);
                nf.setMaximumFractionDigits(2);
                try {
                    resMsg.getData().put("totalCount", (Integer) PublicUtils.mergeCount(list, "SUM_COUNT", "int") + "");// 总笔数
                    resMsg.getData().put("totalAmount", nf.format((BigDecimal) PublicUtils.mergeCount(list, "SUM_AMOUNT", "float")));// 总金额
                    resMsg.getData().put("totalFee", nf.format((BigDecimal) PublicUtils.mergeCount(list, "SUM_FEE", "float")));// 总金额
                } catch (Exception e) {
                    logger.error("查询转账迁移页面报错" + "参数为" + JSONUtils.toJsonString(param) + "错误信息为" + e.getMessage());
                }

            } else {
                resMsg.getData().put("totalCount", 0);// 总笔数
                resMsg.getData().put("totalAmount", 0.00);// 总金额
                resMsg.getData().put("totalFee", 0.00);// 总手续费
            }
        } catch (Exception e) {
            logger.error("查询转账迁移页面报错" + "参数为" + JSONUtils.toJsonString(param) + "错误信息为" + e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }
        return resMsg;
    }

    /**
     * 列表查询
     * @param param
     * @param pageNo
     * @param pageSize
     * @param queryKey
     * @return
     */
    private QueryResult queryOrderList(MigrateTransferOrderReqDTO param, int pageNo, int pageSize, String queryKey) {
        QueryResult result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        QueryParam queryParam = new QueryParam();
        queryParam.setParams(queryMap);
        queryParam.setStartIndex(startIndex);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());
        QueryService queryService = (QueryService) QueryServiceUtil.getBean(
                "queryAccntPayService", QueryService.class);
        result = queryService.query(queryKey, queryParam);

        return result;
    }

    /**
     * 汇总查询
     * @param param
     * @param queryKey
     * @return
     */
    private List<Map<String, Object>> queryOrderListSum(MigrateTransferOrderReqDTO param, String queryKey) {
        List<Map<String, Object>> result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean(
                "queryAccntPayService", QueryService.class);
        result = QueryServiceUtil.query("queryAccntPayService", queryKey, queryMap);
        return result;
    }
}
