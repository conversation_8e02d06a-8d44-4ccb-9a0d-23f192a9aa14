package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.yeepay.g3.app.account.pay.mboss.constant.RemitConstant;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.enumtype.RemitStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.RemitOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.RemitOrganService;
import com.yeepay.g3.app.account.pay.mboss.service.SingleRemitService;
import com.yeepay.g3.app.account.pay.mboss.service.SupplierRemitService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RemitOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.SupplierDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.vo.SupplierRemitReqDTO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.facade.auth2.ocr.dto.IdCardOCRResponse;
import com.yeepay.g3.facade.mer.dto.response.file.MerFileUploadHomeRespDto;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantProductQueryRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantinfoRespDTO;
import com.yeepay.g3.facade.mp.exception.ExceptionWrapper;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.ApplySupplierRequestDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.MgQuerySupplierRequestDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.SupplierAccountInfo;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.SupplierQualInfo;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.ApplySupplierRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.MgQuerySupplierRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.ApplySupplierStatusEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.ReasonTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.SupplierTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.manage.facade.SupplierFacade;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yibao.utils.json.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * @ClassName: SupplierRemitController
 * @Description: 供应商商户后台
 * <AUTHOR>
 * @Date 2022/3/10
 * @Version 1.0
 */
@Controller
@RequestMapping("/supplier")
public class SupplierRemitController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SupplierRemitController.class);


    @Autowired
    private SingleRemitService singleRemitService;

    @Autowired
    private SupplierRemitService supplierRemitService;

    @Autowired
    private RemitOrderService remitOrderService;

    @Autowired
    private RemitOrganService remitOrganService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    @Autowired
    private BusinessCheckRemoteService businessCheckRemoteService;

    @Autowired
    private RemoteService remoteService;

    private SupplierFacade supplierFacade = RemoteServiceFactory.getService(SupplierFacade.class);

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    /**
     * @Description: 供应商查询
     */
    @RequestMapping(value = "/query")
    @ResponseBody
    public BaseRespDTO query(@RequestBody SupplierQueryParam param) {
        logger.info("商户后台供应商查询，请求参数{}", JSONUtils.toJsonString(param));
        MgQuerySupplierRequestDTO mgQuerySupplierRequestDTO = new MgQuerySupplierRequestDTO();
        mgQuerySupplierRequestDTO.setMerchantNo(getCurrentCustomerNumber());
        mgQuerySupplierRequestDTO.setPageNo(param.getPageNo());
        mgQuerySupplierRequestDTO.setPageSize(param.getPageSize());
        mgQuerySupplierRequestDTO.setSupplierName(param.getSupplierName());
        mgQuerySupplierRequestDTO.setReceiverRole("SUPPLIER");
        try {
            MgQuerySupplierRespDTO mgQuerySupplierRespDTO = remitOrganService.mgQuerySupplierPage(mgQuerySupplierRequestDTO);
            return BaseRespDTO.success(mgQuerySupplierRespDTO);
        } catch (YeepayBizException e) {
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            logger.info("商户后台供应商询接口异常,请求参数为={},异常信息为={}", JSONUtils.toJsonString(mgQuerySupplierRequestDTO), e);
            return BaseRespDTO.fail("商户后台供应商查询接口异常");
        }
    }


    /**
     * 供应商下载
     *
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(SupplierQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("商户后台供应商下载，请求参数{}", JSONUtils.toJsonString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            param.setCustomerNumber(getCurrentCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("供应商管理查询数据");
            if (StringUtils.isNotBlank(param.getSupplierName())) {
                param.setSupplierName(AESUtils.encryptDigest(param.getSupplierName()));
            }
            new SupplierDownloadService(getCurrentUser(), param, desc.toString(), "供应商管理查询").download(request, response);
        } catch (Throwable e) {
            logger.info("供应商下载异常，请求参数为={},异常信息为={}", JSONUtils.toJsonString(param), e);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>");
        }
    }


    /**
     * ocr识别反显
     *
     * @param file
     */
    @RequestMapping(value = "/ocrRecogize")
    @ResponseBody
    public ResponseMessage ocrRecogize(@RequestParam("file") MultipartFile file) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        String customerId = getCurrentCustomerNumber();
        byte[] data = null;
        InputStream in = null;
        try {
            in = file.getInputStream();
            data = new byte[in.available()];
            in.read(data);
        } catch (IOException e) {
            logger.info("商户后台供应商图片格式有误", e);
            resMsg.setErrCode("UA0001");
            resMsg.setErrMsg("图片格式有误");
            return resMsg;
        } finally {
            try {
                in.close();
            } catch (Exception e) {
                logger.info("商户后台供应商ocr关闭io流异常={}", e);
            }
        }
        MerFileUploadHomeRespDto uploadFile = remoteService.uploadMerchantUrl(data, file.getOriginalFilename());
        if (uploadFile != null && StringUtils.isNotBlank(uploadFile.getMerQualUrl())) {
            resMsg.put("fileName", uploadFile.getMerQualUrl());
        }else {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("UA0002");
            resMsg.setErrMsg("上传资质文件失败");
            return resMsg;
        }
        IdCardOCRResponse idCardOCRResponse = remoteService.recognize(data, customerId);
        if (idCardOCRResponse != null && "SUCCESS".equals(idCardOCRResponse.getStatus())) {
            resMsg.put("idCard", idCardOCRResponse.getIdCardNumber());
            resMsg.put("name", idCardOCRResponse.getName());
        } else {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode("UA0003");
            resMsg.setErrMsg("请上传正确的身份证照");
        }
        return resMsg;
    }


    /**
     * 去签约付款协议的接口
     *
     * @throws Exception
     */
    @RequestMapping(value = "/queryAgreement")
    @ResponseBody
    public ResponseMessage queryAgreement(@RequestBody SupplierQueryParam param){
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("商户后台去签约付款协议的接口，请求参数{}", JSONUtils.toJsonString(param));
        resMsg.setErrCode(ErrorCode.SUCCESS);
        resMsg.put("remitReason", param.getRemitReason());
        resMsg.put("receiveName", param.getSupplierName());
        resMsg.put("receiveIdcard", param.getSupplierQualLicenceNo());
        MerchantinfoRespDTO merchantInfo = businessCheckRemoteService.queryMerchantInfo(getCurrentCustomerNumber());
        resMsg.put("empower", merchantInfo.getSignedName());
        resMsg.put("delegate", merchantInfo.getCorporationName());
        Calendar calendar = Calendar.getInstance();
        resMsg.put("year", calendar.get(Calendar.YEAR));
        resMsg.put("month", calendar.get(Calendar.MONTH) + 1);
        resMsg.put("day", calendar.get(Calendar.DATE));
        logger.info("商户后台去签约付款协议的接口，返回{}", JSONUtils.toJsonString(resMsg));
        return resMsg;
    }


    /**
     * 增加供应商
     *
     * @param param
     */
    @RequestMapping(value = "/addSupplier")
    @ResponseBody
    public ResponseMessage addSupplier(@RequestBody AddSupplierParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("商户后台增加供应商的接口，请求参数{}", JSONUtils.toJsonString(param));
        resMsg.setErrCode(ErrorCode.SUCCESS);
        try {
            ApplySupplierRequestDTO applySupplierRequestDTO = new ApplySupplierRequestDTO();
            applySupplierRequestDTO.setInitiateMerchantNo(getCurrentCustomerNumber());
            applySupplierRequestDTO.setMerchantNo(getCurrentCustomerNumber());
            applySupplierRequestDTO.setRequestNo("SP" + DateUtil.formatDate(new Date(), "ssyyyyMMddHHmmSSS") + IdGenUtils.getUUIDHashCode(13));
            applySupplierRequestDTO.setInitiateMerchantNo(getCurrentCustomerNumber());
            applySupplierRequestDTO.setReason(param.getReason());
            applySupplierRequestDTO.setReasonType(ReasonTypeEnum.valueOf(param.getReasonType()));
            SupplierAccountInfo supplierAccountInfo = new SupplierAccountInfo();
            supplierAccountInfo.setSupplierName(param.getSupplierName());
            supplierAccountInfo.setSupplierType(SupplierTypeEnum.valueOf(param.getSupplierType()));
            applySupplierRequestDTO.setSupplierAccountInfo(supplierAccountInfo);
            SupplierQualInfo supplierQualInfo = new SupplierQualInfo();
            supplierQualInfo.setSupplierQualLicenceNo(param.getSupplierQualLicenceNo());
            supplierQualInfo.setSupplierQualLicenceUrl(param.getSupplierQualLicenceUrl());
            applySupplierRequestDTO.setSupplierQualInfo(supplierQualInfo);
            applySupplierRequestDTO.setSource("mp");
            logger.info("商户后台增加供应商，请求参数={}", JSONUtils.toJsonString(applySupplierRequestDTO));
            ApplySupplierRespDTO merchantInfo = supplierFacade.applySupplier(applySupplierRequestDTO);
            logger.info("商户后台增加供应商，返回参数={}", JSONUtils.toJsonString(merchantInfo));
            if (merchantInfo != null && "UA00000".equals(merchantInfo.getReturnCode())) {
                if (!ApplySupplierStatusEnum.REJECT.equals(merchantInfo.getApplicationStatus())) {
                    resMsg.setErrMsg("成功");
                    return resMsg;
                }
            }
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(merchantInfo.getReturnCode());
            resMsg.setErrMsg(merchantInfo.getReturnMsg());
            return resMsg;
        } catch (Exception e) {
            return convertException(e, "增加供应商异常");
        }
    }


    /**
     * 供应商下单页面的初始化接口
     *
     */
    @RequestMapping(value = "/remit/init")
    @ResponseBody
    public ResponseMessage init() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        Map<String, Object> map = remitOrganService.getRemitInitInfo(getCurrentCustomerNumber(), RemitConstant.REMIT_SUPPLIER_PRODUCT_CODE);
        //查询余额
        resMsg.put("balance", map.get("balance"));
        //支持的到账时效
        resMsg.put("arriveType", map.get("arriveType"));
        //加密需要的公钥
        resMsg.put("secretPublicKey", map.get("secretPublicKey"));
        resMsg.put("smsCodeBizType", RemitConstant.REMIT_SMS_CODE_TYPE);
        return resMsg;
    }

    /**
     * 供应商下单页面的供应商下拉框查询
     */
    @RequestMapping(value = "/remit/querySupplier")
    @ResponseBody
    public ResponseMessage querySupplier() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        MgQuerySupplierRequestDTO mgQuerySupplierRequestDTO = new MgQuerySupplierRequestDTO();
        mgQuerySupplierRequestDTO.setMerchantNo(getCurrentCustomerNumber());
        mgQuerySupplierRequestDTO.setPageNo(1);
        mgQuerySupplierRequestDTO.setPageSize(100);
        mgQuerySupplierRequestDTO.setReceiverRole("SUPPLIER");
        try {
            List<SupplierSearchResDTO> searchResDTOS = remitOrganService.mgQuerySupplier(mgQuerySupplierRequestDTO);
            resMsg.put("supplierList", searchResDTOS);
        } catch (Exception e) {
            logger.info("付款到个人查询下拉框接口异常,异常信息为={}", e);
            return convertException(e, "查询付款到个人异常");
        }
        return resMsg;
    }


    /**
     * 供应商付款下单前的参数预校验
     *
     * @param supplierRemitReqDTO
     * @return
     */
    @RequestMapping("/remit/checkParam")
    @ResponseBody
    public BaseRespDTO checkParam(@RequestBody SupplierRemitReqDTO supplierRemitReqDTO) {
        try {
            logger.info("供应商付款下单参数预校验：supplierRemitReqDTO:{}", JsonUtils.toJson(supplierRemitReqDTO));
            supplierRemitReqDTO.validateParam();
            if (StringUtils.isNotBlank(supplierRemitReqDTO.getRequestNo())) {
                ShiroUser user = super.getCurrentUser();
                singleRemitService.checkRequestNo(supplierRemitReqDTO.getRequestNo(), user.getCustomerNumber());
            }
            return BaseRespDTO.success();
        } catch (Exception ex) {
            logger.info("下单前的参数预校验，返回信息为={}", ex.getMessage());
            return BaseRespDTO.fail(ex.getMessage());
        }

    }


    /**
     * 供应商付款下单
     *
     * @param supplierRemitReqDTO
     * @return
     */
    @RequestMapping("/remit/confirm")
    @ResponseBody
    public BaseRespDTO single(HttpServletRequest request, @RequestBody SupplierRemitReqDTO supplierRemitReqDTO) {
        logger.info("供应商付款下单参数：supplierRemitReqDTO:{}", JsonUtils.toJson(supplierRemitReqDTO));
        ShiroUser user = super.getCurrentUser();
        checkArgument(StringUtils.isNotBlank(supplierRemitReqDTO.getPasswd()));
        checkArgument(StringUtils.isNotBlank(supplierRemitReqDTO.getSmsCode()));
        try {
            //交易密码改为密文传输，需要解密
            String decryptPassWord = BACRsaUtil.privateDecrypt(supplierRemitReqDTO.getPasswd(), ConfigUtils.getPrivateKey());
            //1.验证密码
            if (!userFacade.validateTradePassword(user.getUserId(), decryptPassWord)) {
                logger.info("供应商付款验证密码失败，请求参数为={}", supplierRemitReqDTO.toString());
                return BaseRespDTO.fail("交易密码不正确");
            }
            //短信验证
            SendSmsUtils.checkVaildFrequency(request, user.getUserId(), supplierRemitReqDTO.getSmsCode(), RemitConstant.REMIT_SMS_CODE_TYPE);
            RemitRespDTO remitRespDTO = remitOrganService.initiateSupplierRemit(supplierRemitReqDTO, getCurrentCustomerNumber(), getCurrentUserSafe().getLoginName());
            if (remitRespDTO != null && "UA00000".equals(remitRespDTO.getReturnCode())) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("requestNo", remitRespDTO.getRequestNo());
                resultMap.put("orderNo", remitRespDTO.getOrderNo());
                resultMap.put("status", remitRespDTO.getStatus());
                resultMap.put("orderTime", DateUtil.date2String(new Date(), "yyyy-MM-dd HH:mm:ss"));
                return BaseRespDTO.success(resultMap);
            } else {
                return BaseRespDTO.fail(remitRespDTO.getReturnMsg());
            }
        } catch (ExceptionWrapper e) {
            logger.info("供应商付款短信验证失败，请求参数为={}", supplierRemitReqDTO.toString());
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            logger.info("供应商付款下单异常，requestNo={}，异常信息={}", supplierRemitReqDTO.getRequestNo(), e.getMessage());
            return BaseRespDTO.fail("付款到个人下单异常，请核实出资情况，避免重复出资风险！");
        }
    }


    /**
     * 供应商订单列表查询
     *
     * @param remitQueryParam
     * @return
     */
    @RequestMapping("/remit/query")
    @ResponseBody
    public ResponseMessage remitQuery(@RequestBody RemitQueryParam remitQueryParam) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("供应商订单列表查询，请求参数{}", JSONUtils.toJsonString(remitQueryParam));
        try {
            if (remitQueryParam.isEmptyCheck()) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg("查询参数为空");
                return resMsg;
            }
            QueryResult queryResult = remitOrganService.queryRemitOrderList(remitQueryParam, getCurrentCustomerNumber(), RemitConstant.REMIT_SUPPLIER_PRODUCT_CODE);
            resMsg.put("dataList", queryResult.getData());
            //查询汇总信息
            resMsg = supplierRemitService.queryRemitOrderListSum(remitQueryParam, resMsg, getCurrentCustomerNumber(), RemitConstant.REMIT_SUPPLIER_PRODUCT_CODE);
            resMsg.put("pageNo", remitQueryParam.getPageNo());
            resMsg.put("pageSize", remitQueryParam.getPageSize());
        } catch (Exception e) {
            logger.error("供应商订单查询异常,请求参数为={},e={}", JSONUtils.toJsonString(remitQueryParam), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }
        return resMsg;
    }

    /**
     * 供应商付款列表下载
     *
     * @param param
     * @return
     */
    @RequestMapping("/remit/download")
    @ResponseBody
    public void download(RemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("开始下载付款记录，请求参数={}", JSONUtils.toJsonString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            param.setCustomerNumber(getCurrentCustomerNumber());
            param = dealParams(param);
            param.setFirstProductCode("PAYMENT_SUPPLIER");
            if (StringUtils.isEmpty(param.getCreateStartDate()) || StringUtils.isEmpty(param.getCreateEndDate())) {
                if (StringUtils.isEmpty(param.getBatchNo()) && StringUtils.isEmpty(param.getRequestNo())) {
                    //如果时间范围是空的,那么批次号和订单号必填
                    throw UnionAccountException.PARAM_REQUIRED_ERROR.newInstance("缺少必要的请求参数");
                }
            }
            StringBuilder desc = new StringBuilder();
            desc.append("付款订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            new RemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-", remitOrderService, merchantRemoteService,false).download(request, response);
            logger.info("下载付款记录excel已完成");
        } catch (Throwable ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    private RemitQueryParam dealParams(RemitQueryParam param) {
        String status = param.getStatus();
        if (RemitStatusEnum.REQUEST_ACCEPT.name().equals(status)) {
            status = RemitStatusEnum.REQUEST_ACCEPT.name() + "," + RemitStatusEnum.REQUEST_RECEIVE.name();
            param.setStatus(status);
        }

        if (StringUtils.isNotBlank(param.getCreateEndDate())) {
            param.setCreateEndDate(DateUtil.addDay(param.getCreateEndDate()));
        }
        return param;
    }


    /**
     * 供应商付款订单详情查询
     * @param request
     * @return
     */
    @RequestMapping(value = "/remit/queryDetail")
    @ResponseBody
    public ResponseMessage queryRemitDetail(HttpServletRequest request) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String orderNo = request.getParameter("orderNo");
        String batchNo = request.getParameter("batchNo");
        String requestNo = request.getParameter("requestNo");
        RemitResponseParam responseParam = remitOrganService.queryDetail(orderNo, batchNo, requestNo, getCurrentCustomerNumber());
        logger.info("查询供应商付款订单详情，remitResponseParam={}", JSONUtils.toJsonString(responseParam));
        resMsg.put("orderDetail", responseParam);
        return resMsg;
    }


    /**
     * 供应商管理页面
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("20220317123")
    @RequestMapping("/supplierManage")
    public ModelAndView supplierManage(HttpServletRequest request){
        ModelAndView mv = new ModelAndView("supplierRemit/supplierManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("供应商管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    /**
     * 供应商付款
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/supplierRemit")
    public ModelAndView supplierRemit(HttpServletRequest request){
        ModelAndView mv = new ModelAndView("supplierRemit/supplierRemit");
        String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
        //判断供应商付款产品
        Boolean checkOpenProduct = checkOpenProduct(getCurrentCustomerNumber(), markProductCode);
        mv.addObject("hasAvailableStatus", true);
        AccountInfoRespDTO responseDto = businessCheckRemoteService.accountStatusAndBalance(getCurrentCustomerNumber(), AccountTypeEnum.FUND_ACCOUNT);
        if (responseDto != null) {
            String accountStatus = responseDto.getAccountStatus();
            if (!("AVAILABLE".equals(accountStatus) || "FROZEN_CREDIT".equals(accountStatus))) {
                mv.addObject("hasAvailableStatus", false);
            }
        }
        mv.addObject("checkOpenProduct", checkOpenProduct);
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("供应商管理页面菜单,{}", request.getSession().getAttribute("tabMenu"));
        return mv;
    }



    private boolean checkOpenProduct(String currentCustomerNumber, String markProductCode) {
        boolean checkOpenProduct = false;
        MerchantProductQueryRespDTO respDto = businessCheckRemoteService.queryMerchantProduct(currentCustomerNumber, "ACCOUNT", markProductCode, "PAYMENT_SUPPLIER", null);
        if ("0000".equals(respDto.getRetCode())) {
            checkOpenProduct = true;
        }
        return checkOpenProduct;
    }


    /**
     * 供应商付款记录
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/supplierRemitRecord")
    public ModelAndView supplierRemitRecord(HttpServletRequest request){
        ModelAndView mv = new ModelAndView("supplierRemit/supplierRemitRecord");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("供应商付款记录页面菜单,{}", request.getSession().getAttribute(("tabMenu")));
        return mv;
    }

    /**
     * 异常转换
     *
     * @param e
     * @param msg
     * @return
     */
    private ResponseMessage convertException(Exception e, String msg) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
        if (e instanceof YeepayBizException) {
            resMsg.setErrCode(((YeepayBizException) e).getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } else {
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(msg);
        }
        return resMsg;
    }
}
