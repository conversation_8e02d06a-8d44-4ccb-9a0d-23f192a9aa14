package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import java.io.Serializable;

public class AliChannelBookDetailRspDTO implements Serializable {
    /**
     * 账号
     */
    private String accountNo;
    /**
     * 户名
     */
    private String accountName;
    /**
     * 支行
     */
    private String branchName;
    /**
     * 开户行
     */
    private String bankName;
    /**
     * 开户地
     */
    private String city;
    /**
     * 联行号
     */
    private String branchBankCode;
    /**
     * 卡状态
     */
    private String cardStatus;
    /**
     * 卡状态
     */
    private String cardStatusDesc;

    /**
     * 余额
     */
    private String balance;

    public String getCardStatusDesc() {
        return cardStatusDesc;
    }

    public void setCardStatusDesc(String cardStatusDesc) {
        this.cardStatusDesc = cardStatusDesc;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getBranchBankCode() {
        return branchBankCode;
    }

    public void setBranchBankCode(String branchBankCode) {
        this.branchBankCode = branchBankCode;
    }

    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }
}
