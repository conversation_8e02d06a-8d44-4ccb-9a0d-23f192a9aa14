package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.utils.common.MaskUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by shxj on 2017/5/3.
 */
@Controller
@RequestMapping("/send")
public class SendController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(SendController.class);

    /**
     * 发送验证码
     *
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/sendMessage")
    @ResponseBody
    public ResponseMessage sendCode(HttpServletRequest request, Model model) {
        // 获取参数
    	String bizType = request.getParameter("bizType");
        String loginName = getCurrentUser().getLoginName();
        String mobileNo = getCurrentUser().getBindedMobile();
        logger.info(String.format("send sms code param loginName %s mobileNo %s bizType %s",loginName,mobileNo,bizType));
        ResponseMessage response = SendSmsUtils.sendSmsVerifyCode(request,mobileNo, bizType, this.getCurrentUser().getUserId(), loginName);
        logger.info(String.format("send sms code response %s ", ToStringBuilder.reflectionToString(response)));
        return response;
    }

    /**
     * 新版的发送验证码（为了兼容前端的响应）
     *
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/newSendMessage")
    @ResponseBody
    public BaseRespDTO newSendMessage(HttpServletRequest request, Model model) {
        try {
            BaseRespDTO baseRespDTO = new BaseRespDTO();
            // 获取参数
            String bizType = request.getParameter("bizType");
            String loginName = getCurrentUser().getLoginName();
            String mobileNo = getCurrentUser().getBindedMobile();
            ResponseMessage response = SendSmsUtils.sendSmsVerifyCode(request,mobileNo, bizType, this.getCurrentUser().getUserId(), loginName);
            if("success".equals(response.getStatus())){
                String mobileNum = MaskUtils.maskCellphone(mobileNo);
                HashMap map=new HashMap<>();
                map.put("detailMsg","验证码已发送至"+mobileNum+",请注意查收");
                baseRespDTO.setData(map);
                return BaseRespDTO.success(map);
            }else{
                return BaseRespDTO.fail(response.getErrMsg());
            }
        }catch (Exception ex){
            return BaseRespDTO.fail(ex.getMessage());
        }
    }
}
