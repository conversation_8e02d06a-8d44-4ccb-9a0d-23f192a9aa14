package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.FundTransferParam;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.FundTransferDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DateUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.handler.RemoteFacadeProxyFactory;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.manage.dto.AccBalAndStatusQueryResultDTO;
import com.yeepay.g3.facade.account.manage.enums.AccountGtypeEnum;
import com.yeepay.g3.facade.account.manage.facade.AccountBalanceAndStatusQueryFacade;
import com.yeepay.g3.facade.account.manage.params.BalanceQueryParams;
import com.yeepay.g3.facade.account.pay.dto.AllRulesDTO;
import com.yeepay.g3.facade.account.pay.dto.AllocateRuleDTO;
import com.yeepay.g3.facade.account.pay.dto.RecordRespDTO;
import com.yeepay.g3.facade.account.pay.dto.RuleRespDTO;
import com.yeepay.g3.facade.account.pay.dto.TogetherAndAllocateRecordDTO;
import com.yeepay.g3.facade.account.pay.dto.TogetherRuleDTO;
import com.yeepay.g3.facade.account.pay.enums.OperationTypeEnum;
import com.yeepay.g3.facade.account.pay.enums.OptTypeEnum;
import com.yeepay.g3.facade.account.pay.enums.RequestStatusEnum;
import com.yeepay.g3.facade.account.pay.enums.RuleStatusTypeEnum;
import com.yeepay.g3.facade.account.pay.enums.RuleTypeEnum;
import com.yeepay.g3.facade.account.pay.exception.AccountPayException;
import com.yeepay.g3.facade.account.pay.facade.TogetherAndAllocateFacade;
import com.yeepay.g3.facade.account.pay.params.ManualAllocateRequestParam;
import com.yeepay.g3.facade.account.pay.params.ModifyRuleStatusParam;
import com.yeepay.g3.facade.account.pay.params.QueryRulesParam;
import com.yeepay.g3.facade.account.pay.params.QueryTogetherAndAllocateRecordParam;
import com.yeepay.g3.facade.account.pay.params.QueryTogetherAndAllocateRuleByIdRequestDTO;
import com.yeepay.g3.facade.account.pay.params.SettingAllocateRulesParam;
import com.yeepay.g3.facade.account.pay.params.SettingTogetherRulesParam;
import com.yeepay.g3.facade.account.special.enums.AccountTypeEnum;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationRespDTO;
import com.yeepay.g3.facade.merchant_platform.enumtype.BusinessType;
import com.yeepay.g3.facade.merchant_platform.facade.GroupBusinessRelationFacade;
import com.yeepay.g3.facade.mp.dto.UserDTO;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.utils.config.ConfigUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 集团账户资金划拨管理
 * @date 2019-09-29 19:00
 */
@Controller
@RequestMapping("/fundTransfer")
public class FundTransferController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FundTransferController.class);
    private static final String FUND_TRANSFER_AUTH = "********";
    private GroupBusinessRelationFacade groupBusinessRelationFacade = RemoteServiceFactory.getService(GroupBusinessRelationFacade.class);
    /** 集团账户接口 */
    private TogetherAndAllocateFacade togetherAndAllocateFacade = RemoteServiceFactory.getService(TogetherAndAllocateFacade.class);
    /** 账务接口 */
    private AccountBalanceAndStatusQueryFacade accountBalanceAndStatusQueryFacade = RemoteServiceFactory.getService(AccountBalanceAndStatusQueryFacade.class);
    /** 商户后台接口 */
    private UserFacade userFacade = RemoteFacadeProxyFactory.getService(UserFacade.class);
    @Resource
    private AccountInfoService accountInfoService;
    private static final String ACCOUNT_FUND = "ACCOUNT_FUND";
    @Autowired
    private MerchantRemoteService merchantRemoteService;

    /**
     * 资金划拨记录首页
     *
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping
    @ResponseBody
    public ModelAndView forwardTradeQuery() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("subGroup", getGroupBusinessRelationReqDTO());
        mav.setViewName("fundTransfer/recordList");
        return mav;
    }

    /**
     * 查询资金划拨记录
     *
     * @param fundTransferParam
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/queryRecordList")
    @ResponseBody
    public ResponseMessage queryRecordList(FundTransferParam fundTransferParam) {
        ResponseMessage rmg = new ResponseMessage(ResponseMessage.Status.ERROR);
        String parentMerchantNo = getCurrentCustomerNumber();
        try {
            LOGGER.info("上级集团商编{}查询划拨记录开始,入参{}", parentMerchantNo, JSON.toJSONString(fundTransferParam));
            QueryTogetherAndAllocateRecordParam queryTogetherAndAllocateRecordParam = new QueryTogetherAndAllocateRecordParam();
            queryTogetherAndAllocateRecordParam.setParentMerchantNo(parentMerchantNo);
            queryTogetherAndAllocateRecordParam.setPageNo(fundTransferParam.getPageNo());
            queryTogetherAndAllocateRecordParam.setPerPageNum(fundTransferParam.getPageSize());
            // 如果选择了下级商编，可以不选时间。。。
            if (StringUtils.isNotBlank(fundTransferParam.getSubMerchantNo())) {
                queryTogetherAndAllocateRecordParam.setSubMerchantNo(fundTransferParam.getSubMerchantNo());
            } else {
                if (StringUtils.isBlank(fundTransferParam.getCreateStartDate()) || StringUtils.isBlank(fundTransferParam.getCreateEndDate())) {
                    rmg.setErrMsg("没有选择下级商编，查询时间不能为空");
                    return rmg;
                }
            }
            if (StringUtils.isNotBlank(fundTransferParam.getCreateStartDate())) {
                queryTogetherAndAllocateRecordParam.setStartTime(DateUtil.string2Date(fundTransferParam.getCreateStartDate(), DateUtil.PATTERN_STANDARD19H));
            }
            if (StringUtils.isNotBlank(fundTransferParam.getCreateEndDate())) {
                queryTogetherAndAllocateRecordParam.setEndTime(DateUtil.string2Date(fundTransferParam.getCreateEndDate(), DateUtil.PATTERN_STANDARD19H));
            }
            if (StringUtils.isNotBlank(fundTransferParam.getFundStatus())) {
                queryTogetherAndAllocateRecordParam.setStatus(RequestStatusEnum.valueOf(fundTransferParam.getFundStatus()));
            }
            if (StringUtils.isNotBlank(fundTransferParam.getFundType())) {
                queryTogetherAndAllocateRecordParam.setOperationType(OperationTypeEnum.valueOf(fundTransferParam.getFundType()));
            }
            // 调用企业账户接口查询记录（把sum也都查出来了）
            LOGGER.info("上级集团商编{}调用企业集团账户划拨记录,入参{}", parentMerchantNo, JSON.toJSONString(queryTogetherAndAllocateRecordParam));
            RecordRespDTO recordRespDTO = togetherAndAllocateFacade.queryRecord(queryTogetherAndAllocateRecordParam);
            LOGGER.info("上级集团商编{}调用企业集团账户划拨记录,返参{}", parentMerchantNo, JSON.toJSONString(recordRespDTO));
            List<Map<String, Object>> resultListMap = adaptReturnResult(recordRespDTO.getRecordList());
            rmg.put("dataList", resultListMap);
            rmg.put("sumCount", recordRespDTO);
            rmg.put("totalCount", recordRespDTO.getSumAllocateNum() + recordRespDTO.getSumTogetherNum());
            return rmg;
        } catch (Exception e) {
            LOGGER.error("商编{}查询划拨记录失败{}", parentMerchantNo, e);
            rmg.setErrMsg("查询划拨记录失败");
            return rmg;
        }
    }


    /**
     * 手动资金划拨页面
     *
     * @param request
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/manualAllocationView")
    @ResponseBody
    public ModelAndView manualAllocation(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String parentMerchantNo = getCurrentCustomerNumber();
        String avaibleBalance = (getAccountAvailableBalance(parentMerchantNo, ACCOUNT_FUND)).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        mav.addObject("avaibleBalance", avaibleBalance);
        mav.addObject("subGroup", getGroupBusinessRelationReqDTO());
        mav.setViewName("fundTransfer/manualAllocation");
        return mav;
    }

    /**
     * 确认划拨
     * @param
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/confirmFund")
    @ResponseBody
    public ResponseMessage confirmFund(HttpServletRequest request, @RequestParam(value = "subMer[]") List<String> subMer) {
        ResponseMessage rmg = new ResponseMessage(ResponseMessage.Status.ERROR, null);
        ManualAllocateRequestParam param = new ManualAllocateRequestParam();
        String amount = request.getParameter("amount");
        String tradePwd = request.getParameter("tradePwd");
        String fundOperateType = request.getParameter("allocate");
        LOGGER.info("划拨类型，fundOperateType={}",fundOperateType);
        if (subMer == null || (subMer != null && subMer.size() <= 0)) {
            rmg.setErrMsg("下级机构不能为空");
            return rmg;
        }

        if (StringUtils.isBlank(tradePwd)) {
            rmg.setErrMsg("交易密码不能为空");
            return rmg;
        }

        if(StringUtils.isBlank(fundOperateType)){
            rmg.setErrMsg("划拨类型不能为空");
            return rmg;
        }

        if (!isCurrectPsw(tradePwd)) {
            rmg.setErrMsg("交易密码错误");
            return rmg;
        }

        try {
            BigDecimal bigAmount = new BigDecimal(amount);
            param.setAmount(bigAmount);
        } catch (NumberFormatException num) {
            rmg.setErrMsg("金额格式错误");
            return rmg;
        }

        String merchantNo = getCurrentCustomerNumber();
        param.setOperator(getCurrentUser().getLoginName());
        param.setFundOperateType(fundOperateType);
        param.setParentMerchantNo(merchantNo);
        param.setParentMerchantName(getSignName(merchantNo));
        List<TogetherAndAllocateRecordDTO> allocateList = new ArrayList<TogetherAndAllocateRecordDTO>();
        for (String str : subMer) {
            TogetherAndAllocateRecordDTO dto = new TogetherAndAllocateRecordDTO();
            String[] strs = str.split("-");
            dto.setSubMerchantName(strs[1]);
            dto.setSubMerchantNo(strs[0]);
            allocateList.add(dto);
        }

        param.setAllocateList(allocateList);
        RecordRespDTO recordRespDTO = togetherAndAllocateFacade.manualAllocate(param);
        if (recordRespDTO.getStatus().equals(RequestStatusEnum.SUCCESS)) {
            rmg.setStatus(ResponseMessage.Status.SUCCESS);
        } else {
            rmg.setStatus(ResponseMessage.Status.ERROR);
            rmg.setErrCode(recordRespDTO.getErrorCode());
            rmg.setErrMsg(recordRespDTO.getErrorMsg());
        }
        return rmg;
    }


    /**
     * 查询划拨策略页面
     * @param fundTransferParam
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/strategySettingView")
    @ResponseBody
    public ModelAndView strategyView(FundTransferParam fundTransferParam) {
        ModelAndView mav = new ModelAndView();
        mav.addObject("subGroup", getGroupBusinessRelationReqDTO());
        mav.setViewName("/fundTransfer/strategySettingView");
        return mav;
    }

    /**
     * 查询划拨策略
     * @param fundTransferParam
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/queryStrategy")
    @ResponseBody
    public ResponseMessage queryStrategy(FundTransferParam fundTransferParam) {
        ResponseMessage rmg = new ResponseMessage(ResponseMessage.Status.ERROR);
        String parentMerchantNo =  getCurrentCustomerNumber();
        try {
            LOGGER.info("上级集团商编{}查询划拨策略开始,入参{}", parentMerchantNo, JSON.toJSONString(fundTransferParam));
            QueryRulesParam queryTogetherAndAllocateRecordParam = new QueryRulesParam();
            queryTogetherAndAllocateRecordParam.setParentMerchantNo(parentMerchantNo);
            queryTogetherAndAllocateRecordParam.setPageNo(fundTransferParam.getPageNo());
            queryTogetherAndAllocateRecordParam.setPerPageNum(fundTransferParam.getPageSize());
            //如果选择了下级商编，可以不选时间。。。
            if (StringUtils.isNotBlank(fundTransferParam.getSubMerchantNo())) {
                queryTogetherAndAllocateRecordParam.setSubMerchantNo(fundTransferParam.getSubMerchantNo());

            }
            if (StringUtils.isNotBlank(fundTransferParam.getStrategySetStatus())) {
                queryTogetherAndAllocateRecordParam.setRuleStatusType(RuleStatusTypeEnum.valueOf(fundTransferParam.getStrategySetStatus()));
            }
            if (StringUtils.isNotBlank(fundTransferParam.getFundType())) {
                queryTogetherAndAllocateRecordParam.setOperationType(OperationTypeEnum.valueOf(fundTransferParam.getFundType()));
            }
            //调用企业账户接口查询记录（把sum也都查出来了）
            LOGGER.info("上级集团商编{}查询划拨策略,入参{}", parentMerchantNo, JSON.toJSONString(queryTogetherAndAllocateRecordParam));
            RuleRespDTO recordRespDTO = togetherAndAllocateFacade.queryRules(queryTogetherAndAllocateRecordParam);
            LOGGER.info("上级集团商编{}查询划拨策略,返参{}", parentMerchantNo, JSON.toJSONString(recordRespDTO));
            List<Map<String, Object>> resultListMap = adaptReturnResultStratege(recordRespDTO.getRules());
            rmg.put("dataList", resultListMap);
            rmg.put("totalCount", recordRespDTO.getSumRulesNum());
            return rmg;
        } catch (Exception e) {
            LOGGER.error("商编{}查询划拨记录失败{}", parentMerchantNo, e);
            rmg.setErrMsg("查询划拨记录失败");
            return rmg;
        }
    }

    /**
     * 修改划拨策略页面
     * @param fundTransferParam
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/toChangeStrategy")
    @ResponseBody
    public ModelAndView toChangeStrategy(FundTransferParam fundTransferParam) {
        String fundType = fundTransferParam.getFundType();

        ModelAndView mav = new ModelAndView();

        QueryRulesParam param = new QueryRulesParam();
        param.setOperationType(OperationTypeEnum.valueOf(fundType));
        param.setSubMerchantNo(fundTransferParam.getSubMerchantNo());
        param.setParentMerchantNo(fundTransferParam.getParentMerchantNo());

        QueryTogetherAndAllocateRuleByIdRequestDTO idParam = new QueryTogetherAndAllocateRuleByIdRequestDTO();
        idParam.setId(fundTransferParam.getStrategySetId());
        RuleRespDTO dto = togetherAndAllocateFacade.queryRuleById(idParam);

        List<AllRulesDTO> rules = dto.getRules();
        AllRulesDTO rulesDTO = new AllRulesDTO();
        if (rules != null && rules.size() > 0) {
            rulesDTO = rules.get(0);
        }
        Map<String, Object> map = adaptReturnResultDto(rulesDTO);
        mav.addObject("rule", map);
        if (OperationTypeEnum.ALLOCATE.toString().equals(fundType)) {
            //下拨
            if (!CheckUtils.isEmpty(rulesDTO.getAllocateThresholdAmount())) {
                mav.addObject("stratege1", "1");
            } else {
                mav.addObject("stratege2", "1");
            }
            mav.setViewName("/fundTransfer/modifyStrategyDown");
        } else if (OperationTypeEnum.TOGETHER.toString().equals(fundType)) {
            //上划
            if (RuleTypeEnum.BASE_ON_AMOUNT.equals(rulesDTO.getRuleType())) {
                mav.addObject("stratege1", "1");
            } else {
                if (CheckUtils.isEmpty(rulesDTO.getTogetherAmount())) {
                    mav.addObject("stratege3", "1");
                } else {
                    mav.addObject("stratege2", "1");
                }
            }
            mav.setViewName("/fundTransfer/modifyStrategyUp");

        }
        return mav;
    }


    /**
     * @Description：修改划拨策略
     * <AUTHOR>
     * @Date 2019-10-14 14:13
     * @Param: [fundTransferParam]
     * @Return: com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/doModifyStrateg")
    @ResponseBody
    public ResponseMessage modifyStrateg(HttpServletRequest request) {
        ResponseMessage rmg = new ResponseMessage(ResponseMessage.Status.ERROR, null);
        String fundType = request.getParameter("fundType");
        String id = request.getParameter("id");
        String subMerchantNo = request.getParameter("subMerchantNo");
        String subMerchatName = request.getParameter("subMerchatName");
        String parentMerchantNo = request.getParameter("parentMerchantNo");
        String parentMerchantName = request.getParameter("parentMerchantName");
        String tradePwd = request.getParameter("tradePsw");
        String strategyId = request.getParameter("strategyId");
        String effectiveTime = request.getParameter("effectiveTime");
        String status = request.getParameter("status");
        //策略1
        String allocateAmount = request.getParameter("allocateAmount");
        String thresholdAmount = request.getParameter("thresholdAmount");
        String ruleType = request.getParameter("ruleType");

        //策略2
        String togetherThresholdAmount1 = request.getParameter("togetherThresholdAmount1");
        String reserveAmount1 = request.getParameter("reserveAmount1");
        String togetherAmount2 = request.getParameter("togetherAmount2");
        String reserveAmount3 = request.getParameter("reserveAmount3");
        String ruleType2 = request.getParameter("ruleType2");
        String ruleType3 = request.getParameter("ruleType3");
        if (!isCurrectPsw(tradePwd)) {
            rmg.setErrMsg("交易密码错误");
            return rmg;
        }
        try {
            RuleRespDTO ruleRespDTO = new RuleRespDTO();
            if ("down".equals(fundType)) {
                //下拨接口
                SettingAllocateRulesParam param = new SettingAllocateRulesParam();
                param.setParentMerchantName(parentMerchantName);
                param.setParentMerchantNo(parentMerchantNo);
                List<AllocateRuleDTO> rules = new ArrayList<AllocateRuleDTO>();
                AllocateRuleDTO dto = new AllocateRuleDTO();
                if ("strategy1".equals(strategyId)) {
                    try {
                        BigDecimal thresholdAmountBig = new BigDecimal(thresholdAmount);
                        dto.setRuleType(RuleTypeEnum.BASE_ON_AMOUNT);
                        dto.setThresholdAmount(thresholdAmountBig);
                    } catch (NumberFormatException num) {
                        rmg.setErrMsg("划拨策略1的金额格式错误");
                        return rmg;
                    }

                } else {
                    dto.setRuleType(RuleTypeEnum.valueOf(ruleType));
                }

                try {
                    BigDecimal allocateAmountBig = new BigDecimal(allocateAmount);
                    dto.setAllocateAmount(allocateAmountBig);
                } catch (NumberFormatException num) {
                    rmg.setErrMsg("划拨金额格式错误");
                    return rmg;
                }

                dto.setCreateTime(new Date());
                dto.setEffectiveTime(DateUtil.string2Date(effectiveTime, DateUtil.PATTERN_STANDARD10H));
                dto.setId(Long.valueOf(id));
                dto.setRuleStatusType(RuleStatusTypeEnum.valueOf(status));
                dto.setSubMerchantNo(subMerchantNo);
                dto.setSubMerchatName(subMerchatName);

                rules.add(dto);
                param.setRules(rules);
                ruleRespDTO = togetherAndAllocateFacade.setAllocateRules(param);
            } else if ("up".equals(fundType)) {
                //上划
                SettingTogetherRulesParam param = new SettingTogetherRulesParam();
                param.setParentMerchantName(parentMerchantName);
                param.setParentMerchantNo(parentMerchantNo);
                List<TogetherRuleDTO> rules = new ArrayList<TogetherRuleDTO>();
                TogetherRuleDTO dto = new TogetherRuleDTO();

                if ("strategy1".equals(strategyId)) {
                    try {
                        BigDecimal thresholdAmount1Big = new BigDecimal(togetherThresholdAmount1);
                        BigDecimal reserveAmount1Big = new BigDecimal(reserveAmount1);
                        dto.setRuleType(RuleTypeEnum.BASE_ON_AMOUNT);
                        dto.setThresholdAmount(thresholdAmount1Big);
                        dto.setReserveAmount(reserveAmount1Big);
                    } catch (NumberFormatException num) {
                        rmg.setErrMsg("划拨策略1的金额格式错误");
                        return rmg;
                    }

                } else if ("strategy2".equals(strategyId)) {
                    dto.setRuleType(RuleTypeEnum.valueOf(ruleType2));
                    try {
                        BigDecimal togetherAmount2Big = new BigDecimal(togetherAmount2);
                        dto.setTogetherAmount(togetherAmount2Big);
                    } catch (NumberFormatException num) {
                        rmg.setErrMsg("划拨策略2的金额格式错误");
                        return rmg;
                    }
                } else {
                    dto.setRuleType(RuleTypeEnum.valueOf(ruleType3));
                    try {
                        BigDecimal reserveAmount3Big = new BigDecimal(reserveAmount3);
                        dto.setReserveAmount(reserveAmount3Big);
                    } catch (NumberFormatException num) {
                        rmg.setErrMsg("划拨策略3的金额格式错误");
                        return rmg;
                    }
                }


                dto.setCreateTime(new Date());
                dto.setEffectiveTime(DateUtil.string2Date(effectiveTime, DateUtil.PATTERN_STANDARD10H));
                dto.setId(Long.valueOf(id));
                dto.setRuleStatusType(RuleStatusTypeEnum.valueOf(status));
                dto.setSubMerchantNo(subMerchantNo);
                dto.setSubMerchatName(subMerchatName);

                rules.add(dto);
                param.setRules(rules);
                ruleRespDTO = togetherAndAllocateFacade.setTogetherRules(param);

            }
            if (ruleRespDTO.getStatus().equals(RequestStatusEnum.SUCCESS)) {
                rmg.setStatus(ResponseMessage.Status.SUCCESS);
            } else {
                rmg.setStatus(ResponseMessage.Status.ERROR);
                rmg.setErrCode(ruleRespDTO.getErrorCode());
                rmg.setErrMsg(ruleRespDTO.getErrorMsg());
            }
            return rmg;
        } catch (Exception e) {
            rmg.setStatus(ResponseMessage.Status.ERROR);
            rmg.setErrCode("101010");
            rmg.setErrMsg("系统异常，请稍后重试");
            return rmg;
        }
    }

    /**
     * @Description：设置
     * <AUTHOR>
     * @Date 2019-10-14 14:13
     * @Param: [fundTransferParam]
     * @Return: com.yeepay.g3.app.newframe.response.ResponseMessage
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/doStrategSet")
    @ResponseBody
    public ResponseMessage doStrategSet(HttpServletRequest request, @RequestParam(value = "subMer[]") List<String> subMer) {
        ResponseMessage rmg = new ResponseMessage(ResponseMessage.Status.ERROR, null);
        String fundType = request.getParameter("fundType");
        String parentMerchantNo = getCurrentCustomerNumber();
        String parentMerchantName = getSignName(parentMerchantNo);
        String tradePwd = request.getParameter("tradePsw");
        String strategyId = request.getParameter("strategyId");
        String effectiveTime = request.getParameter("effectiveTime");
        String allocateAmount = request.getParameter("allocateAmount");
        String thresholdAmount = request.getParameter("thresholdAmount");
        String ruleType = request.getParameter("ruleType");
        String togetherThresholdAmount1 = request.getParameter("togetherThresholdAmount1");
        String reserveAmount1 = request.getParameter("reserveAmount1");
        String togetherAmount2 = request.getParameter("togetherAmount2");
        String reserveAmount3 = request.getParameter("reserveAmount3");
        String ruleType2 = request.getParameter("ruleType2");
        String ruleType3 = request.getParameter("ruleType3");
        if (subMer == null || (subMer != null && subMer.size() <= 0)) {
            rmg.setErrMsg("下级机构不能为空");
            return rmg;
        }
        if (!isCurrectPsw(tradePwd)) {
            rmg.setErrMsg("交易密码错误");
            return rmg;
        }
        try {
            RuleRespDTO ruleRespDTO = new RuleRespDTO();
            if ("down".equals(fundType)) {
                //下拨接口
                SettingAllocateRulesParam param = new SettingAllocateRulesParam();
                param.setParentMerchantName(parentMerchantName);
                param.setParentMerchantNo(parentMerchantNo);
                List<AllocateRuleDTO> rules = new ArrayList<AllocateRuleDTO>();
                for (String str : subMer) {
                    //下级机构批量设置
                    AllocateRuleDTO dto = new AllocateRuleDTO();
                    if ("strategydown1".equals(strategyId)) {
                        try {
                            BigDecimal thresholdAmountBig = new BigDecimal(thresholdAmount);
                            dto.setRuleType(RuleTypeEnum.BASE_ON_AMOUNT);
                            dto.setThresholdAmount(thresholdAmountBig);
                        } catch (NumberFormatException num) {
                            rmg.setErrMsg("划拨策略1的金额格式错误");
                            return rmg;
                        }
                    } else {
                        dto.setRuleType(RuleTypeEnum.valueOf(ruleType));
                    }
                    try {
                        BigDecimal allocateAmountBig = new BigDecimal(allocateAmount);
                        dto.setAllocateAmount(allocateAmountBig);
                    } catch (NumberFormatException num) {
                        rmg.setErrMsg("划拨金额格式错误");
                        return rmg;
                    }
                    dto.setCreateTime(new Date());
                    dto.setEffectiveTime(DateUtil.string2Date(effectiveTime, DateUtil.PATTERN_STANDARD10H));
                    String[] strs = str.split("-");
                    dto.setSubMerchantNo(strs[0]);
                    dto.setSubMerchatName(strs[1]);
                    rules.add(dto);
                }
                param.setRules(rules);
                ruleRespDTO = togetherAndAllocateFacade.setAllocateRules(param);
            } else if ("up".equals(fundType)) {
                SettingTogetherRulesParam param = new SettingTogetherRulesParam();
                param.setParentMerchantName(parentMerchantName);
                param.setParentMerchantNo(parentMerchantNo);
                List<TogetherRuleDTO> rules = new ArrayList<TogetherRuleDTO>();
                for (String str : subMer) {
                    TogetherRuleDTO dto = new TogetherRuleDTO();
                    if ("strategyup1".equals(strategyId)) {
                        try {
                            BigDecimal thresholdAmount1Big = new BigDecimal(togetherThresholdAmount1);
                            BigDecimal reserveAmount1Big = new BigDecimal(reserveAmount1);
                            dto.setRuleType(RuleTypeEnum.BASE_ON_AMOUNT);
                            dto.setThresholdAmount(thresholdAmount1Big);
                            dto.setReserveAmount(reserveAmount1Big);
                        } catch (NumberFormatException num) {
                            rmg.setErrMsg("划拨策略1的金额格式错误");
                            return rmg;
                        }

                    } else if ("strategyup2".equals(strategyId)) {
                        dto.setRuleType(RuleTypeEnum.valueOf(ruleType2));
                        try {
                            BigDecimal togetherAmount2Big = new BigDecimal(togetherAmount2);
                            dto.setTogetherAmount(togetherAmount2Big);
                        } catch (NumberFormatException num) {
                            rmg.setErrMsg("划拨策略2的金额格式错误");
                            return rmg;
                        }
                    } else {
                        dto.setRuleType(RuleTypeEnum.valueOf(ruleType3));
                        try {
                            BigDecimal reserveAmount3Big = new BigDecimal(reserveAmount3);
                            dto.setReserveAmount(reserveAmount3Big);
                        } catch (NumberFormatException num) {
                            rmg.setErrMsg("划拨策略3的金额格式错误");
                            return rmg;
                        }
                    }
                    dto.setCreateTime(new Date());
                    dto.setEffectiveTime(DateUtil.string2Date(effectiveTime, DateUtil.PATTERN_STANDARD10H));
                    String[] strs = str.split("-");
                    dto.setSubMerchantNo(strs[0]);
                    dto.setSubMerchatName(strs[1]);
                    rules.add(dto);
                }
                param.setRules(rules);
                ruleRespDTO = togetherAndAllocateFacade.setTogetherRules(param);
            }
            if (ruleRespDTO.getStatus().equals(RequestStatusEnum.SUCCESS)) {
                rmg.setStatus(ResponseMessage.Status.SUCCESS);
            } else {
                rmg.setStatus(ResponseMessage.Status.ERROR);
                rmg.setErrCode(ruleRespDTO.getErrorCode());
                rmg.setErrMsg(ruleRespDTO.getErrorMsg());
            }
            return rmg;
        } catch (Exception e) {
            rmg.setStatus(ResponseMessage.Status.ERROR);
            rmg.setErrCode("101010");
            rmg.setErrMsg("系统异常，请稍后重试");
            return rmg;
        }
    }

    /**
     * 启用/停用划拨策略
     * @param fundTransferParam
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/changeStrategyStatus")
    @ResponseBody
    public ResponseMessage changeStrategyStatus(FundTransferParam fundTransferParam) {
        ResponseMessage rmg = new ResponseMessage(ResponseMessage.Status.ERROR);

        ModifyRuleStatusParam param = new ModifyRuleStatusParam();
        param.setId(fundTransferParam.getStrategySetId());
        param.setRuleStatusType(RuleStatusTypeEnum.valueOf(fundTransferParam.getStrategySetStatus()));

        try {
            RuleRespDTO ruleRespDTO = togetherAndAllocateFacade.modifyRuleStatus(param);
            if (ruleRespDTO.getStatus().equals(RequestStatusEnum.SUCCESS)) {
                rmg.setStatus(ResponseMessage.Status.SUCCESS);
            } else {
                rmg.setStatus(ResponseMessage.Status.ERROR);
                rmg.setErrCode(ruleRespDTO.getErrorCode());
                rmg.setErrMsg(ruleRespDTO.getErrorMsg());
            }
            return rmg;
        } catch (Exception e) {
            rmg.setStatus(ResponseMessage.Status.ERROR);
            rmg.setErrCode("101010");
            rmg.setErrMsg("系统异常，请稍后重试");
            return rmg;
        }
    }

    /**
     * 划拨策略设置and修改页面（上划、下拨）
     * @param fundTransferParam
     * @return
     */
    @RequiresPermissions(FUND_TRANSFER_AUTH)
    @RequestMapping("/strategySet")
    @ResponseBody
    public ModelAndView strategySettingView(FundTransferParam fundTransferParam) {
        ModelAndView mav = new ModelAndView();
        //获取下级机构
        mav.addObject("subGroup", getGroupBusinessRelationReqDTO());
        mav.setViewName("fundTransfer/strategySetBatch");
        return mav;
    }

    /**
     * @Description: 划拨记录下载
     * <AUTHOR>
     * @date 2020-04-15 17:55
     * @param param:
     * @param request:
     * @param response:
     * @return void
     */
    @RequestMapping(value = "/download")
    public void downloadRecord(FundTransferParam param , HttpServletRequest request, HttpServletResponse response) throws Exception{
        try{
            CheckUtils.notEmpty(param.getFileType(),"fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            param.setParentMerchantNo(getCurrentCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("划拨记录查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            new FundTransferDownloadService(getCurrentUser(),param,desc.toString(),"划拨记录查询-").download(request,response);
        }catch (Throwable ex){
            LOGGER.error("downloadRecord-error",ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('"+ex.getMessage()+"')</script>");
        }
    }

    /**
     * @param merchantNo:
     * @param accountType:
     * @return java.math.BigDecimal
     * @Description: 查询账户余额
     * <AUTHOR>
     * @date 2019-10-08 17:19
     */
    public BigDecimal getAccountAvailableBalance(String merchantNo, String accountType) {
        if(accountInfoService.isZhongTaiMerchantNo(merchantNo)){
            LOGGER.info("查询商户{},账户余额,中台商户", merchantNo);
            return accountInfoService.getUnionAccountAvailableBalance(merchantNo).getBalance();
        }else{
            LOGGER.info("查询未迁移商户{},账户余额", merchantNo);
            BalanceQueryParams balanceQueryParams = new BalanceQueryParams();
            balanceQueryParams.setRequestor("account-pay");
            balanceQueryParams.setCustomerNo(merchantNo);
            balanceQueryParams.setAccGennerationType(AccountGtypeEnum.G3_ACCOUNT);
            balanceQueryParams.setAccountType(accountType);
            if (AccountTypeEnum.ACCOUNT_FUND.name().equals(accountType)) {
                balanceQueryParams.setAccGennerationType(AccountGtypeEnum.G2_ACCOUNT);
                balanceQueryParams.setAccountType("SVA");
            }
            try {
                List<AccBalAndStatusQueryResultDTO> queryResultDTOList = accountBalanceAndStatusQueryFacade.queryAccountBalAndStatusByCustomerNoAndAccountType(balanceQueryParams);

                if (null == queryResultDTOList || queryResultDTOList.size() <= 0) {
                    throw AccountPayException.QUERY_ACCOUNT_BALANCE_EXCEPTION.newInstance("查询账务余额异常");
                }

                AccBalAndStatusQueryResultDTO resultDTO = queryResultDTOList.get(0);
                LOGGER.info("查询商户{},账户{}余额返回：{}", JSON.toJSONString(resultDTO));
                return resultDTO.getAvaibleBalance();
            } catch (Exception e) {
                LOGGER.error("调用账户余额查询接口发生异常 ", e);
                throw AccountPayException.QUERY_ACCOUNT_BALANCE_EXCEPTION.newInstance("查询账务余额异常");
            }
        }
    }

    /**
     * @Description：
     * <AUTHOR>
     * @Date 2019-10-11 16:05
     * @Param: []
     * @Return: com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationRespDTO
     */
    private Object getGroupBusinessRelationReqDTO() {
        GroupBusinessRelationReqDTO dtoP = new GroupBusinessRelationReqDTO();
        dtoP.setBusinessType(BusinessType.GROUP_BUSINESS);
        dtoP.setParentNo(getCurrentCustomerNumber());
        dtoP.setSystem("member-center");
        dtoP.setUid(ConfigUtils.getDefaultValue("MERCHANT_SYS_MEMBER_UID", "d5687d981ad94e1aab9185621c1abc55"));
        dtoP.setReqTime(new Date());
        dtoP.setCharSet("UTF-8");
        GroupBusinessRelationRespDTO dto = groupBusinessRelationFacade.queryChildrenMerchant(dtoP);
        LOGGER.info("子商户信息，dto={}", JSON.toJSONString(dto));
        List<GroupBusinessRelationDTO> list = new ArrayList<GroupBusinessRelationDTO>();
        if (dto != null) {
            list = dto.getGroupBusinessRelationDTOList();
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
        }
        List<Map<String, Object>> resultListMap = new LinkedList<Map<String, Object>>();
        for (GroupBusinessRelationDTO group : list) {
            Map<String, Object> returnMap = BeanUtils.toMap(group);
            returnMap.put("showTextTogether", "false");
            returnMap.put("showTextAllocate", "false");
            QueryRulesParam queryTogetherAndAllocateRecordParam = new QueryRulesParam();
            queryTogetherAndAllocateRecordParam.setParentMerchantNo(getCurrentCustomerNumber());
            RuleRespDTO ruleRespDTO = togetherAndAllocateFacade.queryRules(queryTogetherAndAllocateRecordParam);
            if (!CheckUtils.isEmpty(ruleRespDTO)) {
                for (AllRulesDTO rule : ruleRespDTO.getRules()) {
                    if (group.getCustomerNumber().equals(rule.getSubMerchantNo()) && OperationTypeEnum.TOGETHER.equals(rule.getOperationType())) {
                        returnMap.put("showTextTogether", "true");
                    } else if (group.getCustomerNumber().equals(rule.getSubMerchantNo()) && OperationTypeEnum.ALLOCATE.equals(rule.getOperationType())) {
                        returnMap.put("showTextAllocate", "true");
                    }
                }
            }

            resultListMap.add(returnMap);
        }
        return JSON.toJSON(resultListMap);
    }

    /**
     * 判断交易密码是否正确
     *
     * @return
     */
    private boolean isCurrectPsw(String tradePwd) {
        UserDTO userDTO = userFacade.getUserByLoginName(getCurrentUser().getLoginName());
        boolean flagPsw = userFacade.validateTradePassword(userDTO.getId(), tradePwd);
        return flagPsw;
    }


    /**
     * @Description：
     * <AUTHOR>
     * @Date 2019-10-11 11:24
     * @Param: [list]
     * @Return: void
     */
    private List<Map<String, Object>> adaptReturnResultStratege(List<AllRulesDTO> list) {
        List<Map<String, Object>> resultListMap = new ArrayList<Map<String, Object>>();
        for (AllRulesDTO dto : list) {
            Map<String, Object> returnMap = BeanUtils.toMap(dto);
            if (RuleStatusTypeEnum.DISABLE.equals(dto.getStatus())) {
                returnMap.put("ruleStatusTypeName", RuleStatusTypeEnum.DISABLE.getDesc());
            } else if (RuleStatusTypeEnum.ENABLE.equals(dto.getStatus())) {
                returnMap.put("ruleStatusTypeName", RuleStatusTypeEnum.ENABLE.getDesc());
            } else if (RuleStatusTypeEnum.READY.equals(dto.getStatus())) {
                returnMap.put("ruleStatusTypeName", RuleStatusTypeEnum.READY.getDesc());
            }

            if (OperationTypeEnum.ALLOCATE.equals(dto.getOperationType())) {
                returnMap.put("operationTypeName", OperationTypeEnum.ALLOCATE.getDesc());
            } else if (OperationTypeEnum.TOGETHER.equals(dto.getOperationType())) {
                returnMap.put("operationTypeName", OperationTypeEnum.TOGETHER.getDesc());
            }


            StringBuilder stringBuilder = new StringBuilder();
            if (OperationTypeEnum.TOGETHER.equals(dto.getOperationType())) {
                //上划
                //划拨策略适配
                if (RuleTypeEnum.BASE_ON_AMOUNT.equals(dto.getRuleType())) {
                    stringBuilder.append("按金额,当下级机构账户余额达到").append(dto.getTogetherThresholdAmount()).append("元，留存").append(dto.getReserveAmount()).append("元，剩余金额实时划拨");
                } else {
                    if (CheckUtils.isEmpty(dto.getReserveAmount())) {
                        stringBuilder.append("按").append(dto.getRuleType().getDesc()).append(",划拨固定金额").append(dto.getTogetherAmount()).append("元");
                    } else {
                        stringBuilder.append("按").append(dto.getRuleType().getDesc()).append(",留存").append(dto.getReserveAmount()).append("元,剩余全部划拨");
                    }
                }

            } else if (OperationTypeEnum.ALLOCATE.equals(dto.getOperationType())) {
                //下拨
                if (CheckUtils.isEmpty(dto.getAllocateThresholdAmount())) {
                    stringBuilder.append("按").append(dto.getRuleType().getDesc()).append("，划拨金额").append(dto.getAllocateAmount()).append("元");
                } else {
                    stringBuilder.append("按金额,当下级机构账户余额不足").append(dto.getAllocateThresholdAmount()).append("元，划拨金额").append(dto.getAllocateAmount()).append("元");
                }
            }
            if ("FUND_ACCOUNT".equals(dto.getQueriedOperateAccountType()) || /* TODO 这一节判断是上线兼容使用, 看到了就可以删了 */ dto.getQueriedOperateAccountType() == null) {
                returnMap.put("operateAccountTypeName", "资金账户");
            } else if ("SPECIAL_FUND_ACCOUNT".equals(dto.getQueriedOperateAccountType())) {
                returnMap.put("operateAccountTypeName", "专款账户");
            }
            returnMap.put("operateAccountType", dto.getQueriedOperateAccountType());
            returnMap.put("parentMatchingNo", dto.getMatchingNo());
            returnMap.put("subMatchingNo", dto.getParentMatchingNo());
            returnMap.put("strategyName", stringBuilder);
            returnMap.put("createTime", DateUtil.date2String(dto.getCreateTime(), ""));
            returnMap.put("effectiveTime", DateUtil.date2String(dto.getEffectiveTime(), ""));
            resultListMap.add(returnMap);
        }
        return resultListMap;
    }

    /**
     * @Description：
     * <AUTHOR>
     * @Date 2019-10-11 11:24
     * @Param: [list]
     * @Return: void
     */
    private List<Map<String, Object>> adaptReturnResult(List<TogetherAndAllocateRecordDTO> list) {
        List<Map<String, Object>> resultListMap = new ArrayList<Map<String, Object>>();
        for (TogetherAndAllocateRecordDTO dto : list) {
            Map<String, Object> returnMap = BeanUtils.toMap(dto);
            if (OptTypeEnum.AUTO.equals(dto.getAutoOrManual())) {
                returnMap.put("autoOrManualName", "自动");
            } else if (OptTypeEnum.MANUAL.equals(dto.getAutoOrManual())) {
                returnMap.put("autoOrManualName", "人工");
            }
            if (OperationTypeEnum.ALLOCATE.equals(dto.getOperationType())) {
                returnMap.put("operationTypeName", OperationTypeEnum.ALLOCATE.getDesc());
            } else if (OperationTypeEnum.TOGETHER.equals(dto.getOperationType())) {
                returnMap.put("operationTypeName", OperationTypeEnum.TOGETHER.getDesc());
            }
            if (RequestStatusEnum.SUCCESS.equals(dto.getStatus())) {
                returnMap.put("statusName", RequestStatusEnum.SUCCESS.getDesc());
            } else if (RequestStatusEnum.FAIL.equals(dto.getStatus())) {
                returnMap.put("statusName", RequestStatusEnum.FAIL.getDesc());
            }
            //划拨金额小于0
            if (dto.getAmount().compareTo(BigDecimal.ZERO) == -1) {
                returnMap.put("amount", "0.00");
            } else {
                returnMap.put("amount", dto.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }
            returnMap.put("createTime", DateUtil.date2String(dto.getCreateTime(), ""));
            if ("FUND_ACCOUNT".equals(dto.getOperateAccountType()) || /* TODO 这一节判断是上线兼容使用, 看到了就可以删了 */ dto.getOperateAccountType() == null) {
                returnMap.put("operateAccountTypeName", "资金账户");
            } else if("SPECIAL_FUND_ACCOUNT".equals(dto.getOperateAccountType())) {
                returnMap.put("operateAccountTypeName", "专款账户");
            }
            resultListMap.add(returnMap);
        }
        return resultListMap;
    }

    /**
     * @Description：
     * <AUTHOR>
     * @Date 2019-10-11 11:24
     * @Param: [list]
     * @Return: void
     */
    private Map<String, Object> adaptReturnResultDto(AllRulesDTO dto) {
        Map<String, Object> returnMap = BeanUtils.toMap(dto);
        if (dto.getTogetherAmount() != null) {
            returnMap.put("togetherAmount", dto.getTogetherAmount().setScale(2,   BigDecimal.ROUND_HALF_UP).toString());
        }
        if(dto.getAllocateThresholdAmount() != null) {
            returnMap.put("allocateThresholdAmount", dto.getAllocateThresholdAmount().setScale(2,   BigDecimal.ROUND_HALF_UP).toString());
        }
        if(dto.getAllocateAmount() != null) {
            returnMap.put("allocateAmount", dto.getAllocateAmount().setScale(2,   BigDecimal.ROUND_HALF_UP).toString());
        }
        if(dto.getTogetherThresholdAmount() != null) {
            returnMap.put("togetherThresholdAmount", dto.getTogetherThresholdAmount().setScale(2,   BigDecimal.ROUND_HALF_UP).toString());
        }
        if(dto.getReserveAmount() != null) {
            returnMap.put("reserveAmount", dto.getReserveAmount().setScale(2,   BigDecimal.ROUND_HALF_UP).toString());
        }

        returnMap.put("effectiveTimeFormat", DateUtil.date2String(dto.getEffectiveTime(), DateUtil.PATTERN_STANDARD10H));
        return returnMap;
    }

    /**
     * 获取签约名
     */
    private String getSignName(String merchantNo) {
        return merchantRemoteService.getMerchantName(merchantNo);
    }
}
