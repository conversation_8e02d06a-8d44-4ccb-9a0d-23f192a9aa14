package com.yeepay.g3.app.account.pay.mboss.controller.middleground;


import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BindCardTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.BankAccountManageService;
import com.yeepay.g3.app.account.pay.mboss.service.CommonService;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.MpPaymentAccountOpenDetailRespDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.OpenAccountNotifyConfigRespDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.StandardOpenAccountResponseDTO;
import com.yeepay.g3.facade.merchant_platform.dto.LegalPersonRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customerplatform.MerchantShuntQueryRespDTO;
import com.yeepay.g3.facade.merchant_platform.enumtype.SignType;
import com.yeepay.g3.facade.merchant_platform.enumtype.customerManagement.BusinessRoleEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.OpenBankAccountMerchantType;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.utils.lock.Lock;
import com.yeepay.utils.lock.impl.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @ClassName: PaymentBankAccountManageController
 * @Description: 银行账户管理
 * <AUTHOR>
 * @Date 2023/9/25
 * @Version 1.0
 */
@Controller
@Api(tags = "去支付账户银行账户管理-API")
@RequestMapping("/paymentBankAccount")
public class PaymentBankAccountManageController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentBankAccountManageController.class);
    @Autowired
    private RemoteService remoteService;
    @Autowired
    private BankAccountManageService bankAccountManageService;
    @Resource
    private CommonService commonService;

    /**
     * 去支付账户账户管理
     * 此方法为页面跳转方法, 用于打开开户界面
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/bankManage")
    @ApiOperation(hidden = true, value = "银行账户管理")
    public ModelAndView bankPaymentManage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("bankAccount/bankPaymentAccountManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        LOGGER.info("银行账户管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    /**
     * 查询去支付银行开户有没有过
     * 经过一些简单页面交互之后, 检查是否进行过开户操作
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openAccountAlready", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询当前银行是否开户")
    public ResponseMessage openAccountAlready(@RequestParam(value = "bankCode") String bankCode, @RequestParam(value = "accountType") String accountType) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            LOGGER.info("查询当前银行有没有开户，商编为={},银行={}", merchantNo, bankCode);
            BankAccountOpenRecord resp = bankAccountManageService.getPaymentAccountProcessRecord(merchantNo, bankCode, accountType);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            LOGGER.warn("查询当前银行有没有开户,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            LOGGER.error("查询当前银行有没有开户,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 这里是查询了一个什么奇奇怪怪的授权书
     *
     * @param openBankCode
     * @return
     */
    @RequestMapping(value = "/queryOpenAgreementFill", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询协议填充, 1处:parentMerchantName, 2处:openBankName")
    public ResponseMessage queryLegalPersonBindCardInfo(@RequestParam(value = "openBankCode") String openBankCode) {
        LOGGER.info("查询协议填充 openBankCode={}", openBankCode);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            LOGGER.info("查询协议填充 merchantNo={}", merchantNo);
            MerchantRespDTO merchantRespDTO = remoteService.queryMerchantInfo(merchantNo);
            if (BusinessRoleEnum.SETTLED_MERCHANT.name().equals(merchantRespDTO.getRole())) {
                resMsg.put("showAgreement", true);
                String parentMerchantNo = remoteService.queryFirstAndSecondMerchant(merchantNo).getRight();
                MerchantRespDTO parentMerchantInfo = remoteService.queryMerchantInfo(parentMerchantNo);
                resMsg.put("parentMerchantName", parentMerchantInfo.getSignName());
                resMsg.put("openBankName", BankAccountBankCodeEnum.valueOf(openBankCode).getDescription());
            } else {
                resMsg.put("showAgreement", false);
            }
        } catch (Throwable e) {
            LOGGER.error("查询协议信息异常", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg("查询协议信息异常，请稍后重试");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 去支付账户进度查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/openProcess")
    @ApiOperation(hidden = true, value = "银行进度查询")
    public ModelAndView bankPaymentOpenProcess(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("bankAccount/bankPaymentAccountOpen");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        LOGGER.info("银行进度查询页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 去支付账户开户接口
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openBankPayAccount", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("去支付账户开户接口")
    public ResponseMessage openBankPayAccount(@RequestBody BankPayAccountOpenParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("去支付账户开户接口,请求参数={}", JSONUtils.toJsonString(param));
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        if (StringUtils.isEmpty(param.getOpenAccountType())) {
            param.setOpenAccountType(OpenBankAccountMerchantType.ENTERPRISE.name());
        }
        try {
            //校验参数
            param.validateParam();
            switch (param.getOpenBankCode()) {
                case "SUNINGBANK_DIRECT":
                    MerchantShuntQueryRespDTO respDTO = remoteService.queryMerchant(currentMerchant.getMerchantNo());
                    if (SignType.ENTERPRISE.name().equals(respDTO.getSignType())) {
                        param.setOpenAccountType(OpenBankAccountMerchantType.ENTERPRISE.name());
                    } else if (SignType.INDIVIDUAL.name().equals(respDTO.getSignType())) {
                        param.setOpenAccountType(OpenBankAccountMerchantType.INDIVIDUAL_BUSINESS_TYPE.name());
                    } else {
                        throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("目前仅支持企业或个体工商户开户");
                    }
                    param.getSmbBankOpenAccountDTO().validateParam();
                    break;
                default:
                    throw UnionAccountException.PARAM_VALIDATE_ERROR.newInstance("暂不支持该银行");
            }
        } catch (YeepayBizException e) {
            LOGGER.info("去支付账户开户接口,参数校验异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        }
        Lock lock = new RedisLock("bank_pay_account_open" + "_" + currentMerchant.getMerchantNo(), 4);
        try {
            if (lock.tryLock(3)) {
                LOGGER.info("去支付账户开户接口拿到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                param.setMerchantNo(currentMerchant.getMerchantNo());
                param.setMerchantName(currentMerchant.getSignName());
                StandardOpenAccountResponseDTO resp = bankAccountManageService.innerOpenBankAccountPay(param);
                resMsg.put("data", resp);
            } else {
                LOGGER.warn("去支付账户开户接口没有获取到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请勿重复开户，请稍候重试");
            }
        } catch (YeepayBizException e) {
            LOGGER.warn("去支付账户开户接口,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            LOGGER.error("去支付账户开户接口,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                LOGGER.error("释放分布式锁异常为={}", e);
            }
        }
        return resMsg;
    }

    /**
     * 银行开户说明书
     *
     * @throws Exception
     */
    @RequestMapping(value = "/download/bankOpenIntroduce",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("银行开户说明书")
    public void bankOpenIntroduce(@RequestParam(value = "bankCode") String bankCode, HttpServletResponse response) throws IOException {
        try {
            String filePath;
            switch (BankAccountBankCodeEnum.valueOf(bankCode)) {
                case SUNINGBANK_DIRECT:
                    filePath = this.getClass().getResource("/static/银行账户开户填写说明(江苏苏商直连).xlsx").getPath();
                    break;
                default:
                    throw UnionAccountException.PARAM_VALIDATE_ERROR.newInstance("暂不支持该银行");
            }
            commonService.downloadByPath(filePath, response);
        } catch (YeepayBizException e) {
            LOGGER.info("银行账户开户填写说明业务异常", e.getMessage());
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>");
        } catch (Throwable e) {
            LOGGER.error("银行账户开户填写说明下载异常,异常信息为=", e);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('银行账户开户填写说明下载异常')</script>");
        }
    }

    @RequestMapping(value = "/queryLegalPersonInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询法人信息, 姓名:legalName,证件类型:idCardType,证件号码:idCardNo")
    public ResponseMessage queryLegalPersonInfo() {
        try {
            ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
            String merchantNo = getCurrentCustomerNumber();
            LOGGER.info("查询法人信息 merchantNo={}", merchantNo);
            LegalPersonRespDTO legalPersonInfo = remoteService.queryLegalPersonInfo(merchantNo);
            resMsg.put("legalName", legalPersonInfo.getName());
            resMsg.put("idCardType", legalPersonInfo.getLicenseType());
            resMsg.put("idCardNo", legalPersonInfo.getLicenseNo());
            return resMsg;
        } catch (YeepayBizException e) {
            LOGGER.error("查询法人信息业务异常 message={}", e.getMessage());
            ResponseMessage exceptionMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
            exceptionMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            exceptionMsg.setErrMsg(e.getMessage());
            return exceptionMsg;
        } catch (Throwable e) {
            LOGGER.error("查询法人信息异常", e);
            ResponseMessage exceptionMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
            exceptionMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            exceptionMsg.setErrMsg("查询法人信息异常，请稍后重试");
            return exceptionMsg;
        }
    }

    @RequestMapping(value = "/queryBindCardInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询绑卡信息 绑定卡类型:bindCardType,所属银行:bindBankCode,银行账户名:bankAccountName,银行账户号:bindCardNo")
    public ResponseMessage queryBindCardInfo() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String customerSignName = null;
        try {
            String merchantNo = getCurrentCustomerNumber();
            LOGGER.info("查询绑卡信息 merchantNo={}", merchantNo);
            MerchantRespDTO currentMerchant = getCurrentMerchant();
            customerSignName = currentMerchant.getSignName();
            String finalCustomerSignName = customerSignName;
            // 查询卡信息
            List<QueryBankCardDTO> settleCardList = remoteService.queryMerchantSettleCards(merchantNo);
            List<QueryBankCardDTO> filterResultList = settleCardList.stream().filter(item -> finalCustomerSignName.equals(item.getBindAccountName())).filter(item -> BindCardTypeEnum.PUBLIC_CARD == item.getBindCardType()).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(filterResultList)) {
                LOGGER.info("查询结算卡信息未查询到对公卡,降级查询提现对公卡 merchantNo={}", merchantNo);
                String initiateMerchantNo = remoteService.queryFirstAndSecondMerchant(merchantNo).getLeft();
                List<QueryBankCardDTO> withdrawCardList = remoteService.queryWithdrawBankCards(initiateMerchantNo, merchantNo);
                filterResultList = withdrawCardList.stream().filter(item -> finalCustomerSignName.equals(item.getBindAccountName())).filter(item -> BindCardTypeEnum.PUBLIC_CARD == item.getBindCardType()).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(filterResultList)) {
                resMsg.put("bankAccountName", customerSignName);
            } else {
                QueryBankCardDTO queryBankCardDTO = filterResultList.get(0);
                resMsg.put("bindCardType", queryBankCardDTO.getBindCardType().name());
                resMsg.put("bindCardTypeDesc", queryBankCardDTO.getBindCardType().getDescription());
                resMsg.put("bindBankCode", queryBankCardDTO.getBindBankCode());
                resMsg.put("bankAccountName", customerSignName);
                resMsg.put("bindCardNo", queryBankCardDTO.getBindCardNo());
            }

            return resMsg;
        } catch (YeepayBizException e) {
            LOGGER.error("查询绑卡信息业务异常 message={}", e.getMessage());
            resMsg.put("bankAccountName", customerSignName);
            return resMsg;
        } catch (Throwable e) {
            LOGGER.error("查询绑卡信息异常", e);
            resMsg.put("bankAccountName", customerSignName);
            return resMsg;
        }
    }

    /**
     * 去支付账户信息分页查询
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("去支付账户信息分页查询")
    public ResponseMessage queryPaymentOpenInfo(@RequestParam(value = "bankCode", required = false) String bankCode, @RequestParam(value = "pageNo", required = false) Integer pageNo, @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            if (pageNo == null) {
                pageNo = 1;
            }
            if (pageSize == null) {
                pageNo = 10;
            }
            //这一期只支持苏宁
            if (StringUtils.isEmpty(bankCode)) {
                bankCode = BankAccountBankCodeEnum.SUNINGBANK_DIRECT.name();
            }
            LOGGER.info("查询银行开户列表，商编为={},银行={}", merchantNo, bankCode);
            BankAccountRecordRespDTO resp = bankAccountManageService.queryPaymentOpenInfo(merchantNo, bankCode, pageNo, pageSize);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            LOGGER.warn("查询银行开户列表,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            LOGGER.error("查询银行开户列表,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 去支付账户查询详情
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openDetail", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("去支付账户开户详情")
    public ResponseMessage detail(@RequestParam(value = "requestNo") String requestNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            LOGGER.info("查询开户信息详情，商编为={},请求号为={}", merchantNo, requestNo);
            MpPaymentAccountOpenDetailRespDTO resp = bankAccountManageService.mpPaymentOpenDetail(merchantNo, requestNo);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            LOGGER.warn("查询开户信息详情,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            LOGGER.error("查询开户信息详情,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询开立成功去支付银行账户信息
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/bankAccountInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询开立成功去支付银行账户信息")
    public ResponseMessage bankAccountInfo(@RequestParam(value = "bankCode", required = false) String bankCode) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            //这一期只支持苏宁
            if (StringUtils.isEmpty(bankCode)) {
                bankCode = BankAccountBankCodeEnum.SUNINGBANK_DIRECT.name();
            }
            LOGGER.info("查询开立成功银行账户信息，商编为={},银行={}", merchantNo, bankCode);
            List<BankAccountOpenRecord> resp = bankAccountManageService.getPaymentSuccessBankAccount(merchantNo, bankCode);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            LOGGER.warn("查询开立成功银行账户信息,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            LOGGER.error("查询开立成功银行账户信息,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 去支付账户的开户通知配置更新
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation("通知配置保存")
    @RequestMapping(value = "/notify/config/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage updateOpenAccountNotifyConfig(@RequestBody BankPaymentNotifyConfigParam reqDTO) {
        LOGGER.info("修改银行开户通知配置 request={}", new Gson().toJson(reqDTO));
        reqDTO.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentCustomerNumber();
        try {
            OpenAccountNotifyConfigRespDTO resp = bankAccountManageService.updatePaymentOpenNotifyConfig(reqDTO, merchantNo);
            if(!"AM00000".equals(resp.getReturnCode())){
                resMsg.setErrCode(resp.getReturnCode());
                resMsg.setErrMsg(resp.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        } catch (Exception e) {
            LOGGER.error("修改通知配置异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

}
