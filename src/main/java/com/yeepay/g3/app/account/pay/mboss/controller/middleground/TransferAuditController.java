package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.entity.TransferAuditApplyEntity;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.TransferAuditAppliesDownloadService;
import com.yeepay.g3.app.account.pay.mboss.trade.dao.TransferAuditApplyDao;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.vo.RemitAuditResultVo;
import com.yeepay.g3.app.account.pay.mboss.vo.TransferAuditResultVo;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.enumtype.AuditStatusEnum;
import com.yeepay.g3.facade.unionaccount.transfer.dto.request.TransferAuditReqDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.response.TransferAuditRespDTO;
import com.yeepay.g3.facade.unionaccount.transfer.facade.MgTransferFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 转账审核
 * <AUTHOR>
 * @date Create in 22:21 2021-04-25
 * @Version V1.0
 * @company 易宝支付(YeePay)
 */
@Controller
@RequestMapping("/transferAudit")
public class TransferAuditController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(TransferAuditController.class);

    private static final String REDIS_KEY_TRANSFER_AUDIT_APPLY_IDS = "TRANSFER_AUDIT_AUDIT_IDS_%s";
    private static final String  REDIS_KEY_TRANSFER_AUDIT_APPLY_STATUS = "TRANSFER_AUDIT_AUDIT_STATUS_%s";
    public static final String DO_AUDIT_APPLIES_PERMISSIONS_CODE = "20210428002";
    public static final String DO_AUDIT_APPLIES_PAGE_PERMISSION_CODE = "20210428002";
    public static final String DO_QUERY_APPLIES_PAGE_PERMISSION_CODE = "20210428001";

    private static final String SUCCESS_CODE = "UA00000";

    @Autowired
    private TransferAuditApplyDao transferAuditApplyDao;

    private static UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    private static MgTransferFacade mgTransferFacade = RemoteServiceFactory.getService(MgTransferFacade.class);

    /**
     * 跳转到审核列表
     * @param request
     * @return
     */
    @RequiresPermissions(DO_QUERY_APPLIES_PAGE_PERMISSION_CODE)
    @RequestMapping(value = "/page/list")
    public ModelAndView listPage(HttpServletRequest request) {
        String passwordRsaPublicKey = BacRsaKeysHolder.getPasswordRsaPublicKey();
        Map<String, Object> params = new HashMap<>();
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        params.put("passwordRsaPublicKey", passwordRsaPublicKey);
        return new ModelAndView("/transferAudit/list", params);
    }

    /**
     * 跳转到审核通过列表
     * @param request
     * @return
     */
    @RequiresPermissions(DO_AUDIT_APPLIES_PAGE_PERMISSION_CODE)
    @RequestMapping(value = "/page/auditList")
    public ModelAndView auditListPage(HttpServletRequest request) {
        String passwordRsaPublicKey = BacRsaKeysHolder.getPasswordRsaPublicKey();
        Map<String, Object> params = new HashMap<>();
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        params.put("passwordRsaPublicKey", passwordRsaPublicKey);
        return new ModelAndView("/transferAudit/auditList", params);
    }

    /**
     * 查询密钥（迁移红版后台）
     * @return
     */
   // @RequiresPermissions(DO_AUDIT_APPLIES_PAGE_PERMISSION_CODE)
    @ResponseBody
    @RequestMapping(value = "/queryPasswordRsaPublicKey",method = RequestMethod.GET)
    public BaseRespDTO queryPasswordRsaPublicKey() {
        logger.info("[红版商户后台]查询密钥");
        Map<String,Object> map=new HashMap<>();
        String passwordRsaPublicKey = BacRsaKeysHolder.getPasswordRsaPublicKey();
        map.put("passwordRsaPublicKey", passwordRsaPublicKey);
        return BaseRespDTO.success(map);
    }


    /**
     * 跳转到审核结果页面
     *
     * @return
     */
    @RequestMapping(value = "/page/auditResult")
    public ModelAndView auditResultPage(@RequestParam String applyRequestNo) {
        Map<String, Object> params = new HashMap<>();
        String auditStatus = RedisUtils.get(String.format(REDIS_KEY_TRANSFER_AUDIT_APPLY_STATUS,applyRequestNo));
        params.put("applyRequestNo", applyRequestNo);
        params.put("auditStatus",auditStatus);
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("applyRequestNo={},auditStatus={}",applyRequestNo,auditStatus);
        return new ModelAndView("/transferAudit/auditResult", params);
    }

    /**
     * 查询审核结果（迁移红版后台）
     * 走复核申请流程
     * @param applyRequestNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryAuditResult" ,method = RequestMethod.GET)
    public BaseRespDTO queryAuditResult(@RequestParam String applyRequestNo) {
        logger.info("[红版商户后台]查询审核结果 the applyRequestNo=[{}]",JSONObject.toJSONString(applyRequestNo));
        Map<String, Object> map = new HashMap<>();
        String auditStatus = RedisUtils.get(String.format(REDIS_KEY_TRANSFER_AUDIT_APPLY_STATUS, applyRequestNo));
        map.put("applyRequestNo", applyRequestNo);
        map.put("auditStatus", auditStatus);
        logger.info("[红版商户后台]查询审核结果成功 applyRequestNo={},auditStatus={}", applyRequestNo, auditStatus);
        return BaseRespDTO.success(map);
    }

    /**
     * 提交复核申请的结果页面
     *
     * @param requestNo
     * @return
     */
    @RequestMapping(value = "/page/applyResult")
    public ModelAndView applyResultPage(@RequestParam String requestNo) {
        Map<String, Object> params = null;
        try {
             params  = transferAuditApplyDao.queryOrderByRequestNo(requestNo, getCurrentCustomerNumber());
            if (CollectionUtils.isEmpty(params)) {
                throw new RuntimeException("该订单不存在,requestNo=" + requestNo);
            }

        } catch (Exception ex) {
            logger.error("获取转账订单异常，requestNo=" + requestNo, ex);
        }
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        return new ModelAndView("/transferAudit/applyResult", params);
    }

    /**
     * 查询申请结果（迁移红版后台）
     * 没有走复核申请流程
     * @param requestNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryApplyResult",method = RequestMethod.GET)
    public BaseRespDTO queryApplyResult(@RequestParam String requestNo) {
        logger.info("[红版商户后台]查询申请结果 the requestNo=[{}]",requestNo);
        Map<String, Object> params;
        try {
            params = transferAuditApplyDao.queryOrderByRequestNo(requestNo, getCurrentCustomerNumber());
            PreCheck.checkArgument(MapUtils.isNotEmpty(params), "该订单不存在");
        } catch (Exception ex) {
            logger.error("获取转账订单异常，requestNo=" + requestNo, ex);
            return BaseRespDTO.fail("获取转账订单异常");
        }
        logger.info("[红版商户后台]查询申请结果 the result=[{}]",JSONObject.toJSONString(params));
        return BaseRespDTO.success(params);
    }

    /**
     * 获取转账订单
     *
     * @param requestNo
     * @return
     */
    @ResponseBody
    @RequestMapping("/applyResult")
    public BaseRespDTO<Map> applyResult(@RequestParam String requestNo){
        try{
            Map<String, Object> params = transferAuditApplyDao.queryOrderByRequestNo(requestNo, getCurrentCustomerNumber());
            return BaseRespDTO.success(params);
        }catch (Exception ex){
            logger.error("获取转账订单异常，requestNo="+requestNo,ex);
            return BaseRespDTO.fail(ex.getLocalizedMessage());
        }

    }

    /**
     * 获取审核结果数据
     *
     * @param applyRequestNo
     * @return
     */
    @RequestMapping("/getTransferAuditResult")
    @ResponseBody
    public BaseRespDTO<RemitAuditResultVo> getRemitAuditResult(@RequestParam String applyRequestNo) {

        String redisKey = String.format(REDIS_KEY_TRANSFER_AUDIT_APPLY_IDS, applyRequestNo);
        TransferAuditResultVo vo = new TransferAuditResultVo();
        try{
            if (RedisUtils.exists(redisKey)) {
                long n = RedisUtils.llen(redisKey);
                List<String> ids = RedisUtils.lrange(redisKey, 0L, n);
                logger.debug("redisKey={},ids={}",redisKey,ids);
                if(!CollectionUtils.isEmpty(ids)){
                    List<Map<String, Object>> list = transferAuditApplyDao.queryTransferAuditSumByIds4Result(ids);
                    if (!CollectionUtils.isEmpty(list)) {
                        for (Map<String, Object> map : list) {
                            AuditStatusEnum status = AuditStatusEnum.valueOf(String.valueOf(map.get("status")));
                            Long totalCount = Long.valueOf(map.get("totalCount") + "");
                            BigDecimal totalAmount = new BigDecimal(map.get("totalAmount") + "");
                            switch (status) {
                                case AUDIT_PASS:
                                case AUDIT_REFUSED:
                                    vo.setTotalSuccessCount(vo.getTotalSuccessCount() + totalCount);
                                    vo.setTotalSuccessAmount(vo.getTotalSuccessAmount().add(totalAmount));
                                    break;
                                case AUDIT_PASS_BUT_FAIL:
                                    vo.setTotalFailureAmount(vo.getTotalFailureAmount().add(totalAmount));
                                    vo.setTotalFailureCount(vo.getTotalFailureCount() + totalCount);
                                    break;
                                default:
                                    logger.warn("存在未处理完成的复核记录,status={0},totalCount={1},totalAmount={2}", Arrays.asList(status, totalCount, totalAmount));
                            }
                            vo.setTotalCount(vo.getTotalCount() + totalCount);
                            vo.setTotalAmount(vo.getTotalAmount().add(totalAmount));
                        }
                    }
                    return TransferAuditResultDTO.success(vo);
                }
            }
            return TransferAuditResultDTO.fail("未查到信息");
        }catch (Exception ex){
            logger.error("getTransferAuditResult error token="+applyRequestNo,ex);
            return TransferAuditResultDTO.fail(ex.getLocalizedMessage());
        }
    }

    /**
     * 查询余额和获取待审核金额
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getBalanceAndWaitAuditAmount")
    public BaseRespDTO<QueryWaitAuditAmountRespDTO> getBalanceAndWaitAuditAmount() {
        ShiroUser user = getCurrentUser();
        QueryWaitAuditAmountRespDTO respDTO = new QueryWaitAuditAmountRespDTO();
        try {
            // 查询余额
            respDTO.setBalanceAmount(queryBalance("FUND_ACCOUNT", user.getCustomerNumber()));
            Map<String, Object> map = transferAuditApplyDao.queryTransferAuditWaitAuditSumByMerchantNo(user.getCustomerNumber());
            if (CollectionUtils.isEmpty(map) || !map.containsKey("totalCount") || !map.containsKey("totalAmount")) {
                respDTO.setTotalCount(0L);
                respDTO.setTotalAmount(BigDecimal.ZERO);
            } else {
                respDTO.setTotalCount((Long) map.getOrDefault("totalCount", 0L));
                respDTO.setTotalAmount((BigDecimal) map.getOrDefault("totalAmount", BigDecimal.ZERO));
            }
            return BaseRespDTO.success(respDTO);
        } catch (Exception ex) {
            logger.error("获取待复核金额异常" + user, ex);
            return BaseRespDTO.fail(ex.getLocalizedMessage());
        }

    }

    /**
     * 查询账户余额
     *
     * @param accountType:
     * @return java.math.BigDecimal
     * @Description: 查询账户余额，抛异常
     * <AUTHOR>
     * @date 2020-11-02 15:14
     */
    private BigDecimal queryBalance(String accountType, String merchantNo) {
        //查询资金账户余额
        BigDecimal balance = new BigDecimal(BigInteger.ZERO);
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(merchantNo, com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum.valueOf(accountType));
        BigDecimal queryBalance = queryAccountResponseDto.getBalance();
        if (queryBalance != null) {
            balance = queryBalance;
        }
        return balance;
    }

    /**
     * 转账复核列表查询
     * @param reqDto
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list")
    public BaseRespDTO<TransferAuditApplyListRespDTO> list(TransferAuditApplyQueryReqDTO reqDto, HttpServletRequest request) {

        logger.info("转账复核列表查询，reqDto={}", JSON.toJSONString(reqDto));
        ShiroUser user = getCurrentUser();
        TransferAuditApplyListRespDTO resp = new TransferAuditApplyListRespDTO();
        resp.setTotalCount(0);
        resp.setLoginName(user.getLoginName());
        resp.setTotalAmount(BigDecimal.ZERO);
        resp.setData(Collections.emptyList());
        reqDto.setMerchantNo(user.getCustomerNumber());
        try{
            logger.info("转账复核列表查询,数据汇总查询请求参数：{}",JSON.toJSONString(reqDto));
            Map<String, Object> map = transferAuditApplyDao.queryTransferAuditListByPageCount(reqDto);
            logger.info("转账复核列表查询返回,数据汇总信息：{}",JSON.toJSONString(map));
            if (CollectionUtils.isEmpty(map) || !map.containsKey("totalCount") || !map.containsKey("totalAmount")) {
                return BaseRespDTO.success(resp);
            }

            Integer totalCount = Integer.valueOf(StringUtils.defaultIfEmpty(map.get("totalCount").toString(), "0"));
            BigDecimal totalAmount = new BigDecimal(StringUtils.defaultIfEmpty(map.get("totalAmount").toString(), "0.0"));
            reqDto.doPage();
            logger.info("查询转账复核列表，请求参数：{}",JSON.toJSONString(reqDto));
            List<TransferAuditApplyEntity> list = transferAuditApplyDao.queryTransferAuditListByPage(reqDto);
            logger.info("查询转账复核列表返回，list={}", JSON.toJSONString(list));
            resp.setData(list.stream().map(e -> builTransferAuditApplydDTO(e)).collect(Collectors.toList()));
            resp.setTotalAmount(totalAmount);
            resp.setTotalCount(totalCount);
        }catch (Exception e){
            logger.error("查询待复核转账订单异常",e);
        }

        logger.info("转账复核列表返回，resp={}", JSON.toJSONString(resp));
        return BaseRespDTO.success(resp);
    }

    /**
     * 转账复核审核
     * @param reqDto
     * @param request
     * @return
     */
    @RequiresPermissions(DO_AUDIT_APPLIES_PERMISSIONS_CODE)
    @ResponseBody
    @RequestMapping(value = "/doTransferAudit", method = RequestMethod.POST)
    public BaseRespDTO<AuditAppliesResultDTO> doAuditApplies(@RequestBody TransferAuditApplyAuditReqDto reqDto, HttpServletRequest request) {
        AuditAppliesResultDTO resultDTO = new AuditAppliesResultDTO();
        ShiroUser user = getCurrentUser();
        logger.info("doTransferAudit user={},req={}", user.getLoginName(), reqDto);
        if (AuditStatusEnum.AUDIT_REFUSED.equals(reqDto.getAuditStatus()) && StringUtils.isEmpty(reqDto.getFailReason())) {
            return BaseRespDTO.fail("复核拒绝时，拒绝原因必填！");
        }
        // 校验交易密码
        boolean passResult = false;
        try {
            String pass = BACRsaUtil.privateDecrypt(reqDto.getPassword(), BacRsaKeysHolder.getPasswordRsaPrivateKey());
            passResult = userFacade.validateTradePassword(user.getUserId(), pass);
        } catch (Throwable t) {
            logger.error("密码解密失败", t);
        }
        if (!passResult) {
            logger.info("doTransferAudit 密码错误 user={}", user.getLoginName(), resultDTO);
            return BaseRespDTO.fail("密码错误");
        }
        String token = UUID.randomUUID().toString();
        String redisKey = String.format(REDIS_KEY_TRANSFER_AUDIT_APPLY_IDS, token);
        RedisUtils.lpush(redisKey, reqDto.getAuditApplyIds().stream().map(e -> e.toString()).collect(Collectors.toList()), 60 * 60);
        RedisUtils.set(String.format(REDIS_KEY_TRANSFER_AUDIT_APPLY_STATUS,token),reqDto.getAuditStatus().name(),60*60);

        TransferAuditReqDTO transferAuditReqDto = new TransferAuditReqDTO();
        transferAuditReqDto.setMerchantNo(user.getCustomerNumber());
        transferAuditReqDto.setAuditApplyIds(reqDto.getAuditApplyIds());
        transferAuditReqDto.setAuditStatusEnum(reqDto.getAuditStatus());
        transferAuditReqDto.setOperator(user.getLoginName());
        transferAuditReqDto.setFailReason(reqDto.getFailReason());
        logger.info("转账复核请求， transferAuditReqDto={}", transferAuditReqDto);
        TransferAuditRespDTO transferAuditRespDTO = mgTransferFacade.transferAudit(transferAuditReqDto);
        logger.info("转账复核返回 transferAuditRespDTO={},token={}", transferAuditRespDTO,token);
        if (SUCCESS_CODE.equals(transferAuditRespDTO.getReturnCode())) {
            resultDTO.setApplyRequestNo(token);
            return BaseRespDTO.success(resultDTO);
        } else {
            return BaseRespDTO.fail(transferAuditRespDTO.getReturnMsg());
        }

    }

    /**
     * 下载总数
     * @param downloadReqDTO
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/downloadCount")
    public BaseRespDTO downloadCount(TransferAuditApplyDownloadReqDTO downloadReqDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("下载请求:{}", downloadReqDTO);
        try {
            Map<String, Object> params = TransferAuditAppliesDownloadService.buildParameters(downloadReqDTO);
            params.put("merchantNo",getCurrentCustomerNumber());
            int count = transferAuditApplyDao.queryTransferAuditCount4Download(params);
            Integer asyncCount=Integer.valueOf(UniformConfigUtils.getConfigStringValue("DOWNLOAD_ASYNC_CNT", "10000"));
            JSONObject jo = new JSONObject();
            jo.put("totalCount",count);
            jo.put("isAsyncDownload",count>asyncCount);
            return BaseRespDTO.success(jo);
        } catch (Exception ex) {
            logger.error("下载异常，ex={}", ex);
            return BaseRespDTO.fail(ex.getMessage());
        }
    }

    /**
     * 下载
     * @param downloadReqDTO
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/download")
    public void download(TransferAuditApplyDownloadReqDTO downloadReqDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("下载请求:{}", downloadReqDTO);
        try {
            MerchantRemoteService merchantRemoteService = new MerchantRemoteService();
            new TransferAuditAppliesDownloadService(getCurrentUser(), downloadReqDTO, "转账复核申请下载", "转账复核申请下载", transferAuditApplyDao,merchantRemoteService)
                .download(request, response);
        } catch (Exception ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    private TransferAuditApplyDTO builTransferAuditApplydDTO(TransferAuditApplyEntity entity) {
        TransferAuditApplyDTO transferAuditApplyDTO = new TransferAuditApplyDTO();
        transferAuditApplyDTO.setBatchNo(entity.getBatchNo());
        transferAuditApplyDTO.setId(entity.getId().toString());
        transferAuditApplyDTO.setCreateTime(entity.getCreateTime());
        transferAuditApplyDTO.setLastModifyTime(entity.getLastModifyTime());
        transferAuditApplyDTO.setMerchantNo(entity.getMerchantNo());
        transferAuditApplyDTO.setStatus(entity.getStatus());
        transferAuditApplyDTO.setAuditOperator(entity.getAuditOperator());
        transferAuditApplyDTO.setApplyOperator(entity.getApplyOperator());
        transferAuditApplyDTO.setOrderNo(entity.getOrderNo());
        transferAuditApplyDTO.setRequestNo(entity.getRequestNo());
        transferAuditApplyDTO.setTransferAmount(entity.getTransferAmount());
        transferAuditApplyDTO.setReturnCode(entity.getReturnCode());
        transferAuditApplyDTO.setReturnMessage(entity.getReturnMessage());
        transferAuditApplyDTO.setSalesProductCode(entity.getSalesProductCode());
        transferAuditApplyDTO.setClientIp(entity.getClientIp());
        transferAuditApplyDTO.setReqSource(entity.getReqSource());
        transferAuditApplyDTO.setToCustomerNo(entity.getToCustomerNo());
        transferAuditApplyDTO.setToCustomerName(entity.getToCustomerName());
        transferAuditApplyDTO.setFee(entity.getFee());
        transferAuditApplyDTO.setFromAccountType(entity.getFromAccountType());
        transferAuditApplyDTO.setFromCustomerName(entity.getFromCustomerName());
        transferAuditApplyDTO.setFeeCustomerNo(entity.getFeeCustomerNo());
        transferAuditApplyDTO.setFromCustomerNo(entity.getFromCustomerNo());
        transferAuditApplyDTO.setToAccountType(entity.getToAccountType());
        transferAuditApplyDTO.setUsage(entity.getUsage());
        transferAuditApplyDTO.setFeeDeductType(entity.getFeeDeductType());
        transferAuditApplyDTO.setFeeChargeType(entity.getFeeChargeType());
        transferAuditApplyDTO.setTransferAccountType(entity.getTransferAccountType());
        transferAuditApplyDTO.setCompletedTime(entity.getCompletedTime());
        transferAuditApplyDTO.setTransferType(entity.getTransferType());
        transferAuditApplyDTO.setDeductionAmount(entity.getDeductionAmount());
        transferAuditApplyDTO.setEnteredAmount(entity.getEnteredAmount());

        return transferAuditApplyDTO;
    }

}
