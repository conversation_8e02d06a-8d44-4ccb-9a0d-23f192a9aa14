package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yeepay.g3.app.account.pay.mboss.utils.file.ExcelField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ChannelBookChargeRecordDTO implements Serializable {
    /**
     * 商编
     */
    @ExcelField(title = "商编", order = 1)
    private String merchantNo;
    /**
     * 渠道记账本id
     */
    @ExcelField(title = "记账本id", order = 2)
    private String channelBookId;
    /**
     * 渠道记账本名称
     */
    @ExcelField(title = "记账本名称", order = 3)
    private String channelBookName;

    /**
     * 创建时间
     */
    @ExcelField(title = "划入时间", order = 4)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后修改时间
     */
    @ExcelField(title = "划入完成时间", order = 5)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastModifyTime;
    /**
     * 划拨金额
     */
    @ExcelField(title = "划入金额", order = 6)
    private BigDecimal transferAmount;
    /**
     * 备注
     */
    @ExcelField(title = "备注", order = 7)
    private String remark;
    /**
     * 状态
     */
    private String status;

    /**
     * 状态
     */
    @ExcelField(title = "状态", order = 8)
    private String statusDesc;
    /**
     * 易宝唯一号
     *
     * @return
     */
    @ExcelField(title = "易宝订单号", order = 0)
    private String orderNo;

    /**
     * 渠道订单号
     *
     * @return
     */
    private String channelNo;

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getChannelBookName() {
        return channelBookName;
    }

    public void setChannelBookName(String channelBookName) {
        this.channelBookName = channelBookName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public BigDecimal getTransferAmount() {
        return transferAmount;
    }

    public void setTransferAmount(BigDecimal transferAmount) {
        this.transferAmount = transferAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }
}
