package com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Mr.yin
 * @date: 2024/7/24  17:27
 */
@ApiModel(description = "绑卡信息")

public class BindCardModel implements Serializable {
    private static final long serialVersionUID = -1L;
    @ApiModelProperty(value = "卡账户名称 掩码")
    private String name;
    //卡类型
    @ApiModelProperty(value = "卡类型")
    private String cardType;

    //卡类型
    @ApiModelProperty(value = "卡类型  中文展示")
    private String cardTypeDesc;

    @ApiModelProperty(value = "银行编码")
    private String bankCode;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "卡号 掩码")
    private String cardNo;

    @ApiModelProperty(value = "绑卡ID字符串")
    private String bindId;

    @ApiModelProperty(value = "银行图标前端组件 logoCode")
    private String logoCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardTypeDesc() {
        return cardTypeDesc;
    }

    public void setCardTypeDesc(String cardTypeDesc) {
        this.cardTypeDesc = cardTypeDesc;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getBindId() {
        return bindId;
    }

    public void setBindId(String bindId) {
        this.bindId = bindId;
    }

    public String getLogoCode() {
        return logoCode;
    }

    public void setLogoCode(String logoCode) {
        this.logoCode = logoCode;
    }
}
