package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.yeepay.g3.app.account.pay.mboss.constant.RemitConstant;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.enumtype.RemitStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.service.RemitOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.RemitOrganService;
import com.yeepay.g3.app.account.pay.mboss.service.SingleRemitService;
import com.yeepay.g3.app.account.pay.mboss.service.SupplierRemitService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.IndividualDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RemitOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.vo.SupplierRemitReqDTO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.utils.SendSmsUtils;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.MerchantProductQueryRespDTO;
import com.yeepay.g3.facade.mp.exception.ExceptionWrapper;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.ApplyIndividualRequestDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.MgQuerySupplierRequestDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.ApplyIndividualRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.MgQuerySupplierRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.ApplySupplierStatusEnum;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.SupplierQualTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.manage.facade.IndividualFacade;
import com.yeepay.g3.facade.unionaccount.trade.dto.response.RemitRespDTO;
import com.yeepay.g3.facade.unionaccount.trade.exception.UnionAccountException;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yibao.utils.json.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * @ClassName: IndividualRemitController
 * @Description: 付款到个人商户后台
 * <AUTHOR>
 * @Date 2023/6/12
 * @Version 1.0
 */
@Controller
@RequestMapping("/individual")
public class IndividualRemitController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(IndividualRemitController.class);

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    @Autowired
    private RemitOrganService remitOrganService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    @Autowired
    private BusinessCheckRemoteService businessCheckRemoteService;

    @Autowired
    private SupplierRemitService supplierRemitService;

    @Autowired
    private RemitOrderService remitOrderService;

    @Autowired
    private SingleRemitService singleRemitService;

    private IndividualFacade individualFacade = RemoteServiceFactory.getService(IndividualFacade.class);




    /**
     * @Description: 付款到个人查询
     */
    @RequestMapping(value = "/query")
    @ResponseBody
    public BaseRespDTO query(@RequestBody SupplierQueryParam param) {
        logger.info("付款到个人查询，请求参数{}", JSONUtils.toJsonString(param));
        MgQuerySupplierRequestDTO mgQuerySupplierRequestDTO = new MgQuerySupplierRequestDTO();
        mgQuerySupplierRequestDTO.setMerchantNo(getCurrentCustomerNumber());
        mgQuerySupplierRequestDTO.setPageNo(param.getPageNo());
        mgQuerySupplierRequestDTO.setPageSize(param.getPageSize());
        mgQuerySupplierRequestDTO.setSupplierName(param.getSupplierName());
        mgQuerySupplierRequestDTO.setReceiverRole("INDIVIDUAL");
        try {
            MgQuerySupplierRespDTO mgQuerySupplierRespDTO = remitOrganService.mgQuerySupplierPage(mgQuerySupplierRequestDTO);
            return BaseRespDTO.success(mgQuerySupplierRespDTO);
        } catch (YeepayBizException e) {
            return BaseRespDTO.fail(e.getMessage());
        }catch (Exception e){
            logger.info("商户后台付款到个人查询接口异常,请求参数为={},异常信息为={}",JSONUtils.toJsonString(mgQuerySupplierRequestDTO),e);
            return BaseRespDTO.fail("商户后台付款到个人查询接口异常");
        }
    }


    /**
     * 付款到个人下载
     *
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(SupplierQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("商户后台付款到个人下载，请求参数{}", JSONUtils.toJsonString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            param.setCustomerNumber(getCurrentCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("付款到个人管理查询数据");
            if (StringUtils.isNotBlank(param.getSupplierName())) {
                param.setSupplierName(AESUtils.encryptDigest(param.getSupplierName()));
            }
            if ("appointSync".equals(param.getSyncType())) {
                new IndividualDownloadService(getCurrentUser(), param, desc.toString(), "付款到个人管理查询").syncDownload(request, response);
            } else {
                new IndividualDownloadService(getCurrentUser(), param, desc.toString(), "付款到个人管理查询").download(request, response);
            }
        } catch (Throwable e) {
            logger.info("付款到个人下载异常，请求参数为={},异常信息为={}", JSONUtils.toJsonString(param), e);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>");
        }
    }


    /**
     * 增加付款到个人
     *
     * @param param
     * @throws Exception
     */
    @RequestMapping(value = "/addIndividual")
    @ResponseBody
    public ResponseMessage addIndividual(@RequestBody AddIndividualParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("商户后台增加付款到个人的接口，请求参数{}", JSONUtils.toJsonString(param));
        resMsg.setErrCode(ErrorCode.SUCCESS);
        try {
            ApplyIndividualRequestDTO requestDTO = new ApplyIndividualRequestDTO();
            requestDTO.setInitiateMerchantNo(getCurrentCustomerNumber());
            requestDTO.setMerchantNo(getCurrentCustomerNumber());
            requestDTO.setRequestNo("SP" + DateUtil.formatDate(new Date(), "ssyyyyMMddHHmmSSS") + IdGenUtils.getUUIDHashCode(13));
            requestDTO.setName(param.getName());
            requestDTO.setCertificateType(SupplierQualTypeEnum.valueOf(param.getCertificateType()));
            requestDTO.setCertificateNo(param.getCertificateNo());
            requestDTO.setPhone(param.getPhone());
            requestDTO.setFrontUrl(param.getFrontUrl());
            requestDTO.setContraryUrl(param.getContraryUrl());
            requestDTO.setSource("MP");
            logger.info("商户后台增加付款到个人，请求参数{}", JSONUtils.toJsonString(requestDTO));
            ApplyIndividualRespDTO respDTO = individualFacade.applyIndividual(requestDTO);
            logger.info("商户后台增加付款到个人，返回参数{}", JSONUtils.toJsonString(respDTO));
            if (respDTO != null && "UA00000".equals(respDTO.getReturnCode())) {
                if (!ApplySupplierStatusEnum.REJECT.equals(respDTO.getApplicationStatus())) {
                    resMsg.setErrMsg("成功");
                    return resMsg;
                }
            }
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrCode(respDTO.getReturnCode());
            resMsg.setErrMsg(respDTO.getReturnMsg());
            return resMsg;
        } catch (Exception e) {
            logger.error("商户后台增加付款到个人异常，请求参数为={}，异常信息为={}", JSONUtils.toJsonString(param), e);
            return convertException(e, "增加付款到个人异常");
        }
    }


    /**
     * 付款到个人下单页面的初始化接口
     */
    @RequestMapping(value = "/remit/init")
    @ResponseBody
    public ResponseMessage init() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        resMsg.setErrCode(ErrorCode.SUCCESS);
        Map<String, Object> map = remitOrganService.getRemitInitInfo(getCurrentCustomerNumber(),RemitConstant.REMIT_INDIVIDUAL_PRODUCT_CODE);
        //查询余额
        resMsg.put("balance", map.get("balance"));
        //支持的到账时效
        resMsg.put("arriveType", map.get("arriveType"));
        //加密需要的公钥
        resMsg.put("secretPublicKey", map.get("secretPublicKey"));
        resMsg.put("smsCodeBizType", RemitConstant.REMIT_SMS_CODE_TYPE);
        return resMsg;
    }


    /**
     * 付款到个人下单页面的付款到个人下拉框查询
     */
    @RequestMapping(value = "/remit/queryIndividual")
    @ResponseBody
    public ResponseMessage queryIndividual() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        MgQuerySupplierRequestDTO mgQuerySupplierRequestDTO = new MgQuerySupplierRequestDTO();
        mgQuerySupplierRequestDTO.setMerchantNo(getCurrentCustomerNumber());
        mgQuerySupplierRequestDTO.setPageNo(1);
        mgQuerySupplierRequestDTO.setPageSize(100);
        mgQuerySupplierRequestDTO.setReceiverRole("INDIVIDUAL");
        try {
            List<SupplierSearchResDTO> supplierSearchResDTOS = remitOrganService.mgQuerySupplier(mgQuerySupplierRequestDTO);
            resMsg.put("supplierList", supplierSearchResDTOS);
        } catch (Exception e) {
            logger.info("付款到个人查询下拉框接口异常,异常信息为={}", e);
            return convertException(e, "查询付款到个人异常");
        }
        return resMsg;
    }


    /**
     * 付款到个人下单前的参数预校验
     *
     * @param supplierRemitReqDTO
     * @return
     */
    @RequestMapping("/remit/checkParam")
    @ResponseBody
    public BaseRespDTO checkParam(@RequestBody SupplierRemitReqDTO supplierRemitReqDTO) {
        try {
            logger.info("付款到个人下单前的参数预校验：supplierRemitReqDTO:{}", JsonUtils.toJson(supplierRemitReqDTO));
            supplierRemitReqDTO.validateParam();
            if (StringUtils.isNotBlank(supplierRemitReqDTO.getRequestNo())) {
                ShiroUser user = super.getCurrentUser();
                singleRemitService.checkRequestNo(supplierRemitReqDTO.getRequestNo(), user.getCustomerNumber());
            }
            return BaseRespDTO.success();
        } catch (Exception e) {
            logger.info("付款到个人下单前的参数预校验异常，请求参数为={},异常信息为={}", JSONUtils.toJsonString(supplierRemitReqDTO), e.getMessage());
            return BaseRespDTO.fail(e.getMessage());
        }

    }


    /**
     * 付款到个人下单
     *
     * @param supplierRemitReqDTO
     * @return
     */
    @RequestMapping("/remit/confirm")
    @ResponseBody
    public BaseRespDTO confirm(HttpServletRequest request, @RequestBody SupplierRemitReqDTO supplierRemitReqDTO) {
        logger.info("付款到个人下单参数：supplierRemitReqDTO:{}", JsonUtils.toJson(supplierRemitReqDTO));
        ShiroUser user = super.getCurrentUser();
        checkArgument(StringUtils.isNotBlank(supplierRemitReqDTO.getPasswd()));
        checkArgument(StringUtils.isNotBlank(supplierRemitReqDTO.getSmsCode()));
        try {
            //交易密码改为密文传输，需要解密
            String decryptPassWord = BACRsaUtil.privateDecrypt(supplierRemitReqDTO.getPasswd(), ConfigUtils.getPrivateKey());
            //1.验证密码
            if (!userFacade.validateTradePassword(user.getUserId(), decryptPassWord)) {
                logger.info("付款到个人验证密码失败，请求参数为={}", supplierRemitReqDTO.toString());
                return BaseRespDTO.fail("付款到个人交易密码不正确");
            }
            //短信验证
            SendSmsUtils.checkVaildFrequency(request, user.getUserId(), supplierRemitReqDTO.getSmsCode(), RemitConstant.REMIT_SMS_CODE_TYPE);
            RemitRespDTO remitRespDTO = remitOrganService.initiateSupplierRemit(supplierRemitReqDTO, getCurrentCustomerNumber(), getCurrentUserSafe().getLoginName());
            if (remitRespDTO != null && "UA00000".equals(remitRespDTO.getReturnCode())) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("requestNo", remitRespDTO.getRequestNo());
                resultMap.put("orderNo", remitRespDTO.getOrderNo());
                resultMap.put("status", remitRespDTO.getStatus());
                resultMap.put("orderTime", DateUtil.date2String(new Date(), "yyyy-MM-dd HH:mm:ss"));
                return BaseRespDTO.success(resultMap);
            } else {
                return BaseRespDTO.fail(remitRespDTO.getReturnMsg());
            }
        } catch (ExceptionWrapper e) {
            logger.info("付款到个人短信验证失败，请求参数为={}", supplierRemitReqDTO.toString());
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            logger.info("付款到个人下单异常，requestNo={}，异常信息={}", supplierRemitReqDTO.getRequestNo(), e.getMessage());
            return BaseRespDTO.fail("付款到个人下单异常，请核实出资情况，避免重复出资风险！");
        }
    }


    /**
     * 付款到个人订单查询
     *
     * @param remitQueryParam
     * @return
     */
    @RequestMapping("/remit/query")
    @ResponseBody
    public ResponseMessage remitQuery(@RequestBody RemitQueryParam remitQueryParam) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("付款到个人列表查询，请求参数{}", JSONUtils.toJsonString(remitQueryParam));
        try {
            if (remitQueryParam.isEmptyCheck()) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg("查询参数为空");
                return resMsg;
            }
            QueryResult queryResult = remitOrganService.queryRemitOrderList(remitQueryParam, getCurrentCustomerNumber(), RemitConstant.REMIT_INDIVIDUAL_PRODUCT_CODE);
            resMsg.put("dataList", queryResult.getData());
            //查询汇总信息
            resMsg = supplierRemitService.queryRemitOrderListSum(remitQueryParam, resMsg, getCurrentCustomerNumber(), RemitConstant.REMIT_INDIVIDUAL_PRODUCT_CODE);
            resMsg.put("pageNo", remitQueryParam.getPageNo());
            resMsg.put("pageSize", remitQueryParam.getPageSize());
        } catch (Exception e) {
            logger.error("付款到个人订单查询异常,请求参数为={},e={}", JSONUtils.toJsonString(remitQueryParam), e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }
        return resMsg;
    }

    /**
     * 付款到个人付款列表下载
     *
     * @param param
     * @return
     */
    @RequestMapping("/remit/download")
    @ResponseBody
    public void download(RemitQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            logger.info("开始下载付款记录，请求参数{}", JSONUtils.toJsonString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            param.setCustomerNumber(getCurrentCustomerNumber());
            if (param.getStatus().equals(RemitStatusEnum.REQUEST_ACCEPT.name())) {
                param.setStatus(RemitStatusEnum.REQUEST_ACCEPT.name() + "," + RemitStatusEnum.REQUEST_RECEIVE.name());
            }
            if (StringUtils.isNotBlank(param.getCreateEndDate())) {
                param.setCreateEndDate(DateUtil.addDay(param.getCreateEndDate()));
            }
            param.setFirstProductCode(RemitConstant.REMIT_INDIVIDUAL_PRODUCT_CODE);
            if (StringUtils.isEmpty(param.getCreateStartDate()) || StringUtils.isEmpty(param.getCreateEndDate())) {
                if (StringUtils.isEmpty(param.getBatchNo()) && StringUtils.isEmpty(param.getRequestNo())) {
                    //如果时间范围是空的,那么批次号和订单号必填
                    throw UnionAccountException.PARAM_REQUIRED_ERROR.newInstance("缺少必要的请求参数");
                }
            }
            StringBuilder desc = new StringBuilder();
            desc.append("付款订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");

            if ("appointSync".equals(param.getSyncType())) {
                new RemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-", remitOrderService, merchantRemoteService, false).syncDownload(request, response);
            } else {
                new RemitOrderDownloadService(getCurrentUser(), param, desc.toString(), "付款订单查询-", remitOrderService, merchantRemoteService, false).download(request, response);
            }
            logger.info("下载付款记录excel已完成");
        } catch (Throwable ex) {
            logger.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }


    /**
     * 付款到个人的订单详情查询
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/remit/queryDetail")
    @ResponseBody
    public ResponseMessage queryDetail(HttpServletRequest request) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String orderNo = request.getParameter("orderNo");
        String batchNo = request.getParameter("batchNo");
        String requestNo = request.getParameter("requestNo");
        RemitResponseParam responseParam = remitOrganService.queryDetail(orderNo, batchNo, requestNo, getCurrentCustomerNumber());
        logger.info("付款到个人的订单详情查询，remitResponseParam={}", JSONUtils.toJsonString(responseParam));
        resMsg.put("orderDetail", responseParam);
        return resMsg;
    }


    /**
     * 付款到个人管理页面
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("20230614011")
    @RequestMapping("/individualManage")
    public ModelAndView individualManage(HttpServletRequest request) throws Exception {
        ModelAndView mv = new ModelAndView("individualRemit/individualManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("付款到个人管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    /**
     * 付款到个人付款
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/individualRemit")
    public ModelAndView individualRemit(HttpServletRequest request) throws Exception {
        ModelAndView mv = new ModelAndView("individualRemit/individualRemit");
        String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
        //判断付款到个人付款产品
        mv.addObject("hasAvailableStatus", true);
        //查询余额
        AccountInfoRespDTO responseDto = businessCheckRemoteService.accountStatusAndBalance(getCurrentCustomerNumber(), AccountTypeEnum.FUND_ACCOUNT);
        if (responseDto != null) {
            String accountStatus = responseDto.getAccountStatus();
            if (!("AVAILABLE".equals(accountStatus) || "FROZEN_CREDIT".equals(accountStatus))) {
                mv.addObject("hasAvailableStatus", false);
            }
        }
        mv.addObject("checkOpenProduct", checkOpenProduct(getCurrentCustomerNumber(), markProductCode));
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("付款到个人管理页面菜单,{}", request.getSession().getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 判断付款产品
     *
     * @param currentCustomerNumber
     * @param markProductCode
     * @return
     */
    private boolean checkOpenProduct(String currentCustomerNumber, String markProductCode) {
        boolean checkOpenProduct = false;
        MerchantProductQueryRespDTO respDto = businessCheckRemoteService.queryMerchantProduct(currentCustomerNumber, "PAY",
                markProductCode, RemitConstant.REMIT_INDIVIDUAL_PRODUCT_CODE, null);
        if ("0000".equals(respDto.getRetCode())) {
            checkOpenProduct = true;
        }
        return checkOpenProduct;
    }

    /**
     * 付款到个人付款记录
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/individualRemitRecord")
    public ModelAndView individualRemitRecord(HttpServletRequest request) throws Exception {
        ModelAndView mv = new ModelAndView("individualRemit/individualRemitRecord");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("付款到个人付款记录页面菜单,{}", request.getSession().getAttribute(("tabMenu")));
        return mv;
    }

    /**
     * 异常转换
     * @param e
     * @param msg
     * @return
     */
    private ResponseMessage convertException(Exception e, String msg) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
        if (e instanceof YeepayBizException) {
            resMsg.setErrCode(((YeepayBizException) e).getDefineCode());
            resMsg.setErrMsg(e.getMessage());
        } else {
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(msg);
        }
        return resMsg;
    }

    /**
     * 付款到个人付款
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/init/info")
    @ResponseBody
    public BaseRespDTO getInitInfo(HttpServletRequest request) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            Map<String, Object> map = new HashMap<>();
            String markProductCode = businessCheckRemoteService.queryMarketProduct(getCurrentCustomerNumber());
            //判断付款到个人付款产品
            map.put("hasAvailableStatus", true);
            //查询余额
            AccountInfoRespDTO responseDto = businessCheckRemoteService.accountStatusAndBalance(getCurrentCustomerNumber(), AccountTypeEnum.FUND_ACCOUNT);
            if (responseDto != null) {
                String accountStatus = responseDto.getAccountStatus();
                if (!("AVAILABLE".equals(accountStatus) || "FROZEN_CREDIT".equals(accountStatus))) {
                    map.put("hasAvailableStatus", false);
                }
            }
            map.put("checkOpenProduct", checkOpenProduct(getCurrentCustomerNumber(), markProductCode));
            logger.info("付款到个人管理页面菜单,{}", request.getSession().getAttribute("tabMenu"));
            return BaseRespDTO.success(map);
        } catch (Exception e) {
            logger.error("初始化付款到个人,商编为" + currentCustomerNumber + "异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }
}
