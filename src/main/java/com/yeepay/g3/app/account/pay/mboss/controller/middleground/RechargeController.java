package com.yeepay.g3.app.account.pay.mboss.controller.middleground;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.dto.baseDTO.ResponseMessageDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.baseDTO.SumOrderBaseDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.DownloadElectronicReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.DownloadSplitRechargeReceiptReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.SplitRechargeReq;
import com.yeepay.g3.app.account.pay.mboss.entity.RechargeOrder;
import com.yeepay.g3.app.account.pay.mboss.entity.SplitRechargeOrderEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.SplitRechargeSubOrderEntity;
import com.yeepay.g3.app.account.pay.mboss.enumtype.OrderStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.PlatformTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.model.*;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.CapitalManageService;
import com.yeepay.g3.app.account.pay.mboss.service.CookieService;
import com.yeepay.g3.app.account.pay.mboss.service.RechargeOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.SplitRechargeBatchService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.RechargeOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.SplitRechargeOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.trade.dao.SplitRechargeBatchDao;
import com.yeepay.g3.app.account.pay.mboss.trade.dao.SplitRechargeSubOrderDao;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.account.pay.mboss.vo.AccountBookVo;
import com.yeepay.g3.app.account.pay.mboss.vo.QueryAccountBookInfoRespDTO;
import com.yeepay.g3.app.account.pay.mboss.vo.RechargeManageRequestVO;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.handler.RemoteFacadeProxyFactory;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.capital.manage.dto.req.QueryMatchingLoanReqDTO;
import com.yeepay.g3.capital.manage.dto.resp.MatchingLoanInfoVo;
import com.yeepay.g3.capital.manage.dto.resp.QueryMatchingLoanRespDTO;
import com.yeepay.g3.facade.account.adapter.facade.AccountFrontFacade;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.BaseProductDTO;
import com.yeepay.g3.facade.merchant_platform.dto.customermanagement.QueryMerchantProductRespDTO;
import com.yeepay.g3.facade.mp.dto.TradePasswordValidateDTO;
import com.yeepay.g3.facade.mp.enumtype.security.AuthenticationStatusEnum;
import com.yeepay.g3.facade.mp.exception.UserBusinessException;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.merchant.enums.UserStatusEnum;
import com.yeepay.g3.facade.ncconfig.common.NCConfigException;
import com.yeepay.g3.facade.ncconfig.facade.OnlineBankCashierTemplateFacade;
import com.yeepay.g3.facade.ncconfig.param.OnlineAllBankRuleInfoDTO;
import com.yeepay.g3.facade.ncconfig.param.QueryOnlineBankRulesByTemplateParam;
import com.yeepay.g3.facade.ncconfig.result.OnlineBankRuleInfoDTO;
import com.yeepay.g3.facade.ncconfig.result.OnlineBankTemplateInfoDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.ReceiptReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.TradeTypeEnum;
import com.yeepay.g3.facade.unionaccount.manage.exception.UnionAccountManageException;
import com.yeepay.g3.facade.unionaccount.recharge.dto.request.*;
import com.yeepay.g3.facade.unionaccount.recharge.dto.response.*;
import com.yeepay.g3.facade.unionaccount.recharge.enumtype.*;
import com.yeepay.g3.facade.unionaccount.recharge.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.recharge.exception.UnionAccountRechargeException;
import com.yeepay.g3.facade.unionaccount.recharge.facade.RechargeFacade;
import com.yeepay.g3.facade.unionaccount.recharge.facade.RechargeManageFacade;
import com.yeepay.g3.facade.unionaccount.recharge.params.AccountBasicParam;
import com.yeepay.g3.facade.unionaccount.recharge.params.ProductCodeParam;
import com.yeepay.g3.facade.unionaccount.recharge.utils.AccountBasicConfigUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 充值
 */
@Controller
@Api(tags = "企业账户-充值")
@RequestMapping("/recharge")
public class RechargeController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RechargeController.class);

    /**
     * 收银台返回银行列表接口
     */
    private OnlineBankCashierTemplateFacade onlineBankCashierTemplateFacade = RemoteFacadeProxyFactory.getService(OnlineBankCashierTemplateFacade.class);

    private RechargeFacade rechargeFacade = RemoteFacadeProxyFactory.getService(RechargeFacade.class);

    private RechargeManageFacade rechargeManageFacade = RemoteFacadeProxyFactory.getService(RechargeManageFacade.class);


    private AccountFrontFacade accountFrontFacade = RemoteFacadeProxyFactory.getService(AccountFrontFacade.class);

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    public static final String RECHARGE_PERMISSION = "********";

    public static final String RECHARGE_QUERY_PERMISSION = "********";

    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";

    // 默认当前
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private MerchantRemoteService merchantRemoteService;

    @Autowired
    private CookieService cookieService;

    @Autowired
    private CapitalManageService capitalManageService;

    @Autowired
    private SplitRechargeBatchDao splitRechargeBatchDao;

    @Autowired
    private SplitRechargeSubOrderDao splitRechargeSubOrderDao;

    /**
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     * @Description: 充值页面
     * <AUTHOR>
     * @date 2020-03-11 11:33
     */
    @RequiresPermissions(RECHARGE_PERMISSION)
    @RequestMapping(value = "/view", method = RequestMethod.GET)
    public ModelAndView view(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("recharge/index");
        return mav;
    }

    /**
     * 充值获取首页数据
     * @param request
     * @param accountType
     * @return
     */
    @RequestMapping(value = "/getIndexInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getIndexInfo(HttpServletRequest request,@RequestParam(value = "accountType",required = false) String accountType) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        //当前商编
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("商户{}获取首页数据", currentCustomerNumber);
        //当前商编
        List<AccountInfoParam> accountTypeList = new ArrayList<>();
        try{
            //账户基本信息配置
            Map<String, AccountBasicParam> accountBasicConfig = AccountBasicConfigUtils.getAccountBasicConfig();
            //获取账户类型列表配置
            List<String> rechargePageAccountTypeList = ConfigUtils.getRechargePageAccountTypeList();
            List<String> allProductList = new ArrayList<>();
            //根据商编查询产品开通情况
            List<String> allProducts = getAllProductByMerchantNo(currentCustomerNumber, allProductList);
            LOGGER.info("allProducts:" + new Gson().toJson(allProducts));
            for (String type : rechargePageAccountTypeList) {
                AccountInfoParam param = new AccountInfoParam();
                //查询余额
                try {
                    BigDecimal accountBalance = queryBalance(type);
                    param.setAccountBalance(accountBalance);
                } catch (Exception e) {
                    LOGGER.warn("商编" + currentCustomerNumber + "手续费账户查询不存在或异常" + e);
                    continue;
                }

                AccountBasicParam accountBasicParam = accountBasicConfig.get(type);
                if(accountBasicParam == null){
                    LOGGER.warn("商编" + currentCustomerNumber + "不支持该账户类型，请联系运营处理");
                    continue;
                }
                List<String> accountProductList = new ArrayList<>();
                //服务类型
                String serviceType = accountBasicParam.getServiceType();
                accountProductList = getAccountTypeList(serviceType, accountBasicParam, accountProductList,allProducts,param);
                if(CollectionUtils.isEmpty(accountProductList)){
                    continue;
                }
                doPayEmployee(currentCustomerNumber, allProducts, type, param);
                param.setAccountType(type);
                param.setAccountTypeDesc(com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum.valueOf(type).getDesc());
                //设置为服务还是账户
                param.setServiceType(serviceType);
                param.setAccountProductList(accountProductList);
                accountTypeList.add(param);

            }
        }catch (Exception e){
            LOGGER.error("获取首页接口数据异常" + currentCustomerNumber,e);
        }

        resMsg.put("accountTypeList", accountTypeList);
        resMsg.put("accountType", accountType);
        LOGGER.info("获取首页数据返回，resMsg={}",JSON.toJSON(resMsg));
        return resMsg;
    }

    private void doPayEmployee(String currentCustomerNumber, List<String> allProducts, String type, AccountInfoParam param) {
        PayEmployeeDTO payEmployee = new PayEmployeeDTO();
        payEmployee.setDisplay("0");
        if(Objects.equals(type, AccountTypeEnum.FEE_ACCOUNT.name())){
            payEmployee.setDisplay("1");
            RechargeManageInfoQueryReqDTO rechargeManageInfoQueryReq = new RechargeManageInfoQueryReqDTO();
            rechargeManageInfoQueryReq.setMerchantNo(currentCustomerNumber);
            RechargeManageInfoQueryRespDTO rechargeManageInfo = rechargeManageFacade.getRechargeManageInfo(rechargeManageInfoQueryReq);
            List<String> strList = rechargeManageInfo.getList().stream().map(x -> {
                StringBuilder str = new StringBuilder();
                str.append(HiddenCodeUtils.hiddenName(x.getName()));
                str.append("(");
                str.append(HiddenCodeUtils.hiddenIdentityCode(x.getCertificateNo()));
                str.append(")");
                return str.toString();
            }).collect(Collectors.toList());
            payEmployee.setPayEmployeeList(strList);
        }
        if (Objects.equals(type, AccountTypeEnum.FUND_ACCOUNT.name()) || Objects.equals(type, AccountTypeEnum.MARKET_ACCOUNT.name())) {
            List<String> strList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(allProducts)) {
                AccountBasicParam basicParam = AccountBasicConfigUtils.getAccountBasicConfigByAccountType(AccountTypeEnum.valueOf(type).name());
                ProductCodeParam productCodeParam = basicParam.getPayTypeMap().get(PayTypeEnum.B2C.name());
                if (Objects.nonNull(productCodeParam) && Objects.nonNull(productCodeParam.getFirstProductCode()) && Objects.nonNull(productCodeParam.getSecondProductCode())) {
                    StringBuilder stringBuilder = new StringBuilder().append(productCodeParam.getFirstProductCode()).append(",").append(productCodeParam.getSecondProductCode());
                    List<String> productList = allProducts.stream().filter(x -> x.contains(stringBuilder.toString())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(productList) && productList.size() == 1 && productList.get(0).contains("SAMENAME")) {
                        payEmployee.setDisplay("1");
                        RechargeManageInfoQueryReqDTO rechargeManageInfoQueryReq = new RechargeManageInfoQueryReqDTO();
                        rechargeManageInfoQueryReq.setMerchantNo(currentCustomerNumber);
                        RechargeManageInfoQueryRespDTO rechargeManageInfo = rechargeManageFacade.getRechargeManageInfo(rechargeManageInfoQueryReq);
                        strList = rechargeManageInfo.getList().stream().map(x -> {
                            StringBuilder str = new StringBuilder();
                            str.append(HiddenCodeUtils.hiddenName(x.getName()));
                            str.append("(");
                            str.append(HiddenCodeUtils.hiddenIdentityCode(x.getCertificateNo()));
                            str.append(")");
                            return str.toString();
                        }).collect(Collectors.toList());
                    }
                }
            }
            payEmployee.setPayEmployeeList(strList);
        }
        param.setPayEmployee(payEmployee);
    }


    /**
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     * @Description: 跳转充值页面
     * <AUTHOR>
     * @date 2020-03-11 11:33
     */
    @RequiresPermissions(RECHARGE_PERMISSION)
    @RequestMapping(value = "/view/{accountType}", method = RequestMethod.GET)
    public ModelAndView rechargeView(HttpServletRequest request, @PathVariable(value = "accountType") String accountType) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("recharge/index");
        mav.addObject("accountType", accountType);
        return mav;
    }

    private List<String> getAccountTypeList(String serviceType,AccountBasicParam accountBasicParam,List<String> accountProductList,List<String> allProducts,AccountInfoParam param){
        //产品服务
        if (AccountServiceEnum.CHARGE.name().equals(serviceType)) {
            accountProductList = setRechargeProduct(accountProductList, accountBasicParam,allProducts);

        } else {
            //账户服务 默认走b2c，b2b
            Map<String, ProductCodeParam> payTypeMap = accountBasicParam.getPayTypeMap();
            ProductCodeParam b2bProductCodeParam = payTypeMap.get(PayTypeEnum.B2B.name());
            if(b2bProductCodeParam != null && StringUtils.isNotBlank(b2bProductCodeParam.getSecondProductCode())){
                accountProductList.add(b2bProductCodeParam.getSecondProductCode());
            }

            ProductCodeParam b2cProductCodeParam = payTypeMap.get(PayTypeEnum.B2C.name());
            if(b2cProductCodeParam != null && StringUtils.isNotBlank(b2cProductCodeParam.getSecondProductCode())){
                accountProductList.add(b2cProductCodeParam.getSecondProductCode());
            }

            ProductCodeParam bankTransferProductCodeParam = payTypeMap.get(PayTypeEnum.BANK_TRANSFER.name());
            if(bankTransferProductCodeParam != null && StringUtils.isNotBlank(bankTransferProductCodeParam.getSecondProductCode())){
                accountProductList.add(bankTransferProductCodeParam.getSecondProductCode());
            }

        }
        return accountProductList;
    }

    /**
     * 商户获取账户类型的收银台银行列表
     * @param request
     * @param accountType
     * @return
     */
    @RequestMapping(value = "/getCashierList", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getCashierList(HttpServletRequest request, @RequestParam(value = "accountType") String accountType) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        //当前商编
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("商户{}获取{}账户类型的收银台银行列表，accountType={}", currentCustomerNumber, accountType);
        List<BankInfo> businessBankInfoList = queryB2BBankList();
        List<BankInfo> personBankInfoList = queryB2CBankList();
        resMsg.put("businessBankList",businessBankInfoList);
        resMsg.put("personBankList",personBankInfoList);
        return resMsg;
    }

    /**
     * 跳转充值记录页
     *
     * @param request
     * @return
     */
    @RequiresPermissions(RECHARGE_QUERY_PERMISSION)
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public ModelAndView queryView(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("recharge/query");

        String customerNumber = super.getCurrentCustomerNumber();
        if(StringUtils.isNotBlank(customerNumber)) {
            String customerSign = remoteService.queryMerchantSign(customerNumber);
            mav.addObject("customerSign", customerSign);
        }

        return mav;
    }

    @Autowired
    private RechargeOrderService rechargeOrderService;
    /**
     * 跳转充值详情页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView detailView(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        String customerNumber = request.getParameter("customerNumber");
        PreCheck.checkArgument(StringUtils.isNotBlank(customerNumber), "商户编号不能为空");
        LOGGER.info("查询充值订单明细，订单号：orderNo={},customerNumber={}", orderNo, customerNumber);
        if (!CheckUtils.isEmpty(orderNo)) {
            RechargeOrder rechargeOrder = null;
            RechargeOrder dto = new RechargeOrder();
            dto.setOrderNo(orderNo);
            dto.setMerchantNo(customerNumber);
            try {
                rechargeOrder = rechargeOrderService.queryRechargeOrderInfo(dto);
                LOGGER.info("查询订单返回，rechargeOrder={}", JSON.toJSONString(rechargeOrder));
            } catch (Exception e) {
                LOGGER.error("查询充值订单异常", e);
            }
            RechargeResponseParam rechargeResponseParam = new RechargeResponseParam();
            if (rechargeOrder != null) {
                RechargeOrderStatus orderStatus = rechargeOrder.getStatus();
                if (OrderStatusEnum.PAY_SUCCESS.name().equals(orderStatus.name()) || OrderStatusEnum.ACCOUNTING.name().equals(orderStatus.name())
                        || (OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(orderStatus.name()))) {
                    rechargeResponseParam.setStatus(OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.getDesc());

                } else if (OrderStatusEnum.ACCOUNTING_FAIL.name().equals(orderStatus.name())) {
                    rechargeResponseParam.setStatus(OrderStatusEnum.ACCOUNTING_EXCEPTION.getDesc());
                } else {
                    rechargeResponseParam.setStatus(OrderStatusEnum.valueOf(rechargeOrder.getStatus().name()).getDesc());
                }
                rechargeResponseParam.setCreateDate(DateUtil.formatByDateTimePattern(rechargeOrder.getCreateTime()));
                rechargeResponseParam.setOrderNo(rechargeOrder.getOrderNo());
                rechargeResponseParam.setAmount(null == rechargeOrder.getApplyAmount() ? null : NumberUtils.formateNum(null, rechargeOrder.getApplyAmount()));
                rechargeResponseParam.setDeductAmount(null == rechargeOrder.getOrderAmount() ? null : NumberUtils.formateNum(null, rechargeOrder.getOrderAmount()));
                rechargeResponseParam.setDesc(rechargeOrder.getRemark());
                rechargeResponseParam.setFee(null == rechargeOrder.getFee() ? null : NumberUtils.formateNum(null, rechargeOrder.getFee()));
                rechargeResponseParam.setBankName(null == rechargeOrder.getBankName()?"":rechargeOrder.getBankName());
                rechargeResponseParam.setProductType(rechargeOrder.getPayType().name());
                rechargeResponseParam.setFailReason(rechargeOrder.getReturnMsg());
                if(StringUtils.isNotEmpty(rechargeOrder.getFeeUndertakerMerchantNo())&& !StringUtils.equals(rechargeOrder.getMerchantNo(),rechargeOrder.getFeeUndertakerMerchantNo())){
                    rechargeResponseParam.setFee(null);
                }
                rechargeResponseParam.setAccountType(UniformConfigUtils.getUnionAccountTypeNames().getOrDefault(rechargeOrder.getAccountType().name(),rechargeOrder.getAccountType().desc()));


                rechargeResponseParam.setProductTypeDesc(rechargeOrder.getPayType().desc());
                switch (rechargeOrder.getPayType()) {
                    case B2B:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case B2C:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case BANK_TRANSFER:
                        rechargeResponseParam.setRemitComment(rechargeOrder.getRemitComment());
                        break;
                    case BANK_TRANSFER_AUTO:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case ACCOUNT_PAY:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        rechargeResponseParam.setProductTypeDesc(rechargeOrder.getPayType().desc() + " - " + rechargeOrder.getPayerAccountName());
                        break;
                    case QUICK_PUBLIC:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case BANK_PAY:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case PUBLIC_ENTRUST:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                }

                if (rechargeOrder.getBankTrxTime() != null) {
                    rechargeResponseParam.setBankTrxTime(DateUtil.formatByDateTimePattern(rechargeOrder.getBankTrxTime()));
                }
                if (rechargeOrder.getFinishTime() != null) {
                    rechargeResponseParam.setCompletionTime(DateUtil.formatByDateTimePattern(rechargeOrder.getFinishTime()));
                }
                rechargeResponseParam.setOperateUser(rechargeOrder.getOperator());
                rechargeResponseParam.setCapitalInfo(rechargeOrder.getCapitalInfo());
            }

            LOGGER.info("查询订单明细返回，rechargeResponseParam={}", JSON.toJSONString(rechargeResponseParam));
            mav.addObject("orderDetail", rechargeResponseParam);
        }
        mav.setViewName("recharge/detail");
        return mav;
    }

    /**
     * 跳转成功页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/success", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView successView(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        LOGGER.info("订单号，orderNo={}", orderNo);
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            RechargeOrderQueryRespDTO rechargeOrderQueryRespDTO = queryRechargeOrder(currentCustomerNumber, orderNo);
            LOGGER.info("查询充值订单，queryBacOrderRespDTO={}", JSON.toJSONString(rechargeOrderQueryRespDTO));
            if (rechargeOrderQueryRespDTO != null && rechargeOrderQueryRespDTO.getCreditAmount() != null) {
                mav.addObject("amount", rechargeOrderQueryRespDTO.getCreditAmount().setScale(2,BigDecimal.ROUND_HALF_UP));
            }
        } catch (Exception e) {
            LOGGER.error("查询充值订单异常，e={}", e);
        }

        mav.setViewName("recharge/success");
        return mav;
    }

    /**
     * 跳转成功页(迁移红版后台)
     *
     * @param orderNo 订单编号
     * @return
     */
    @RequestMapping(value = "/rechargeSuccess", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO rechargeSuccess(@RequestParam("orderNo") String orderNo) {
        LOGGER.info("[红版商户后台]充值成功跳转到成功页面，orderNo={}", orderNo);
        PreCheck.checkArgument(StringUtils.isNotBlank(orderNo), "订单编号不能为空");
        Map<String, Object> map = new HashMap<>();
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            RechargeOrderQueryRespDTO rechargeOrderQueryRespDTO = queryRechargeOrder(currentCustomerNumber, orderNo);
            LOGGER.info("[红版商户后台]查询充值订单，queryBacOrderRespDTO={}", JSON.toJSONString(rechargeOrderQueryRespDTO));
            if (rechargeOrderQueryRespDTO != null) {
                map.put("amount", rechargeOrderQueryRespDTO.getCreditAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        } catch (Exception e) {
            LOGGER.error("[红版商户后台]查询充值订单异常，e={}", e);
            return BaseRespDTO.fail();
        }
        return BaseRespDTO.success(map);
    }


    /**
     * 查询充值订单
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryRechargeDetail", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView queryRechargeDetail(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        LOGGER.info("订单号，orderNo={}", orderNo);
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        try {
            RechargeOrderQueryRespDTO rechargeOrderQueryRespDTO = queryRechargeOrder(currentMerchant.getMerchantNo(), orderNo);
            LOGGER.info("查询充值订单，queryBacOrderRespDTO={}", JSON.toJSONString(rechargeOrderQueryRespDTO));
            if (rechargeOrderQueryRespDTO != null) {
                mav.addObject("remittanceCode", rechargeOrderQueryRespDTO.getRemitComment());
                mav.addObject("merchantSignName", currentMerchant.getSignName());
                mav.addObject("amount", rechargeOrderQueryRespDTO.getOrderAmount().setScale(2,BigDecimal.ROUND_HALF_UP));
            }
        } catch (Exception e) {
            LOGGER.error("查询充值订单异常，e={}", e);
        }

        mav.setViewName("recharge/bankTransferSuccess");
        return mav;
    }

    /**
     * 查询订单详情(迁移红版后台)
     *
     * @param orderNo
     * @return
     */
    @RequestMapping(value = "/queryRechargeOrderDetail", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryRechargeOrderDetail(@RequestParam("orderNo") String orderNo) {
        LOGGER.info("[红版商户后台]查询充值订单详情，orderNo={}", orderNo);
        PreCheck.checkArgument(StringUtils.isNotBlank(orderNo), "订单编号不能为空");
        Map<String,Object> result=new HashMap<>();
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        try {
            RechargeOrderQueryRespDTO rechargeOrderQueryRespDTO = queryRechargeOrder(currentMerchant.getMerchantNo(), orderNo);
            LOGGER.info("[红版商户后台]查询充值订单，queryBacOrderRespDTO={}", JSON.toJSONString(rechargeOrderQueryRespDTO));
            if (rechargeOrderQueryRespDTO != null) {
                result.put("remittanceCode", rechargeOrderQueryRespDTO.getRemitComment());
                result.put("merchantSignName", currentMerchant.getSignName());
                result.put("amount", rechargeOrderQueryRespDTO.getOrderAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        } catch (Exception e) {
            LOGGER.error("[红版商户后台]查询充值订单异常，e={}", e);
            return BaseRespDTO.fail("查询充值订单失败，请稍后再试");
        }
        return BaseRespDTO.success(result);
    }



    /**
     * 确认充值
     *
     * @param amount
     * @param bankCode
     * @param productType
     * @param fee
     * @param deductAmount
     * @param desc
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage confirm(@RequestParam("amount") String amount,
                                   @RequestParam(value="bankCode",required = false) String bankCode,
                                   @RequestParam(value = "redirectUrlFlag", required = false) boolean redirectUrlFlag,
                                   @RequestParam("productType") String productType,
                                   @RequestParam("fee") String fee,
                                   @RequestParam("deductAmount") String deductAmount,
                                   @RequestParam("desc") String desc,
                                   @RequestParam("accountType") String accountType,
                                   @RequestParam(value = "bankName",required = false) String bankName,
                                   @RequestParam(value = "merchantSignName",required = false) String merchantSignName,
                                   @RequestParam(value = "payerUserName",required = false) String payerUserName,
                                   @RequestParam(value = "payerUserPassword",required = false) String payerUserPassword
                                   ) {

        LOGGER.info("充值确认请求参数，amount={},bankCode={},productType={},fee={},deductAmount={},desc={},merchantSignName={},payerUserName={},payerUserPassword={},redirectUrlFlag={}",
                amount, bankCode, productType, fee, deductAmount, desc,payerUserName,payerUserPassword,redirectUrlFlag);
        String currentCustomerNumber = getCurrentCustomerNumber();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        RechargeRequestDTO dto = new RechargeRequestDTO();
        EBRequestDTO ebRequestDto = new EBRequestDTO();
        ebRequestDto.setBankId(bankCode);
        ebRequestDto.setBankName(bankName);
        dto.setEbRequestDTO(ebRequestDto);
        dto.setRechargeAmount(new BigDecimal(amount));
        dto.setInitiateMerchantNo(currentCustomerNumber);
        dto.setMerchantNo(currentCustomerNumber);
        dto.setAccountType(AccountTypeEnum.valueOf(accountType));
        if (PayTypeEnum.B2C.name().equals(productType)) {
            dto.setPayType(PayTypeEnum.B2C);
        } else if (PayTypeEnum.B2B.name().equals(productType)) {
            dto.setPayType(PayTypeEnum.B2B);
        }else if(PayTypeEnum.BANK_TRANSFER.name().equals(productType)){
            dto.setPayType(PayTypeEnum.BANK_TRANSFER);
            RemittanceRequestDTO remittanceRequestDTO = new RemittanceRequestDTO();
            remittanceRequestDTO.setAccountName(merchantSignName);
            remittanceRequestDTO.setRemitComment(GenerateUniqueCode.generateCode());
            dto.setRemittanceRequestDTO(remittanceRequestDTO);
        } else if (PayTypeEnum.ACCOUNT_PAY.name().equals(productType)) {
            dto.setPayType(PayTypeEnum.ACCOUNT_PAY);
            try {
                checkUserPassword(payerUserName, payerUserPassword, dto);
            } catch (IllegalArgumentException | AccountPayException e) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg(e.getMessage());
                return resMsg;
            }
        }
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        dto.setMarketProductCode(businessCheckRemoteService.queryMarketProduct(currentCustomerNumber));
        dto.setOperator(getCurrentUser().getLoginName());
        //有效期，订单号，回调地址,一级，二级
        Long requestNo = SnowflakeIdFactory.generateId();
        dto.setRequestNo("BZCZ" + requestNo);
        dto.setClientIp(NetUtils.getIpAddress());
        dto.setRemark(desc);
        if (redirectUrlFlag) {
            LOGGER.info("充值确认请求 跳转到红版商户后台 the redirectUrlFlag=[{}]", redirectUrlFlag);
            dto.setRedirectUrlFlag(true);
        } else {
            LOGGER.info("充值确认请求 充值成功跳转到绿版商户后台 the redirectUrlFlag=[{}]", redirectUrlFlag);
        }
        try {
            LOGGER.info("充值确认请求参数，dto={}", JSON.toJSONString(dto));
            RechargeRespDTO rechargeRespDTO = null;
            List<String> frontMerchantNos = ConfigUtils.getFrontMerchantNos();
            if (CollectionUtils.isNotEmpty(frontMerchantNos) && frontMerchantNos.contains(currentCustomerNumber)) {
                LOGGER.info("发起方商编属于配置走前置层商编, 调用前置层, merchantNo:{}", currentCustomerNumber);
                rechargeRespDTO = accountFrontFacade.initiateRecharge(dto);
            } else {
                rechargeRespDTO = rechargeFacade.initiateRecharge(dto);
            }
            LOGGER.info("充值结果返回参数，load={}", JSON.toJSONString(rechargeRespDTO));
            if (rechargeRespDTO != null) {
                //判断充值请求正常返回
                String errorCode = rechargeRespDTO.getReturnCode();

                if ("UA00000".equals(errorCode)) {
                    if(PayTypeEnum.BANK_TRANSFER.name().equals(productType)){
                        String orderNo = rechargeRespDTO.getOrderNo();
                        if(StringUtils.isNotBlank(orderNo)){
                            resMsg.put("orderNo", rechargeRespDTO.getOrderNo());
                        }else{
                            resMsg.setStatus(ResponseMessage.Status.ERROR);
                            resMsg.setErrMsg("充值异常");
                        }
                    }else if(PayTypeEnum.B2C.name().equals(productType) || PayTypeEnum.B2B.name().equals(productType)){
                        EBRechargeRespDTO ebRechargeRespDTO = rechargeRespDTO.getEbRechargeRespDTO();
                        if (ebRechargeRespDTO != null) {
                            String payUrl = ebRechargeRespDTO.getPayUrl();
                            if (StringUtils.isNotBlank(payUrl)) {
                                resMsg.put("payUrl", ebRechargeRespDTO.getPayUrl());
                                resMsg.put("orderNo", rechargeRespDTO.getOrderNo());
                                resMsg.put("payCharset", ebRechargeRespDTO.getPayCharset());
                                resMsg.put("payParams", ebRechargeRespDTO.getPayParams());
                                resMsg.put("payMethod", ebRechargeRespDTO.getPayMethod());

                            } else {
                                resMsg.setStatus(ResponseMessage.Status.ERROR);
                                resMsg.setErrMsg(rechargeRespDTO.getReturnMsg());
                            }
                        } else {
                            resMsg.setStatus(ResponseMessage.Status.ERROR);
                            resMsg.setErrMsg(rechargeRespDTO.getReturnMsg());
                        }
                    }else if (PayTypeEnum.ACCOUNT_PAY.name().equals(productType)) {
                        resMsg.put("orderNo", rechargeRespDTO.getOrderNo());
                        resMsg.put("amount", rechargeRespDTO.getAccountPayRechargeRespDTO().getAmount().setScale(2,BigDecimal.ROUND_HALF_UP));
                    }
                } else if ("UAR00022".equals(errorCode)) {
                    // 账户支付返处理中
                    if (PayTypeEnum.ACCOUNT_PAY.name().equals(productType)) {
                        resMsg.setStatus(ResponseMessage.Status.WARNING);
                        resMsg.put("orderNo", rechargeRespDTO.getOrderNo());
                    }
                } else if ("UAR00021".equals(errorCode)) {
                    // 账户支付失败
                    if (PayTypeEnum.ACCOUNT_PAY.name().equals(productType)) {
                        resMsg.setStatus(ResponseMessage.Status.ERROR);
                        resMsg.put("orderNo", rechargeRespDTO.getOrderNo());
                        resMsg.setErrMsg(rechargeRespDTO.getReturnMsg());
                    }
                } else {
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg(rechargeRespDTO.getReturnMsg());
                }
            } else {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg("充值异常");
            }
        } catch (Exception e) {
            LOGGER.error("充值异常，e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常，请稍后再试");
        }
        return resMsg;
    }


    @RequestMapping(value = "/pay/employee", method = RequestMethod.GET)
    public ModelAndView rechargePayer(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        return new ModelAndView("recharge/payableStaff", params);
    }

    /**
     * 充值可支付员工新增
     *
     * @param rechargeManageRequestVO
     * @return
     */
    @RequestMapping(value = "/payer/save", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage save(@RequestBody RechargeManageRequestVO rechargeManageRequestVO) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        RechargeManageRequestDTO rechargeManageRequest = new RechargeManageRequestDTO();
        rechargeManageRequest.setMerchantNo(currentCustomerNumber);
        rechargeManageRequest.setInitiateMerchantNo(currentCustomerNumber);
        rechargeManageRequest.setParentMerchantNo(currentCustomerNumber);
        rechargeManageRequest.setName(rechargeManageRequestVO.getName().trim());
        rechargeManageRequest.setCertificateType(rechargeManageRequestVO.getCertificateType());
        rechargeManageRequest.setCertificateNo(rechargeManageRequestVO.getCertificateNo());
        rechargeManageRequest.setPayType(rechargeManageRequestVO.getPayType());
        rechargeManageRequest.setCreateTime(new Date());
        rechargeManageRequest.setLastModifyTime(new Date());
        rechargeManageRequest.setOperator(getCurrentUser().getLoginName());
        try {
            rechargeManageFacade.saveRechargeManage(rechargeManageRequest);
        }catch (UnionAccountRechargeException e){
            LOGGER.error("充值可支付员工新增异常，e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }catch (Exception e){
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常，请联系管理员");
        }
        return resMsg;
    }

    /**
     * 充值可支付员工列表
     *
     * @param name
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/payer/query", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage query(@RequestParam(required = false) String name,
                                 @RequestParam("pageNum") Integer pageNum,
                                 @RequestParam("pageSize") Integer pageSize) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        RechargeManageInfoQueryReqDTO rechargeManageInfoQueryReq = new RechargeManageInfoQueryReqDTO();
        rechargeManageInfoQueryReq.setMerchantNo(getCurrentCustomerNumber());
        rechargeManageInfoQueryReq.setName(name);
        RechargeManageInfoQueryRespDTO rechargeManageInfo = rechargeManageFacade.getRechargeManageInfo(rechargeManageInfoQueryReq);
        List<RechargeManageInfoDTO> newChargeManageRespList = Lists.newArrayList();

        int pageTotals = getTotalPages(rechargeManageInfo.getList().size(), pageSize);
        if (pageNum <= pageTotals) {
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = ((pageNum == pageTotals) ? rechargeManageInfo.getList().size() : (pageNum * pageSize));
            newChargeManageRespList = rechargeManageInfo.getList().subList(fromIndex, toIndex);
        }
        resMsg.put("rechargeManageRespList",newChargeManageRespList);
        resMsg.put("pageNum",pageNum);
        resMsg.put("pageSize",pageSize);
        resMsg.put("pageTotals",pageTotals);
        resMsg.put("total", rechargeManageInfo.getList().size());
        return resMsg;
    }

    public static int getTotalPages(Integer totalSize, Integer pageSize) {
        double totalPages = Math.ceil((double)totalSize / (double)pageSize);
        if (totalPages < 1.0) {
            totalPages = 1.0;
        }

        return (int)totalPages;
    }

    /**
     * 充值可支付员工列表更新
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/payer/update", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage update(@RequestParam("id") Long id) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        RechargeManageRequestDTO rechargeManageRequest = new RechargeManageRequestDTO();
        rechargeManageRequest.setId(id);
        RechargeManageInfoRespDTO rechargeManageInfoResp = rechargeManageFacade.deleteById(rechargeManageRequest);
        if(!Objects.equals(rechargeManageInfoResp.getReturnCode(), "UA00000")){
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("删除失败");
        }
        return resMsg;
    }

    @RequestMapping(value = "/payType/query", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getPayType(HttpServletRequest request) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        List<PayTypeDTO> payTypeList = Lists.newArrayList();
        PayTypeDTO payType = new PayTypeDTO();
        payType.setCode(PayTypeEnum.B2C.getSecondProductCode());
        payType.setValue(PayTypeEnum.B2C.desc());
        payTypeList.add(payType);
        resMsg.put("payTypeList",payTypeList);
        return resMsg;
    }

    /**
     * 校验用户名密码
     * @param payerUserName
     * @param payerUserPassword
     * @param dto
     */
    private void checkUserPassword(String payerUserName, String payerUserPassword, RechargeRequestDTO dto) {
        Assert.isTrue(!StringUtils.isEmpty(payerUserName), "付款方用户名不能为空");
        Assert.isTrue(!StringUtils.isEmpty(payerUserPassword), "交易密码不能为空");
        try {
            // 交易密码需要解密
            payerUserPassword = BACRsaUtil.privateDecrypt(payerUserPassword, ConfigUtils.getPrivateKey());
            TradePasswordValidateDTO tradePasswordValidateDTO = userFacade.queryTradePasswordValidateResult(payerUserName, payerUserPassword);
            LOGGER.info("付款方用户名:{},校验交易密码响应:{}", payerUserName, new Gson().toJson(tradePasswordValidateDTO));
            if (tradePasswordValidateDTO == null) {
                LOGGER.error("付款方用户名:{},校验交易密码无响应", payerUserName);
                throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("校验付款方信息异常");
            }
            if (tradePasswordValidateDTO.getIsTradeValidate() && UserStatusEnum.ACTIVATION == tradePasswordValidateDTO.getUserStatus()
                    && AuthenticationStatusEnum.SUCCESS == tradePasswordValidateDTO.getAuthenticationStatusEnum()) {
                if (dto.getMerchantNo().equals(tradePasswordValidateDTO.getMerchantNumber())) {
                    throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("付款方不能为当前登录商户，请重新输入");
                }
                AccountPayRequestDTO accountPayRequestDTO = new AccountPayRequestDTO();
                accountPayRequestDTO.setPayerMerchantNo(tradePasswordValidateDTO.getMerchantNumber());
                accountPayRequestDTO.setPayerUserName(payerUserName);
                dto.setAccountPayRequestDTO(accountPayRequestDTO);
            } else {
                if (AuthenticationStatusEnum.FORBID == tradePasswordValidateDTO.getAuthenticationStatusEnum()) {
                    long minute = tradePasswordValidateDTO.getFrezonSecond() / 60;
                    long second = tradePasswordValidateDTO.getFrezonSecond() % 60;
                    long showMinute = second > 0 ? minute + 1 : minute;
                    throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("付款方信息有误，请于" + (showMinute == 0 ? 1 : showMinute) + "分钟后重试");
                }
                throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("付款方信息有误");
            }
        } catch(UserBusinessException ex) {
            LOGGER.error("付款方用户名:{},付款方信息有误,Cause By:{}", payerUserName, ex.getMessage());
            throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("付款方信息有误");
        } catch (AccountPayException ex) {
            LOGGER.error("付款方用户名:{},付款方信息有误,Cause By:{}", payerUserName, ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            LOGGER.error("付款方用户名:" + payerUserName + ",调用校验交易密码系统异常", ex);
            throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("校验付款方信息异常");
        }
    }

    /**
     * 查询充值订单状态（单笔）
     *
     * @param request:
     * @return com.yeepay.g3.app.newframe.response.ResponseMessage
     * @Description: 查询充值订单状态（单笔）
     * <AUTHOR>
     * @date 2020-03-11 11:34
     */
    @RequestMapping(value = "/queryOrder", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage queryOrder(HttpServletRequest request, @RequestParam("orderNo") String orderNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        if (StringUtils.isEmpty(orderNo)) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("订单号为空");
            return resMsg;
        }
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            RechargeOrderQueryRespDTO rechargeOrderQueryRespDTO = queryRechargeOrder(currentCustomerNumber, orderNo);
            LOGGER.info("查询单笔充值订单返回，queryBacOrderRespDTO={}", JSON.toJSONString(rechargeOrderQueryRespDTO));
            RechargeResponseParam rechargeResponseParam = new RechargeResponseParam();
            if (rechargeOrderQueryRespDTO != null) {
                rechargeResponseParam.setStatus(rechargeOrderQueryRespDTO.getStatus().name());
                rechargeResponseParam.setOrderNo(rechargeOrderQueryRespDTO.getOrderNo());
            }
            resMsg.put("rechargeOrder", rechargeResponseParam);
        } catch (Exception e) {
            LOGGER.error("queryBacOrder,查询异常，e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
        }

        LOGGER.info("订单明细返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    /**
     * 查询充值订单记录
     * @param rechargeQueryParam:
     * @Description: 查询充值订单列表（批量)
     */
    @RequestMapping(value = "/queryOrderList")
    @ResponseBody
    public ResponseMessage queryOrderList(RechargeQueryParam rechargeQueryParam,
                                          @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                          @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo
    ) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("查询充值订单，请求参数{}", JSON.toJSONString(rechargeQueryParam));
        PreCheck.checkArgument(StringUtils.isNotBlank(rechargeQueryParam.getCustomerNumber()), "商户编号不能为空");
        if (rechargeQueryParam.isEmptyCheck()) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询参数为空");
            return resMsg;
        }
        String status = rechargeQueryParam.getStatus();
        if (status.equals(OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.name())) {
            status = OrderStatusEnum.PAY_SUCCESS.name() + "," + OrderStatusEnum.ACCOUNTING.name() + "," + OrderStatusEnum.ACCOUNTING_EXCEPTION.name();
            rechargeQueryParam.setStatus(status);
        } else if (OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(status)) {
            status = OrderStatusEnum.ACCOUNTING_FAIL.name();
            rechargeQueryParam.setStatus(status);
        }
        QueryResult queryResult = null;
        try {
            queryResult = this.queryRechargeOrderList(rechargeQueryParam, pageNo, pageSize);
        } catch (Exception e) {
            LOGGER.error("queryRechargeOrderList,查询异常,e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }
        if (queryResult != null) {
            if (!CheckUtils.isEmpty(queryResult.getData())) {
                for (Map<String, Object> map : queryResult.getData()) {
                    adaptReturnResult(map);
                }
            }
            resMsg.put("dataList", queryResult.getData());
        }
        resMsg = this.queryRechargeOrderListSum(rechargeQueryParam, resMsg);
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        LOGGER.info("查询充值订单列表返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    /**
     * 下载充值电子回单
     *
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/downElectronicReceipt")
    @ResponseBody
    public void downElectronicReceipt(DownloadElectronicReqDTO param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("下载充值电子回单 请求参数{}",JSONObject.toJSONString(param));
            String orderNo = param.getOrderNo();
            if (StringUtils.isBlank(orderNo)) {
                LOGGER.error("充值下载[电子回单]  orderNo 为空");
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("订单编号为空");
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            long start = System.currentTimeMillis();
            //查询充值详情
            RechargeOrder query = new RechargeOrder();
            query.setMerchantNo(param.getCustomerNumber());
            query.setOrderNo(orderNo);
            RechargeOrder rechargeOrder = rechargeOrderService.queryRechargeOrderInfo(query);
            if (rechargeOrder == null) {
                LOGGER.error("充值下载[电子回单]  rechargeOrder 为空 query={}", JSONObject.toJSONString(query));
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("充值订单详情为空");
            }

            //构建打印pdf
            PdfUtils pdfUtils = new PdfUtils();
            OrderParam orderParam = buildParam(rechargeOrder);
            String data = pdfUtils.generateReceipt(orderParam, getCurrentCustomerNumber());

            BASE64Decoder decoder = new BASE64Decoder();
            //Base64解码
            byte[] len = decoder.decodeBuffer(data);
            for (int i = 0; i < len.length; ++i) {
                //调整异常数据
                if (len[i] < 0) {
                    len[i] += 256;
                }
            }
            /*在开始写入的那一刻再定义这些，不然影响异常的弹窗*/
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + "RECHARGE" + "_" + System.currentTimeMillis() + ".pdf");
            OutputStream out = response.getOutputStream();
            out.write(len);
            out.flush();
            out.close();
            long end = System.currentTimeMillis();
            LOGGER.info("充值电子回单耗时{}ms", (end - start));
        } catch (Throwable ex) {
            LOGGER.error("充值下载[电子回单] 异常， 来个弹窗 ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * 下载充值记录
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(RechargeQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("开始下载充值记录，请求参数{}", JSON.toJSONString(param));
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant()) {
                LOGGER.info("[红版商户后台]下载充值记录，customerNumber=[{}]", param.getCustomerNumber());
                param.setPlatformType(PlatformTypeEnum.RED_PLATFORM_MERCHANT.name());
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            String status = param.getStatus();
            if (status.equals(OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.name())) {
                status = OrderStatusEnum.PAY_SUCCESS.name() + "," + OrderStatusEnum.ACCOUNTING.name() +","+OrderStatusEnum.ACCOUNTING_EXCEPTION.name();
                param.setStatus(status);
            }else if(OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(status)){
                status = OrderStatusEnum.ACCOUNTING_FAIL.name();
                param.setStatus(status);
            }
            Boolean capitalManageOpen = getCapitalManage(param.getCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("充值订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant() && StringUtils.isNotBlank(param.getSyncType())
                    && "appointSync".equals(param.getSyncType())) {
                new RechargeOrderDownloadService(getCurrentUser(), param, desc.toString(), "充值订单查询-", capitalManageOpen).syncDownload(request, response);
            } else {
                new RechargeOrderDownloadService(getCurrentUser(), param, desc.toString(), "充值订单查询-", capitalManageOpen).download(request, response);
            }
            LOGGER.info("下载充值记录excel已完成");
        } catch (Throwable ex) {
            LOGGER.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * 获取银行汇款码和商户名称
     * @return
     */
    @RequestMapping(value="/offline/getRemittanceCode")
    @ResponseBody
    public ResponseMessage getRemittanceCode(){

        ResponseMessage response = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        //当前商户
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        //签约名称
        String signName = currentMerchant.getSignName();
        response.put("merchantSignName",signName);
        return response;

    }



    /**
     * @Description: 查询b2b银行列表
     * <AUTHOR>
     * @date 2020-11-02 15:02

     * @return java.util.List<com.yeepay.g3.app.account.pay.mboss.dto.BankInfo>
     */
    private List<BankInfo> queryB2BBankList(){
        QueryOnlineBankRulesByTemplateParam dto = new QueryOnlineBankRulesByTemplateParam();
        dto.setMerchantNo(getCurrentCustomerNumber());
        dto.setCardType("DEBIT");
        String ncB2BConfig = UniformConfigUtils.getNcB2BConfig();
        dto.setTemplateCode(ncB2BConfig);
        List<BankInfo> businessBankInfoList = new ArrayList<BankInfo>();
        try {
            OnlineBankTemplateInfoDTO onlineBankTemplateInfoDTO = onlineBankCashierTemplateFacade.queryOnlineBankRuleInfos(dto);
            LOGGER.info("B2B返回收银台列表，onlineBankTemplateInfo={}", JSON.toJSONString(onlineBankTemplateInfoDTO));
            if (onlineBankTemplateInfoDTO != null) {
                OnlineAllBankRuleInfoDTO bankRules = onlineBankTemplateInfoDTO.getBankRules();
                if (bankRules != null) {
                    List<OnlineBankRuleInfoDTO> b2bRules = bankRules.getB2bRules();
                    if (CollectionUtils.isNotEmpty(b2bRules)) {
                        for (OnlineBankRuleInfoDTO onlineBankRuleDto : b2bRules) {
                            BankInfo bankInfo = new BankInfo();
                            bankInfo.setBankCode(onlineBankRuleDto.getBankCode());
                            bankInfo.setBankName(onlineBankRuleDto.getBankName());
                            businessBankInfoList.add(bankInfo);
                        }
                    }
                }
            }
        } catch (NCConfigException e) {
            LOGGER.error("返回收银台列表异常，e={}", e);
        }
        return businessBankInfoList;
    }

    /**
     * @Description: 查询b2c银行列表
     * <AUTHOR>
     * @date 2020-11-02 15:02

     * @return java.util.List<com.yeepay.g3.app.account.pay.mboss.dto.BankInfo>
     */
    private List<BankInfo> queryB2CBankList(){
        QueryOnlineBankRulesByTemplateParam dto = new QueryOnlineBankRulesByTemplateParam();
        List<BankInfo> personBankInfoList = new ArrayList<BankInfo>();
        String ncB2CConfig = UniformConfigUtils.getNcB2CConfig();
        dto.setTemplateCode(ncB2CConfig);
        dto.setMerchantNo(getCurrentCustomerNumber());
        dto.setCardType("DEBIT");
        try {
            OnlineBankTemplateInfoDTO onlineBankTemplateInfoDTO = onlineBankCashierTemplateFacade.queryOnlineBankRuleInfos(dto);
            if (onlineBankTemplateInfoDTO != null) {
                OnlineAllBankRuleInfoDTO bankRules = onlineBankTemplateInfoDTO.getBankRules();
                if (bankRules != null) {
                    List<OnlineBankRuleInfoDTO> b2cRules = bankRules.getB2cRules();
                    if (CollectionUtils.isNotEmpty(b2cRules)) {
                        for (OnlineBankRuleInfoDTO onlineBankRuleDto : b2cRules) {
                            BankInfo bankInfo = new BankInfo();
                            bankInfo.setBankCode(onlineBankRuleDto.getBankCode());
                            bankInfo.setBankName(onlineBankRuleDto.getBankName());
                            personBankInfoList.add(bankInfo);
                        }
                    }
                }
            }
        } catch (NCConfigException e) {
            LOGGER.error("返回收银台列表异常，e={}", e);
        }
        return personBankInfoList;
    }

    /**
     * @Description: 查询账户余额 (抛异常)
     * <AUTHOR>
     * @date 2020-11-02 15:14
     * @param accountType:
     * @return java.math.BigDecimal
     */
    private BigDecimal queryBalance(String accountType){
        //查询资金账户余额
        BigDecimal balance = new BigDecimal(BigInteger.ZERO);
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(getCurrentCustomerNumber(), com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum.valueOf(accountType));
        BigDecimal queryBalance = queryAccountResponseDto.getBalance();
        if (queryBalance != null) {
            balance = queryBalance;
        }
        return balance;
    }

    /**
     * 查询充值汇总
     *
     * @param param
     * @param resMsg
     */
    private ResponseMessage queryRechargeOrderListSum(RechargeQueryParam param, ResponseMessage resMsg) {
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getCustomerNumber());
        List<Map<String, Object>> withOrderListSum = null;
        try {
            withOrderListSum = QueryServiceUtil.query("bacRechargeService", "queryRechargeOrderListSum", queryMap);
        } catch (Exception e) {
            LOGGER.error("queryRechargeOrderListSum-参数异常", e);
            // 直接把异常信息返回
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }
        // 如果查询结果不为空的话
        if (withOrderListSum != null && !withOrderListSum.isEmpty()) {
            Map<String, Object> sumResult = withOrderListSum.get(0);
            String sum_amount = sumResult.get("sum_amount").toString();
            String sum_fee = sumResult.get("sum_fee").toString();

            resMsg.getData().put("sum_count", sumResult.get("sum_count").toString());// 总笔数
            resMsg.getData().put("sum_amount", new BigDecimal(sum_amount).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总金额
            resMsg.getData().put("sum_fee", new BigDecimal(sum_fee).setScale(2, BigDecimal.ROUND_HALF_UP).toString());// 总手续费
            resMsg.getData().put("totalCount", sumResult.get("sum_count").toString());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", "0.00");// 总金额
            resMsg.getData().put("sum_fee", "0.00");// 总手续费
        }
        return resMsg;
    }

    /**
     * @param param:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     * @Description: 查询充值列表
     */
    private QueryResult queryRechargeOrderList(RechargeQueryParam param, int pageNo, int pageSize) {
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        queryMap.put("customerNumber", param.getCustomerNumber());
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("bacRechargeService", QueryService.class);
        QueryResult queryRechargeOrderList = queryService.query("queryRechargeOrderList", queryParam);
        return queryRechargeOrderList;
    }

    /**
     * 查询订单信息
     */
    private RechargeOrderQueryRespDTO queryRechargeOrder(String currentCustomerNumber, String orderNo) {
        RechargeOrderQueryReqDTO dto = new RechargeOrderQueryReqDTO();
        dto.setInitiateMerchantNo(currentCustomerNumber);
        dto.setOrderNo(orderNo);
        LOGGER.info("查询单笔充值订单，dto={}", JSON.toJSONString(dto));
        RechargeOrderQueryRespDTO rechargeOrderQueryRespDTO = rechargeFacade.queryRechargeOrderInfo(dto);
        return rechargeOrderQueryRespDTO;
    }


    /**
     * 适配返回结果
     *
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }

        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (null != detail.get("credit_amount")) {
                detail.put("credit_amount", new BigDecimal(detail.get("credit_amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }
            if (null != detail.get("apply_amount")) {
                detail.put("apply_amount", new BigDecimal(detail.get("apply_amount").toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }

            if (null != detail.get("fee") && StringUtils.isNotBlank(detail.get("fee").toString())) {
                if(StringUtils.equals(""+detail.get("fee_undertaker_merchant_no"),""+detail.get("merchant_no"))){
                    detail.put("fee", new BigDecimal(detail.get("fee").toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                }else{
                    detail.put("fee","无");
                }

            }else {
                detail.put("fee","无");
            }
            String accountType = (String)detail.get("account_type");
            if(null != accountType){
                detail.put("account_type",UniformConfigUtils.getUnionAccountTypeNames().getOrDefault(accountType,accountType));
            }

            String orderStatus = (String) detail.get("status");
            if (OrderStatusEnum.PAY_SUCCESS.name().equals(orderStatus) || OrderStatusEnum.ACCOUNTING.name().equals(orderStatus) || OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(orderStatus)) {
                detail.put("status", OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.name());
            }else if(OrderStatusEnum.ACCOUNTING_FAIL.name().equals(orderStatus)){
                detail.put("status", OrderStatusEnum.ACCOUNTING_EXCEPTION.name());
            }

            //下单时间
            Object obj = detail.get("create_time");
            if (null != obj) {
                if (obj instanceof String) {
                    String str = String.valueOf(obj);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("create_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("create_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (obj instanceof Timestamp) {
                    detail.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                }
            }

        } catch (Exception e) {
            LOGGER.error("这都能错..擦....", e);
        }
        return detail;
    }

    /**
     * 变更为可结算
     * @param requestNo
     * @return
     */
    @RequestMapping(value = "settle", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage getRemittanceCode(@Param("requestNo") String requestNo) {
        ResponseMessage response = null;
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        LOGGER.info("[充值结算] 请求变更结算订单为可结算 requestNo: {}, merchantNo: {}", requestNo, currentMerchant.getMerchantNo());
        if (org.springframework.util.StringUtils.isEmpty(requestNo)) {
            response = new ResponseMessage(ResponseMessage.Status.ERROR);
            response.setErrCode(ErrorCode.SYSTEM_ERROR);
            response.setErrMsg(ErrorCode.getErrorMsgByCode(ErrorCode.SYSTEM_ERROR));
            return response;
        }
        RechargeSettleReqDTO reqDTO = new RechargeSettleReqDTO();
        reqDTO.setMerchantNo(currentMerchant.getMerchantNo());
        reqDTO.setRequestNo(requestNo);
        RechargeSettleRespDTO rechargeSettleRespDTO = null;
        try {
            rechargeSettleRespDTO = rechargeFacade.rechargeSettle(reqDTO);
        } catch (Exception e) {
            LOGGER.error("[充值结算] 通讯失败, 系统异常 requestNo: {}, merchantNo: {}", requestNo, currentMerchant.getMerchantNo());
            response = new ResponseMessage(ResponseMessage.Status.ERROR);
            response.setStatus(ResponseMessage.Status.ERROR);
            response.setErrCode(ErrorCode.SYSTEM_ERROR);
            response.setErrMsg(ErrorCode.getErrorMsgByCode(ErrorCode.SYSTEM_ERROR));
            return response;
        }
        if ("UA00000".equals(rechargeSettleRespDTO.getReturnCode())) {
            response = new ResponseMessage(ResponseMessage.Status.SUCCESS);
            return response;
        } else {
            response = new ResponseMessage(ResponseMessage.Status.ERROR);
            response.setStatus(ResponseMessage.Status.ERROR);
            response.setErrCode(rechargeSettleRespDTO.getReturnCode());
            response.setErrMsg(rechargeSettleRespDTO.getReturnMsg());
            return response;
        }
    }


    private List<String> setRechargeProduct(List<String> accountProductList, AccountBasicParam accountBasicParam, List<String> allProducts) {

        if (CollectionUtils.isNotEmpty(allProducts)) {
            Map<String, ProductCodeParam> payTypeMap = accountBasicParam.getPayTypeMap();
            try {
                //产品属性
                String productAttribute = accountBasicParam.getProductAttribute();
                for (PayTypeEnum payType : PayTypeEnum.values()) {
                    //支付类型
                    if(PayTypeEnum.B2B.name().equals(payType.name()) && payTypeMap.containsKey("B2B_SAME")){
                        addAccountProductList(payTypeMap.get("B2B_SAME"),payType,accountProductList,allProducts,productAttribute);
                    }
                    if(PayTypeEnum.B2C.name().equals(payType.name()) && payTypeMap.containsKey("B2C_SAME")){
                        addAccountProductList(payTypeMap.get("B2C_SAME"),payType,accountProductList,allProducts,productAttribute);
                    }
                    addAccountProductList(payTypeMap.get(payType.name()),payType,accountProductList,allProducts,productAttribute);
                }
            } catch (Exception e) {
                LOGGER.error("获取配置异常", e);
            }
        }
        LOGGER.info("开通产品，accountProductList={}", JSON.toJSONString(accountProductList));
        return accountProductList;
    }


    /**
     * 取有没有开通产品
     * @param productCodeParam
     * @param payType
     * @param accountProductList
     * @param allProducts
     */
    public void addAccountProductList(ProductCodeParam productCodeParam,PayTypeEnum payType,
                                      List<String> accountProductList,List<String> allProducts,String productAttribute){
        StringBuffer buffer = new StringBuffer();
        if (productCodeParam != null) {
            String firstProductCode = productCodeParam.getFirstProductCode();
            String secondProductCode = productCodeParam.getSecondProductCode();
            String thirdProductCode = productCodeParam.getThirdProductCode();
            if (StringUtils.isNotBlank(productAttribute)) {
                buffer.append(productAttribute);
            }
            if (StringUtils.isNotBlank(firstProductCode)) {
                buffer.append("," + firstProductCode);
            }
            if (StringUtils.isNotBlank(secondProductCode)) {
                buffer.append("," + secondProductCode);
            }
            if (StringUtils.isNotBlank(thirdProductCode)) {
                buffer.append("," + thirdProductCode);
            }
            if (CollectionUtils.isNotEmpty(allProducts)) {
                for (String product : allProducts) {
                    if (!product.equals(buffer.toString())) {
                        continue;
                    }
                    accountProductList.add(payType.name());

                }
            }
        }
    }


    private List<String> getAllProductByMerchantNo(String merchantNo,List<String> allProductList){
        BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
        QueryMerchantProductRespDTO merchantProductRespDTO = businessCheckRemoteService.queryMerchantProduct(merchantNo);
        if("0000".equals(merchantProductRespDTO.getRetCode()) && !CheckUtils.isEmpty(merchantProductRespDTO.getBaseProductList())){
            for (BaseProductDTO baseProductDTO : merchantProductRespDTO.getBaseProductList()) {
                StringBuffer buffer = new StringBuffer();
                if(!"AVAILABLE".equals(baseProductDTO.getProductStatus())){
                    continue;
                }

                if(!CheckUtils.isEmpty(baseProductDTO.getProductType())){
                    buffer.append(baseProductDTO.getProductType());
                }
                if (!CheckUtils.isEmpty(baseProductDTO.getFirstBaseProductCode())) {
                    buffer.append(","+baseProductDTO.getFirstBaseProductCode());
                }
                if(!CheckUtils.isEmpty(baseProductDTO.getSecondBaseProductCode())){
                    buffer.append(","+baseProductDTO.getSecondBaseProductCode());
                }
                if(!CheckUtils.isEmpty(baseProductDTO.getThirdBaseProductCode())){
                    buffer.append(","+baseProductDTO.getThirdBaseProductCode());
                }
                if(StringUtils.isNotBlank(buffer.toString())){
                    allProductList.add(buffer.toString());
                }
            }
        }
        return allProductList;

    }

    /**
     * 跳转到历史订单记录
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/queryHistoryOrder")
    public ModelAndView queryHistoryOrder(HttpServletRequest request,HttpServletResponse response) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("recharge/rechargeQueryHistory");
        mav.addObject("UIWebRootUrl",WebPropertiesHolder.getUIWebRootUrl());
        cookieService.addCookie(request,response,"/bac-app");
        return mav;
    }


    /**
     * 是否有授信业务
     *
     * @param merchantNo
     * @return
     */
    private Boolean getCapitalManage(String merchantNo) {
        QueryMatchingLoanReqDTO reqDTO = new QueryMatchingLoanReqDTO();
        reqDTO.setToMerchantNo(merchantNo);
        reqDTO.setRequestBiz("RECHARGE");
        QueryMatchingLoanRespDTO respDTO = capitalManageService.queryLoanConfig(reqDTO);
        if (AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(respDTO.getReturnCode())) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 跳转充值详情页(迁移红版后台)
     *
     * @param orderNo
     * @return
     */
    @RequestMapping(value = "/rechargeDetail", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO rechargeDetail(@RequestParam("orderNo") String orderNo,@RequestParam(value = "customerNumber") String customerNumber) {
        LOGGER.info("[红版商户后台]跳转充值详情页，orderNo={} the customerNumber=[{}]", orderNo,customerNumber);
        Map<String,Object> map=new HashMap<>();
        if (!CheckUtils.isEmpty(orderNo) && !CheckUtils.isEmpty(customerNumber)) {
            RechargeOrder rechargeOrder;
            RechargeOrder dto = new RechargeOrder();
            dto.setOrderNo(orderNo);
            dto.setMerchantNo(customerNumber);
            try {
                rechargeOrder = rechargeOrderService.queryRechargeOrderInfo(dto);
                LOGGER.info("[红版商户后台]查询订单返回，rechargeOrder={}", JSON.toJSONString(rechargeOrder));
            } catch (Exception e) {
                LOGGER.error("[红版商户后台]查询充值订单异常", e);
                return BaseRespDTO.fail("查询充值订单失败，请稍后再试");
            }
            RechargeResponseParam rechargeResponseParam = new RechargeResponseParam();
            if (rechargeOrder != null) {
                RechargeOrderStatus orderStatus = rechargeOrder.getStatus();
                if (OrderStatusEnum.PAY_SUCCESS.name().equals(orderStatus.name()) || OrderStatusEnum.ACCOUNTING.name().equals(orderStatus.name()) || (OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(orderStatus.name()))) {
                    rechargeResponseParam.setStatus(OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.getDesc());

                } else if (OrderStatusEnum.ACCOUNTING_FAIL.name().equals(orderStatus.name())) {
                    rechargeResponseParam.setStatus(OrderStatusEnum.ACCOUNTING_EXCEPTION.getDesc());
                } else {
                    rechargeResponseParam.setStatus(OrderStatusEnum.valueOf(rechargeOrder.getStatus().name()).getDesc());
                }
                rechargeResponseParam.setCreateDate(DateUtil.formatByDateTimePattern(rechargeOrder.getCreateTime()));
                rechargeResponseParam.setOrderNo(rechargeOrder.getOrderNo());
                rechargeResponseParam.setAmount(null == rechargeOrder.getApplyAmount() ? null : NumberUtils.formateNum(null, rechargeOrder.getApplyAmount()));
                rechargeResponseParam.setDeductAmount(null == rechargeOrder.getOrderAmount() ? null : NumberUtils.formateNum(null, rechargeOrder.getOrderAmount()));
                rechargeResponseParam.setDesc(rechargeOrder.getRemark());
                rechargeResponseParam.setFee(null == rechargeOrder.getFee() ? null : NumberUtils.formateNum(null, rechargeOrder.getFee()));
                rechargeResponseParam.setBankName(null == rechargeOrder.getBankName() ? "" : rechargeOrder.getBankName());
                rechargeResponseParam.setProductType(rechargeOrder.getPayType().name());
                rechargeResponseParam.setFailReason(rechargeOrder.getReturnMsg());
                if (StringUtils.isNotEmpty(rechargeOrder.getFeeUndertakerMerchantNo()) && !StringUtils.equals(rechargeOrder.getMerchantNo(), rechargeOrder.getFeeUndertakerMerchantNo())) {
                    rechargeResponseParam.setFee(null);
                }
                rechargeResponseParam.setAccountType(UniformConfigUtils.getUnionAccountTypeNames().getOrDefault(rechargeOrder.getAccountType().name(), rechargeOrder.getAccountType().desc()));


                rechargeResponseParam.setProductTypeDesc(rechargeOrder.getPayType().desc());
                switch (rechargeOrder.getPayType()) {
                    case B2B:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case B2C:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case BANK_TRANSFER:
                        rechargeResponseParam.setRemitComment(rechargeOrder.getRemitComment());
                        break;
                    case ACCOUNT_PAY:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        rechargeResponseParam.setProductTypeDesc(rechargeOrder.getPayType().desc() + " - " + rechargeOrder.getPayerAccountName());
                        break;
                    case QUICK_PUBLIC:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case BANK_PAY:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                    case PUBLIC_ENTRUST:
                        rechargeResponseParam.setBankOrderId(rechargeOrder.getBankOrderNo());
                        break;
                }

                if (rechargeOrder.getBankTrxTime() != null) {
                    rechargeResponseParam.setBankTrxTime(DateUtil.formatByDateTimePattern(rechargeOrder.getBankTrxTime()));
                }
                if (rechargeOrder.getFinishTime() != null) {
                    rechargeResponseParam.setCompletionTime(DateUtil.formatByDateTimePattern(rechargeOrder.getFinishTime()));
                }
                rechargeResponseParam.setOperateUser(rechargeOrder.getOperator());
                rechargeResponseParam.setCapitalInfo(rechargeOrder.getCapitalInfo());
                rechargeResponseParam.setMerchantNo(rechargeOrder.getMerchantNo());
            }

            LOGGER.info("[红版商户后台]查询订单明细返回，rechargeResponseParam={}", JSON.toJSONString(rechargeResponseParam));
            map.put("orderDetail", rechargeResponseParam);
        }
        return BaseRespDTO.success(map);
    }

    /**
     * 处理数据
     * @param rechargeOrder
     * @return
     */
    private OrderParam buildParam(RechargeOrder rechargeOrder ) {
        OrderParam orderParam = new OrderParam();
        orderParam.setParentMerchantNo(rechargeOrder.getParentMerchantNo());
        orderParam.setOrderNo(rechargeOrder.getOrderNo());
        orderParam.setInitiateMerchantNo(rechargeOrder.getInitiateMerchantNo());
        orderParam.setTradeType(TradeTypeEnum.RECHARGE);
        return orderParam;
    }

    /**
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     * @Description: 充值页面
     * <AUTHOR>
     * @date 2020-03-11 11:33
     */
    @RequestMapping(value = "/split-view", method = RequestMethod.GET)
    public ModelAndView splitView(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("recharge/unionOrder");
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        LOGGER.info("[银联订单B2B支付] 查询页面菜单,{}", request.getSession().getAttribute(("tabMenu")));
        return mav;
    }

    /**
     * @param request:
     * @return org.springframework.web.servlet.ModelAndView
     * @Description: 充值页面
     * <AUTHOR>
     * @date 2020-03-11 11:33
     */
    @RequestMapping(value = "/split-order-detail", method = RequestMethod.GET)
    public ModelAndView splitOrderDetailView(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("recharge/unionOrderDetail");
        return mav;
    }

    /**
     * 专款账户充值，查询商户的授信业务的 资金提供方信息
     * @return
     */
    @RequestMapping(value = "/query/match-capital", method = RequestMethod.GET)
    @ApiOperation(value = "查询授信匹配资方出款信息")
    @ResponseBody
    public BaseRespDTO<List<MatchCapitalInfo>> queryMatchCapital() {
        String currentCustomerNumber = getCurrentCustomerNumber();
        String merchantSignName =  merchantRemoteService.getMerchantName(currentCustomerNumber);
        LOGGER.info("[查询授信匹配资方出款信息] 商户={}", currentCustomerNumber);
        try {
            QueryMatchingLoanReqDTO queryMatchingLoanReqDTO = new QueryMatchingLoanReqDTO();
            queryMatchingLoanReqDTO.setToMerchantNo(currentCustomerNumber);
            queryMatchingLoanReqDTO.setRequestBiz("RECHARGE");
            QueryMatchingLoanRespDTO loanConfigResp = capitalManageService.queryLoanConfig(queryMatchingLoanReqDTO);
            if (null ==  loanConfigResp || AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(loanConfigResp.getReturnCode())|| null == loanConfigResp.getLoanInfoVos()) {
               throw AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.newInstance("暂时未配置资方信息，请联系行业线进行配置。");
            }
            Map<String, List<MatchingLoanInfoVo>> matchingLoanMap = loanConfigResp.getLoanInfoVos().stream()
                    .filter(loanInfo -> "YEEPAY_ACCOUNT".equals(loanInfo.getPayeeAccountSource()))
                    .collect(Collectors.groupingBy(MatchingLoanInfoVo::getCapitalNo));
            if(matchingLoanMap.size() == 0){
                LOGGER.info("[查询授信匹配资方出款信息] 没有出金到易宝账户的放款信息 商户={}", currentCustomerNumber);
                throw AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.newInstance("暂时未配置资方信息，请联系行业线进行配置。");
            }

            /*资方记账簿余额信息-- 这个 商户+资方 唯一的记账簿*/
            QueryAccountBookInfoRespDTO capitalMerchantBalanceResp = capitalManageService.queryAccountBookInfo(currentCustomerNumber);
            if(CollectionUtils.isEmpty(capitalMerchantBalanceResp.getAccountBookInfoVos())){
                throw AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.newInstance("暂时未配置资方信息，请联系行业线进行配置。");
            }
            Map<String, List<AccountBookVo>> capitalMerchantBalanceMap = capitalMerchantBalanceResp.getAccountBookInfoVos().stream()
                    .collect(Collectors.groupingBy(AccountBookVo::getCapitalNo));
            List<MatchCapitalInfo> list = Lists.newArrayList();
            for (Map.Entry<String, List<MatchingLoanInfoVo>> entry : matchingLoanMap.entrySet()) {
                MatchCapitalInfo capitalInfo = new MatchCapitalInfo();

                List<MatchCapitalLoanInfo> loanInfoList = entry.getValue().stream().map(loan -> {
                    MatchCapitalLoanInfo capitalLoanInfo = new MatchCapitalLoanInfo();
                    capitalLoanInfo.setPayerAccountName(loan.getAccountName());
                    capitalLoanInfo.setPayerAccountNo(loan.getAccountNo());
                    capitalLoanInfo.setBankCode(loan.getBankCode());
                    return capitalLoanInfo;
                }).collect(Collectors.toList());
                capitalInfo.setLoanInfoList(loanInfoList);
                AccountBookVo accountBookInfoVo = capitalMerchantBalanceMap.get(entry.getKey()).get(0);
                capitalInfo.setCapitalName(accountBookInfoVo.getCapitalName());
                capitalInfo.setCapitalNo(accountBookInfoVo.getCapitalNo());
                capitalInfo.setReceiveAccountNo(accountBookInfoVo.getAccountBookNo());
                capitalInfo.setReceiveAccountName(merchantSignName);/*这里取融资人的签约名*/
                capitalInfo.setCapitalAccountBookBalance(accountBookInfoVo.getBalance());
                list.add(capitalInfo);
            }

            LOGGER.info("[查询授信匹配资方出款信息] currentCustomerNumber={}，list={}", currentCustomerNumber, JSONUtils.toJsonString(list));
            return BaseRespDTO.success(list);
        } catch (YeepayBizException e) {
            LOGGER.warn("[查询提现绑定卡] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("查询绑卡信息异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail("系统异常");
        }
    }

    /**
     * 确认 拆单充值
     *
     * @return
     */
    @ApiOperation(value = "确认 拆单充值")
    @RequestMapping(value = "/confirmSplitRecharge", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessageDTO<SplitRechargeResp> confirm(@Valid @RequestBody SplitRechargeReq reqDTO) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[拆单充值下单] 请求参数，currentCustomerNumber ={},reqDTO={}", currentCustomerNumber, JSON.toJSONString(reqDTO));
        try {
            verifyReq(reqDTO, currentCustomerNumber);
            SplitRechargeRequestDTO dto = assembleSplitRechargeRequestDTO(reqDTO);
            LOGGER.info("充值确认请求参数，dto={}", JSON.toJSONString(dto));
            SplitRechargeRespDTO rechargeRespDTO = rechargeFacade.splitRecharge(dto);
            LOGGER.info("充值结果返回参数，rechargeRespDTO={}", JSON.toJSONString(rechargeRespDTO));
            if (null == rechargeRespDTO) {
                return ResponseMessageDTO.fail("充值异常");
            }
            SplitRechargeResp resp = new SplitRechargeResp();
            if ("UA00000".equals(rechargeRespDTO.getReturnCode())) {
                resp.setOrderNo(rechargeRespDTO.getOrderNo());
                return ResponseMessageDTO.success(resp);
            }
            /*异常信息*/
            return ResponseMessageDTO.fail(rechargeRespDTO.getReturnMsg());
        } catch (Exception e) {
            LOGGER.error("充值异常，，reqDTO= " + JSON.toJSONString(reqDTO) + ",cased by", e);
            return ResponseMessageDTO.fail("系统异常，请稍后再试");
        }
    }

    private void verifyReq(SplitRechargeReq reqDTO, String currentCustomerNumber) {
        reqDTO.validateParam();
        BigDecimal amount = new BigDecimal(reqDTO.getAmount());
        if(amount.compareTo(BigDecimal.ZERO) <= 0){
            throw AccountPayException.PARAM_ERROR.newInstance("充值金额必须大于0");
        }
        if("SPECIAL_FUND_ACCOUNT".equals(reqDTO.getAccountType()) && "UNION_PAY_B2B".equals(reqDTO.getProductType())){
            QueryMatchingLoanReqDTO queryMatchingLoanReqDTO = new QueryMatchingLoanReqDTO();
            queryMatchingLoanReqDTO.setToMerchantNo(currentCustomerNumber);
            queryMatchingLoanReqDTO.setRequestBiz("RECHARGE");
            QueryMatchingLoanRespDTO loanConfigResp = capitalManageService.queryLoanConfig(queryMatchingLoanReqDTO);
            if (null ==  loanConfigResp || AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.getDefineCode().equals(loanConfigResp.getReturnCode())|| null == loanConfigResp.getLoanInfoVos()) {
                LOGGER.warn("[拆单充值下单] 请求参数， 未查询到 放款信息 merchantNo={},reqDTO={}", currentCustomerNumber, JSON.toJSONString(reqDTO));
                throw AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.newInstance("未查询到匹配资方放款信息，无法进行充值");
            }
            List<MatchingLoanInfoVo> list = loanConfigResp.getLoanInfoVos().stream().filter(loan ->
                loan.getAccountNo().equals(reqDTO.getPayerAccountNo()) && loan.getRemittanceNo().equals(reqDTO.getCapitalAccountBookNo())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(list)){
                LOGGER.warn("[拆单充值下单] 请求参数， 放款信息填写有误  merchantNo={},reqDTO={}", currentCustomerNumber, JSON.toJSONString(reqDTO));
                throw AccountPayException.ACCOUNT_BUSINESS_CONFIG_ERROR.newInstance("未查询到匹配资方放款信息，无法进行充值");
            }
        }
    }

    private SplitRechargeRequestDTO assembleSplitRechargeRequestDTO(SplitRechargeReq reqDTO) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        SplitRechargeRequestDTO dto = new SplitRechargeRequestDTO();
        dto.setSource("MP");
        dto.setRechargeAmount(new BigDecimal(reqDTO.getAmount()));
        dto.setInitiateMerchantNo(currentCustomerNumber);
        dto.setMerchantNo(currentCustomerNumber);
        dto.setAccountType(AccountTypeEnum.valueOf(reqDTO.getAccountType()));
        dto.setOperator(getCurrentUser().getLoginName());
        //有效期，订单号，回调地址,一级，二级
        Long requestNo = SnowflakeIdFactory.generateId();
        dto.setRequestNo("BZCZ" + requestNo);
        dto.setClientIp(NetUtils.getIpAddress());
        dto.setRemark(reqDTO.getDesc());
        if (PayTypeEnum.UNION_PAY_B2B.name().equals(reqDTO.getProductType())) {
            dto.setPayType(PayTypeEnum.UNION_PAY_B2B);
            UnionPayBankRequestDTO unionPayBankReq = new UnionPayBankRequestDTO();
            unionPayBankReq.setBankCode(reqDTO.getBankCode());
            unionPayBankReq.setPayerAccountNo(reqDTO.getPayerAccountNo());
            unionPayBankReq.setPayerAccountName(reqDTO.getPayerAccountName());
            unionPayBankReq.setReceiverAccountNo(reqDTO.getReceiveAccountNo());
            unionPayBankReq.setReceiverAccountName(reqDTO.getReceiveAccountName());
            dto.setUnionPayBankReq(unionPayBankReq);
        } else {
            LOGGER.warn("暂不支持的支付方式,reqDTO={}", JSON.toJSONString(reqDTO));
            throw new IllegalArgumentException("暂不支持的支付方式");
        }
        return dto;
    }



    /**
     * 查询拆单充值订单记录
     *
     */
    @ApiOperation(value = "查询拆单充值订单记录列表")
    @RequestMapping(value = "/querySplitOrderList" ,method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO<SumOrderBaseDTO<SplitRechargeOrderInfo>> querySplitOrderList(@Valid @RequestBody SplitRechargeQueryParam reqDTO) {
        LOGGER.info("[查询拆单充值]，请求参数{}", JSON.toJSONString(reqDTO));
        String currentCustomerNumber = getCurrentCustomerNumber();
        try {
            reqDTO.validateParam();
            Date createStartDate = DateUtil.getStartOfDayMinNano(DateUtil.string2Date(reqDTO.getCreateStartDate(), DateUtil.PATTERN_STANDARD10H));
            Date createEndDate = DateUtil.getEndOfDayMaxNano(DateUtil.string2Date(reqDTO.getCreateEndDate(), DateUtil.PATTERN_STANDARD10H));
            Map<String, Object> queryParam = new HashMap<>();
            queryParam.put("createStartDate", createStartDate);
            queryParam.put("createEndDate", createEndDate);
            queryParam.put("status", reqDTO.getStatus());
            queryParam.put("batchFlowNo", reqDTO.getBatchFlowNo());
            queryParam.put("customerNumber", StringUtils.isBlank(reqDTO.getCustomerNumber())?currentCustomerNumber:reqDTO.getCustomerNumber());
            queryParam.put("accountType", reqDTO.getAccountType());
            queryParam.put("startIndex", (reqDTO.getPageNo() - 1) * reqDTO.getPageSize());
            queryParam.put("pageSize", reqDTO.getPageSize());
            SumOrderBaseDTO resp = splitRechargeBatchDao.sumQuerySplitOrder(queryParam);
            if (null == resp || resp.getSumCount() == 0L) {
                LOGGER.info("[查询拆单充值]订单列表返回，数据不存在 ，resMsg={}", JSON.toJSONString(resp));
                resp.setDataList(Lists.newArrayList());
                return BaseRespDTO.success(resp);
            }
            List<SplitRechargeOrderInfo> list = splitRechargeBatchDao.pageQuerySplitOrder(queryParam).stream().map(e -> {
                SplitRechargeOrderInfo orderInfo = new SplitRechargeOrderInfo();
                orderInfo.setCreateTime(DateUtil.formatByDateTimePattern(e.getCreateTime()));
                orderInfo.setBatchFlowNo(e.getBatchFlowNo());
                orderInfo.setInitiateMerchantNo(e.getInitiateMerchantNo());
                orderInfo.setRequestNo(e.getRequestNo());
                orderInfo.setMerchantNo(e.getMerchantNo());
                orderInfo.setAccountType(e.getAccountType());
                orderInfo.setAmount(covertBigDecimal(e.getAmount()));
                orderInfo.setPayType(e.getPayType());
                orderInfo.setBankCode(e.getBankCode());
                orderInfo.setStatus(e.getStatus());
                orderInfo.setSumSuccessAmount(covertBigDecimal(e.getSumSuccessAmount()));
                orderInfo.setSumCreditAmount(covertBigDecimal(e.getSumCreditAmount()));
                orderInfo.setSumFee(covertBigDecimal(e.getSumFee()));
                orderInfo.setCompleteTime(DateUtil.formatByDateTimePattern(e.getCompleteTime()));
                orderInfo.setRemark(e.getRemark());
                orderInfo.setOperator(e.getOperator());
                orderInfo.setSource(e.getSource());
                orderInfo.setClientIp(e.getClientIp());
                orderInfo.setFirstMerchantNo(e.getFirstMerchantNo());
                orderInfo.setSecondMerchantNo(e.getSecondMerchantNo());
                return orderInfo;
            }).collect(Collectors.toList());
            resp.setPageNo(reqDTO.getPageNo());
            resp.setPageSize(reqDTO.getPageSize());
            resp.setDataList(list);
            LOGGER.info("[查询拆单充值]订单列表返回，resMsg={}", JSON.toJSONString(resp));
            return BaseRespDTO.success(resp);
        } catch (YeepayBizException e) {
            LOGGER.warn("[查询拆单充值] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[查询拆单充值] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail("系统异常");
        }
    }

    /**
     * 有元哈
     * @param amount
     * @return
     */
    private String covertBigDecimal(BigDecimal amount) {
        if(null == amount){
            return "-";
        }
        return NumberUtils.formateNum(null, amount) + " 元";
    }

@Resource
private SplitRechargeBatchService splitRechargeBatchService;
    /**
     * 下载拆单充值记录
     * @param param
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/split-recharge/download")
    @ApiOperation(value = "下载拆单充值记录")
    @ResponseBody
    public void downloadSplitRechargeRecord(SplitRechargeListDownParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            param.validateParam();
            String createStartDate = param.getCreateStartDate() + " 00:00:00";
            param.setCreateStartDate(createStartDate);
            String createEndDate = param.getCreateEndDate() + " 23:59:59";
            param.setCreateEndDate(createEndDate);
            LOGGER.info("[拆单充值]开始下载充值记录，请求参数{}", JSON.toJSONString(param));
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant()) {
                LOGGER.info("[拆单充值][红版商户后台]下载充值记录，customerNumber=[{}]", param.getCustomerNumber());
                param.setPlatformType(PlatformTypeEnum.RED_PLATFORM_MERCHANT.name());
            }
            PreCheck.checkArgument(StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            Boolean capitalManageOpen = getCapitalManage(param.getCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("充值订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            if (null != param.getQuerySubMerchant() && param.getQuerySubMerchant() && StringUtils.isNotBlank(param.getSyncType())
                    && "appointSync".equals(param.getSyncType())) {
                new SplitRechargeOrderDownloadService(getCurrentUser(), param, desc.toString(), "充值订单查询-", capitalManageOpen,splitRechargeBatchService).syncDownload(request, response);
            } else {
                new SplitRechargeOrderDownloadService(getCurrentUser(), param, desc.toString(), "充值订单查询-", capitalManageOpen,splitRechargeBatchService).download(request, response);
            }
            LOGGER.info("[拆单充值]下载充值记录excel已完成");
        } catch (Throwable ex) {
            LOGGER.error("[拆单充值]下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * 查询拆单充值订单记录
     *
     */
    @ApiOperation(value = "查询拆单充值订单详情")
    @RequestMapping(value = "/querySplitOrderDetail" ,method = RequestMethod.POST)
    @ResponseBody
    public BaseRespDTO<SplitRechargeOrderDetailModel> querySplitOrderDetail(@Valid @RequestBody SplitRechargeDetailQueryParam reqDTO) {
        LOGGER.info("[查询拆单充值详情]，请求参数{}", JSON.toJSONString(reqDTO));
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[查询拆单充值详情] 商户={}", currentCustomerNumber);
        try {
            SplitRechargeOrderEntity entity = splitRechargeBatchDao.selectSplitOrderByBatchFlowNo(reqDTO.getBatchFlowNo());
            if (null == entity) {
                LOGGER.info("[查询拆单充值详情]订单列表返回，数据不存在 ，BatchFlowNo={}", reqDTO.getBatchFlowNo());
                return BaseRespDTO.fail("订单不存在");
            }
            List<SplitRechargeSubOrderEntity> list = splitRechargeSubOrderDao.selectAllRechargeSubOrderByBatchFlowNo(reqDTO.getBatchFlowNo());
            List<SplitRechargeSubOrderModel> detailList = list.stream().map(e -> assembleSplitRechargeSubOrderModel(e)).collect(Collectors.toList());
            SplitRechargeOrderDetailModel resp = assembleSplitRechargeOrderDetailModel(entity, detailList);
            LOGGER.info("[查询拆单充值详情]订单列表返回，resMsg={}", JSON.toJSONString(resp));
            return BaseRespDTO.success(resp);
        } catch (YeepayBizException e) {
            LOGGER.warn("[查询拆单充值详情] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[查询拆单充值详情] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
            return BaseRespDTO.fail("系统异常");
        }
    }

    /*兼容一下原来的 详情页面*/
    /**
     * 跳转充值详情页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/querySplitOrderDetailView", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView querySplitOrderDetailView(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("recharge/unionOrderDetail");
        String orderNo = request.getParameter("orderNo");
        String customerNumber = request.getParameter("customerNumber");
        try {
            SplitRechargeOrderEntity entity = splitRechargeBatchDao.selectSplitOrderByBatchFlowNo(orderNo);
            if (null == entity) {
                LOGGER.info("[查询拆单充值详情]订单列表返回，数据不存在 ，BatchFlowNo={}", orderNo);
                mav.addObject("orderDetail", null);
                return mav;
            }
            List<SplitRechargeSubOrderEntity> list = splitRechargeSubOrderDao.selectAllRechargeSubOrderByBatchFlowNo(orderNo);
            List<SplitRechargeSubOrderModel> detailList = list.stream().map(e -> assembleSplitRechargeSubOrderModel(e)).collect(Collectors.toList());
            SplitRechargeOrderDetailModel resp = assembleSplitRechargeOrderDetailModel(entity, detailList);
            LOGGER.info("[查询拆单充值详情]订单列表返回，resMsg={}", JSON.toJSONString(resp));
            mav.addObject("orderDetail", resp);
        } catch (YeepayBizException e) {
            LOGGER.warn("[查询拆单充值详情] 业务异常，商编=" + customerNumber + ",异常为={}", e);
            mav.addObject("orderDetail", null);
            return mav;
        } catch (Exception e) {
            LOGGER.error("[查询拆单充值详情] 异常，商编=" + customerNumber + ",异常为={}", e);
            mav.addObject("orderDetail", null);
            return mav;
        }
        return mav;
    }

    private SplitRechargeOrderDetailModel assembleSplitRechargeOrderDetailModel(SplitRechargeOrderEntity entity, List<SplitRechargeSubOrderModel> detailList) {
        SplitRechargeOrderDetailModel resp = new SplitRechargeOrderDetailModel();
        resp.setCreateTime(DateUtil.formatByDateTimePattern(entity.getCreateTime()));
        resp.setBatchFlowNo(entity.getBatchFlowNo());
        resp.setInitiateMerchantNo(entity.getInitiateMerchantNo());
        resp.setRequestNo(entity.getRequestNo());
        resp.setMerchantNo(entity.getMerchantNo());
        resp.setAccountType(entity.getAccountType());
        resp.setAmount(covertBigDecimal(entity.getAmount()));
        resp.setSumSuccessAmount(covertBigDecimal(entity.getSumSuccessAmount()));
        resp.setSumCreditAmount(covertBigDecimal(entity.getSumCreditAmount()));
        resp.setSumPayerAmount(covertBigDecimal(entity.getSumCreditAmount()));
        resp.setSumFee(covertBigDecimal(entity.getSumFee()));
        resp.setCompleteTime(DateUtil.formatByDateTimePattern(entity.getCompleteTime()));
        resp.setPayType(entity.getPayType());
        resp.setBankCode(entity.getBankCode());

        resp.setStatus(entity.getStatus());
        resp.setRemark(entity.getRemark());
        resp.setOperator(entity.getOperator());
        resp.setSource(entity.getSource());
        resp.setFirstMerchantNo(entity.getFirstMerchantNo());
        resp.setSecondMerchantNo(entity.getSecondMerchantNo());

        resp.setSumPayerAmount(covertBigDecimal(entity.getSumSuccessAmount()));/*银联B2B这两个金额相等*/
        resp.setDetailList(detailList);
        return resp;
    }

    private SplitRechargeSubOrderModel assembleSplitRechargeSubOrderModel(SplitRechargeSubOrderEntity e) {
        SplitRechargeSubOrderModel orderInfo = new SplitRechargeSubOrderModel();
        orderInfo.setBatchFlowNo(e.getBatchFlowNo());
        orderInfo.setSubFlowNo(e.getSubFlowNo());
        orderInfo.setRelateOrderNo(e.getRelateOrderNo());
        orderInfo.setPayType(e.getPayType());
        orderInfo.setBankCode(e.getBankCode());
        orderInfo.setAmount(covertBigDecimal(e.getAmount()));
        orderInfo.setCreditAmount(covertBigDecimal(e.getCreditAmount()));
        orderInfo.setFee(covertBigDecimal(e.getFee()));
        orderInfo.setStatus(e.getStatus());
        orderInfo.setCode(e.getCode());
        orderInfo.setMessage(e.getMessage());
        orderInfo.setRemark(e.getRemark());
        orderInfo.setMemo(e.getMemo());
        Map<String, String> memoMap = JSON.parseObject(e.getMemo(), Map.class);
        orderInfo.setAirLineAccountName(memoMap.get("airLineAccountName"));

        return orderInfo;
    }

    /**
     * 下载拆单充值电子回单
     *
     * @param param
     * @param response
     * @throws Exception
     */
    @RequestMapping(value = "/downSplitRechargeReceipt")
    @ApiOperation(value = "下载拆单充值电子回单")
    @ResponseBody
    public void downSplitRechargeReceipt(DownloadSplitRechargeReceiptReqDTO param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("[拆单充值电子回单] 下载 电子回单 请求参数{}",JSONObject.toJSONString(param));
            param.validateParam();
            String orderNo = param.getOrderNo();
            long start = System.currentTimeMillis();
            //查询订单
            SplitRechargeOrderEntity entity = splitRechargeBatchDao.selectSplitOrderByBatchFlowNo(orderNo);
            if (entity == null || !entity.getMerchantNo().equals(param.getCustomerNumber())) {
                LOGGER.error("[拆单充值电子回单]  订单信息为空或者不匹配 entity={}", JSONObject.toJSONString(entity));
                throw UnionAccountManageException.SYSTEM_ERROR.newInstance("充值订单详情为空");
            }

            //构建打印pdf
            PdfUtils pdfUtils = new PdfUtils();
            ReceiptReqDTO reqDTO = new ReceiptReqDTO();
            reqDTO.setTradeType(TradeTypeEnum.SPLIT_RECHARGE);
            reqDTO.setOrderNo(param.getOrderNo());
            reqDTO.setInitiateMerchantNo(entity.getInitiateMerchantNo());
            reqDTO.setParentMerchantNo(entity.getInitiateMerchantNo());
            reqDTO.setMerchantNo(entity.getMerchantNo());
            String data = pdfUtils.generateReceipt(reqDTO);

            BASE64Decoder decoder = new BASE64Decoder();
            //Base64解码
            byte[] len = decoder.decodeBuffer(data);
            for (int i = 0; i < len.length; ++i) {
                //调整异常数据
                if (len[i] < 0) {
                    len[i] += 256;
                }
            }
            /*在开始写入的那一刻再定义这些，不然影响异常的弹窗*/
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + "RECHARGE" + "_" + System.currentTimeMillis() + ".pdf");
            OutputStream out = response.getOutputStream();
            out.write(len);
            out.flush();
            out.close();
            long end = System.currentTimeMillis();
            LOGGER.info("[拆单充值电子回单] 耗时{}ms", (end - start));
        } catch (Throwable ex) {
            LOGGER.error("[拆单充值电子回单] 异常， 来个弹窗 ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }




}
