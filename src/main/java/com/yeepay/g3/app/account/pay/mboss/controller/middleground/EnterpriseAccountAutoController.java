package com.yeepay.g3.app.account.pay.mboss.controller.middleground;


import com.yeepay.g3.app.account.pay.mboss.dto.req.AddAutoRuleReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.ModifyAutoRuleReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.QueryAutoRuleReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.QueryQualifiedAccountReqDTO;
import com.yeepay.g3.app.account.pay.mboss.service.AutoAllocateService;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.AddAutoAllocateRuleRespDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.ModifyAutoAllocateRuleRespDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.QueryAutoAllocateRuleRespDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.QueryQualifiedRespDTO;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;


/**
 * @ClassName: EnterpriseAccountAutoController
 * @Description: 企业号自动划拨管理
 * <AUTHOR>
 * @Date 2024/05/20
 * @Version 1.0
 */
@Controller
@Api(tags = "企业号自动划拨管理-API")
@RequestMapping("/enterprise/auto")
public class EnterpriseAccountAutoController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseAccountAutoController.class);


    @Autowired
    private AutoAllocateService autoAllocateService;

    @RequiresPermissions("***********")
    @RequestMapping("/allocate/manage")
    @ApiOperation(hidden = true, value = "划拨管理")
    public ModelAndView allocateManage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("enterpriseAccount/enterpriseAutoAllocate");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("订单管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 查询自动划拨规则
     *
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/allocate/queryAll", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询自动划拨规则")
    public ResponseMessage queryAllRule(QueryAutoRuleReqDTO reqDTO) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            logger.info("查询自动划拨规则请求参数为 req={}", JSONUtils.toJsonString(reqDTO));
            ShiroUser currentUser = getCurrentUser();
            reqDTO.setMerchantNo(currentUser.getCustomerNumber());
            QueryAutoAllocateRuleRespDTO respDTO = autoAllocateService.queryPageRule(reqDTO);
            if (!"AM00000".equals(respDTO.getReturnCode())) {
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg(respDTO.getReturnMsg());
                resMsg.setErrCode(respDTO.getReturnCode());
                return resMsg;
            }
            resMsg.put("data", respDTO);
            logger.info("查询自动划拨规则结果为 resp={}", JSONUtils.toJsonString(respDTO));
        } catch (Exception e) {
            logger.error("查询自动划拨规则,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 新增自动划拨规则
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/allocate/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("新增自动划拨规则")
    public ResponseMessage addRule(@RequestBody AddAutoRuleReqDTO param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("新增自动划拨规则，请求参数{}", JSONUtils.toJsonString(param));
        try {
            param.validateParam();
            ShiroUser currentUser = getCurrentUser();
            param.setMerchantNo(currentUser.getCustomerNumber());
            param.setOperator(currentUser.getLoginName());
            AddAutoAllocateRuleRespDTO resp = autoAllocateService.addRule(param);
            if (!"AM00000".equals(resp.getReturnCode())) {
                resMsg.setErrCode(resp.getReturnCode());
                resMsg.setErrMsg(resp.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            }
        } catch (Exception e) {
            logger.error("新增自动划拨规则,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }


    /**
     * 失效自动划拨规则
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/allocate/modify", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("失效自动划拨规则")
    public ResponseMessage modifyRule(@RequestBody ModifyAutoRuleReqDTO param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("失效自动划拨规则，请求参数{}", JSONUtils.toJsonString(param));
        try {
            param.validateParam();
            ShiroUser currentUser = getCurrentUser();
            param.setMerchantNo(currentUser.getCustomerNumber());
            param.setOperator(currentUser.getLoginName());
            ModifyAutoAllocateRuleRespDTO resp = autoAllocateService.modifyRule(param);
            if (!"AM00000".equals(resp.getReturnCode())) {
                resMsg.setErrCode(resp.getReturnCode());
                resMsg.setErrMsg(resp.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            }
        } catch (Exception e) {
            logger.error("失效自动划拨规则,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询可设置企业号
     *
     * @return
     */
    @RequestMapping(value = "/allocate/queryQualifiedAccount", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询可设置企业号列表")
    public ResponseMessage queryQualifiedAccount() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            ShiroUser currentUser = getCurrentUser();
            QueryQualifiedAccountReqDTO reqDTO = new QueryQualifiedAccountReqDTO();
            reqDTO.setMerchantNo(currentUser.getCustomerNumber());
            logger.info("查询自动划拨可设置的企业号，请求参数为={}", JSONUtils.toJsonString(reqDTO));
            QueryQualifiedRespDTO resp = autoAllocateService.queryQualifiedAccount(reqDTO);
            resMsg.put("data", resp);
        } catch (Exception e) {
            logger.error("查询自动划拨可设置的企业号,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

}
