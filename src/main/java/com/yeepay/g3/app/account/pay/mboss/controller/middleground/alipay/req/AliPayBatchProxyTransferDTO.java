package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import java.io.Serializable;
import java.util.List;

public class AliPayBatchProxyTransferDTO implements Serializable {
    private List<AliPayProxyFileInfoDTO> aliPaySingleProxyTransferDTOList;
    /**
     * 记账本号
     */
    private String merchantNo;
    /**
     * 记账本号
     */
    private String channelBookId;
    /**
     * 总数
     */
    private Integer totalCount;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 总金额
     */
    private String totalAmount;
    /**
     * 标题
     */
    private String title;
    /**
     * 操作人
     */
    private String operate;

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public List<AliPayProxyFileInfoDTO> getAliPaySingleProxyTransferDTOList() {
        return aliPaySingleProxyTransferDTOList;
    }

    public void setAliPaySingleProxyTransferDTOList(List<AliPayProxyFileInfoDTO> aliPaySingleProxyTransferDTOList) {
        this.aliPaySingleProxyTransferDTOList = aliPaySingleProxyTransferDTOList;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
