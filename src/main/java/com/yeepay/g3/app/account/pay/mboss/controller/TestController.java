package com.yeepay.g3.app.account.pay.mboss.controller;

import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.utils.common.log.LoggerFactory;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Description:
 * Author: jiawen.huang
 * Date: 2018/4/17
 * Time: 15:19
 * Version: 1.0
 * Copyright © 2018 YeePay.com All rights reserved.
 */
@Controller
@RequestMapping("test/")
public class TestController extends BaseController{
    
	
	private static final com.yeepay.g3.utils.common.log.Logger logger = LoggerFactory.getLogger(TestController.class);

	@RequestMapping("list")
	public String list(){
		logger.info("mcvstartsuccess");
		return "pay/test";
	}
}
