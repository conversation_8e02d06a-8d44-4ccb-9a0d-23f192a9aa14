package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.req;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 支付宝记账本代发查询
 */
@ApiModel(description = "支付宝记账本代发查询")
public class AliPayProxyTransferQueryDTO implements Serializable {
    /**
     * 记账本id
     */
    private String channelBookId;
    /**
     * 记账本名称
     */
    private String channelBookName;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 批次号
     */
    private String merchantNo;
    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间开始
     *
     * @return
     */
    private Date createTimeStart;
    /**
     * 创建时间结束
     *
     * @return
     */
    private Date createTimeEnd;
    private Integer pageSize=10;

    private  String downType;

    private  String orderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getDownType() {
        return downType;
    }

    public void setDownType(String downType) {
        this.downType = downType;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    private Integer pageNo=1;

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public Date getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(Date createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public Date getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(Date createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getChannelBookName() {
        return channelBookName;
    }

    public void setChannelBookName(String channelBookName) {
        this.channelBookName = channelBookName;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
