package com.yeepay.g3.app.account.pay.mboss.controller;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeepay.g3.app.account.pay.mboss.dao.DirectTransferOperatorCreditNoRelaDao;
import com.yeepay.g3.app.account.pay.mboss.dao.DirectTransferOperatorDao;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorCreditNoRelaEntity;
import com.yeepay.g3.app.account.pay.mboss.entity.DirectTransferOperatorEntity;
import com.yeepay.g3.app.account.pay.mboss.utils.BeanUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.DataFormater;
import com.yeepay.g3.app.account.pay.mboss.utils.QueryServiceUtil;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.app.newframe.response.ResponseMessage.Status;
import com.yeepay.g3.component.platform.facade.CustomerQueryFacade;
import com.yeepay.g3.core.member.util.ConfigureUtil;
import com.yeepay.g3.facade.account.pay.exception.AccountPayException;
import com.yeepay.g3.facade.account.pay.facade.AccountPayTransferFacade;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.facade.MerchantFacade;
import com.yeepay.g3.facade.mp.dto.UserAndFunctionDTO;
import com.yeepay.g3.facade.mp.dto.UserDTO;
import com.yeepay.g3.facade.mp.facade.OperateFacade;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


@Controller
@RequestMapping("/boss/transfersetting")
public class TransferSettingsController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(TransferSettingsController.class);
    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);
    private AccountPayTransferFacade accountPayTransferFacade =
            RemoteServiceFactory.getService(AccountPayTransferFacade.class);
    private CustomerQueryFacade customerQueryFacade =
            RemoteServiceFactory.getService(CustomerQueryFacade.class);

    private MerchantFacade merchantFacade = RemoteServiceFactory.getService(MerchantFacade.class);

    protected final DataFormater dataFormater = new DataFormater();

    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认pageNo
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    private OperateFacade operateFacade = RemoteServiceFactory.getService(OperateFacade.class);

    @Autowired
    DirectTransferOperatorDao directTransferOperatorDao;

    @Autowired
    DirectTransferOperatorCreditNoRelaDao directTransferOperatorCreditNoRelaDao;

    @Autowired
    TransactionTemplate transactionTemplate;

    @RequestMapping("/transferOptionInfo")
    public ModelAndView transferOptionInfo(HttpServletRequest request,
            HttpServletResponse response) {
//        String operatorId = request.getParameter("operatorId");
        String loginName = request.getParameter("loginName");
        String debitCustomerNo = request.getParameter("debitCustomerNo");
        CheckUtils.notEmpty(loginName, "loginName");
        CheckUtils.notEmpty(debitCustomerNo, "debitCustomerNo");
        
        final DirectTransferOperatorEntity operator = directTransferOperatorDao.queryByDebitCustomerNoAndLoginName(debitCustomerNo, loginName);
        
        ModelAndView mav = new ModelAndView();
        // String orderString = request.getParameter("orderNo");
        mav.addObject("loginName", loginName);
        mav.addObject("selfCustomerNO", getCurrentCustomerNumber());
//        mav.addObject("operatorId", operatorId);
        mav.addObject("debitCustomerNo", debitCustomerNo);
        mav.setViewName("transfer/transferOptionInfo");
        // mav.addObject("orderNo",orderString);
       
        List<DirectTransferOperatorCreditNoRelaEntity> relas = directTransferOperatorCreditNoRelaDao.queryByOperatorId(operator.getId());
        StringBuilder relasStr = new StringBuilder();        
        if(!CheckUtils.isEmpty(relas)) {
            for(DirectTransferOperatorCreditNoRelaEntity rela : relas) {
                relasStr.append(rela.getCreditCustomerNo());
                relasStr.append(",");
            }
        }
        mav.addObject("relas", relasStr);
        return mav;
    }

    /**
     * 查询定向转账设计列表
     * 
     * @return
     */
    @RequestMapping("/list")
    public ModelAndView transferOption(HttpServletRequest request, HttpServletResponse response) {
        String customerNumber = getCurrentCustomerNumber();
        logger.info("TransferSettingsController-list customerNumber={}", customerNumber);

        List<String> operatorLoginNames = new ArrayList<String>();
        List<UserAndFunctionDTO> operators = operateFacade.fetchUsers(customerNumber, null, null);
        if (!CheckUtils.isEmpty(operators)) {
            for (UserAndFunctionDTO user : operators) {
                UserDTO userDto = user.getUserDTO();
                String loginName = userDto.getLoginName();
                operatorLoginNames.add(loginName);
            }
        }
        List<DirectTransferOperatorEntity> localOperators =
                directTransferOperatorDao.queryByDebitCustomerNo(customerNumber);
        List<String> localLoginNames = new ArrayList<String>();
        if (!CheckUtils.isEmpty(localOperators)) {
            for (DirectTransferOperatorEntity operator : localOperators) {
                localLoginNames.add(operator.getDebitOperatorLoginName());
            }
        } else {
            localOperators = new ArrayList<DirectTransferOperatorEntity>();
        }

        List<String> waitAdd = new ArrayList<String>();
        for (String operatorLoginName : operatorLoginNames) {
            if (!localLoginNames.contains(operatorLoginName)) {
                waitAdd.add(operatorLoginName);
                // 本地没有添加
                DirectTransferOperatorEntity directTrans = new DirectTransferOperatorEntity();
                directTrans.setDebitCustomerNo(customerNumber);
                directTrans.setDebitOperatorLoginName(operatorLoginName);
                directTransferOperatorDao.add(directTrans);
            }
        }

        List<Long> deleteIds = new ArrayList<Long>();
        for (DirectTransferOperatorEntity localOperator : localOperators) {
            if (!operatorLoginNames.contains(localOperator.getDebitOperatorLoginName())) {
                // 本地有，商户后台没有，删除本地
                deleteIds.add(localOperator.getId());
                final Long operatorId = localOperator.getId();
                transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                    @Override
                    public void doInTransactionWithoutResult(TransactionStatus status) {
                        directTransferOperatorCreditNoRelaDao.deleteByOperatorId(operatorId);
                        directTransferOperatorDao.delete(operatorId);
                    }
                });

            }
        }

        ModelAndView mav = new ModelAndView();
        // mav.setViewName("transfer/transfersettinglist");
        mav.setViewName("transfer/transferOption");
        return mav;
    }

    
    
    /**转账查询转入商编列表
     */
    @RequestMapping(value = "/queryCreditListData")
    @ResponseBody
    public ResponseMessage creditListData(HttpServletRequest request,
            HttpServletResponse response, QueryTransferSettingsParam param
         ) {
        ResponseMessage resMsg = new ResponseMessage("success");
//        if (param == null) {
//            param = new QueryTransferSettingsParam();
//        }
//        String loginName = request.getParameter("loginName");
//        String debitCustomerNo = request.getParameter("debitCustomerNo");
        
//        param.setOperatorLoginName(loginName);
//        param.setDebitCustomerNo(request.getParameter("debitCustomerNo"));
//        param.setDebitCustomerNo(getCurrentCustomerNumber());
     
//        CheckUtils.notEmpty(loginName,"loginName");
//        CheckUtils.notEmpty(debitCustomerNo,"debitCustomerNo");
        
        try {
            List<DirectTransferOperatorCreditNoRelaEntity> relas = directTransferOperatorCreditNoRelaDao.
                    queryByDebitCustomerAndLoginName(getCurrentCustomerNumber(), getCurrentUser().getLoginName());
            resMsg.setStatus(Status.SUCCESS);
            resMsg.put("dataList", relas);
        }catch(Throwable e) {
            e.printStackTrace();
            resMsg.setStatus(Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        }
        return resMsg;
    }
    
    /**
     * 订单列表查询(ajax)
     */
    @RequestMapping(value = "/listdata")
    @ResponseBody
    public ResponseMessage queryTransferSettingsList(HttpServletRequest request,
            HttpServletResponse response, QueryTransferSettingsParam param,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        if (param == null) {
            param = new QueryTransferSettingsParam();
        }
        param.setOperatorLoginName(request.getParameter("operatorLoginName"));
        param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
        param.setDebitCustomerNo(getCurrentCustomerNumber());
        logger.info("queryTransferSettingsListparam={},pageSize={},pageNo={}",
                param == null ? null : ToStringBuilder.reflectionToString(param), pageSize, pageNo);
        ResponseMessage resMsg = new ResponseMessage("success");

        String queryKey = "";
        String querySumKey = "";
        if (!CheckUtils.isEmpty(param.getCreditCustomerNo())) {
            queryKey = "queryTransferSettingsByCredit";
            querySumKey = "queryTransferSettingsByCreditSum";
        } else {
            queryKey = "queryTransferSettingsByOperator";
            querySumKey = "queryTransferSettingsByOperatorSum";
        }

//        if("queryTransferSettingsByCredit".equals(queryKey)) {
//            resMsg.put("queryKey", "queryTransferSettingsByCredit");
//        }
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        
        try {
            Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
            if("queryTransferSettingsByCredit".equals(queryKey)) {
                
                QueryResult result = this.queryTransferSettings(queryMap, pageNo, pageSize, "queryTransferSettingsByCredit");
                if (result != null && !CheckUtils.isEmpty(result.getData())) {
                    
                    List<Map<String, Object>> datas = (List<Map<String, Object>>) result.getData();
                    
                    if(!CheckUtils.isEmpty(datas)) {    
                        for (Map<String, Object> map : datas) {
                            Long id = (Long) map.get("operator_id");
                            String debitCustomerNo = (String) map.get("debit_customer_no");
                            String loginName = (String) map.get("debit_operator_login_name");
                            //
                            //Map<String, Object> queryOperatorRelaParam =BeanUtils.toMapWithResourceType(param);
                            Map<String, Object> queryOperatorRelaParam = new HashMap<String,Object>();
                            queryOperatorRelaParam.put("operatorId", id);
                            queryOperatorRelaParam.put("debitCustomerNo", debitCustomerNo);
                            QueryResult relaResult = this.queryTransferSettings(queryOperatorRelaParam,
                                    pageNo, 1000000, "queryTransferRelaByOperatorId");
                            map.put("creditCustomers", relaResult.getData());
                        }
                    }
                    resMsg.put("dataList", datas);
                }
                
                List<Map<String, Object>> list =
                        this.queryOrderListSum(queryMap, querySumKey);
                // // 如果查询结果不为空的话
                if (!CheckUtils.isEmpty(list)) {
                    // int sumCount = Integer.parseInt((String) list.get(0).get("SUM_COUNT"));
                    int sumCount = (Integer) mergeCount(list, "SUM_COUNT", "int");
                    resMsg.put("totalCount", sumCount);// 这是啥
                }
            }else {            
                queryTransferRelaByOperatorId(queryMap,pageNo,pageSize,queryKey, querySumKey, resMsg);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resMsg;
    }
    
    private void queryTransferRelaByOperatorId( Map<String, Object> queryMap,int pageNo,int pageSize,String queryKey,String querySumKey, ResponseMessage resMsg) {
        QueryResult result = this.queryTransferSettings(queryMap, pageNo, pageSize, queryKey);

        if (result != null && !CheckUtils.isEmpty(result.getData())) {
            // for (Map<String, Object> detail : result.getData()) {
            // adaptReturnResult(detail,pageNo,pageSize,path);
            // }

            List<Map<String, Object>> datas = (List<Map<String, Object>>) result.getData();
            
            if ("queryTransferSettingsByOperator".equals(queryKey) && !CheckUtils.isEmpty(datas)) {
                for (Map<String, Object> map : datas) {
                    Long id = (Long) map.get("id");
                    String debitCustomerNo = (String) map.get("debit_customer_no");
                    String loginName = (String) map.get("debit_operator_login_name");
                    //
//                    Map<String, Object> queryOperatorRelaParam =BeanUtils.toMapWithResourceType(param);
                    Map<String, Object> queryOperatorRelaParam = new HashMap<String, Object>();
                    queryOperatorRelaParam.put("operatorId", id);
                    queryOperatorRelaParam.put("debitCustomerNo", debitCustomerNo);
                    QueryResult relaResult = this.queryTransferSettings(queryOperatorRelaParam,
                            1, 1000000, "queryTransferRelaByOperatorId");
                    map.put("creditCustomers", relaResult.getData());
                }
            }
           
            resMsg.put("dataList", datas);
        }
        List<Map<String, Object>> list =
                this.queryOrderListSum(queryMap, querySumKey);
        // // 如果查询结果不为空的话
        if (!CheckUtils.isEmpty(list)) {
            // int sumCount = Integer.parseInt((String) list.get(0).get("SUM_COUNT"));
            int sumCount = (Integer) mergeCount(list, "SUM_COUNT", "int");
            resMsg.put("totalCount", sumCount);// 这是啥
        }
    }


    private Object mergeCount(List<Map<String, Object>> list, String key, String clazz) {
        if (clazz.equals("int")) {
            int result = 0;
            for (Map<String, Object> map : list) {
                result += Integer.parseInt((String) map.get(key));
            }
            return result;

        } else {
            BigDecimal result = new BigDecimal("0.00").setScale(2, RoundingMode.DOWN);
            for (Map<String, Object> map : list) {
                result = result
                        .add(new BigDecimal((String) map.get(key)).setScale(2, RoundingMode.DOWN));
                // result +=Float.parseFloat((String) map.get(key));
            }
            return result;

        }

    }

    private List<Map<String, Object>> queryOrderListSum(Map<String, Object> queryMap,
            String queryKey) {
        List<Map<String, Object>> result = null;
        QueryService queryService =
                (QueryService) QueryServiceUtil.getBean("transferQueryService", QueryService.class);
        result = QueryServiceUtil.query("transferQueryService", queryKey, queryMap);
        // result = (List<Map<String, Object>>) queryService.query("queryPayOrderSum", queryParam);
        return result;
    }


    @RequestMapping(value = "/setting")
    @ResponseBody
    public ResponseMessage setting(final @RequestBody QueryCustomerNameParam customerNameParam,HttpServletRequest request, HttpServletResponse response) {
        ResponseMessage resMsg = new ResponseMessage("success");
        CheckUtils.notEmpty(customerNameParam, "customerNameParam");
        List<String> creditCustomers = new ArrayList<String>();
        if(!CheckUtils.isEmpty(customerNameParam.getCreditCustomerNos())) {
            creditCustomers.addAll(customerNameParam.getCreditCustomerNos());   
        }
        final String loginName = customerNameParam.getLoginName();
        CheckUtils.notEmpty(loginName, "loginName");
        final String debitCustomerNo = getCurrentCustomerNumber();
      
        final DirectTransferOperatorEntity operator = directTransferOperatorDao.queryByDebitCustomerNoAndLoginName(debitCustomerNo, loginName);
        try {
            if(CheckUtils.isEmpty(creditCustomers)) {
                directTransferOperatorCreditNoRelaDao
                .deleteByOperatorId(operator.getId());
            }else {
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    directTransferOperatorCreditNoRelaDao
                            .deleteByOperatorId(operator.getId());
                    for (String creditNo : customerNameParam.getCreditCustomerNos()) {
                            DirectTransferOperatorCreditNoRelaEntity rela =
                                    new DirectTransferOperatorCreditNoRelaEntity();
                            rela.setDebitCustomerNo(debitCustomerNo);
                            rela.setDebitOperatorLoginName(loginName);
                            rela.setOperatorId(operator.getId());
                            rela.setCreditCustomerNo(creditNo.trim());
                            
                            String customerName = queryMerchantByMerchantNo(creditNo.trim());
                            rela.setCreditCustomerName(customerName);
                            directTransferOperatorCreditNoRelaDao.add(rela);
                    }
                }
            });
            }
            resMsg.setErrMsg("");
            return resMsg;
        } catch (Throwable e) {
            e.printStackTrace();
            resMsg.setStatus(Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }

    }

    @SuppressWarnings("unused")
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    private static class QueryTransferSettingsParam implements Serializable {
        private String operatorLoginName;
        private String creditCustomerNo;
        private String debitCustomerNo;

        public String getOperatorLoginName() {
            return operatorLoginName;
        }

        public void setOperatorLoginName(String operatorLoginName) {
            this.operatorLoginName = operatorLoginName;
        }

        public String getCreditCustomerNo() {
            return creditCustomerNo;
        }

        public void setCreditCustomerNo(String creditCustomerNo) {
            this.creditCustomerNo = creditCustomerNo;
        }

        public String getDebitCustomerNo() {
            return debitCustomerNo;
        }

        public void setDebitCustomerNo(String debitCustomerNo) {
            this.debitCustomerNo = debitCustomerNo;
        }

    }

    private QueryResult queryTransferSettings(Map<String, Object> queryMap, int pageNo,
            int pageSize, String queryKey) {
        QueryResult result = null;
        // queryMap.put("customerNumber", getCurrentCustomerNumber());

        Integer startIndex = (pageNo - 1) * pageSize + 1;

        QueryParam queryParam = new QueryParam();
        queryParam.setParams(queryMap);
        queryParam.setStartIndex(startIndex);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);

        logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam)
                + "    isSystemMode:" + isSystemMode());

        QueryService queryService =
                (QueryService) QueryServiceUtil.getBean("transferQueryService", QueryService.class);
        result = queryService.query(queryKey, queryParam);
//        result = QueryServiceUtil.query("transferQueryService", queryKey, queryMap);

        return result;
    }

    @RequestMapping("/queryByCustomerNo")
    @ResponseBody
    public JSONObject queryPlatformNameById(
            @RequestBody QueryCustomerNameParam queryCustomerNameParam, HttpServletRequest request,
            HttpServletResponse response) {
        // Object creditCustomerNos = request.getParameter("creditCustomerNos");
        CheckUtils.notEmpty(queryCustomerNameParam, "queryCustomerNameParam");
        List<String> creditCustomerNos= new ArrayList<String>();
        if(!CheckUtils.isEmpty(queryCustomerNameParam.getCreditCustomerNos())) {
            creditCustomerNos.addAll(queryCustomerNameParam.getCreditCustomerNos());
        }

        JSONObject result = new JSONObject();
        List<CustomerInfoDto> list = new ArrayList<CustomerInfoDto>();
        try {
            if(creditCustomerNos.size() > 0) {
                for (String customerNo : queryCustomerNameParam.getCreditCustomerNos()) {
                    CustomerInfoDto dto = new CustomerInfoDto();
                    list.add(dto);
                    dto.setCustomerNo(customerNo);
                    String customerName = null;
                    try {
                        customerName= queryMerchantByMerchantNo(customerNo);
                        dto.setCustomerName(customerName);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
                result.put("status", "success");
                result.put("data", list);
            }else {
                result.put("status", "nodata");
            }
        } catch (Throwable e) {
            result.put("status", "fail");
            result.put("msg", "查询商户名称失败");
        }
        return result;
    }


    private String queryMerchantByMerchantNo(String merchantNo) {
        logger.info("queryMerchantByMerchantNo merchantNo={}", merchantNo);
        try {
            MerchantReqDTO reqDTO = new MerchantReqDTO();
            reqDTO.setSystem("member-center");
            reqDTO.setUid(ConfigureUtil.getConfigureValue("MERCHANT_SYS_MEMBER_UID"));
            reqDTO.setReqTime(new Date());
            reqDTO.setCharSet("UTF-8");
            reqDTO.setMerchantNo(merchantNo);
            MerchantRespDTO respDTO = merchantFacade.getMerchantUsingCache(reqDTO);
            if ("0000".equals(respDTO.getRetCode())) {
                // if (!"ACTIVE".equals(respDTO.getStatus())) {
                // throw
                // PlatformBasicsException.MERCHANT_STATUS_INVAILD.newInstance("商户{0}状态异常,当前状态:{1}"
                // , merchantNo, respDTO.getStatus());
                // }
                return respDTO.getName();
            } else if ("2005".equals(respDTO.getRetCode())) {
                logger.info("商户{}不存在", merchantNo);
                throw AccountPayException.MERCHANT_NOT_EXIST.newInstance("商户{0}不存在", merchantNo);
            } else {
                logger.info("商户{}查询异常:retCode:{},retMsg:{}", merchantNo, respDTO.getRetCode(),
                        respDTO.getRetMsg());
                throw AccountPayException.MERCHANT_QUERY_EXCEPTION.newInstance("商户{0}查询异常",
                        merchantNo);
            }
        } catch (Throwable e) {
            logger.error("调用商户查询系统异常:", e);
            throw AccountPayException.MERCHANT_QUERY_EXCEPTION
                    .newInstance("调用商户查询系统异常，customerNo:{}", merchantNo);
        }
    }

    @SuppressWarnings("unused")
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    private static class QueryCustomerNameParam implements Serializable {

        private List<String> creditCustomerNos = new ArrayList<String>();
        private String loginName;
        public List<String> getCreditCustomerNos() {
            return creditCustomerNos;
        }

        public void setCreditCustomerNos(List<String> creditCustomerNos) {
            this.creditCustomerNos = creditCustomerNos;
        }

        public String getLoginName() {
            return loginName;
        }

        public void setLoginName(String loginName) {
            this.loginName = loginName;
        }


    }

    @SuppressWarnings("unused")
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    private static class CustomerInfoDto implements Serializable {
        private String customerNo;
        private String customerName;

        public String getCustomerNo() {
            return customerNo;
        }

        public void setCustomerNo(String customerNo) {
            this.customerNo = customerNo;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }
    }

}
