package com.yeepay.g3.app.account.pay.mboss.controller.app;

import com.yeepay.g3.app.account.pay.mboss.dto.AccountInfoDetailDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.AccountQueryResult;
import com.yeepay.g3.app.account.pay.mboss.dto.BankAccountQueryResult;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.enumtype.EnumHelper;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfo;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BankAccountInfo;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BankGroupListResp;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 银行账户信息
 */
@Controller
@RequestMapping("/app/bankAccount/")
public class BankAccountInfoController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BankAccountInfoController.class);

    @Resource
    private RemoteService remoteService;


    /**
     * 查询当前商户账户
     *
     * @return 分页请求结果
     */
    @RequestMapping(value = "accountList")
    @ResponseBody
    public BaseRespDTO<AccountQueryResult> yeepayAccountList() {
        AccountQueryResult result=new AccountQueryResult();
        String merchantNo = getCurrentUser().getCustomerNumber();
        LOGGER.info("[APP账户余额总览] 商户={}", merchantNo);
        AccountInfoQueryRespDTO respDTO = remoteService.queryAccountInfoList(merchantNo, merchantNo);
        BigDecimal totalBalance = BigDecimal.ZERO;
        List<AccountInfoDetailDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(respDTO.getAccountInfoList())) {
            for (AccountInfo accountInfo : respDTO.getAccountInfoList()) {
                if ("HANDLE_ACCOUNT".equals(accountInfo.getAccountType()) ||
                        "MEMBER_ACCOUNT".equals(accountInfo.getAccountType()) ||
                        "UNDERWRITTEN_ACCOUNT".equals(accountInfo.getAccountType()) ||
                        "VCC_ACCOUNT".equals(accountInfo.getAccountType()) ||
                        "DIVIDE_TRANSIT_ACCOUNT".equals(accountInfo.getAccountType()) ||
                        "REBATE_ACCOUNT".equals(accountInfo.getAccountType())) {
                    continue;
                }
                AccountInfoDetailDTO detailDTO = new AccountInfoDetailDTO();
                detailDTO.setAccountType(accountInfo.getAccountType());
                detailDTO.setAccountTypeDesc(EnumHelper.getAccountTypeDesc(accountInfo.getAccountType()));
                detailDTO.setBalance(accountInfo.getBalance() == null ? BigDecimal.ZERO : accountInfo.getBalance());
                resultList.add(detailDTO);
                totalBalance = totalBalance.add(accountInfo.getBalance() == null ? BigDecimal.ZERO : accountInfo.getBalance());
            }
        }
        result.setAccountTypeList(resultList);
        result.setTotalBalance(totalBalance);
        return BaseRespDTO.success(result);
    }

    /**
     * 查询当前银行账户余额
     */
    @RequestMapping(value = "bankAccountList")
    @ResponseBody
    public BaseRespDTO<BankAccountQueryResult> bankAccountList() {
        BankAccountQueryResult result=new BankAccountQueryResult();
        String merchantNo = getCurrentUser().getCustomerNumber();
        LOGGER.info("[APP银行账户余额总览] 商户={}", merchantNo);
        BankGroupListResp respDTO = remoteService.queryBankAccountList(merchantNo);

        result.setAccountTypeList(respDTO.getBankAccountInfos());
        if (CollectionUtils.isNotEmpty(respDTO.getBankAccountInfos())) {
            BigDecimal reduce = respDTO.getBankAccountInfos().stream().map(BankAccountInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            result.setTotalBalance(reduce);
            result.setShow(Boolean.TRUE);
        }else {
            result.setShow(Boolean.FALSE);
        }
        return BaseRespDTO.success(result);
    }
}
