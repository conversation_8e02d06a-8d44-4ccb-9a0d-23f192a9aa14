package com.yeepay.g3.app.account.pay.mboss.dto.app;

import java.io.Serializable;

public class PageReqBase implements Serializable {

    public static final Integer DEFAULT_PAGE_NO = 1;
    public static final Integer DEFAULT_PAGE_OFFSET = 10;
    private int pageNo;
    private int pageSize;
    private String orderByStr;
    private String seqStr;

    public PageReqBase() {
        this.pageNo = DEFAULT_PAGE_NO;
        this.pageSize = DEFAULT_PAGE_OFFSET;
        this.seqStr = "desc";
    }

    public int getPageNo() {
        return Math.max(this.pageNo, 0);
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return this.pageSize < 0 ? 0 : (Math.min(this.pageSize, 1000));
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getStartRow() {
        int startRow = (this.getPageNo() - 1) * this.getPageSize();
        return 0 == startRow ? startRow + 1 : startRow;
    }

    public String getOrderByStr() {
        return this.orderByStr;
    }

    public void setOrderByStr(String orderByStr) {
        this.orderByStr = orderByStr;
    }

    public String getSeqStr() {
        return this.seqStr;
    }

    public void setSeqStr(String seqStr) {
        this.seqStr = seqStr;
    }
}
