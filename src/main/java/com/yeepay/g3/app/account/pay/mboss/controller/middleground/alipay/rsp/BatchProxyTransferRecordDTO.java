package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yeepay.g3.app.account.pay.mboss.utils.file.ExcelField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class BatchProxyTransferRecordDTO implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 创建时间
     */
    @ExcelField(title = "下单时间", order = 3)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date lastModifyTime;
    /**
     * 渠道记账本id
     */
    @ExcelField(title = "记账本id", order = 4)
    private String channelBookId;
    /**
     * 渠道记账本名称
     */
    @ExcelField(title = "记账本名称", order = 5)
    private String channelBookName;

    /**
     * 批次号
     */
    @ExcelField(title = "批次号", order = 1)
    private String batchNo;
    /**
     * 状态
     */
    private String status;
    /**
     * 状态
     */
    @ExcelField(title = "批次状态", order = 11)
    private String statusDes;
    /**
     * 金额
     */
    @ExcelField(title = "代发总金额", order = 6)
    private BigDecimal totalAmount;
    /**
     * 金额
     */
    @ExcelField(title = "成功金额", order = 7)
    private BigDecimal successAmount;
    /**
     * 金额
     */
    @ExcelField(title = "失败金额", order = 8)
    private BigDecimal failAmount;
    /**
     * 成功个数
     */
    @ExcelField(title = "成功笔数", order = 9)
    private Integer successCount;
    /**
     * 失败个数
     */
    @ExcelField(title = "失败笔数", order = 10)
    private Integer failCount;
    /**
     * 手续费
     */
    @ExcelField(title = "手续费", order = 11)
    private BigDecimal serviceCharge;
    /**
     * 商编
     */
    @ExcelField(title = "商编", order = 0)
    private String merchantNo;

    /**
     * 易宝订单号
     *
     * @return
     */
    @ExcelField(title = "易宝订单号", order = 2)
    private String orderNo;

    public String getStatusDes() {
        return statusDes;
    }

    public void setStatusDes(String statusDes) {
        this.statusDes = statusDes;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public String getChannelBookId() {
        return channelBookId;
    }

    public void setChannelBookId(String channelBookId) {
        this.channelBookId = channelBookId;
    }

    public String getChannelBookName() {
        return channelBookName;
    }

    public void setChannelBookName(String channelBookName) {
        this.channelBookName = channelBookName;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getSuccessAmount() {
        return successAmount;
    }

    public void setSuccessAmount(BigDecimal successAmount) {
        this.successAmount = successAmount;
    }

    public BigDecimal getFailAmount() {
        return failAmount;
    }

    public void setFailAmount(BigDecimal failAmount) {
        this.failAmount = failAmount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    public BigDecimal getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }
}
