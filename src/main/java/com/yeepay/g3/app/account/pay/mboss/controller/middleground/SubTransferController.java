package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yeepay.g3.app.account.pay.mboss.controller.file.BatchTransferAccountFile;
import com.yeepay.g3.app.account.pay.mboss.dto.BatchAccountTransferDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.TransferParamDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.TransferQueryParam;
import com.yeepay.g3.app.account.pay.mboss.dto.TransferResponseParam;
import com.yeepay.g3.app.account.pay.mboss.enumtype.TransferTypeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.service.SubTransferService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.TransferOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.transfer.dto.request.BatchTransferDetailDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.request.BatchTransferRequestDTO;
import com.yeepay.g3.facade.unionaccount.transfer.dto.response.BatchTransferResponseDTO;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.FeeChargeTypeEnum;
import com.yeepay.g3.facade.unionaccount.transfer.enumtype.ReqSourceEnum;
import com.yeepay.g3.facade.unionaccount.transfer.exception.ErrorCode;
import com.yeepay.g3.facade.unionaccount.transfer.facade.MgTransferFacade;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.utils.web.IpUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * description: 下级商户转账
 *
 * <AUTHOR>
 * @since 2022/8/23 10:13
 */

@Controller
@RequestMapping("/subTransfer")
public class SubTransferController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubTransferController.class);

    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认当前
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    @Resource
    private SubTransferService subTransferService;
    private BusinessCheckRemoteService businessCheckRemoteService = new BusinessCheckRemoteService();
    private static final Gson GSON = new Gson();
    MgTransferFacade mgTransferFacade = RemoteServiceFactory.getService(MgTransferFacade.class);


    @RequestMapping(value = "/view", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView view() {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("subTransfer/index");
        return mav;
    }

    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public ModelAndView queryView() {
        ModelAndView mav = new ModelAndView();
        mav.setViewName("subTransfer/query");
        return mav;
    }

    @RequestMapping(value = "/subBatchTransfer", method = RequestMethod.GET)
    public ModelAndView subView() {
        ModelAndView mav = new ModelAndView();
        mav.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        mav.setViewName("subTransfer/subBatchTransferIndex");
        return mav;
    }

    @RequestMapping(value = "/queryFeeUndertakerBalance", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage queryFeeUndertakerBalance(@RequestParam("fromMerchantNo") String fromMerchantNo) {
        LOGGER.info("[下级转账]获取手续费承担方及余额, fromMerchantNo:{}", fromMerchantNo);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Assert.isTrue(!StringUtils.isEmpty(fromMerchantNo), "转出方商编不能为空");
            String[] merchantInfo = fromMerchantNo.split(";;;");
            fromMerchantNo = merchantInfo[0];
            resMsg = subTransferService.queryFeeUndertakerBalance(fromMerchantNo);
        } catch (AccountPayException | IllegalArgumentException e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[下级转账]获取手续费承担方及余额异常 Cause By: ", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常请稍后再试");
        }
        LOGGER.info("[下级转账]获取手续费承担方及余额响应:{}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    @RequestMapping(value = "/checkToMerchant", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage checkToMerchant(@RequestParam("toMerchantNo") String toMerchantNo,
                                           @RequestParam(value = "toMerchantName") String toMerchantName) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            Assert.isTrue(!StringUtils.isEmpty(toMerchantNo), "校验转入方信息，转入方商编必填");
            Assert.isTrue(!StringUtils.isEmpty(toMerchantName), "校验转入方信息，转入方商户名称必填");
            toMerchantNo = toMerchantNo.trim();
            LOGGER.info("[下级转账]校验转入方信息,toMerchantNo:{},toMerchantName:{}", toMerchantNo, toMerchantName);
            resMsg = subTransferService.checkToMerchant(toMerchantNo, toMerchantName);
        } catch (IllegalArgumentException | AccountPayException e) {
            resMsg.put("isMatch", false);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("商户查询异常, Cause By: ", e);
            resMsg.put("isMatch", false);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("商户查询异常");
        }
        LOGGER.info("[下级转账]转入方商户校验返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    /**
     * 跳转转账详情页
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView queryOrderDetail(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String orderNo = request.getParameter("orderNo");
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[下级转账]查询转账订单明细,订单号：orderNo={}", orderNo);
        Assert.isTrue(!StringUtils.isEmpty(orderNo), "订单号不能为空");
        TransferResponseParam transferResponseParam = subTransferService.queryTransferOrder(currentCustomerNumber, orderNo);
        LOGGER.info("[下级转账]查询订单明细返回，transferResponseParam:{}", JSON.toJSONString(transferResponseParam));
        mav.addObject("orderDetail", transferResponseParam);
        mav.setViewName("subTransfer/detail");
        return mav;
    }

    /**
     * 确认转账
     *
     * @return
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage confirm(@RequestParam("amount") String amount,
                                   @RequestParam(value = "fromMerchantNo", required = false) String fromMerchantNo,
                                   @RequestParam(value = "toMerchantNo", required = false) String toMerchantNo,
                                   @RequestParam("transferType") String transferType,
                                   @RequestParam(value = "toMerchantName", required = false) String toMerchantName,
                                   @RequestParam(value = "feeChargeSide", required = false) String feeChargeSide,
                                   @RequestParam(value = "usage") String usage,
                                   @RequestParam(value = "tradePassword") String tradePassword
    ) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("[下级转账]确认请求参数 amount:{},fromMerchantNo:{},toMerchantNo:{},transferType:{}," +
                        "toMerchantName:{},feeChargeSide:{},usage{}",
                amount, fromMerchantNo, toMerchantNo, transferType, toMerchantName, feeChargeSide, usage);
        // 交易密码需要解密
        tradePassword = BACRsaUtil.privateDecrypt(tradePassword, ConfigUtils.getPrivateKey());
        String currentCustomerNumber = getCurrentCustomerNumber();
        ShiroUser currentUser = getCurrentUser();
        try {
            Assert.isTrue(!StringUtils.isEmpty(fromMerchantNo), "转出方商户不能为空");
            Assert.isTrue(!StringUtils.isEmpty(toMerchantNo), "转入方商户不能为空");
            Assert.isTrue(!StringUtils.isEmpty(toMerchantName), "转入方商户名称不能为空");
            Assert.isTrue(!StringUtils.isEmpty(amount), "金额不能为空");
            Assert.isTrue(!StringUtils.isEmpty(usage), "转账备注不能为空");
            Assert.isTrue(!StringUtils.isEmpty(feeChargeSide), "手续费承担方不能为空");
            Assert.isTrue(!StringUtils.isEmpty(transferType), "转账方式不能为空");
            TransferParamDTO transferParamDTO = new TransferParamDTO();
            transferParamDTO.setAmount(amount);
            String[] merchantInfo = fromMerchantNo.split(";;;");
            transferParamDTO.setFromMerchantNo(merchantInfo[0]);
            transferParamDTO.setFromMerchantName(merchantInfo[1]);
            transferParamDTO.setToMerchantNo(toMerchantNo.trim());
            transferParamDTO.setToMerchantName(toMerchantName);
            transferParamDTO.setFeeChargeSide(feeChargeSide);
            transferParamDTO.setUsage(usage);
            transferParamDTO.setTransferType(transferType);
            if (!TransferTypeEnum.MERCHANT.name().equals(transferParamDTO.getTransferType())) {
                throw AccountPayException.TRANSFER_BIZ_ERROR.newInstance("转账方式不支持");
            }
            if (transferParamDTO.getFromMerchantNo().equals(currentCustomerNumber)) {
                throw AccountPayException.TRANSFER_BIZ_ERROR.newInstance("转出方商户编号有误，请输入正确的下级商户编号");
            }
            if (transferParamDTO.getFromMerchantNo().equals(transferParamDTO.getToMerchantNo())) {
                throw AccountPayException.TRANSFER_BIZ_ERROR.newInstance("商户转账时转出方与转入方不能相同");
            }
            if (currentUser == null) {
                throw AccountPayException.TRANSFER_BIZ_ERROR.newInstance("获取当前用户信息为空");
            }
            subTransferService.validateTradePassword(currentCustomerNumber, currentUser, tradePassword);
            resMsg = subTransferService.transfer(transferParamDTO, currentUser);
        } catch (IllegalArgumentException e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (AccountPayException e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            if (AccountPayException.TRADE_PASSWORD_ERROR.getDefineCode().equals(e.getDefineCode())) {
                resMsg.put("errorType", "passwordError");
            }
        } catch (Exception e) {
            LOGGER.error("[下级转账]转账异常，e:{}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常，请稍后再试");
        }
        LOGGER.info("[下级转账]转账返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    /**
     * @param transferQueryParam
     * @Description: 查询转账订单列表（批量)
     */
    @RequestMapping(value = "/queryOrderList")
    @ResponseBody
    public ResponseMessage queryOrderList(TransferQueryParam transferQueryParam,
                                          @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                          @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("[下级转账]查询转账订单, 请求参数{}", JSON.toJSONString(transferQueryParam));
        if (transferQueryParam.subordinateIsEmptyCheck()) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询参数为空");
            return resMsg;
        }
        try {
            resMsg = subTransferService.querySubTransferOrderList(transferQueryParam,
                    pageNo, pageSize, getCurrentCustomerNumber());
        } catch (AccountPayException e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[下级转账]查询转账订单列表异常, Cause By: ", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
        }
        LOGGER.info("[下级转账]查询转账订单列表返回, resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }


    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(TransferQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("[下级转账]开始下载转账记录，请求参数{}", JSON.toJSONString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            String currentCustomerNumber = getCurrentCustomerNumber();
            param.setInitMerchantNo(currentCustomerNumber);
            param.setSubTransfer(true);
            if (StringUtils.isNotEmpty(param.getFromCustomerNumber())) {
                String[] merchantInfo = param.getFromCustomerNumber().split(";;;");
                param.setCustomerNumber(merchantInfo[0]);
            }
            StringBuilder desc = new StringBuilder();
            desc.append("转账订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            new TransferOrderDownloadService(getCurrentUser(), param, desc.toString(), "转账订单查询-", false).download(request, response);
            LOGGER.info("[下级转账]下载转账记录excel已完成");
        } catch (Throwable ex) {
            LOGGER.error("[下级转账]下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    /**
     * 批量下载电子回单 连单个都不让下载， 先别批量了
     * downElectronicReceipt
     */
//    @RequestMapping(value = "/batchDownloadElectronic")
//    @ResponseBody
//    public void batchDownloadElectronic(TransferQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
//        LOGGER.info("【下级】开始批量下载 [转账] 电子回单，请求参数 param={}", JSON.toJSONString(param));
//        try {
//            long start = System.currentTimeMillis();
//            PreCheck.checkArgument(org.apache.commons.lang3.StringUtils.isNotBlank(param.getCustomerNumber()), "商户编号不能为空");
//            String currentCustomerNumber = getCurrentCustomerNumber();
//            param.setInitMerchantNo(currentCustomerNumber);
//            param.setSubTransfer(true);
//            if (StringUtils.isNotEmpty(param.getFromCustomerNumber())) {
//                String[] merchantInfo = param.getFromCustomerNumber().split(";;;");
//                param.setCustomerNumber(merchantInfo[0]);
//            }
//
//            if(/**/org.apache.commons.lang3.StringUtils.isBlank(param.getPlatformType())){
//                param.setPlatformType(PlatformTypeEnum.GREEN_PLATFORM_MERCHANT.name());
//            }
//            /*异步下载时的描述*/
//            String description = String.format("转账电子回单[%s]-%s~%s", param.getCustomerNumber(), param.getCreateStartDate(), param.getCreateEndDate());
//            new TransferElectronicReceiptDownloader(getCurrentUser(), param, description).download(request, response);
//            long end = System.currentTimeMillis();
//            LOGGER.info("【下级】批量下载 [转账] 电子回单已完成,耗时{}s", (end - start) / 1000);
//        } catch (Throwable ex) {
//            LOGGER.error("【下级】批量下载异常， param=" + JSON.toJSONString(param) + "， cased by", ex);
//            response.setHeader("Content-type", "text/html;charset=UTF-8");
//            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
//        }
//    }

    /**
     * 预计费
     *
     * @param tradeAmount
     * @param productType
     * @param feeChargeSide
     * @return
     */
    @RequestMapping(value = "/preCalFee", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage preCalfee(@RequestParam("fromMerchantNo") String fromMerchantNo,
                                     @RequestParam("tradeAmount") String tradeAmount,
                                     @RequestParam(value = "productType") String productType,
                                     @RequestParam(value = "feeChargeSide") String feeChargeSide) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("[下级转账]转账预计费请求参数,fromMerchantNo:{},tradeAmount:{},productType:{},feeChargeSide:{}",
                fromMerchantNo, tradeAmount, productType, feeChargeSide);
        try {
            Assert.isTrue(!StringUtils.isEmpty(fromMerchantNo), "转出方商编不能为空");
            Assert.isTrue(!StringUtils.isEmpty(tradeAmount), "转账金额不能为空");
            Assert.isTrue(!StringUtils.isEmpty(productType), "转账类型不能为空");
            Assert.isTrue(!StringUtils.isEmpty(feeChargeSide), "手续费承担方不能为空");
            String[] merchantInfo = fromMerchantNo.split(";;;");
            fromMerchantNo = merchantInfo[0];
            resMsg = subTransferService.preCalFee(fromMerchantNo, tradeAmount, productType, feeChargeSide);
        } catch (AccountPayException | IllegalArgumentException e) {
            LOGGER.error("[下级转账]预计费业务异常, Cause By:{}", e.getMessage());
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[下级转账]预计费系统异常 Cause By:", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统异常，请联系销售经理或客服处理");
        }
        return resMsg;
    }


    @Resource
    BatchTransferAccountFile batchTransferAccountFile;

    @RequestMapping(value = "/ajaxBatchSendConfirm")
    @ResponseBody
    public ResponseMessage ajaxBatchSendConfirm(HttpServletResponse response, HttpServletRequest request, @RequestParam("file") MultipartFile file,
                                                @RequestParam("totalCount") Integer totalCount,
                                                @RequestParam("batchNo") String batchNo,
                                                @RequestParam("totalAmount") String totalAmount) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        LOGGER.info("ajaxBatchSendConfirm批量确认{}", batchNo);
        try {
            ShiroUser shiroUser = super.getCurrentUser();
            String customerNumber = super.getCurrentCustomerNumber();
            String filename = file.getOriginalFilename();
            if (StringUtils.isEmpty(batchNo)) {
                batchNo = String.valueOf(IdGenUtils.generateId());
                checkBatchNoAndFileName(customerNumber, null, filename);
            } else {
                if (batchNo.length() > 32) {
                    resMsg.setErrCode(ResponseMessage.Status.ERROR.getValue());
                    resMsg.setErrMsg("文件名超长");
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    return resMsg;
                }
                // 查询批次是否存在
                checkBatchNoAndFileName(customerNumber, batchNo, filename);
            }
            //生成全局唯一标识
            List<TransferParamDTO> transferParamDTOS = batchTransferAccountFile.dealFile(file);
            if (transferParamDTOS.size() > 500) {
                resMsg.setErrCode(ResponseMessage.Status.ERROR.getValue());
                resMsg.setErrMsg("文件条数超过500 请拆分上传");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            }
            if (CollectionUtils.isEmpty(transferParamDTOS)) {
                resMsg.setErrCode(ResponseMessage.Status.ERROR.getValue());
                resMsg.setErrMsg("文件为空");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            }
            BatchAccountTransferDTO batchAccountTransferDTO = new BatchAccountTransferDTO();
            batchAccountTransferDTO.setTransferParamDTOS(transferParamDTOS);
            batchAccountTransferDTO.setBatchNo(batchNo);
            batchAccountTransferDTO.setTotalAmount(totalAmount);
            batchAccountTransferDTO.setTotalCount(totalCount);
            batchAccountTransferDTO.setOperator(shiroUser.getSignName());
            batchAccountTransferDTO.setCustomerNumber(customerNumber);
            batchAccountTransferDTO.setFileName(filename);
            ResponseMessage responseMessage = subTransferService.preBatchTransfer(batchAccountTransferDTO, FileUtil.multipartFileToFile(file));
            LOGGER.error("批量转账处理, Cause By:{}",JSON.toJSONString(responseMessage));
            return responseMessage;
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("系统异常: " + batchNo, e);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;

    }


    private void checkBatchNoAndFileName(String merchantNo, String batchNo, String fileName) {
        Map params = new HashMap();
        params.put("batch_no", batchNo);
        params.put("file_name", fileName);
        params.put("merchant_no", merchantNo);
        List<Map<String, Object>> list = QueryServiceUtil.query("accountTradeService", "queryTransferBatchOrFileNameExist", params);
        if (!CollectionUtils.isEmpty(list)) {
            String existFileName = list.stream().findFirst().get().get("batch_file_name").toString();
            if (fileName.equals(existFileName)) {
                throw AccountPayException.BATCH_TRANSFER_FILE_NAME_EXIST;
            } else {
                throw AccountPayException.BATCH_TRANSFER_BATCH_EXIST;
            }
        }

    }



    @RequestMapping(value = "/confirmTransfer")
    @ResponseBody
    public ResponseMessage ajaxBatchSendConfirm(@RequestParam("batchNo") String batchNo, HttpServletRequest request) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            LOGGER.info("ajaxBatchSendConfirm批量确认{}", batchNo);
            ShiroUser shiroUser = super.getCurrentUser();
            String dataString = RedisUtils.get(batchNo + "_data");
            if (org.springframework.util.StringUtils.isEmpty(dataString)) {
                LOGGER.warn("batchNo=[{}],已过期", batchNo);
                resMsg.setErrCode(ResponseMessage.Status.ERROR.getValue());
                resMsg.setErrMsg("数据已过期");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            }
            LOGGER.debug("confirm dataString=[{}]", dataString);
            BatchAccountTransferDTO batchTransferCheckDTO = GSON.fromJson(dataString, BatchAccountTransferDTO.class);
            BatchTransferRequestDTO batchTransferRequestDTO = new BatchTransferRequestDTO();
            batchTransferRequestDTO.setBatchNo(batchTransferCheckDTO.getBatchNo());
            batchTransferRequestDTO.setInitiateMerchantNo(batchTransferCheckDTO.getCustomerNumber());
            batchTransferRequestDTO.setParentMerchantNo(batchTransferCheckDTO.getCustomerNumber());
            batchTransferRequestDTO.setOperator(shiroUser.getLoginName());
            batchTransferRequestDTO.setReqSource(ReqSourceEnum.MP);
            batchTransferRequestDTO.setBatchFileName(batchTransferCheckDTO.getFileName());
            batchTransferRequestDTO.setClientIp(IpUtils.getIpAddr(request));
            batchTransferRequestDTO.setSalesProductCode(businessCheckRemoteService.queryMarketProduct(batchTransferCheckDTO.getCustomerNumber()));
            List<BatchTransferDetailDTO> details = new LinkedList<>();
            for (TransferParamDTO detail : batchTransferCheckDTO.getTransferParamDTOS()) {
                BatchTransferDetailDTO detailDTO = new BatchTransferDetailDTO();
                detailDTO.setOrderAmount(new BigDecimal(detail.getAmount()).setScale(2, BigDecimal.ROUND_CEILING));
                detailDTO.setUsage(detail.getUsage());
                detailDTO.setRequestNo("zz" + SnowflakeIdFactory.generateId());
                detailDTO.setToMerchantName(detail.getToMerchantName());
                detailDTO.setToMerchantNo(detail.getToMerchantNo());
                detailDTO.setTransferType(com.yeepay.g3.facade.unionaccount.transfer.enumtype.TransferTypeEnum.B2B);
                detailDTO.setFromAccountType(AccountTypeEnum.FUND_ACCOUNT);
                detailDTO.setToAccountType(AccountTypeEnum.FUND_ACCOUNT);
                detailDTO.setFromMerchantName(detail.getFromMerchantName());
                detailDTO.setFromMerchantNo(detail.getFromMerchantNo());
                if (!org.springframework.util.StringUtils.isEmpty(detail.getFeeChargeSide())) {
                    switch (detail.getFeeChargeSide()) {
                        case "收款方":
                            detailDTO.setFeeChargeSide(FeeChargeTypeEnum.INSIDE);
                            break;
                        case "付款方":
                            detailDTO.setFeeChargeSide(FeeChargeTypeEnum.OUTSIDE);
                            break;
                    }
                }
                details.add(detailDTO);
            }
            batchTransferRequestDTO.setTransferDetails(details);
            LOGGER.info("批量转账请求参数=[{}]", batchTransferRequestDTO);
            BatchTransferResponseDTO batchTransferResponseDTO = mgTransferFacade.batchTransferByParentRequest(batchTransferRequestDTO);
            LOGGER.info("批量转账返回[{}]", batchTransferResponseDTO);
            if (ErrorCode.SUCCESS.equals(batchTransferResponseDTO.getReturnCode())) {
                try {
                    //用过了删除
                    RedisUtils.delete(batchNo);
                    RedisUtils.delete(batchNo + "_data");
                } catch (Exception ex) {
                }
                return resMsg;
            } else {
                resMsg.setErrCode(ResponseMessage.Status.ERROR.getValue());
                resMsg.setErrMsg(batchTransferResponseDTO.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                return resMsg;
            }
        } catch (Exception ex) {
            resMsg.setErrCode(ResponseMessage.Status.ERROR.getValue());
            resMsg.setErrMsg(ex.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            return resMsg;
        }

    }

    /**
     * 下载模版
     *
     * @param request
     * @param response
     */
    @RequestMapping("/downloadBatchTemplate")
    public void downloadBatchTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            File file = ResourceUtils.getFile("classpath:/fileTemplate/子商户批量转账文件上传模板.xls");
            Workbook wb = WorkbookFactory.create(file);
            POIUtil.downLoadData(response, file.getName(), (HSSFWorkbook) wb);
        } catch (Exception ex) {
            LOGGER.error("下载模版异常", ex);
            try {
                response.getWriter().write("<script type='text/javascript'>parent.mpAlert('下载模版异常')</script>");
            } catch (IOException e) {

            }
        }
    }
}
