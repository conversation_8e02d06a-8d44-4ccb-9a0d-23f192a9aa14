package com.yeepay.g3.app.account.pay.mboss.controller.app.dto;

import com.yeepay.g3.app.account.pay.mboss.dto.BaseDto;
import com.yeepay.g3.facade.unionaccount.recharge.annotation.verify.NotEmpty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Mr.yin
 * @date: 2024/7/25  14:59
 */
@ApiModel(description = "确认提现请求参数")
public class WithdrawReqDTO extends BaseDto {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "短信验证码")
    @NotEmpty(message = "短信验证码")
    private String verifyCode;

    @ApiModelProperty(value = "提现账户类型")
    @NotEmpty(message = "提现账户")
    private String accountType;

    @ApiModelProperty(value = "订单金额")
    @NotEmpty(message = "提现金额")
    private String amount;

    @ApiModelProperty(value = "到账绑卡ID")
    @NotEmpty(message = "提现银行卡")
    private String bindId;

    @ApiModelProperty(value = "到账时效类型 REAL_TIME、TWO_HOUR、NEXT_DAY")
    @NotEmpty(message = "到账类型")
    private String arriveType;

    @ApiModelProperty(value = "备注/附言")
    private String remark;

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getBindId() {
        return bindId;
    }

    public void setBindId(String bindId) {
        this.bindId = bindId;
    }

    public String getArriveType() {
        return arriveType;
    }

    public void setArriveType(String arriveType) {
        this.arriveType = arriveType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
