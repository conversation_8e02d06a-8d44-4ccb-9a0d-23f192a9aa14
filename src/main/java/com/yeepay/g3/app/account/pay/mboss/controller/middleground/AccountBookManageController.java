package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base.BasePageRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.AccountBookInfoModel;
import com.yeepay.g3.app.account.pay.mboss.dto.AccountBookOpenRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.AccountBookOpenReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.req.AccountBookQueryListReqDTO;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.IdGenUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.LocalDateTimeUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.model.AccountBookInfoDetail;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.req.InnerAccountBookCreateRequestDto;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.req.PageQueryAccountBookInfoReqDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.InnerAccountBookCreateResponseDto;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.PageQueryAccountBookInfoRespDTO;
import com.yeepay.g3.facade.unionaccount.recharge.enumtype.AccountTypeEnum;
import com.yeepay.g3.facade.unionaccount.recharge.exception.UnionAccountRechargeException;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.utils.lock.Lock;
import com.yeepay.utils.lock.impl.RedisLock;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/account-book/manage")
public class AccountBookManageController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountBookManageController.class);

    public static final String PERMISSION = "**************";

    @Resource
    private RemoteService remoteService;

    /**
     * 跳转记账簿管理页面
     *
     * @param request
     * @return
     */
    @RequiresPermissions(PERMISSION)
    @RequestMapping("/page")
    @ApiOperation(value = "记账簿管理页面")
    public ModelAndView page(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("accountBook/manage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl_ACCOUNTBOOK());
        LOGGER.info("【记账簿列表查询】查询页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "记账簿列表查询")
    @ResponseBody
    public BasePageRespDTO<AccountBookInfoModel> list(@RequestBody AccountBookQueryListReqDTO reqDTO) {
        String customerNumber = getCurrentCustomerNumber();
        LOGGER.info("[记账簿列表查询] 商户={} reqDTO={}", customerNumber, JSON.toJSONString(reqDTO));
        try {
            reqDTO.validateParam();
            Date startTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getCreateStartTime(), "yyyy-MM-dd HH:mm:ss");
            Date endTime = LocalDateTimeUtils.getDateFromFormatterString(reqDTO.getCreateEndTime(), "yyyy-MM-dd HH:mm:ss");
            Assert.isTrue(LocalDateTimeUtils.getDayDiff(startTime, endTime) <= 92, "查询时间间隔不能超过3个月");
            //调用account-management-sys 查询记账簿列表
            PageQueryAccountBookInfoReqDTO param = new PageQueryAccountBookInfoReqDTO();
            param.setAccountType(AccountTypeEnum.ADVANCE_ACCOUNT.name());
            param.setMerchantNo(customerNumber);
            param.setAccountNo(reqDTO.getAccountBookNo());
            param.setMerchantAccountBookName(reqDTO.getMerchantAccountBookName());
            param.setStatus(reqDTO.getStatus());
            param.setStartTime(startTime);
            param.setEndTime(endTime);
            param.setPageNo(reqDTO.getPageNo());
            param.setPageSize(reqDTO.getPageSize());
            PageQueryAccountBookInfoRespDTO resp = remoteService.pageQueryAccountBookInfo(param);
            if (resp.getReturnCode().equals("AM00000")) {
                //组装数据返回
                List<AccountBookInfoModel> result = new ArrayList<>();
                for (AccountBookInfoDetail detail : resp.getAccountBookList()) {
                    AccountBookInfoModel model = new AccountBookInfoModel();
                    model.setAccountBookNo(detail.getAccountNo());
                    model.setMerchantAccountBookName(detail.getMerchantAccountBookName());
                    model.setCreateTime(DateUtils.toString(detail.getCreateTime(), DateUtils.DATE_FORMAT_DATETIME));
                    model.setStatus(detail.getStatus());
                    model.setBalance(detail.getBalance().setScale(2).toPlainString());
                    result.add(model);
                }
                LOGGER.info("[记账簿列表查询] 商编为={}，成功,", customerNumber);
                return BasePageRespDTO.successPage(result, resp.getTotalCount().longValue());
            }
            return BasePageRespDTO.fail(resp.getReturnMsg());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("[记账簿列表查询] 参数异常，商编=" + customerNumber + ",异常为={}", e.getMessage());
            throw UnionAccountRechargeException.PARAM_REQUIRED_ERROR.newInstance(e.getMessage());
        } catch (YeepayBizException e) {
            LOGGER.warn("[记账簿列表查询] 业务异常，商编=" + customerNumber + ",异常为={}", e);
            return BasePageRespDTO.fail(e.getDefineCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[记账簿列表查询] 异常，商编=" + customerNumber + ",异常为={}", e);
            return BasePageRespDTO.systemError("系统异常");
        }
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "记账簿开立")
    @ResponseBody
    public BaseRespDTO save(@RequestBody AccountBookOpenReqDTO reqDTO) {
        String currentCustomerNumber = getCurrentCustomerNumber();
        LOGGER.info("[记账簿开立] 请求参数 = {}", JSON.toJSONString(reqDTO));
        Lock lock = new RedisLock("account_book_open:" + currentCustomerNumber, 4);
        try {
            reqDTO.validateParam();
            if (StringUtils.isNotEmpty(reqDTO.getMerchantAccountBookName()) && reqDTO.getMerchantAccountBookName().length() > 32) {
                return BaseRespDTO.fail("记账簿名称不能超过32个字");
            }
            InnerAccountBookCreateRequestDto param = new InnerAccountBookCreateRequestDto();
            param.setMerchantNo(currentCustomerNumber);
            param.setMerchantAccountBookNo(String.valueOf(IdGenUtils.generateId()));
            param.setAccountType(AccountTypeEnum.ADVANCE_ACCOUNT.name());
            param.setMerchantAccountBookName(reqDTO.getMerchantAccountBookName());
            if (!lock.tryLock(3)) {
                LOGGER.warn("[记账簿开立] 没有获取到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请勿重复开户，请稍候重试");
            }
            LOGGER.info("[记账簿开立] 获取到锁资源，请求参数{}", JSONUtils.toJsonString(param));
            InnerAccountBookCreateResponseDto resp = remoteService.mpCreateAccountBook(param);
            if (resp.getReturnCode().equals("AM00000")) {
                AccountBookOpenRespDTO result = new AccountBookOpenRespDTO();
                result.setStatus(resp.getStatus());
                return BaseRespDTO.success(result);
            }
            return BaseRespDTO.fail(resp.getReturnMsg());
        } catch (YeepayBizException e) {
            LOGGER.warn("[记账簿开立] 业务异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("[记账簿开立] 异常为={}", e);
            return BaseRespDTO.fail(e.getMessage());
        } finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                LOGGER.error("[记账簿开立] 释放分布式锁异常为={}", e);
            }
        }
    }


}
