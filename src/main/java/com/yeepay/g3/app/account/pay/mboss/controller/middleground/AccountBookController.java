package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.entity.RechargeOrder;
import com.yeepay.g3.app.account.pay.mboss.enumtype.AccountBookOrderStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.service.RechargeOrderService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountBookOrderDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;

import com.yeepay.g3.facade.unionaccount.recharge.enumtype.FeeUnderTakerTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.DateUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/accountBook")
public class AccountBookController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountBookController.class);

    @Autowired
    private RechargeOrderService rechargeOrderService;

    public static final String ACCOUNTBOOK_QUERY_PERMISSION = "**********";

    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";

    // 默认当前
    private static final String PAGE_NO_DEFAULT_VAL = "1";



  /**
   * 跳转记账簿预收款记录页
   *
   * @param request
   * @return
   */
  @RequiresPermissions(ACCOUNTBOOK_QUERY_PERMISSION)
  @RequestMapping(value = "/query", method = RequestMethod.GET)
  public ModelAndView queryView(HttpServletRequest request) {
    ModelAndView mav = new ModelAndView();
    mav.setViewName("accountBook/query");
    return mav;
  }


  /**
   * @param queryParam:
   * @Description: 查询记账簿预收款订单列表
   */
  @RequestMapping(value = "/queryOrderList")
  @ResponseBody
  public ResponseMessage queryOrderList(AccountBookQueryParam queryParam,
                                        @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
                                        @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
    ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
    LOGGER.info("查询记账簿预收款订单，请求参数{}", JSON.toJSONString(queryParam));
    if (queryParam.isEmptyCheck() && StringUtils.isEmpty(queryParam.getOrderNo())) {
      resMsg.setStatus(ResponseMessage.Status.ERROR);
      resMsg.setErrMsg("查询时间不能为空");
      return resMsg;
    }
    queryParam.setCustomerNumber(getCurrentCustomerNumber());
    String status = queryParam.getStatus();
    if (AccountBookOrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(status)) {
      status = AccountBookOrderStatusEnum.ACCOUNTING_EXCEPTION.name() + "," + AccountBookOrderStatusEnum.ACCOUNTING_FAIL.name();
      queryParam.setStatus(status);
    }
    if(StringUtils.isEmpty(status)){
      StringBuilder str = new StringBuilder();
      for (AccountBookOrderStatusEnum statusEnum : AccountBookOrderStatusEnum.values()) {
        str.append(statusEnum+",");
      }
      queryParam.setStatus(str.substring(0,str.length()-1).toString());
    }
    QueryResult queryResult = null;
    try {
      queryResult = this.queryAccountBookOrderList(queryParam, pageNo, pageSize);
    } catch (Exception e) {
      LOGGER.error("queryAccountBookOrderList,查询异常,e={}", e);
      resMsg.setStatus(ResponseMessage.Status.ERROR);
      resMsg.setErrMsg("查询异常");
      return resMsg;
    }
    if (queryResult != null) {
      if (!CheckUtils.isEmpty(queryResult.getData())) {
        for (Map<String, Object> map : queryResult.getData()) {
          adaptReturnResult(map);
        }
      }
      resMsg.put("dataList", queryResult.getData());
    }
    resMsg = this.queryAccountBookOrderListSum(queryParam, resMsg);
    resMsg.put("pageNo", pageNo);
    resMsg.put("pageSize", pageSize);
    LOGGER.info("查询记账簿预收款订单列表返回，resMsg={}", JSON.toJSONString(resMsg));
    return resMsg;
  }


  @RequestMapping(value = "/download")
  @ResponseBody
  public void downloadRecord(AccountBookQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
    try {
      LOGGER.info("开始下载记账簿预收款订单，请求参数{}", JSON.toJSONString(param));
      CheckUtils.notEmpty(param.getFileType(), "fileType");
      response.setHeader("Content-type", "text/html;charset=UTF-8");
      param.setCustomerNumber(getCurrentCustomerNumber());
      String status = param.getStatus();
      if (AccountBookOrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(status)) {
        status = AccountBookOrderStatusEnum.ACCOUNTING_EXCEPTION.name() + "," + AccountBookOrderStatusEnum.ACCOUNTING_FAIL.name();
        param.setStatus(status);
      }
      if(StringUtils.isEmpty(status)){
        StringBuilder str = new StringBuilder();
        for (AccountBookOrderStatusEnum statusEnum : AccountBookOrderStatusEnum.values()) {
          str.append(statusEnum+",");
        }
        param.setStatus(str.substring(0,str.length()-1).toString());
      }
      StringBuilder desc = new StringBuilder();
      desc.append("记账簿预收款订单查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
      new AccountBookOrderDownloadService(getCurrentUser(), param, desc.toString(), "记账簿预收款订单查询-").download(request, response);
      LOGGER.info("下载记账簿预收款订单excel已完成");
    } catch (Throwable ex) {
      LOGGER.error("下载异常，ex={}", ex);
      response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
    }
  }


  /**
   * 跳转记账簿预收款详情页
   *
   * @param request
   * @return
   */
  @RequestMapping(value = "/detail", method = RequestMethod.GET)
  @ResponseBody
  public ModelAndView detailView(HttpServletRequest request) {
    ModelAndView mav = new ModelAndView();
    String orderNo = request.getParameter("orderNo");
    if (!CheckUtils.isEmpty(orderNo)) {
      RechargeOrder rechargeOrder = null;
      RechargeOrder dto = new RechargeOrder();
      dto.setOrderNo(orderNo);
      dto.setMerchantNo(getCurrentCustomerNumber());
      try {
        rechargeOrder = rechargeOrderService.queryRechargeOrderInfo(dto);
        LOGGER.info("查询记账簿预收款订单返回，rechargeOrder={}", JSON.toJSONString(rechargeOrder));
      } catch (Exception e) {
        LOGGER.error("查询记账簿预收款订单异常", e);
      }
      RechargeResponseParam rechargeResponseParam = new RechargeResponseParam();
      if (rechargeOrder != null) {
        rechargeResponseParam.setStatus(AccountBookOrderStatusEnum.valueOf(rechargeOrder.getStatus().name()).getDesc());
        rechargeResponseParam.setCreateDate(DateUtil.formatByDateTimePattern(rechargeOrder.getCreateTime()));
        rechargeResponseParam.setOrderNo(rechargeOrder.getOrderNo());
        rechargeResponseParam.setSubAccountNo(rechargeOrder.getSubAccountNo());
        rechargeResponseParam.setAmount(null == rechargeOrder.getApplyAmount() ? null : NumberUtils.formateNum(null, rechargeOrder.getApplyAmount()));
        rechargeResponseParam.setDeductAmount(null == rechargeOrder.getOrderAmount() ? null : NumberUtils.formateNum(null, rechargeOrder.getOrderAmount()));
        rechargeResponseParam.setCreditAmount(null == rechargeOrder.getCreditAmount() ? null : NumberUtils.formateNum(null, rechargeOrder.getCreditAmount()));
        rechargeResponseParam.setDesc(rechargeOrder.getRemark());
        rechargeResponseParam.setBankName(null == rechargeOrder.getBankName()?"":rechargeOrder.getBankName());
        rechargeResponseParam.setFailReason(rechargeOrder.getReturnMsg());
        rechargeResponseParam.setFee(null == rechargeOrder.getFee() ? null : NumberUtils.formateNum(null, rechargeOrder.getFee()));
        if(FeeUnderTakerTypeEnum.PLATFORM_MERCHANT.equals(rechargeOrder.getFeeUndertaker())){
          rechargeResponseParam.setFee(null);
        }
        if (rechargeOrder.getFinishTime() != null) {
          rechargeResponseParam.setCompletionTime(DateUtil.formatByDateTimePattern(rechargeOrder.getFinishTime()));
        }
        rechargeResponseParam.setOperateUser(rechargeOrder.getOperator());
        String bankNo = "";
        String bankAccount = "";
        if(StringUtils.isNotBlank(rechargeOrder.getPayerAccountNo()) && rechargeOrder.getPayerAccountNo().length()>4){
            bankNo = "("+rechargeOrder.getPayerAccountNo().substring(rechargeOrder.getPayerAccountNo().length() - 4, rechargeOrder.getPayerAccountNo().length())+")";
        }
        if(StringUtils.isNotBlank(rechargeOrder.getPayerAccountName())){
            bankAccount = rechargeOrder.getPayerAccountName();
        }
        rechargeResponseParam.setPayerAccountInfo(bankAccount + bankNo);
      }

      LOGGER.info("查询记账簿预收款订单明细返回，rechargeResponseParam={}", JSON.toJSONString(rechargeResponseParam));
      mav.addObject("orderDetail", rechargeResponseParam);
    }
    mav.setViewName("accountBook/detail");
    return mav;
  }

    /**
     * 查询充值汇总
     *
     * @param param
     * @param resMsg
     */
    private ResponseMessage queryAccountBookOrderListSum(AccountBookQueryParam param, ResponseMessage resMsg) {
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        List<Map<String, Object>> withOrderListSum = null;
        try {
            withOrderListSum = QueryServiceUtil.query("bacRechargeService", "queryAccountBookOrderListSum", queryMap);
        } catch (Exception e) {
            LOGGER.error("queryAccountBookOrderListSum-参数异常", e);
            // 直接把异常信息返回
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }
        // 如果查询结果不为空的话
        if (withOrderListSum != null && !withOrderListSum.isEmpty()) {
            Map<String, Object> sumResult = withOrderListSum.get(0);
            String sum_amount = sumResult.get("sum_amount").toString();
            String sum_fee = sumResult.get("sum_fee").toString();

            resMsg.getData().put("sum_count", sumResult.get("sum_count").toString());// 总笔数
            resMsg.getData().put("sum_amount", NumberUtils.formateNum(null, new BigDecimal(sum_amount)));// 总金额
            resMsg.getData().put("sum_fee", NumberUtils.formateNum(null, new BigDecimal(sum_fee)));;// 总手续费
            resMsg.getData().put("totalCount", sumResult.get("sum_count").toString());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", "0.00");// 总金额
            resMsg.getData().put("sum_fee", "0.00");// 总手续费
        }
        return resMsg;
    }

    /**
     * @param param:
     * @param pageNo:
     * @param pageSize:
     * @return com.yeepay.g3.utils.query.QueryResult
     * @Description: 查询充值列表
     */
    private QueryResult queryAccountBookOrderList(AccountBookQueryParam param, int pageNo, int pageSize) {
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setParams(queryMap);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        QueryService queryService = (QueryService) QueryServiceUtil.getBean("bacRechargeService", QueryService.class);
        QueryResult queryRechargeOrderList = queryService.query("queryAccountBookOrderList", queryParam);
        return queryRechargeOrderList;
    }

    /**
     * 适配返回结果
     *
     * @param detail
     * @return
     */
    private Map<String, Object> adaptReturnResult(Map<String, Object> detail) {
        if (detail == null || detail.isEmpty()) {
            return new HashMap<String, Object>();
        }
        try {
            detail.put("status",AccountBookOrderStatusEnum.valueOf(detail.get("status").toString()).getDesc());
            if(StringUtils.isEmpty(detail.get("sub_account_no").toString())){
              detail.put("sub_account_no","");
            }
            if (null != detail.get("order_amount")) {
              detail.put("order_amount", NumberUtils.formateNum(null, new BigDecimal(detail.get("order_amount").toString())));
            }
            String bankNo = "";
            String bankAccount = "";
            if (null != detail.get("payer_account_no")) {
              bankNo = AESUtils.decryptWithBase64(detail.get("payer_account_no").toString());
              if(StringUtils.isNotBlank(bankNo) && bankNo.length()>4){
                bankNo = bankNo.substring(bankNo.length() - 4, bankNo.length());
              }
              bankNo = "（" + bankNo + "）";
            }
            if (null != detail.get("payer_account_name")) {
              bankAccount = AESUtils.decryptWithBase64(detail.get("payer_account_name").toString());
              if(StringUtils.isNotBlank(bankAccount) && bankAccount.length()>24){
                bankAccount = bankAccount.substring(0, 22)+"...";
              }
            }
            detail.put("payer_account_name", bankAccount + bankNo );
            SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Object obj = detail.get("create_time");
            if (null != obj) {
                if (obj instanceof String) {
                    String str = String.valueOf(obj);
                    if (StringUtils.isNotBlank(str)) {
                        if (str.length() == 10) {
                            detail.put("create_time", smf.format(DateUtils.parseDate(str, DateUtils.DATE_FORMAT_DATEONLY)));
                        } else {
                            detail.put("create_time", smf.format(smf.parse(str)));
                        }
                    }
                } else if (obj instanceof Timestamp) {
                    detail.put("create_time", DateUtils.toSqlTimestampString((Timestamp) obj, DateUtils.DATE_FORMAT_DATETIME));
                }
            }
        } catch (Exception e) {
            LOGGER.error("这都能错..擦....", e);
        }
        return detail;
    }


}
