package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yeepay.g3.app.account.pay.mboss.dto.ChannelRechargeQueryParam;
import com.yeepay.g3.app.account.pay.mboss.dto.req.ChannelRechargeDetailParam;
import com.yeepay.g3.app.account.pay.mboss.entity.MerchantAccountInfoEntity;
import com.yeepay.g3.app.account.pay.mboss.service.AccountManageInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.ChannelRechargeService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.ChannelRechargeDetailDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.ChannelRechargeDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Controller
@Api(tags = "渠道充值-API")
@RequestMapping("/channel/recharge/")
public class ChannelRechargeController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChannelRechargeController.class);

    @Resource
    private ChannelRechargeService channelRechargeService;
    @Resource
    private AccountManageInfoService accountManageInfoService;


    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";

    // 默认当前
    private static final String PAGE_NO_DEFAULT_VAL = "1";


    @Resource
    private MerchantRemoteService merchantRemoteService;

    /**
     * 分页 查询渠道充值订单
     * @param queryParam
     * @param pageSize
     * @param pageNo
     * @return
     */
    @RequestMapping(value = "queryOrderList")
    @ApiOperation("分页查询渠道充值订单列表")
    @ResponseBody
    public ResponseMessage queryOrderList(ChannelRechargeQueryParam queryParam, @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize, @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        LOGGER.info("查询渠道充值订单，请求参数{}", JSON.toJSONString(queryParam));
        queryParam.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        if (StringUtils.isBlank(queryParam.getIsQueryUnder())){
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("是否查询下级为空");
            return resMsg;
        }
        //非查询下级
        if ("false".equals(queryParam.getIsQueryUnder())) {
            queryParam.setMerchantNos(Lists.newArrayList(getCurrentCustomerNumber()));
        }
        //查询下级
        if ("true".equals(queryParam.getIsQueryUnder())) {
            List<String> subMerchantNos = new ArrayList<>();
            if (StringUtils.isBlank(queryParam.getMerchantNo())) {
                subMerchantNos = getSubMerchantNos(getCurrentCustomerNumber());
            } else {
                subMerchantNos.add(queryParam.getMerchantNo());
            }
            queryParam.setMerchantNos(subMerchantNos);
        }
        try {
            QueryResult queryResult = channelRechargeService.queryOrderPageList(queryParam, pageNo, pageSize);
            resMsg.put("dataList", Objects.isNull(queryResult) ? null : queryResult.getData());
            resMsg.put("pageNo", pageNo);
            resMsg.put("pageSize", pageSize);
            resMsg.put("totalCount", Objects.isNull(queryResult) ? null : queryResult.getTotalCount());
        } catch (Exception e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询失败");
        }
        return resMsg;
    }

    @RequestMapping(value = "switchSettleAccount")
    @ApiOperation("切换结算账户")
    @ResponseBody
    public ResponseMessage switchSettleAccount(@RequestParam(value = "type") String type) {
        String merchantNo = getCurrentCustomerNumber();
        LOGGER.info("切换结算账户 the merchantNo=[{}],the type=[{}]", merchantNo, type);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        if (StringUtils.isBlank(type)) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("请求参数类型不能为空");
            return resMsg;
        }
        try {
            channelRechargeService.settleAccount(merchantNo, type, getCurrentUser().getLoginName());
        } catch (Exception e) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询失败");
        }
        return resMsg;
    }

    /**
     * 下载渠道订单管理
     * @param queryParam
     * @param request
     * @param response
     * @throws Exception
     */
    @ApiOperation("下载渠道订单管理页面")
    @RequestMapping(value = "downloadRecord")
    @ResponseBody
    public ResponseMessage downloadRecord(ChannelRechargeQueryParam queryParam, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            LOGGER.info("开始下载多渠道订单管理列表，请求参数{}", JSON.toJSONString(queryParam));
            CheckUtils.notEmpty(queryParam.getFileType(), "fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            queryParam.validateParam();

            if (StringUtils.isBlank(queryParam.getIsQueryUnder())){
                resMsg.setStatus(ResponseMessage.Status.ERROR);
                resMsg.setErrMsg("是否查询下级为空");
                return resMsg;
            }
            //非查询下级
            if ("false".equals(queryParam.getIsQueryUnder())) {
                queryParam.setMerchantNos(Lists.newArrayList(getCurrentCustomerNumber()));
            }
            //查询下级
            if ("true".equals(queryParam.getIsQueryUnder())) {
                List<String> subMerchantNos = new ArrayList<>();
                if (StringUtils.isBlank(queryParam.getMerchantNo())) {
                    subMerchantNos = getSubMerchantNos(getCurrentCustomerNumber());
                } else {
                    subMerchantNos.add(queryParam.getMerchantNo());
                }
                queryParam.setMerchantNos(subMerchantNos);
            }
            StringBuilder desc = new StringBuilder();
            desc.append("多渠道订单查询,").append(queryParam.getStartDate()).append("至").append(queryParam.getEndDate()).append("数据");
            new ChannelRechargeDownloadService(getCurrentUser(), queryParam, merchantRemoteService, desc.toString(), "多渠道订单查询-").download(request, response);
            LOGGER.info("下载多渠道订单excel已完成");
        } catch (Throwable ex) {
            LOGGER.error("下载异常，ex={}", ex);
            if (ex.getMessage().equals("下载文件为空")){
                resMsg.setErrMsg("下载文件为空");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        }
        return resMsg;
    }

    /**
     * 下载多渠道订单明细
     * @param queryParam
     * @param request
     * @param response
     * @throws Exception
     */
    @ApiOperation("下载多渠道订单明细")
    @RequestMapping(value = "downloadDetail")
    @ResponseBody
    public ResponseMessage downloadDetail(ChannelRechargeDetailParam queryParam, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            LOGGER.info("开始下载多渠道订单明细，请求参数{}", JSON.toJSONString(queryParam));
            queryParam.validateParam();
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            StringBuilder desc = new StringBuilder();
            desc.append("多渠道订单明细查询数据");
            new ChannelRechargeDetailDownloadService(getCurrentUser(), queryParam, desc.toString(), "多渠道订单明细查询-").download(request, response);
            LOGGER.info("下载多渠道订单明细excel已完成");
        } catch (Throwable ex) {
            LOGGER.error("下载异常，ex={}", ex);
            if (ex.getMessage().equals("下载文件为空")){
                resMsg.setErrMsg("下载文件失败无下载数据");
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        }
        return resMsg;
    }


    /**
     * 获取下级商编
     *
     * @param merchantNo
     * @return
     */
    private List<String> getSubMerchantNos(String merchantNo) {
        List<MerchantAccountInfoEntity> subMerchantList = accountManageInfoService.querySubMerchant(merchantNo);
        if (CollectionUtils.isEmpty(subMerchantList)) {
            return new ArrayList<>();
        }
        List<String> subMerchantNos = subMerchantList.stream().map(MerchantAccountInfoEntity::getMerchantNo).distinct().collect(Collectors.toList());
        return subMerchantNos;
    }
}
