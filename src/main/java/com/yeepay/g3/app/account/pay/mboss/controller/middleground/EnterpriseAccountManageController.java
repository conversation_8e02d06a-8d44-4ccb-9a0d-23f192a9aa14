package com.yeepay.g3.app.account.pay.mboss.controller.middleground;


import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.BankAccountManageService;
import com.yeepay.g3.app.account.pay.mboss.service.EnterpriseAccountManageService;
import com.yeepay.g3.app.account.pay.mboss.utils.WebPropertiesHolder;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.EnterpriseAccountNotifyConfigModifyRespDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.EnterpriseAccountOpenDetailRespDTO;
import com.yeepay.g3.facade.account.management.sys.transaction.dto.resp.EnterpriseOpenAccountRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.BizLicenseRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.LegalPersonRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.UniCrdCodeCertRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.OpenBankAccountMerchantType;
import com.yeepay.g3.facade.unionaccount.manage.exception.ErrorCode;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.utils.lock.Lock;
import com.yeepay.utils.lock.impl.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Base64;
import java.util.List;


/**
 * <AUTHOR>
 * @Description: 企业号账户管理
 * @Date 2023/11/06
 * @Version 1.0
 */
@Controller
@Api(tags = "企业号账户管理-API")
@RequestMapping("/enterpriseAccount")
public class EnterpriseAccountManageController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(EnterpriseAccountManageController.class);

    @Resource
    private EnterpriseAccountManageService enterpriseAccountManageService;
    @Resource
    private BankAccountManageService bankAccountManageService;

    @Autowired
    private RemoteService remoteService;

    /**
     * 企业号账户管理
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/bankManage")
    @ApiOperation(hidden = true, value = "银行账户管理")
    public ModelAndView bankPaymentManage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("enterpriseAccount/enterpriseAccountManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("银行账户管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }


    /**
     * 企业号账户进度查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/openProcess")
    @ApiOperation(hidden = true, value = "银行进度查询")
    public ModelAndView bankPaymentOpenProcess(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("enterpriseAccount/enterpriseAccountOpen");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        logger.info("银行进度查询页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    /**
     * 开户接口
     *
     * @throws Exception
     */
    @RequestMapping(value = "/openEnterpriseAccount", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("企业号开户接口")
    public ResponseMessage openBankPayAccount(@RequestBody EnterpriseAccountOpenParam param) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("企业号开户接口,请求参数={}", JSONUtils.toJsonString(param));
        //商户名称
        MerchantRespDTO currentMerchant = getCurrentMerchant();
        if (StringUtils.isEmpty(param.getOpenAccountType())) {
            param.setOpenAccountType(OpenBankAccountMerchantType.ENTERPRISE.name());
        }
        Lock lock = new RedisLock("enterprise_account_open" + "_" + currentMerchant.getMerchantNo(), 4);
        try {
            if (lock.tryLock(3)) {
                logger.info("企业号开户接口拿到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                param.setMerchantNo(currentMerchant.getMerchantNo());
                param.setMerchantName(currentMerchant.getSignName());
                EnterpriseOpenAccountRespDTO resp = enterpriseAccountManageService.innerEnterpriseOpenAccount(param);
                resMsg.put("data", resp);
            } else {
                logger.warn("企业号开户接口没有获取到锁资源，请求参数{}", JSONUtils.toJsonString(param));
                throw AccountPayException.CONDITION_NOT_AVAILABLE.newInstance("请勿重复开户，请稍候重试");
            }
        } catch (YeepayBizException e) {
            logger.warn("企业号开户接口,业务异常为={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("企业号开户接口,异常为=", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 企业号信息分页查询
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("企业号信息分页查询")
    public ResponseMessage queryPaymentOpenInfo(@RequestParam(value = "bankCode", required = false) String bankCode,
                                                @RequestParam(value = "bankAccountNo", required = false) String bankAccountNo,
                                                @RequestParam(value = "status", required = false) String status,
                                                @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            if (pageNo == null) {
                pageNo = 1;
            }
            if (pageSize == null) {
                pageNo = 10;
            }
            //暂只支持苏宁
            if (StringUtils.isEmpty(bankCode)) {
                bankCode = BankAccountBankCodeEnum.SUNINGBANK_ENTERPRISE.name();
            }
            logger.info("查询企业号银行开户列表，商编为={},银行={},bankAccountNo={},status={}", merchantNo, bankCode, bankAccountNo, status);
            EnterpriseAccountRecordRespDTO resp = enterpriseAccountManageService.queryOpenRecordPage(merchantNo, bankCode, bankAccountNo, status, pageNo, pageSize);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询企业号银行开户列表,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询企业号银行开户列表,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 企业号查询详情
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openDetail", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("企业号开户详情")
    public ResponseMessage detail(@RequestParam(value = "requestNo") String requestNo) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            logger.info("企业号查询开户信息详情，商编为={},请求号为={}", merchantNo, requestNo);
            EnterpriseAccountOpenDetailRespDTO resp = enterpriseAccountManageService.queryEnterpriseOpenDetail(merchantNo, requestNo);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("企业号查询开户信息详情,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("企业号查询开户信息详情,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询企业号开立成功银行账户信息
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/bankAccountInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询企业号开立成功银行账户信息")
    public ResponseMessage bankAccountInfo(@RequestParam(value = "bankCode", required = false) String bankCode) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            //这一期只支持苏宁
            if (StringUtils.isEmpty(bankCode)) {
                bankCode = BankAccountBankCodeEnum.SUNINGBANK_ENTERPRISE.name();
            }
            logger.info("查询企业号开立成功银行账户信息，商编为={},银行={}", merchantNo, bankCode);
            List<EnterpriseAccountOpenRecord> resp = enterpriseAccountManageService.queryEnterpriseOpenSuccessRecord(merchantNo, bankCode);
            resMsg.put("data", resp);
        } catch (YeepayBizException e) {
            logger.warn("查询企业号开立成功银行账户信息,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询企业号开立成功银行账户信息,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 企业号的开户通知配置更新
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation("通知配置保存")
    @RequestMapping(value = "/notify/config/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage updateOpenAccountNotifyConfig(@RequestBody EnterpriseNotifyConfigParam reqDTO) {
        logger.info("修改企业号银行开户通知配置 request={}", JSONUtils.toJsonString(reqDTO));
        reqDTO.validateParam();
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            EnterpriseAccountNotifyConfigModifyRespDTO resp = enterpriseAccountManageService.modifyEnterpriseNotifyConfig(reqDTO);
            if (!"AM00000".equals(resp.getReturnCode())) {
                resMsg.setErrCode(resp.getReturnCode());
                resMsg.setErrMsg(resp.getReturnMsg());
                resMsg.setStatus(ResponseMessage.Status.ERROR);
            }
        } catch (YeepayBizException e) {
            logger.warn("修改企业号通知配置业务异常为={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("修改企业号通知配置异常 ", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 查询企业号开户有没有过
     *
     * @throws Exception
     */
    @RequestMapping(value = "/query/openAccountAlready", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询开立企业号当前银行是否开户")
    public ResponseMessage openAccountAlready(@RequestParam(value = "bankCode") String bankCode) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            String merchantNo = getCurrentCustomerNumber();
            logger.info("查询开立企业号当前银行有没有开户,商编为={},银行={}", merchantNo, bankCode);
            EnterpriseAccountOpenRecord enterpriseAccountOpenRecord = enterpriseAccountManageService.queryEnterpriseAccountProcessRecord(merchantNo, bankCode);
            resMsg.put("data", enterpriseAccountOpenRecord);
        } catch (YeepayBizException e) {
            logger.warn("查询开立企业号当前银行有没有开户,业务异常为={}", e);
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            logger.error("查询开立企业号当前银行有没有开户,异常为={}", e);
            resMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    @RequestMapping(value = "/queryLegalInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage queryLegalInfo(String merchantNo) {
        try {
            ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
            merchantNo = getCurrentCustomerNumber();
            logger.info("查询法人信息 merchantNo={}", merchantNo);
            LegalOpenAccountInfo legalOpenAccountInfo = new LegalOpenAccountInfo();
            LegalPersonRespDTO legalPersonInfo = remoteService.queryLegalPersonInfo(merchantNo);
            UniCrdCodeCertRespDTO crdCodeCert = remoteService.getUniCrdCodeCert(merchantNo);

            LegalLicenseInfo legalLicenseInfo = new LegalLicenseInfo();
            /*优先取新的，如果新的没有取旧的*/
            if (crdCodeCert != null) {
                legalLicenseInfo.setExpiryDate(crdCodeCert.getExpiryDate());
                legalLicenseInfo.setUrl(crdCodeCert.getUrl());
                legalLicenseInfo.setRegistNo(crdCodeCert.getNumber());
            } else {/*如果新的没有值再调用旧的*/
                BizLicenseRespDTO license = remoteService.getBizLicense(merchantNo);
                legalLicenseInfo.setExpiryDate(license.getExpiryDate());
                legalLicenseInfo.setUrl(license.getUrl());
                legalLicenseInfo.setRegistNo(license.getRegistNo());
            }

            legalOpenAccountInfo.setLegalPersonInfo(legalPersonInfo);
            legalOpenAccountInfo.setLicense(legalLicenseInfo);

            resMsg.put("data", legalOpenAccountInfo);
            return resMsg;
        } catch (YeepayBizException e) {
            logger.error("查询法人信息业务异常 message={}", e.getMessage());
            ResponseMessage exceptionMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
            exceptionMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            exceptionMsg.setErrMsg(e.getMessage());
            return exceptionMsg;
        } catch (Throwable e) {
            logger.error("查询法人信息异常", e);
            ResponseMessage exceptionMsg = new ResponseMessage(ResponseMessage.Status.ERROR);
            exceptionMsg.setErrCode(ErrorCode.SYSTEM_ERROR);
            exceptionMsg.setErrMsg("查询法人信息异常，请稍后重试");
            return exceptionMsg;
        }
    }

    @RequestMapping(value = "/viewPhoto", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage viewPhoto(String photoUrl) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        logger.info("viewPhoto:"+ photoUrl);
        try {
            InputStream inputStream = directUrlToInputStream(getNewFileUrl(photoUrl));
            String base64 = inputStreamToBase64(inputStream);
            resMsg.put("data", base64);
            return resMsg;
        } catch (Exception e) {
            logger.error("handleDirectUrl 图片直接下载异常", e);
            return resMsg;
        }
    }

    private String inputStreamToBase64(InputStream inputStream) throws Exception {
        byte[] bytes = IOUtils.toByteArray(inputStream);
        return Base64.getEncoder().encodeToString(bytes);
    }

    private InputStream directUrlToInputStream(String faceUrl) throws Exception {
        URL url = new URL(faceUrl);
        URLConnection connection = url.openConnection();
        return connection.getInputStream();
    }
    public static String getNewFileUrl(String fileUrl){
        fileUrl.replace(" ","%20");
        String staticWebUriFix = "staticres.yeepay.com" ;
        String staticReadUriFix = "staticres.tc.yp:30253"  ;
        if(fileUrl.contains(staticWebUriFix)){
            fileUrl = fileUrl.replace(staticWebUriFix,staticReadUriFix);
            // 转http-内部域名用https无法访问
            if (fileUrl.contains("https:")) {
                fileUrl = fileUrl.replace("https", "http");
            }
        }
        return fileUrl;
    }
}
