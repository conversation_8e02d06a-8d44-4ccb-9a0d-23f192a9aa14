package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.account.pay.mboss.service.ParamTransferService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.pay.dto.TransferResultDTO;
import com.yeepay.g3.facade.account.pay.enums.CheckTypeEnum;
import com.yeepay.g3.facade.account.pay.enums.ResponseMessageEnum;
import com.yeepay.g3.facade.account.pay.enums.TransferStatus;
import com.yeepay.g3.facade.account.pay.facade.AccountPayTransferFacade;
import com.yeepay.g3.facade.account.pay.params.QueryTransferCheckParam;
import com.yeepay.g3.facade.mp.facade.UserFacade;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.utils.lock.Lock;
import com.yeepay.utils.lock.impl.RedisLock;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;


@Controller
@RequestMapping("/boss/accountpay/transferorderS0")
public class TransferOrderS0Controller extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(TransferOrderS0Controller.class);
	private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    private AccountPayTransferFacade accountPayTransferFacade = RemoteServiceFactory.getService(AccountPayTransferFacade.class);

	
	protected final DataFormater dataFormater = new DataFormater();
	
	 // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";
    // 默认pageNo
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    private static final String QUERY_PERMISSION_CONSTANT = "1003";

    private static final String NEW_QUERY_PERMISSION_CONSTANT = "**********";


    private static final String CHECK_PASS_CONSTANT = "100301";

    private static final String CHECK_REJECT_CONSTANT = "100302";

    private static final String TRANSFER_CHECK_KEY = "transferCheck";

    // 金额格式
    private static final String AMOUT_FORMAT = "^([1-9]\\d*\\.\\d*|0\\.\\d+|[1-9]\\d*|0)$";

    // 产品类型集合
    private static final Map<String, List<String>> PRODUCT_TYPE_MAP = Maps.newHashMap();
    //状态集合
    private static final Map<String, List<String>> STATUS_MAP = Maps.newHashMap();
    //剩余可退款金额展示为'-'
    private static final List<String> NOT_SHOW_SURPLUS_REFUND_AMOUNT = Arrays.asList(new String[]{"INIT", "WAIT_PAY", "CLOSE", "REJECT", "TIME_OUT", "FULLY_PAY", "CS_ACCEPT", "CS_SUCCESS", "REPEAL_ACCEPT", "REPEALED"});

    @Autowired
    private ParamTransferService paramTransferService;
      
    
    /**
     * 查询账户支付请求订单
     * @return
     */
    @RequiresPermissions(value = {QUERY_PERMISSION_CONSTANT,NEW_QUERY_PERMISSION_CONSTANT},logical= Logical.OR)
    @RequestMapping("/queryS0")
    public ModelAndView queryPayOrderS0(HttpServletRequest request, HttpServletResponse response){
    	logger.info("refundquerySuccess");
        boolean checkPassFlag = SecurityUtils.getSubject().isPermitted(CHECK_PASS_CONSTANT);
        boolean checkRejectFlag = SecurityUtils.getSubject().isPermitted(CHECK_REJECT_CONSTANT);
        ModelAndView mav = new ModelAndView();
        mav.setViewName("transfer/queryTransferS0");
        mav.addObject("checkPass",checkPassFlag);
        mav.addObject("checkReject",checkRejectFlag);
        return mav;
    }


    
    /**
     * 订单列表查询(ajax)
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequiresPermissions(value = {QUERY_PERMISSION_CONSTANT,NEW_QUERY_PERMISSION_CONSTANT},logical= Logical.OR)
    @RequestMapping(value = "/list")
    @ResponseBody
    public ResponseMessage queryHistoryOrderList(HttpServletRequest request, HttpServletResponse response,
    		QueryInputParam param,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param), pageSize, pageNo);
        ResponseMessage resMsg = new ResponseMessage("success");
        param.setOrderNo(request.getParameter("orderNo"));
        param.setInnerOrderNo(request.getParameter("innerOrderNo"));
        param.setStartTime(request.getParameter("startTime"));
        param.setEndTime(request.getParameter("endTime"));
        param.setRequestSys(request.getParameter("requestSys"));
        param.setStatus(request.getParameter("status"));
        
        param.setDebitCustomerNo( getCurrentCustomerNumber());
        param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
        param.setTrxStartTime(request.getParameter("trxStartTime"));
        param.setTrxEndTime(request.getParameter("trxEndTime"));
        param.setTransferWay(request.getParameter("transferWay"));
        param.setAccountNo(request.getParameter("accountNo"));
        param.setRequestNo(request.getParameter("requestNo"));
        param.setTransferType(request.getParameter("transferType"));

        String merchant = getCurrentCustomerNumber();
        String transferWay = request.getParameter("transferWay");
        String queryKey = "";
        String querySumKey = "";
        if(StringUtils.isNotBlank(transferWay)){
        	if(transferWay.equals("S0")){
        		queryKey = "queryTransferOrder";
        		querySumKey = "queryTransferOrderSum";
        	}else if(transferWay.equals("T0")){
        		queryKey = "queryTimingRecord";
        		querySumKey = "queryTimingRecordSum";

        	}
        }else{
        	queryKey = "queryTransferOrder";
    		querySumKey = "queryTransferOrderSum";
        }
        if(StringUtils.isNotBlank(request.getParameter("mark"))){
           param.setDebitCustomerNo(merchant);
        }
       /* param.setCustomerNo(merchant);
        param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));*/
        
        logger.info("..."+ToStringBuilder.reflectionToString(param), pageSize, pageNo);
        
       /* try {
            checkInputParam(param);

        } catch (RuntimeException e) {
            resMsg.setStatus(Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }*/
        String path = request.getContextPath();

        try {
			QueryResult result = this.queryOrderList(param, pageNo, pageSize,queryKey);

			if (result != null && !CheckUtils.isEmpty(result.getData())) {
			    for (Map<String, Object> detail : result.getData()) {
                    paramTransferService.adaptTransferReturnResult(detail,pageNo,pageSize,path);
			    }
			    resMsg.put("pageNo", pageNo);
			    resMsg.put("pageSize", pageSize);
			    resMsg.put("dataList", result.getData());

			}

			List<Map<String, Object>> list =  this.queryOrderListSum(param,querySumKey);
			// 如果查询结果不为空的话
			if (!CheckUtils.isEmpty(list)) {
			    // int sumCount = Integer.parseInt((String) list.get(0).get("SUM_COUNT"));
				int sumCount = (Integer) PublicUtils.mergeCount(list, "SUM_COUNT", "int");
			    resMsg.put("totalCount", sumCount);// 这是啥

			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();

		}

        return resMsg;
    }


    @RequiresPermissions(value = {QUERY_PERMISSION_CONSTANT,NEW_QUERY_PERMISSION_CONSTANT},logical= Logical.OR)
   @RequestMapping(value = "/listsum")
    @ResponseBody
    public ResponseMessage queryHistoryOrderListSum(HttpServletRequest request, HttpServletResponse response,QueryInputParam param) {
    	 logger.info("查询历史收款列表入参 param={},pageSize={},pageNo={}", ToStringBuilder.reflectionToString(param));
         ResponseMessage resMsg = new ResponseMessage("success");
         param.setOrderNo(request.getParameter("orderNo"));
         param.setInnerOrderNo(request.getParameter("innerOrderNo"));
         param.setStartTime(request.getParameter("startTime"));
         param.setEndTime(request.getParameter("endTime"));
         param.setRequestSys(request.getParameter("requestSys"));
         param.setStatus(request.getParameter("status"));
         
         param.setDebitCustomerNo( getCurrentCustomerNumber());
         param.setCreditCustomerNo(request.getParameter("creditCustomerNo"));
         param.setTrxStartTime(request.getParameter("trxStartTime"));
         param.setTrxEndTime(request.getParameter("trxEndTime"));
         param.setTransferWay(request.getParameter("transferWay"));
         param.setTransferType(request.getParameter("transferType"));
         param.setAccountNo(request.getParameter("accountNo"));
         param.setRequestNo(request.getParameter("requestNo"));
         String merchant = getCurrentCustomerNumber();
         String transferType = request.getParameter("transferType");
         String transferWay = request.getParameter("transferWay");

         String queryKey = "";
         String querySumKey = "";
         if(StringUtils.isNotBlank(transferWay)){
         	if(transferWay.equals("S0")){
         		queryKey = "queryTransferOrder";
         		querySumKey = "queryTransferOrderSum";
         	}else if(transferWay.equals("T0")){
         		queryKey = "queryTimingRecord";
         		querySumKey = "queryTimingRecordSum";

         	}
         }else{
        	 queryKey = "queryTransferOrder";
      		querySumKey = "queryTransferOrderSum"; 
         }
         
         
         if(StringUtils.isNotBlank(request.getParameter("mark"))){
            param.setDebitCustomerNo(merchant);
         }

         logger.info("..."+ToStringBuilder.reflectionToString(param));

        List<Map<String, Object>> list = this.queryOrderListSum(param,querySumKey);


        // 如果查询结果不为空的话
        if (list != null && !list.isEmpty()) {
            Map<String, Object> sumResult = list.get(0);
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumFractionDigits(2);
            nf.setMaximumFractionDigits(2);

            /*resMsg.getData().put("sum_count", sumResult.get("SUM_COUNT").toString());// 总笔数
            resMsg.getData().put("sum_amount", nf.format(new BigDecimal(sumResult.get("SUM_AMOUNT").toString())));// 总金额
            resMsg.getData().put("sum_fee", nf.format(new BigDecimal(sumResult.get("SUM_FEE").toString())));// 总手续费
*/      
           try {
				resMsg.getData().put("sum_count", (Integer) PublicUtils.mergeCount(list, "SUM_COUNT", "int")+"");// 总笔数
				resMsg.getData().put("sum_amount", nf.format((BigDecimal)PublicUtils.mergeCount(list, "SUM_AMOUNT", "float")));// 总金额
				resMsg.getData().put("sum_fee", nf.format((BigDecimal)PublicUtils.mergeCount(list, "SUM_FEE", "float")));// 总金额
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_amount", 0.00);// 总金额
            resMsg.getData().put("sum_fee", 0.00);// 总手续费
        }

        return resMsg;
    }


    /**
     * 转账复核
     * @return
     */
    @RequestMapping(value = "/transferCheck")
    @ResponseBody
    public ResponseMessage transferCheck(@RequestParam(value = "auditId[]", required = false) String[] auditId,
                                             @RequestParam(value = "auditStatus", required = false) String auditStatus,
                                         @RequestParam(value = "remark", required = false) String remark) {
        logger.info("转账复核 audit[]={},auditStatus={},remark={}", JSON.toJSONString(auditId), auditStatus,remark);
        ResponseMessage resMsg = new ResponseMessage("success");
        //转账成功结果
        AtomicInteger successCount = new AtomicInteger(0);
        //转账拒绝
        AtomicInteger rejectCount = new AtomicInteger(0);

        ShiroUser currentUser = getCurrentUser();
        //商户登录名
        String loginName = currentUser.getLoginName();

        for(String id : auditId) {
            Lock lock = new RedisLock(TRANSFER_CHECK_KEY + "-" + id, 0);
            try {
                if (lock.tryLock(10)) {
                    //查询出待复核订单
                    QueryTransferCheckParam param = new QueryTransferCheckParam();
                    param.setId(Long.parseLong(id));
                    param.setStatus(TransferStatus.CHECKING);
                    TransferResultDTO transferResultDTO = accountPayTransferFacade.queryTransferInfoById(param);
                    if (transferResultDTO != null) {
                        if (loginName.equals(transferResultDTO.getOperator())) {
                            resMsg.setStatus(ResponseMessage.Status.ERROR);
                            resMsg.setErrCode(ResponseMessageEnum.NOT_ALLOW.getCode());
                            resMsg.setErrMsg(ResponseMessageEnum.NOT_ALLOW.getMessage());
                            return resMsg;
                        }

                        QueryTransferCheckParam vo = new QueryTransferCheckParam();
                        vo.setId(Long.parseLong(id));
                        vo.setCheckDate(new Date());
                        vo.setChecker(currentUser.getLoginName());
                        vo.setCheckRemark(remark);
                        if (auditStatus.equals(CheckTypeEnum.CHECKPASS.name())) {
                            accountPayTransferFacade.calFeeAndTransfer(vo);
                            successCount.getAndAdd(1);
                        } else if (auditStatus.equals(CheckTypeEnum.CHECKREJECT.name())) {
                            vo.setStatus(TransferStatus.REJECT);
                            accountPayTransferFacade.updateTransferInfoById(vo);
                            rejectCount.getAndAdd(1);
                        }

                    }else{
                        QueryTransferCheckParam dto = new QueryTransferCheckParam();
                        dto.setId(Long.parseLong(id));
                        TransferResultDTO transferDTO = accountPayTransferFacade.queryTransferInfoById(dto);
                        if(TransferStatus.SUCCESS.equals(transferDTO.getStatus())
                                || TransferStatus.FAIL.equals(transferDTO.getStatus())
                                || TransferStatus.REJECT.equals(transferDTO.getStatus())
                                || TransferStatus.PROCESSING.equals(transferDTO.getStatus())){

                            logger.info("该订单已经复核，orderNo={},status={}",transferDTO.getInnerOrderNo(),transferDTO.getStatus().name());
                            resMsg.setErrCode(ResponseMessageEnum.TRANSFER_CHECKED.getCode());
                            resMsg.setErrMsg(transferDTO.getInnerOrderNo()+"："+ResponseMessageEnum.TRANSFER_CHECKED.getMessage());
                            return resMsg;
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("转账复核异常,e={}", e.toString());
            } finally {
                lock.unlock();
            }
        }
        logger.info("转账成功数={},复核拒绝数={}", successCount.get(), rejectCount.get());
        return resMsg;

    }

   public void exportExcelWithData(HttpServletResponse response, HttpServletRequest request, ExportExcelParam excelParam,List<Map<String, Object>> data)
			throws IOException {
		DataExportUtils dataEcportUtil = new DataExportUtils();
		dataEcportUtil.setTextResource(textResource);
		dataEcportUtil.setMessageFormater(messageFormater);
		dataEcportUtil.setDataFormater(dataFormater);
		dataEcportUtil.exportExcelWithData(response, request, excelParam,data);
	}
  
   /**
    * 历史交易入参绑定dto
    */
   @SuppressWarnings("unused")
   @Data
   @ToString
   @AllArgsConstructor
   @NoArgsConstructor
   private static class QueryInputParam {

	   public String getTransferWay() {
			return transferWay;
		}

		public void setTransferWay(String transferWay) {
			this.transferWay = transferWay;
		}


	private String transferWay;
	   public String getOrderNo() {
			return orderNo;
		}




		public void setOrderNo(String orderNo) {
			this.orderNo = orderNo;
		}




		public String getInnerOrderNo() {
			return innerOrderNo;
		}




		public void setInnerOrderNo(String innerOrderNo) {
			this.innerOrderNo = innerOrderNo;
		}




		public String getRequestSys() {
			return requestSys;
		}




		public void setRequestSys(String requestSys) {
			this.requestSys = requestSys;
		}




		public String getStatus() {
			return status;
		}




		public void setStatus(String status) {
			this.status = status;
		}




		public String getDebitCustomerNo() {
			return debitCustomerNo;
		}




		public void setDebitCustomerNo(String debitCustomerNo) {
			this.debitCustomerNo = debitCustomerNo;
		}




		public String getCreditCustomerNo() {
			return creditCustomerNo;
		}




		public void setCreditCustomerNo(String creditCustomerNo) {
			this.creditCustomerNo = creditCustomerNo;
		}




		public String getTransferType() {
			return transferType;
		}




		public void setTransferType(String transferType) {
			this.transferType = transferType;
		}




		public String getStartTime() {
			return startTime;
		}




		public void setStartTime(String startTime) {
			this.startTime = startTime;
		}




		public String getEndTime() {
			return endTime;
		}




		public void setEndTime(String endTime) {
			this.endTime = endTime;
		}




		public String getTrxStartTime() {
			return trxStartTime;
		}




		public void setTrxStartTime(String trxStartTime) {
			this.trxStartTime = trxStartTime;
		}




		public String getTrxEndTime() {
			return trxEndTime;
		}




		public void setTrxEndTime(String trxEndTime) {
			this.trxEndTime = trxEndTime;
		}




		public String getAccountNo() {
			return accountNo;
		}




		public void setAccountNo(String accountNo) {
			this.accountNo = accountNo;
		}




		public String getRequestNo() {
			return requestNo;
		}




		public void setRequestNo(String requestNo) {
			this.requestNo = requestNo;
		}




		private String orderNo;
	    private String innerOrderNo;
	    private String requestSys;

	    private String status;
	    private String debitCustomerNo;
	    private String creditCustomerNo;
	    private String transferType;
	    private String startTime;
	    private String endTime;
	    private String trxStartTime;
	    private String trxEndTime;
	    private String accountNo;
	    private String requestNo;
   

   }
   private QueryResult queryOrderList(QueryInputParam param, int pageNo, int pageSize, String queryKey) {
       QueryResult result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
       //queryMap.put("customerNumber", getCurrentCustomerNumber());

       Integer startIndex = (pageNo - 1) * pageSize + 1;

       QueryParam queryParam = new QueryParam();
       queryParam.setParams(queryMap);
       queryParam.setStartIndex(startIndex);
       queryParam.setMaxSize(pageSize);
       queryParam.setDoSum(true);

       logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
      result = queryService.query(queryKey, queryParam);

       return result;
   }
   private List<Map<String, Object>> queryOrderListSum(QueryInputParam param, String queryKey) {
       List<Map<String, Object>> result = null;
       Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
       //queryMap.put("customerNumber", getCurrentCustomerNumber());

      // logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

       QueryService queryService = (QueryService) QueryServiceUtil.getBean(
               "queryAccntPayService", QueryService.class);
       result = QueryServiceUtil.query("queryAccntPayService", queryKey, queryMap);

       //result = (List<Map<String, Object>>) queryService.query("queryPayOrderSum", queryParam);

       return result;
   }

    private QueryResult queryOrderListHelp(QueryInputParam param, int pageNo, int pageSize) {
        QueryResult result = null;
        Map<String, Object> queryMap = BeanUtils.toMapWithResourceType(param);
        //queryMap.put("customerNumber", getCurrentCustomerNumber());




        Integer startIndex = (pageNo - 1) * pageSize + 1;

        QueryParam queryParam = new QueryParam();
        queryParam.setParams(queryMap);
        queryParam.setStartIndex(startIndex);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);

        logger.info("queryHistoryOrderList queryParam:" + JSON.toJSONString(queryParam) + "    isSystemMode:" + isSystemMode());

        QueryService queryService = (QueryService) QueryServiceUtil.getBean(
                "queryAccntPayService", QueryService.class);
       result = queryService.query("queryPayOrder", queryParam);

        return result;
    }
   

   
   
}
