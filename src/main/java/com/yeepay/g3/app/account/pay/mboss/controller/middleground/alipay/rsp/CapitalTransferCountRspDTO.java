package com.yeepay.g3.app.account.pay.mboss.controller.middleground.alipay.rsp;

import java.io.Serializable;
import java.math.BigDecimal;

public class CapitalTransferCountRspDTO implements Serializable {
    private Integer count;
    private BigDecimal amount;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
