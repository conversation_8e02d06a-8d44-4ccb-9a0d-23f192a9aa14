package com.yeepay.g3.app.account.pay.mboss.controller.app.dto;

import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base.BasePageReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseDto;
import com.yeepay.g3.facade.unionaccount.recharge.annotation.verify.NotEmpty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Mr.yin
 * @date: 2024/7/25  14:59
 */
@ApiModel(description = "提现订单记录查询请求参数")
public class WithdrawListQueryReqDTO extends BasePageReqDTO {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "开始时间")
    @NotEmpty
    private String createTime;

    @ApiModelProperty(value = "结束时间")
    @NotEmpty
    private String endTime;

    /**
     * {@link com.yeepay.g3.facade.unionaccount.trade.enumtype.WithdrawStatusTypeEnum}
     */
    @ApiModelProperty(value = "订单状态 (REQUEST_RECEIVE 已接收;REQUEST_ACCEPT 已受理;;REMITING 处理中) 统一合并成处理中PROCESS，SUCCESS已到账;FAIL失败;REVERSED银行撤销")
    private String orderStatus;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }
}
