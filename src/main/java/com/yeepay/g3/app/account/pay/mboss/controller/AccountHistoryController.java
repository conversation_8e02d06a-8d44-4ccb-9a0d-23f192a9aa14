package com.yeepay.g3.app.account.pay.mboss.controller;

import com.yeepay.g3.app.account.pay.mboss.dto.AccountHistoryQueryParam;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountHistoryDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.CheckParamUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.mp.dto.AccountHistoryQueryResp;
import com.yeepay.g3.app.mp.service.AccountBalanceQueryService;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.manage.enums.AccountGtypeEnum;
import com.yeepay.g3.facade.account.manage.union.enums.AmountDirectionEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 *  @author: jbp
 *  @Date: 2019/8/12 上午11:30
 *  @Description:专款账户&
 */
@Controller
@RequestMapping("/account/history")
public class AccountHistoryController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountHistoryController.class);

    @Autowired
    private AccountBalanceQueryService accountBalanceQueryService;

    private static final String QUERY_PERMISSION_CONSTANT = "21402";

    // 默认pageSize
    private static final String PAGE_SIZE_DEFAULT_VAL = "20";

    // 默认当前
    private static final String PAGE_NO_DEFAULT_VAL = "1";

    private static final String REQUESTOR = "account-pay";

    /**
    * @Description
    * <AUTHOR>
    * @Date   2019/8/13 下午3:19
    * @Param  
    * @Return      
    * @Exception 查询专款账户账务历史页面
    */
    @RequestMapping("/specialList")
    public ModelAndView specialList(){
        ModelAndView mav = new ModelAndView();
        mav.addObject("accountType", "SPECIAL_FUND");
        mav.setViewName("accountHistory/specialList");
        return mav;
    }

    /**
     * @Description
     * <AUTHOR>
     * @Date   2019/8/13 下午3:19
     * @Param
     * @Return
     * @Exception 查询分账账户账务历史页面
     */
    @RequestMapping("/guaranteeList")
    public ModelAndView guaranteeList(){
        ModelAndView mav = new ModelAndView();
        mav.addObject("accountType", "GUARANTEE_SVA");
        mav.setViewName("/accountHistory/guaranteeList");
        return mav;
    }

    /**
    * @Description
    * <AUTHOR>
    * @Date   2019/8/13 下午3:19
    * @Param  
    * @Return      
    * @Exception 查询账户历史
    * 
    */
    @RequestMapping(value = "/queryAccountHistoryList")
    @ResponseBody
    public ResponseMessage queryAccountHistoryList(
            AccountHistoryQueryParam accountHistoryQueryParam,
            @RequestParam(value = "pageSize", defaultValue = PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage("success");
        LOGGER.info("queryAccountHistoryList param={},pageSize={},pageNo={}",
                ToStringBuilder.reflectionToString(accountHistoryQueryParam), pageSize, pageNo);
        AmountDirectionEnum directionEnum = null;
        try {
            if(!CheckUtils.isEmpty(accountHistoryQueryParam.getDirection())) {
                directionEnum = AmountDirectionEnum.valueOf(accountHistoryQueryParam.getDirection());
            }
            CheckParamUtils.checkQueryHistoryParam(accountHistoryQueryParam);
        } catch (Throwable e) {
            LOGGER.error("queryAccountHistoryList-参数异常");
            // 直接把异常信息返回
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }

        AccountHistoryQueryResp accountHistoryQueryResp = null;
        try {
            accountHistoryQueryResp = accountBalanceQueryService.accountHistoryQuery(REQUESTOR, accountHistoryQueryParam.getAccountType(), getCurrentCustomerNumber(),
                    AccountGtypeEnum.G3_ACCOUNT, accountHistoryQueryParam.getCreateStartDate(),
                    accountHistoryQueryParam.getCreateEndDate(),pageNo, pageSize, directionEnum, accountHistoryQueryParam.getBizType());
            LOGGER.info("返回的信息为 {}", JSONUtils.toJsonString(accountHistoryQueryResp));

        } catch (Throwable e) {
            LOGGER.error("账务历史查询异常",e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("账务历史查询异常");
            return resMsg;
        }
        if (accountHistoryQueryResp != null && accountHistoryQueryResp.getHistoryMap() != null) {
            resMsg.put("totalCount", accountHistoryQueryResp.getTotalCount());
            CheckParamUtils.checkQueryHistoryResp(accountHistoryQueryResp);
            resMsg.put("dataList", accountHistoryQueryResp.getHistoryMap());
            resMsg.put("totalPageCount", accountHistoryQueryResp.getPageCount());
            resMsg.put("totalCount", accountHistoryQueryResp.getTotalCount());
            resMsg.put("debitAmount", accountHistoryQueryResp.getDebitAmount());
            resMsg.put("creditAmount", accountHistoryQueryResp.getCreditAmount());
        }
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        return resMsg;
    }

    /**
     *  @author: jbp
     *  @Date: 2019/8/13 下午3:22
     *  @Description: 账户历史下载
     */
    @RequestMapping(value = "/download")
    public void downloadRecord(AccountHistoryQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception{
        try{
            CheckParamUtils.checkDownloadParam(param);
            param.setCustomerNumber(getCurrentCustomerNumber());
            StringBuilder desc = new StringBuilder();
            desc.append("账务历史查询,").append(param.getCreateStartDate()).append("至").append(param.getCreateEndDate()).append("数据");
            new AccountHistoryDownloadService(getCurrentUser(),param,desc.toString(),"账务历史查询-",accountBalanceQueryService).download(request,response);
        }catch (Throwable ex){
            LOGGER.error("downloadRecord-error",ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('"+ex.getMessage()+"')</script>");
        }
    }



}
