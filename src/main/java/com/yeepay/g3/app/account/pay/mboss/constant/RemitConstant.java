package com.yeepay.g3.app.account.pay.mboss.constant;

/**
 *
 */
public class RemitConstant {

	public static final String MAP_KEY_BATCH_REMIT_RESULT = "BATCH_REMIT_RESULT_LIST";
	public static final String MAP_KEY_BATCH_REMIT_RESULT_ERROR = "BATCH_REMIT_RESULT_LIST_ERROR";


	public static final String MAP_KEY_BATCH_REMIT_ERROR = "BATCH_REMIT_ERROR_LIST";

	public static final String PRODUCT_CODE_WTJS = "WTJS";

	public static final String PRODUCT_CODE_RJT = "RJT";

	public static final String TRUE = "true";

	public static final String STRING_EMPTY = "";

	public static final int URL_SUB_INDEX = 8;

	public static final String REPEAT_REMIT_ERR_MSG = "两小时内，已有相同金额和笔数的出款纪录！";

	public static final String KEY_TRADE_PASSWORD_URL = "tradePasswordUrl";

	public static final String KEY_TRADE_PASSWORD_BACK_URL = "backUrl";

	public static final String AUTH_CODE_SMS = "SMS";

	public static final String AUTH_CODE_TOTP = "TOTP";

	public static final String REMIT_INDIVIDUAL_PRODUCT_CODE = "PAY_INDIVIDUAL";

	public static final String REMIT_SMS_CODE_TYPE = "CONFIRM_SEND_REMIT";

	public static final String BATCH_REMIT_SMS_CODE_TYPE = "CONFIRM_SEND_BATCH_REMIT";

	public static final String REMIT_SUPPLIER_PRODUCT_CODE = "PAYMENT_SUPPLIER";

	public static final String BIZ_TYPE_SUFFIX = "_BANK_TRANSFER_ACCOUNT";

	public static final String MP_BOSS_ALL_BANK_RESULT_CACHE = "mp_boss_all_bank_result_cache";

	/* todo CONFIRM_SEND_APP_WITHDRAW*/
	public static final String APP_WITHDRAW_SMS_CODE_TYPE = "CONFIRM_SEND_APP_WITHDRAW";
}
