package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yeepay.g3.app.account.pay.mboss.constant.NewRemitConstant;
import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.base.BasePageRespDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.*;
import com.yeepay.g3.app.account.pay.mboss.entity.MerchantAccountInfoEntity;
import com.yeepay.g3.app.account.pay.mboss.enumtype.AccountStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.model.MerchantAccountSummaryModel;
import com.yeepay.g3.app.account.pay.mboss.model.MerchantInfoModel;
import com.yeepay.g3.app.account.pay.mboss.model.MerchantRelationContext;
import com.yeepay.g3.app.account.pay.mboss.remote.MerchantPlatformRemoteService;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.remote.impl.MerchantPlatformRemoteServiceImpl;
import com.yeepay.g3.app.account.pay.mboss.service.AccountManageInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountBalanceDownloadService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountBalanceDownloadV2Service;
import com.yeepay.g3.app.account.pay.mboss.utils.ConfigUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfo;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountInfoQueryRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.VirtualAccountInfoDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.VirtualAccountResponseDTO;
import com.yeepay.g3.unionaccount.base.facadecommon.enumtype.AccountTypeEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 账户信息
 * <AUTHOR>
 * @date Create in 14:17 2021-01-22
 * @Version V1.0
 * @company 易宝支付(YeePay)
 */
@Controller
@RequestMapping("/accountinfo")
public class AccountBalanceController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountBalanceController.class);
    private static final String VERSION_1_0 = "1.0";
    private static final String VERSION_2_0 = "2.0";
    @Autowired
    private AccountManageInfoService accountManageInfoService;
    @Autowired
    private RemoteService remoteService;
    @Resource
    private MerchantPlatformRemoteService merchantPlatformRemoteService;

    private static final Function<String, ModelAndView> VIEW_NAME_FUNCTION = version -> {
        ModelAndView modelAndView = new ModelAndView();
        if (VERSION_1_0.equals(version)) {
            modelAndView.setViewName("lowerLevelAccountInfo/index");
        } else if (VERSION_2_0.equals(version)) {
            modelAndView.setViewName("subaccountinfo/index");
        } else {
            modelAndView.setViewName("lowerLevelAccountInfo/index");
        }
        return modelAndView;
    };

    @RequestMapping(value = "/view/{subManage}", method = RequestMethod.GET)
    @ResponseBody
    public ModelAndView view(@PathVariable String subManage) {
        String merchantNo = getCurrentCustomerNumber();
        String version = ConfigUtils.getSubAccountInfoPageVersion(merchantNo);
        ModelAndView modelAndView = VIEW_NAME_FUNCTION.apply(version);
        if ("subMerchant".equals(subManage)) {
            modelAndView.addObject("subMerchantManage", true);
        } else {
            modelAndView.addObject("subMerchantManage", false);
        }
        return modelAndView;
    }

    @RequestMapping(value = "/queryAccountTypeList", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage queryAccountTypeList() {
        // 查询账户类型
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        List<AccountTypeInfoParam> accountTypeList = new ArrayList<>();
        Map<String, String> accountTypeMap = ConfigUtils.getOuterAccountTypeEnumMap();
        if (accountTypeMap.isEmpty()) {
            resMsg.put("accountTypeList", accountTypeList);
            return resMsg;
        }
        if (!accountTypeMap.isEmpty()) {
            Set<String> accountTypes = accountTypeMap.keySet();
            for (String accountType : accountTypes) {
                AccountTypeInfoParam param = new AccountTypeInfoParam();
                param.setAccountType(accountType);
                param.setAccountTypeName(accountTypeMap.get(accountType));
                accountTypeList.add(param);
            }
        }
        LOGGER.info("账户类型列表返回，accountTypeList={}", JSON.toJSONString(accountTypeList));
        resMsg.put("accountTypeList", accountTypeList);
        return resMsg;
    }

    @RequestMapping(value = "/queryMerchantList", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage queryMerchantList(HttpServletRequest request) {
        //查询下级商户名称和商户编号
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        List<MerchantAccountInfoEntity> list = null;
        try {
            //查询下级商户列表
            list =  accountManageInfoService.queryMerchantList(getCurrentCustomerNumber());

        } catch (Exception e) {
            LOGGER.error("querySubordinateMerchantList,查询异常,e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }

        List<MerchantInfoParam>  merchantInfoList = new ArrayList<>();
        if (list != null) {
            list.forEach(merchantNosByParentNoDTO -> {
                MerchantInfoParam  merchantInfo = new MerchantInfoParam();
                merchantInfo.setMerchantNo(merchantNosByParentNoDTO.getMerchantNo());
                merchantInfo.setMerchantName(merchantNosByParentNoDTO.getSignName());
                merchantInfo.setShortName(merchantNosByParentNoDTO.getShortName());
                merchantInfo.setAccountType(merchantNosByParentNoDTO.getAccountType());
                merchantInfoList.add(merchantInfo);
            });
            resMsg.put("merchantList", merchantInfoList);
        }
        LOGGER.info("查询商户列表信息返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    @RequestMapping(value = "/v2/queryMerchantList")
    @ResponseBody
    public BaseRespDTO<QuerySubMerchantInfoResponseDTO> querySubMerchantInfo(QuerySubMerchantInfoDTO requestDTO) {
        LOGGER.info("查询下级商户信息, 渲染下拉框: {}", NewRemitConstant.GSON.toJson(requestDTO));
        String currentCustomerNumber = getCurrentCustomerNumber();
        MerchantRelationContext context = merchantPlatformRemoteService.queryMerchantRelation(currentCustomerNumber);
        String platformMerchantNo = requestDTO.getPlatformMerchantNo();
        String[] subMerchantNoArray = requestDTO.getSubMerchantNoArray();
        List<MerchantInfoModel> platformList = Lists.newArrayList();
        List<MerchantInfoModel> subMerchantList = Lists.newArrayList();
        List<String> selectPlatformMerchantNoList = Lists.newArrayList();
        List<String> selectSubMerchantNoList = Lists.newArrayList();
        if (/* 没写平台的话就把平台全掏出来 */ org.springframework.util.StringUtils.isEmpty(platformMerchantNo)) {
            platformList.addAll(context.getPlatformMerchantMap().values());
            if (/* 平台和子商户都没写, 空查走全量 */ null == subMerchantNoArray || subMerchantNoArray.length == 0) {
                subMerchantList.addAll(context.getSubMerchantMap().values());
            } else /* 没有平台, 有子商户 */ {
                subMerchantList.addAll(context.getSubMerchantMap().values());
                for (String subMerchantNo : subMerchantNoArray) {
                    selectSubMerchantNoList.add(subMerchantNo);
                }
            }
        } else {
            platformList.addAll(context.getPlatformMerchantMap().values());
            selectPlatformMerchantNoList.add(platformMerchantNo);
            List<MerchantInfoModel> childList = context.getAscRelationMap().get(platformMerchantNo);
            if (/* 平台和子商户都没写, 空查走全量 */ null == subMerchantNoArray || subMerchantNoArray.length == 0) {
                subMerchantList.addAll(childList);
            } else /* 有平台, 有子商户, 裁剪子商户 */ {
                subMerchantList.addAll(childList);
                for (String subMerchantNo : subMerchantNoArray) {
                    MerchantInfoModel subMerchant = context.getSubMerchantMap().get(subMerchantNo);
                    if (subMerchant.getParentMerchantNo().equals(platformMerchantNo)) {
                        selectSubMerchantNoList.add(subMerchantNo);
                    }
                }
            }
        }
        QuerySubMerchantInfoResponseDTO responseDTO = new QuerySubMerchantInfoResponseDTO();
        responseDTO.setPlatformModelList(platformList);
        responseDTO.setSubModelList(subMerchantList);
        responseDTO.setSelectedPlatformList(selectPlatformMerchantNoList);
        responseDTO.setSelectedSubMerchantList(selectSubMerchantNoList);
        return BaseRespDTO.success(responseDTO);
    }


    @RequestMapping(value = "/queryAccountInfoList")
    @ResponseBody
    public ResponseMessage queryAccountInfoList(SubMerchantQueryParam param) {
        //查询下级商户名称和商户编号
        LOGGER.info("查询下级账户信息，请求参数{}", JSON.toJSONString(param));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        List<MerchantAccountInfoEntity> totalList = new ArrayList<>();
        List<MerchantAccountInfoEntity> allList = new ArrayList<>();
        List<MerchantAccountInfoEntity> pageList = new ArrayList<>();
        List<MerchantAccountInfoEntity> allMerchants = new ArrayList<>();
        param = handleParam(param);
        try {
            if (CollectionUtils.isEmpty(param.getMerchantAccountInfos())) {
                allMerchants = accountManageInfoService.queryMerchantList(getCurrentCustomerNumber());
                //下级商户如果超过一定数量，不让查询全部，
                if (allMerchants.size() > ConfigUtils.getMaxSubMerchantNumber()) {
                    resMsg.setStatus(ResponseMessage.Status.ERROR);
                    resMsg.setErrMsg("数据量太大，查询缓慢，建议指定具体下级商户查询！");
                    return resMsg;
                }
            }else {
                allMerchants = param.getMerchantAccountInfos();
            }
            if(CollectionUtils.isNotEmpty(allMerchants)) {
                allList = querySubordinateMerchantAccountList(allMerchants, param, param.getPageSize(), param.getPageNo());
                if(CollectionUtils.isNotEmpty(allList)){
                    totalList=filterAccountList(allList);
                }
                if (CollectionUtils.isNotEmpty(totalList)) {
                    int page = totalList.size() / param.getPageSize() + 1;
                    if (param.getPageNo() == page ) {
                        pageList = totalList.subList((param.getPageNo()-1)* param.getPageSize(), totalList.size());
                    } else if(param.getPageNo() < page) {
                        pageList = totalList.subList((param.getPageNo()-1)* param.getPageSize(), param.getPageNo() * param.getPageSize());
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("querySubordinateMerchantList,查询异常,e={}", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }
        if (CollectionUtils.isNotEmpty(pageList)) {
            List<Map<String, Object>> result = new ArrayList<>();
            for (MerchantAccountInfoEntity merchantAccountInfo : pageList) {
                //处理返回参数
                Map<String, Object>  map = adaptReturnResult(merchantAccountInfo);
                result.add(map);
            }
            resMsg.put("dataList", result);
        }
        //查询汇总信息
        resMsg = this.querySubordinateMerchantAccountSum(totalList, resMsg);
        resMsg = querySubordinateMerchantSum(totalList,allMerchants, param,resMsg);
        resMsg.put("pageNo", param.getPageNo());
        resMsg.put("pageSize", param.getPageSize());
        LOGGER.info("查询商户列表信息返回，resMsg={}", JSON.toJSONString(resMsg));
        return resMsg;
    }

    public static List<MerchantAccountInfoEntity> filterAccountList(List<MerchantAccountInfoEntity> allList) {
        Map<String, String> accountTypeMap = ConfigUtils.getOuterAccountTypeEnumMap();
        return allList.stream()
                .filter(account -> accountTypeMap.containsKey(account.getAccountType()))
                .collect(Collectors.toList());
    }

    @RequestMapping(value = "/app/queryAccountInfoList")
    @ResponseBody
    public BasePageRespDTO<MerchantAccountSummaryModel> queryAppAccountInfoList(SubMerchantQueryParam param) {
        //查询下级商户名称和商户编号
        LOGGER.info("app版查询下级账户信息，请求参数{}", JSON.toJSONString(param));
        List<MerchantAccountInfoEntity> totalList = new ArrayList<>();
        List<MerchantAccountInfoEntity> filterList = new ArrayList<>();
        List<SubMerchantAccountBalanceRespDto> totalBalanceSummaryList = new ArrayList<>();
        List<SubMerchantAccountBalanceRespDto> pageBalanceSummaryList = new ArrayList<>();
        List<MerchantAccountInfoEntity> allMerchants = new ArrayList<>();
        param = handleParam(param);
        try {
            if (CollectionUtils.isEmpty(param.getMerchantAccountInfos())) {
                allMerchants = accountManageInfoService.queryMerchantList(getCurrentCustomerNumber());
//                下级商户如果超过一定数量，不让查询全部，
                if (allMerchants.size() > ConfigUtils.getMaxSubMerchantNumber()) {
                    return BasePageRespDTO.fail(AccountPayException.EXCEED_MAX_SUB_MERCHANT_NUMBER.getDefineCode(),"数据量太大，查询缓慢，建议指定具体下级商户查询！");
                }
            }else {
                allMerchants = param.getMerchantAccountInfos();
            }
            if(CollectionUtils.isNotEmpty(allMerchants)) {
                totalList = querySubordinateMerchantAccountList(allMerchants, param, param.getPageSize(), param.getPageNo());
                if(CollectionUtils.isNotEmpty(totalList)){
                    filterList=filterAccountList(totalList);
                    totalBalanceSummaryList = this.convertMerchantAccountSummary(filterList);
                }
                if (CollectionUtils.isNotEmpty(totalBalanceSummaryList)) {
                    int page = totalBalanceSummaryList.size() / param.getPageSize() + 1;
                    if (param.getPageNo() == page ) {
                        pageBalanceSummaryList = totalBalanceSummaryList.subList((param.getPageNo()-1)* param.getPageSize(), totalBalanceSummaryList.size());
                    } else if(param.getPageNo() < page) {
                        pageBalanceSummaryList = totalBalanceSummaryList.subList((param.getPageNo()-1)* param.getPageSize(), param.getPageNo() * param.getPageSize());
                    }
                }
            }
        } catch (Throwable e) {
            LOGGER.error("querySubordinateMerchantList,查询异常,e={}", e);
            return BasePageRespDTO.systemError(e.getMessage());
        }
        List<MerchantAccountSummaryModel> modelList = new ArrayList<>();
       //查询汇总信息
        MerchantAccountSummaryModel model = this.queryMerchantAccountSum(totalList,totalBalanceSummaryList, allMerchants, param);
        model.setSubMerchantAccountBalanceList(pageBalanceSummaryList);
        modelList.add(model);
        LOGGER.info("app版查询商户列表信息返回{}", JSON.toJSONString(modelList));
        return BasePageRespDTO.successPage(modelList, Long.parseLong(String.valueOf(totalBalanceSummaryList.size())));
    }

    private MerchantAccountSummaryModel queryMerchantAccountSum(List<MerchantAccountInfoEntity> totalList, List<SubMerchantAccountBalanceRespDto> totalBalanceSummaryList, List<MerchantAccountInfoEntity> allMerchants, SubMerchantQueryParam param) {
        MerchantAccountSummaryModel model = new MerchantAccountSummaryModel();
        // 获取accountTypeMap
        Map<String, String> accountTypeMap = ConfigUtils.getOuterAccountTypeEnumMap();
        // 总余额，只计算accountType在accountTypeMap的key中的商户余额
        model.setSumBalance(CollectionUtils.isNotEmpty(totalList) ? totalList.stream().filter(entity -> accountTypeMap.containsKey(entity.getAccountType())) // 过滤出accountType在accountTypeMap的key中的商户
                        .map(MerchantAccountInfoEntity::getBalance)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        : BigDecimal.ZERO
        );
        // sumCount
        model.setSumCount(CollectionUtils.isNotEmpty(totalBalanceSummaryList) ? totalBalanceSummaryList.size() : 0);
        // 根据accountType统计商户数量（去重）
        String accountType = param.getAccountType();
        if (CollectionUtils.isNotEmpty(totalList)) {
            if (StringUtils.isNotBlank(accountType)) {
                // 按accountType过滤并去重统计商户数量
                long count = totalList.stream()
                        .filter(entity -> accountType.equals(entity.getAccountType()))
                        .map(MerchantAccountInfoEntity::getMerchantNo)
                        .distinct()
                        .count();
                model.setMerchantCount((int) count);
            } else {
                long count = totalList.stream()
                        .filter(entity -> accountTypeMap.containsKey(entity.getAccountType()))
                        .map(MerchantAccountInfoEntity::getMerchantNo)
                        .distinct()
                        .count();
                model.setMerchantCount((int) count);
            }
        } else {
            model.setMerchantCount(0);
        }

        return model;
    }
    @RequestMapping(value = "/v2/queryAccountInfoList")
    @ResponseBody
    public ResponseMessage queryAccountInfoListV2(SubMerchantAccountQueryRequestDTO param) {
        LOGGER.info(" 查询下级商户的账户信息, version 2.0, param: {}", NewRemitConstant.GSON.toJson(param));
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try{
            List<MerchantAccountInfoEntity> queryMerchantList = new ArrayList<>();
            String accountType = param.getAccountType();
            List<String> accountTypeList = Lists.newArrayList();
            if (org.springframework.util.StringUtils.hasLength(accountType)) {
                if (AccountTypeEnum.FUND_ACCOUNT.name().equals(param.getAccountType())) {
                    accountTypeList.add(AccountTypeEnum.FUND_ACCOUNT.name());
                    accountTypeList.add(AccountTypeEnum.DEPOSIT_ACCOUNT.name());
                } else {
                    accountTypeList.add(param.getAccountType());
                }
            }
            // 获取关系上下文
            MerchantRelationContext context = merchantPlatformRemoteService.queryMerchantRelation(getCurrentCustomerNumber());
            // 设置查参
            queryMerchantList = setQueryMerchantList(param, context);
            // 查
            List<MerchantAccountInfoEntity> totalList = doQueryMerchantAccountBalance(queryMerchantList, accountTypeList);
            // 裁剪
            List<MerchantAccountInfoEntity> pageList = getMerchantAccountInfoEntities(totalList, param.getPageSize(), param.getPageNo());
            // 转换响应结果
            List<Map<String, Object>> dataList = convertToPageResult(pageList, context);
            // 设置结果
            querySubordinateMerchantAccountSumV2(totalList, resMsg, context);
            querySubordinateMerchantSumV2(queryMerchantList, resMsg, context);
            resMsg.put("dataList", dataList);
            resMsg.put("pageNo", param.getPageNo());
            resMsg.put("pageSize", param.getPageSize());
        } catch (Exception e) {
            LOGGER.error("查询下级商户的账户信息, version 2.0 ,查询异常, cased by ", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询异常");
            return resMsg;
        }
        LOGGER.info("查询下级商户的账户信息, version 2.0, resMsg: {}", NewRemitConstant.GSON.toJson(resMsg));
        return resMsg;
    }

    private List<MerchantAccountInfoEntity> getMerchantAccountInfoEntities(List<MerchantAccountInfoEntity> totalList, Integer pageSize, Integer pageNo) {
        List<MerchantAccountInfoEntity> pageList = Lists.newArrayList();
        if (!org.springframework.util.CollectionUtils.isEmpty(totalList)) {
            int page = totalList.size() / pageSize + 1;
            if (pageNo == page) {
                pageList = totalList.subList((pageNo - 1) * pageSize, totalList.size());
            } else if (pageNo < page) {
                pageList = totalList.subList((pageNo - 1) * pageSize, pageNo * pageSize);
            }
        }
        return pageList;
    }

    private List<Map<String, Object>> convertToPageResult(List<MerchantAccountInfoEntity> pageList, MerchantRelationContext context) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (MerchantAccountInfoEntity accountInfo : pageList) {
            Map<String, Object> resultMap = convertToSingleRowMap(accountInfo, context);
            resultList.add(resultMap);
        }
        return resultList;
    }

    private List<MerchantAccountInfoEntity> setQueryMerchantList(SubMerchantAccountQueryRequestDTO param, MerchantRelationContext context) {
        List<MerchantAccountInfoEntity> queryMerchantList = Lists.newArrayList();
        String platformNo = param.getPlatformNo();
        String[] subMerchantNoArray = param.getSubMerchantNoArray();

        if (/* 空参查询 */ StringUtils.isEmpty(platformNo) && (null == subMerchantNoArray || subMerchantNoArray.length == 0)) {
            for (MerchantInfoModel model : context.getAllMerchantMap().values()) {
                setQueryParam(model, queryMerchantList);
            }
        } else {
            if (/* 有平台, 有子商户 */ org.springframework.util.StringUtils.hasLength(platformNo) && !(null == subMerchantNoArray || subMerchantNoArray.length == 0)) {
                MerchantInfoModel platformModel = context.getAllMerchantMap().get(platformNo);
                if (null == platformModel) {
                    LOGGER.warn("顶级: {} 关系结构下, 未能找到二级商编: {}, 查询返回空", getCurrentMerchant(), platformNo);
                    return queryMerchantList;
                }
                setQueryParam(platformModel, queryMerchantList);
                for (String subMerchantNo : subMerchantNoArray) {
                    MerchantInfoModel model = context.getAllMerchantMap().get(subMerchantNo);
                    if (null == model) {
                        LOGGER.warn("顶级: {} 关系结构下, 未能找到三级商编: {}, 排除此商户", getCurrentMerchant(), subMerchantNo);
                        continue;
                    }
                    if (platformNo.equals(model.getParentMerchantNo())) {
                        setQueryParam(model, queryMerchantList);
                    }
                }
            } else if (/* 有平台, 没有子商户 */ org.springframework.util.StringUtils.hasLength(platformNo) && (null == subMerchantNoArray || subMerchantNoArray.length == 0)) {
                MerchantInfoModel platformModel = context.getAllMerchantMap().get(platformNo);
                if (null == platformModel) {
                    LOGGER.warn("顶级: {} 关系结构下, 未能找到二级商编: {}, 查询返回空", getCurrentMerchant(), platformNo);
                    return queryMerchantList;
                }
                setQueryParam(platformModel, queryMerchantList);
                List<MerchantInfoModel> subModelList = context.getAscRelationMap().get(platformNo);
                for (MerchantInfoModel model : subModelList) {
                    setQueryParam(model, queryMerchantList);
                }
            } else {
                Map<String, MerchantInfoModel> subMerchantMap = context.getSubMerchantMap();
                for (String subMerchantNo : subMerchantNoArray) {
                    MerchantInfoModel model = subMerchantMap.get(subMerchantNo);
                    if (null == model) {
                        LOGGER.warn("顶级: {} 关系结构下, 未能找到三级商编: {}, 排除此商户", getCurrentMerchant(), subMerchantNo);
                        continue;
                    }
                    setQueryParam(model, queryMerchantList);
                }
            }
        }
        return queryMerchantList;
    }

    private void setQueryParam(MerchantInfoModel platformModel, List<MerchantAccountInfoEntity> allMerchants) {
        MerchantAccountInfoEntity singleParam = new MerchantAccountInfoEntity();
        singleParam.setMerchantNo(platformModel.getMerchantNo());
        singleParam.setSignName(platformModel.getMerchantSignName());
        allMerchants.add(singleParam);
    }


    @RequestMapping(value = "/download")
    @ResponseBody
    public void downloadRecord(SubMerchantQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            LOGGER.info("开始下载下级账户信息，请求参数{}", JSON.toJSONString(param));
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            String currentCustomerNumber = getCurrentCustomerNumber();
            param = handleParam(param);
            param.setCustomerNumber(currentCustomerNumber);

            List<Map<String,Object>> queryResult = new ArrayList<>();
            List<MerchantAccountInfoEntity> allMerchants = new ArrayList<>();
            if (CollectionUtils.isEmpty(param.getMerchantAccountInfos())) {
                allMerchants = accountManageInfoService.queryMerchantList(param.getCustomerNumber());
            }else {
                allMerchants = param.getMerchantAccountInfos();
            }
            List<MerchantAccountInfoEntity>  totalList = new ArrayList<>();
            List<MerchantAccountInfoEntity>  filterList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(allMerchants)) {
                List<MerchantAccountInfoEntity> merchantAccountInfoEntityList = accountManageInfoService.batchQueryAccountInfos(allMerchants, param.getAccountTypeList());
                filterList=filterAccountList(merchantAccountInfoEntityList);
                totalList.addAll(filterList);
            }
            if(CollectionUtils.isNotEmpty(totalList)){
                param.setMerchantAccountInfos(totalList);
            }

            StringBuilder desc = new StringBuilder();
            desc.append("下级账户查询,").append(currentCustomerNumber).append("数据");
            new AccountBalanceDownloadService(getCurrentUser(), param, desc.toString(), "下级账户查询-").download(request, response);
            LOGGER.info("下级账户查询记录excel已完成");
        } catch (Throwable ex) {
            LOGGER.error("下载异常，ex={}", ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + ex.getMessage() + "')</script>");
        }
    }

    @RequestMapping(value = "/v2/download")
    @ResponseBody
    public void downloadRecordV2(DownloadSubMerchantAccountQueryRequestDTO param, HttpServletRequest request, HttpServletResponse response) throws Exception {
        LOGGER.info("开始下载下级账户信息, version 2.0, param: {}", JSON.toJSONString(param));
        try {
            CheckUtils.notEmpty(param.getFileType(), "fileType");
            String accountType = param.getAccountType();
            List<String> accountTypeList = Lists.newArrayList();
            if (org.springframework.util.StringUtils.hasLength(accountType)) {
                accountTypeList.add(accountType);
            }
            // 获取关系上下文
            MerchantRelationContext context = merchantPlatformRemoteService.queryMerchantRelation(getCurrentCustomerNumber());
            // 设置查参
            List<MerchantAccountInfoEntity> queryMerchantList = setQueryMerchantList(param, context);
            // 查
            List<MerchantAccountInfoEntity> totalList = doQueryMerchantAccountBalance(queryMerchantList, accountTypeList);
            StringBuilder desc = new StringBuilder();
            desc.append("下级账户查询,").append(getCurrentCustomerNumber()).append("数据");
            response.setHeader("Content-type", "text/html;charset=UTF-8");
            new AccountBalanceDownloadV2Service(getCurrentUser(), param, desc.toString(), "下级账户查询-", totalList, context).download(request, response);
            LOGGER.info("下载下级账户信息完成, version 2.0, param: {}", JSON.toJSONString(param));
        } catch (Exception e) {
            LOGGER.error("查询下级商户的账户信息, version 2.0 ,查询异常, cased by ", e);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('" + e.getMessage() + "')</script>");
        }
    }

    /**
     * 查询下级商户余额信息列表
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    private List<MerchantAccountInfoEntity> querySubordinateMerchantAccountList(List<MerchantAccountInfoEntity> allMerchants, SubMerchantQueryParam param, int pageNo, int pageSize) {
        List<MerchantAccountInfoEntity> totalList = new ArrayList<>();
        List<MerchantAccountInfoEntity> merchantAccountInfoEntityList = accountManageInfoService.batchQueryAccountInfos(allMerchants, param.getAccountTypeList());
        if (CollectionUtils.isNotEmpty(merchantAccountInfoEntityList)) {
            totalList.addAll(merchantAccountInfoEntityList);
        }
        return totalList;
    }

    /**
     * 查询, 底层逻辑没改, 还是乱糟糟的
     *
     * @param merchantList
     * @param accountTypeList
     * @return
     */
    private List<MerchantAccountInfoEntity> doQueryMerchantAccountBalance(List<MerchantAccountInfoEntity> merchantList, List<String> accountTypeList) {
        List<MerchantAccountInfoEntity> totalList = Lists.newArrayList();
        if (org.springframework.util.CollectionUtils.isEmpty(merchantList)) {
            return totalList;
        }
        List<MerchantAccountInfoEntity> resultList = accountManageInfoService.batchQueryAccountInfos(merchantList, accountTypeList);
        if (CollectionUtils.isNotEmpty(resultList)) {
            totalList.addAll(resultList);
        }
        return totalList;
    }


    /**
     * 统计总余额和总数量
     *
     * @param totalList
     * @param resMsg
     * @return
     */
    private ResponseMessage querySubordinateMerchantAccountSum(List<MerchantAccountInfoEntity> totalList, ResponseMessage resMsg) {
        if (CollectionUtils.isNotEmpty(totalList)) {
            BigDecimal reduce = totalList.stream().map(MerchantAccountInfoEntity::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            resMsg.getData().put("sum_balance", reduce);// 总金额
            resMsg.getData().put("sum_count", totalList.size());// 总数
        } else {
            resMsg.getData().put("sum_count", 0);// 总笔数
            resMsg.getData().put("sum_balance", "0.00");// 总金额
        }
        return resMsg;
    }

    public List<SubMerchantAccountBalanceRespDto> convertMerchantAccountSummary(
            List<MerchantAccountInfoEntity> merchantAccountInfoEntityList) {
        // 按商户号分组账户信息
        Map<String, List<MerchantAccountInfoEntity>> accountGroupByMerchant = merchantAccountInfoEntityList.stream()
                .collect(Collectors.groupingBy(MerchantAccountInfoEntity::getMerchantNo));
        // 遍历分组后的商户账户信息生成DTO
        return accountGroupByMerchant.entrySet().stream()
                .map(entry -> {
                    String merchantNo = entry.getKey();
                    List<MerchantAccountInfoEntity> accounts = entry.getValue();
                    // 使用第一个账户的非空信息填充商户基础信息
                    MerchantAccountInfoEntity firstAccount = accounts.get(0);
                    SubMerchantAccountBalanceRespDto dto = new SubMerchantAccountBalanceRespDto();
                    dto.setMerchantNo(merchantNo);
                    dto.setSignName(firstAccount.getSignName());
                    dto.setShortName(firstAccount.getShortName());

                    // 遍历账户信息填充各类型余额
                    accounts.forEach(account -> {
                        String accountType = account.getAccountType();
                        BigDecimal balance = Optional.ofNullable(account.getBalance())
                                .orElse(BigDecimal.ZERO);

                        switch (accountType) {
                            case "SETTLE_ACCOUNT":
                                dto.setSettleAccountBalance(balance);
                                break;
                            case "DIVIDE_ACCOUNT":
                                dto.setDivideAccountBalance(balance);
                                break;
                            case "FUND_ACCOUNT":
                                dto.setFundAccountBalance(balance);
                                break;
                            case "FEE_ACCOUNT":
                                dto.setFeeAccountBalance(balance);
                                break;
                            case "MARKET_ACCOUNT":
                                dto.setMarketAccountBalance(balance);
                                break;
                            case "MEMBER_ACCOUNT":
                                dto.setMemberAccountBalance(balance);
                                break;
                            case "BASIC_ACCOUNT":
                                dto.setBasicAccountBalance(balance);
                                break;
                            case "SPECIAL_FUND_ACCOUNT":
                                dto.setSpecialFundAccountBalance(balance);
                                break;
                            case "UNDERWRITTEN_ACCOUNT":
                                dto.setUnderwrittenAccountBalance(balance);
                                break;
                            case "DIVIDE_TRANSIT_ACCOUNT":
                                dto.setDivideTransitAccountBalance(balance);
                                break;
                            case "ADVANCE_ACCOUNT":
                                dto.setAdvanceAccountBalance(balance);
                                break;
                            case "FROZEN_ACCOUNT":
                                dto.setFrozenAccountBalance(balance);
                                break;
                            default:
                                // 可选：处理未知账户类型或记录日志
                                break;
                        }
                    });

                    return dto;
                })
                .collect(Collectors.toList());
    }


    private ResponseMessage querySubordinateMerchantSum(List<MerchantAccountInfoEntity> totalList,List<MerchantAccountInfoEntity> merchantNosByParentNoDTOList,SubMerchantQueryParam param, ResponseMessage resMsg) {
        Map<String, String> accountTypeMap = ConfigUtils.getOuterAccountTypeEnumMap();
        // 根据accountType统计商户数量（去重）
        String accountType = param.getAccountType();
        if (CollectionUtils.isNotEmpty(totalList)) {
            if (StringUtils.isNotBlank(accountType)) {
                // 按accountType过滤并去重统计商户数量
                long merchantCount = totalList.stream()
                        .filter(entity -> accountType.equals(entity.getAccountType()))
                        .map(MerchantAccountInfoEntity::getMerchantNo)
                        .distinct()
                        .count();
                resMsg.getData().put("merchant_count", merchantCount);// 总数
            } else {
                long merchantCount = totalList.stream()
                        .filter(entity -> accountTypeMap.containsKey(entity.getAccountType()))
                        .map(MerchantAccountInfoEntity::getMerchantNo)
                        .distinct()
                        .count();
                resMsg.getData().put("merchant_count", merchantCount);
            }
        } else {
            resMsg.getData().put("merchant_count", 0);// 总笔数
        }
        return resMsg;
    }

    /**
     * 统计总余额和总数量
     *
     * @param totalList
     * @param resMsg
     * @return
     */
    private ResponseMessage querySubordinateMerchantAccountSumV2(List<MerchantAccountInfoEntity> totalList, ResponseMessage resMsg, MerchantRelationContext context) {
        if (CollectionUtils.isNotEmpty(totalList)) {
            List<MerchantAccountInfoEntity> subMerchantList = totalList.stream().filter(e -> context.getSubMerchantMap().containsKey(e.getMerchantNo())).collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(subMerchantList)) {
                resMsg.getData().put("sum_sub_balance", "0.00");// 总金额
            } else {
                BigDecimal reduce = subMerchantList.stream().map(MerchantAccountInfoEntity::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                resMsg.getData().put("sum_sub_balance", reduce);// 总金额
            }
            List<MerchantAccountInfoEntity> platMerchantList = totalList.stream().filter(e -> context.getPlatformMerchantMap().containsKey(e.getMerchantNo())).collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(platMerchantList)) {
                resMsg.getData().put("sum_plat_balance", "0.00");// 总金额
            } else {
                BigDecimal reduce = platMerchantList.stream().map(MerchantAccountInfoEntity::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                resMsg.getData().put("sum_plat_balance", reduce);// 总金额
            }
            resMsg.getData().put("sum_count", totalList.size());// 总数
        } else {
            resMsg.getData().put("sum_sub_balance", "0.00");// 总金额
            resMsg.getData().put("sum_plat_balance", "0.00");// 总金额
            resMsg.getData().put("sum_count", 0);
        }
        return resMsg;
    }

    private ResponseMessage querySubordinateMerchantSumV2(List<MerchantAccountInfoEntity> queryMerchantList, ResponseMessage resMsg, MerchantRelationContext context) {
        if (CollectionUtils.isNotEmpty(queryMerchantList)) {
            List<MerchantAccountInfoEntity> subMerchantList = queryMerchantList.stream().filter(e -> context.getSubMerchantMap().containsKey(e.getMerchantNo())).collect(Collectors.toList());
            List<MerchantAccountInfoEntity> platMerchantList = queryMerchantList.stream().filter(e -> context.getPlatformMerchantMap().containsKey(e.getMerchantNo())).collect(Collectors.toList());
            resMsg.getData().put("merchant_sub_count", Optional.ofNullable(subMerchantList).orElse(Lists.newArrayList()).size());
            resMsg.getData().put("merchant_plat_count", Optional.ofNullable(platMerchantList).orElse(Lists.newArrayList()).size());
        } else {
            resMsg.getData().put("merchant_sub_count", 0);// 总数
            resMsg.getData().put("merchant_plat_count", 0);// 总数
        }
        return resMsg;
    }



    /**
     * 适配返回结果
     *
     * @param req
     * @return
     */
    private Map<String, Object> adaptReturnResult(MerchantAccountInfoEntity req) {
        Map<String, Object> detail = new HashMap<>();

        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (null != req.getBalance()) {
                detail.put("balance", req.getBalance().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }

            if (null != req.getMerchantNo()) {
                detail.put("merchant_no", req.getMerchantNo());
            }

            if (null != req.getSignName()) {
                detail.put("sign_name", req.getSignName());
            }
            if(null != req.getShortName()){
                detail.put("short_name", req.getShortName());
            }

            if (null != req.getStatus()) {
                detail.put("status", AccountStatusEnum.valueOf(req.getStatus()).desc());
            }

            if (null != req.getAccountType()) {
                if (AccountTypeEnum.DEPOSIT_ACCOUNT.name().equals(req.getAccountType()) || AccountTypeEnum.FUND_ACCOUNT.name().equals(req.getAccountType())) {
                    detail.put("account_type", "商户资金账户");
                } else if ("ADVANCE_ACCOUNT".equals(req.getAccountType())) {
                    detail.put("account_type", "预收账户");
                } else {
                    detail.put("account_type", AccountTypeEnum.valueOf(req.getAccountType()).getDesc());
                }
            }
            //下单时间
            Date obj = req.getCreateDate();
            if (null != obj) {
                detail.put("create_date", smf.format(req.getCreateDate()));
            }
        } catch (Exception e) {
            LOGGER.error("转化异常", e);
        }
        return detail;
    }
    /**
     * 适配返回结果
     *
     * @param accountInfo
     * @return
     */
    private Map<String, Object> convertToSingleRowMap(MerchantAccountInfoEntity accountInfo, MerchantRelationContext context) {
        Map<String, Object> detail = new HashMap<>();
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            MerchantInfoModel model = context.getAllMerchantMap().get(accountInfo.getMerchantNo());
            if (null != accountInfo.getBalance()) {
                detail.put("balance", accountInfo.getBalance().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }
            if (null != accountInfo.getMerchantNo()) {
                detail.put("merchant_no", accountInfo.getMerchantNo());
            }
            if (null != accountInfo.getSignName()) {
                detail.put("sign_name", accountInfo.getSignName());
            }
            if (null != accountInfo.getStatus()) {
                detail.put("status", AccountStatusEnum.valueOf(accountInfo.getStatus()).desc());
            }
            if (null != accountInfo.getAccountType()) {
                if (AccountTypeEnum.DEPOSIT_ACCOUNT.name().equals(accountInfo.getAccountType()) || AccountTypeEnum.FUND_ACCOUNT.name().equals(accountInfo.getAccountType())) {
                    detail.put("account_type", "商户资金账户");
                } else if ("ADVANCE_ACCOUNT".equals(accountInfo.getAccountType())) {
                    detail.put("account_type", "预收账户");
                } else {
                    detail.put("account_type", AccountTypeEnum.valueOf(accountInfo.getAccountType()).getDesc());
                }
            }
            Date obj = accountInfo.getCreateDate();
            if (null != obj) {
                detail.put("create_date", smf.format(accountInfo.getCreateDate()));
            }
            if (MerchantPlatformRemoteServiceImpl.SUN_SET.contains(model.getBusinessRole())) {
                MerchantInfoModel platformModel = context.getAllMerchantMap().get(model.getParentMerchantNo());
                detail.put("platform_no", platformModel.getMerchantNo());
                detail.put("platform_name", platformModel.getMerchantSignName());
            } else {
                detail.put("platform_no", "");
                detail.put("platform_name", "");
            }
        } catch (Exception e) {
            LOGGER.error("转化异常", e);
        }
        return detail;
    }

    /**
     * 参数预处理
     * @param param
     * @return
     */
    private SubMerchantQueryParam handleParam(SubMerchantQueryParam param) {
        if (StringUtils.isNotBlank(param.getAccountType())) {
            if(AccountTypeEnum.FUND_ACCOUNT.name().equals(param.getAccountType())){
                param.setAccountTypeList(Arrays.asList(AccountTypeEnum.FUND_ACCOUNT.name(),AccountTypeEnum.DEPOSIT_ACCOUNT.name()));
            }else {
                param.setAccountTypeList(Arrays.asList(param.getAccountType()));
            }
        }
        List<MerchantAccountInfoEntity> merchantAccountInfos = new ArrayList<>();
        if(param.getMerchantNoArr() != null){
            for (String merchantNoAndName:param.getMerchantNoArr()){
                if(StringUtils.isNotBlank(merchantNoAndName)){
                    MerchantAccountInfoEntity merchantAccountInfoEntity = new MerchantAccountInfoEntity();
                    String[] array = merchantNoAndName.split(";;;");
                    merchantAccountInfoEntity.setMerchantNo(array[0].trim());
                    if(array.length>1) {
                        merchantAccountInfoEntity.setSignName(array[1].trim());
                    }
                    //简称
                    if(array.length>2){
                        merchantAccountInfoEntity.setShortName(array[2].trim());
                    }
                    merchantAccountInfos.add(merchantAccountInfoEntity);
                }
            }
            param.setMerchantAccountInfos(merchantAccountInfos);
        }
        LOGGER.info("参数处理，param={}", JSON.toJSONString(param));
        return param;
    }

    /**
     * 提供给红版商户后台展示 账户总览
     * @param
     * @return
     */
    @RequestMapping(value = "/queryAccountBalance", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage queryAccountBalance() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentUser().getCustomerNumber();
        LOGGER.info("[账户余额总览] 商户={}", merchantNo);
        try {
            AccountInfoQueryRespDTO respDTO = remoteService.queryAccountInfoList(merchantNo, merchantNo);
            BigDecimal totalBalance = BigDecimal.ZERO;
            List<AccountInfoDetailDTO> resultList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(respDTO.getAccountInfoList())) {
                for (AccountInfo accountInfo : respDTO.getAccountInfoList()) {
                    if ("HANDLE_ACCOUNT".equals(accountInfo.getAccountType()) ||
                            "MEMBER_ACCOUNT".equals(accountInfo.getAccountType()) ||
                            "UNDERWRITTEN_ACCOUNT".equals(accountInfo.getAccountType()) ||
                            "VCC_ACCOUNT".equals(accountInfo.getAccountType()) ||
                            "DIVIDE_TRANSIT_ACCOUNT".equals(accountInfo.getAccountType()) ||
                            "REBATE_ACCOUNT".equals(accountInfo.getAccountType())) {
                        continue;
                    }
                    AccountInfoDetailDTO detailDTO = new AccountInfoDetailDTO();
                    detailDTO.setAccountType(accountInfo.getAccountType());
                    detailDTO.setBalance(accountInfo.getBalance() == null ? BigDecimal.ZERO : accountInfo.getBalance());
                    resultList.add(detailDTO);
                    totalBalance = totalBalance.add(accountInfo.getBalance() == null ? BigDecimal.ZERO : accountInfo.getBalance());
                }
            }
            resMsg.put("accountTypeList", resultList);
            resMsg.put("totalBalance", totalBalance);
        } catch (AccountPayException e) {
            LOGGER.info("查询账户信息业务异常，currentCustomerNumber=" + merchantNo, e);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            LOGGER.error("查询账户信息异常，currentCustomerNumber=" + merchantNo, e);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

    /**
     * 提供给app展示 账户总览
     * @return
     */
    @RequestMapping(value = "/accountQuery", method = RequestMethod.GET)
    @ApiOperation("app账户总览")
    @ResponseBody
    /*这个权限交由APP自己控制了入口实现了*/
//    @RequiresPermissions(Costants.APP_BALANCE_SHOW)
    public BaseRespDTO<AccountQueryResult> accountQuery() {
        String merchantNo = getCurrentUser().getCustomerNumber();
        try {
            AccountQueryResult result = accountManageInfoService.accountQuery(merchantNo);
            return BaseRespDTO.success(result);
        } catch (AccountPayException e) {
            LOGGER.info("查询账户信息 业务异常，currentCustomerNumber=" + merchantNo, e);
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("查询账户信息 系统异常，currentCustomerNumber=" + merchantNo, e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    /**
     * 查询当前和下级商户编号和名称
     *
     * @return
     */
    @RequestMapping(value = "/queryCurrentAndSubMerchantList", method = RequestMethod.GET)
    @ResponseBody
    public BaseRespDTO queryCurrentAndSubMerchantList() {
        ShiroUser currentUser = getCurrentUser();
        LOGGER.info("查询商户下拉列表信息返回，currentCustomerNumber={},signName={}", currentUser.getCustomerNumber(),currentUser.getSignName());
        //查询下级商户名称和商户编号
        Map<String,Object> map=new HashMap<>();
        List<MerchantAccountInfoEntity> list;
        try {
            //查询下级商户列表
            list = accountManageInfoService.queryCurrentAndSubMerchantList(currentUser.getCustomerNumber());
        } catch (Exception e) {
            LOGGER.error("queryCurrentAndSubMerchantList,查询异常,e={}", e);
            return BaseRespDTO.fail("查询异常");
        }
        if (CollectionUtils.isEmpty(list)) {
            LOGGER.info("查询商户下拉列表信息返回，resMsg={}", JSON.toJSONString(list));
            return BaseRespDTO.success(map);
        }
        List<MerchantInfoParam> merchantInfoList = new ArrayList<>();
        list.forEach(merchantNosByParentNoDTO -> {
            MerchantInfoParam merchantInfo = new MerchantInfoParam();
            merchantInfo.setMerchantNo(merchantNosByParentNoDTO.getMerchantNo());
            merchantInfo.setMerchantName(merchantNosByParentNoDTO.getSignName());
            merchantInfoList.add(merchantInfo);
        });
        map.put("merchantList", merchantInfoList);
        LOGGER.info("查询商户下拉列表信息返回，resMsg={}", JSON.toJSONString(merchantInfoList));
        return BaseRespDTO.success(map);
    }


    /**
     * 提供给红版商户后台展示 顶点虚户信息
     * @param
     * @return
     */
    @RequestMapping(value = "/queryVirtualAccountBalance", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage queryVirtualAccountBalance() {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String merchantNo = getCurrentUser().getCustomerNumber();
        LOGGER.info("[queryVirtualAccountBalance 顶点虚户信息查询] 商户={}", merchantNo);
        try {
            VirtualAccountResponseDTO respDTO = remoteService.queryVirtualAccountBalance(merchantNo);
            BigDecimal totalBalance = BigDecimal.ZERO;
            List<AccountInfoDetailDTO> resultList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(respDTO.getVirtualAccountInfoDTOList())) {
                for (VirtualAccountInfoDTO virtualAccountInfoDTO : respDTO.getVirtualAccountInfoDTOList()) {
                    AccountInfoDetailDTO detailDTO = new AccountInfoDetailDTO();
                    detailDTO.setAccountType(virtualAccountInfoDTO.getAccountType());
                    detailDTO.setBalance(virtualAccountInfoDTO.getBalance() == null ? BigDecimal.ZERO : virtualAccountInfoDTO.getBalance());
                    resultList.add(detailDTO);
                    totalBalance = totalBalance.add(virtualAccountInfoDTO.getBalance() == null ? BigDecimal.ZERO : virtualAccountInfoDTO.getBalance());
                }
            }
            resMsg.put("accountTypeList", resultList);
            resMsg.put("totalBalance", totalBalance);
        } catch (AccountPayException e) {
            LOGGER.info("queryVirtualAccountBalance 查询顶点虚户信息业务异常，currentCustomerNumber=" + merchantNo, e);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        } catch (Exception e) {
            LOGGER.error("queryVirtualAccountBalance 查询顶点虚户信息系统异常，currentCustomerNumber=" + merchantNo, e);
            resMsg.setErrMsg(e.getMessage());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
        }
        return resMsg;
    }

}
