package com.yeepay.g3.app.account.pay.mboss.databaseScan.manage.entity;

import com.yeepay.g3.facade.unionaccount.manage.enumtype.*;
import lombok.Data;

import java.util.Date;


@Data
public class BankAccountOpenRecordEntity {

    /**
     * id
     */
    private Long id;

    /**
     * 乐观锁标记位
     */
    private Short optimistic;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date lastModifyTime;

    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 商户编号
     */
    private String customerNo;

    /**
     * 银行账户号
     */
    private String bankAccountNo;

    /**
     * 通道账户类型
     * {@link ChannelAccountTypeEnum}
     */
    private String channelAccountType;

    /**
     * 开户类型
     * <p>
     * 企业/个人
     * {@link OpenBankAccountMerchantType}
     */
    private String merchantType;

    /**
     * 银行标识
     * {@link BankAccountBankCodeEnum}
     */
    private String bankIdentityCode;

    /**
     * 状态
     * {@link BankAccountOpenStatusEnum}
     */
    private String status;

    /**
     * 账户状态
     */
    private String accountStatus;

    /**
     * 请求通道时间
     */
    private Date requestTime;

    /**
     * 开户完成时间
     */
    private Date completeTime;

    /**
     * 返回码
     */
    private String returnCode;

    /**
     * 返回信息
     */
    private String returnMessage;

    /**
     * 通知状态
     * {@link BankAccountNotifyStatusEnum}
     */
    private String notifyStatus;

    /**
     * 通知次数
     */
    private Long notifyCount;

    /**
     * 易宝订单号
     */
    private String orderNo;

    /**
     * 技术发起方商编
     */
    private String initiateMerchantNo;

    /**
     * 业务商编
     */
    private String parentMerchantNo;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 证件类型
     * {@link CertificateTypeEnum}
     */
    private String certificateType;

    /**
     * 证件号
     */
    private String certificateNo;

    /**
     * 经办人手机号
     */
    private String mobileNo;

    /**
     * 页面跳转地址
     */
    private String returnUrl;

    /**
     * 回调通知地址
     */
    private String notifyUrl;


    /**
     * 银行客户号-厦门银行用
     */
    private String bankCustomerNo;

    /**
     * 应用标识
     */
    private String appKey;

    /**
     * 辅助认证类型
     * {@link MinisterAuthTypeEnum}
     */
    private String ministerAuthType;

    /**
     * 辅助认证状态
     * {@link MinisterAuthStatusEnum}
     */
    private String ministerAuthStatus;

    /**
     * 请求来源
     * {@link BankAccountRequestSourceEnum}
     */
    private String requestSource;

    /**
     * 通知类型
     * {@link NotifyTypeEnum}
     */
    private String notifyType;

    /**
     * 操作商编
     */
    private String operateMerchantNo;
}