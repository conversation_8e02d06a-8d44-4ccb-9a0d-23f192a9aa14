package com.yeepay.g3.app.account.pay.mboss.controller.file;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FileHandle {
    String value() default "";

    String desc() default "";

    int index() default 0;
}
