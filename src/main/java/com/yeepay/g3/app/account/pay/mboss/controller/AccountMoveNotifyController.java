package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.unionaccount.manage.dto.request.AccountNotifyRuleReqDTO;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.AccountNotifyRuleRespDTO;
import com.yeepay.g3.facade.unionaccount.manage.enumtype.*;
import com.yeepay.g3.facade.unionaccount.manage.facade.MoveAccountNotifyFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date Create in 11:19 2020-02-27
 * @Version V1.0
 * @company 易宝支付(YeePay)
 */

@Controller
@RequestMapping("/account/notify")
public class AccountMoveNotifyController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountMoveNotifyController.class);


    private static final String QUERY_PERMISSION_CONSTANT = "21403";

    private MoveAccountNotifyFacade moveAccountNotifyFacade = RemoteServiceFactory.getService(MoveAccountNotifyFacade.class);

    @RequestMapping("/notifyList")
    public ModelAndView accountList(HttpServletRequest request, HttpServletResponse response) {

        //获取当前商编
        String customerNumber = getCurrentCustomerNumber();
        ModelAndView mav = new ModelAndView();

        AccountNotifyRuleReqDTO dto = new AccountNotifyRuleReqDTO();
        AccountNotifyRuleRespDTO accountNotifyRuleInfo = null;
        List<AccountNotifyRuleRespDTO> accountNotifyRuleRespDtos = null;
        dto.setMerchantNo(customerNumber);
        try {
            accountNotifyRuleRespDtos = moveAccountNotifyFacade.queryAccountNotifyRuleList(dto);
            LOGGER.info("商户通知规则信息返回，accountNotifyRuleRespDtos={}", JSON.toJSONString(accountNotifyRuleRespDtos));
        } catch (Exception e) {
            LOGGER.error("查询通知规则信息异常，e={}", e);

        }
        if (CollectionUtils.isNotEmpty(accountNotifyRuleRespDtos)) {
            accountNotifyRuleInfo = accountNotifyRuleRespDtos.get(0);
            mav.addObject("notifyStatus", accountNotifyRuleInfo.getNotifyStatus());
            mav.addObject("merchantNo", accountNotifyRuleInfo.getMerchantNo());
            mav.addObject("accountType", accountNotifyRuleInfo.getMerchantNo());
        }
        mav.setViewName("accountNotityRule/accountNotifyList");
        return mav;
    }


    /**
     * 新增规则设置页面
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/notifyRule")
    public ModelAndView rulePage(HttpServletRequest request,
                                 HttpServletResponse response) {

        ModelAndView mav = new ModelAndView();
        mav.setViewName("accountNotityRule/notifyRule");
        return mav;
    }


    /**
     * 详情页面
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions(QUERY_PERMISSION_CONSTANT)
    @RequestMapping("/notifyRuleDetail")
    @ResponseBody
    public ResponseMessage notifyRuleDetail(HttpServletRequest request,
                                            HttpServletResponse response) {

        ResponseMessage responseMessage = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        String customerNo = getCurrentCustomerNumber();
        AccountNotifyRuleReqDTO dto = new AccountNotifyRuleReqDTO();
        dto.setMerchantNo(customerNo);
        List<AccountNotifyRuleRespDTO> infos = null;

        try {
            //根据商编查询记录通知规则
            infos = moveAccountNotifyFacade.queryAccountNotifyRuleList(dto);
            LOGGER.info("查询通知规则数据，infos={}", JSON.toJSONString(infos));
        } catch (Exception e) {
            LOGGER.error("查询通知规则信息异常，e={}", e);

        }
        if (CollectionUtils.isNotEmpty(infos)) {
            //合并通知规则返回前端
            AccountNotifyRuleRespDTO respDTO = infos.get(0);
            responseMessage.put("respDTO", respDTO);
        } else {
            responseMessage.setStatus(ResponseMessage.Status.ERROR);
        }
        return responseMessage;
    }


    @RequestMapping(value = "/setNotifyRule")
    @ResponseBody
    public ResponseMessage settingNotityRule(@RequestParam(value = "notifyType[]") String[] notifyType,
                                             @RequestParam(value = "phone") String phone,
                                             @RequestParam(value = "systemAddress") String systemAddress,
                                             @RequestParam(value = "email") String email,
                                             @RequestParam(value = "smsNightReceivedStatus") String smsNightReceivedStatus,
                                             @RequestParam(value = "notifyStatus") String notifyStatus) {

        LOGGER.info("通知规则 notifyType[]={},phone={},systemAddress={}，email={}，notifyStatus={}",
                JSON.toJSONString(notifyType), phone, systemAddress, email, notifyStatus);

        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);

        if (CheckUtils.isEmpty(notifyType)) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(ResponseMessageEnum.NOTIFY_TYPE_NOT_SET.getMessage());
        }
        if (CheckUtils.isEmpty(notifyStatus)) {
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(ResponseMessageEnum.NOTIFY_STATUS_NOT_SET.getMessage());
        }

        String customerNo = getCurrentCustomerNumber();
        try {

            for (String type : notifyType) {
                AccountNotifyRuleReqDTO dto = new AccountNotifyRuleReqDTO();
                dto.setNotifyType(NotifyTypeEnum.valueOf(type));
                dto.setMerchantNo(customerNo);
                AccountNotifyRuleRespDTO accountNotifyRule = moveAccountNotifyFacade.
                        queryAccountNotifyByMerchantNoAndType(dto);
                dto.setNotifyStatus(NotifyStatusEnum.valueOf(notifyStatus));
                dto.setAccountType(AccountTypeEnum.SVA);
                dto.setOperator(getCurrentUser().getLoginName());

                if (accountNotifyRule != null) {
                    dto.setId(accountNotifyRule.getId());
                    dto = buildNotifyRuleParam(dto, type, systemAddress, email, phone, smsNightReceivedStatus);
                    dto.setUpdateTime(new Date());
                    moveAccountNotifyFacade.updateAccountNotifyRule(dto);
                } else {
                    dto = buildNotifyRuleParam(dto, type, systemAddress, email, phone, smsNightReceivedStatus);
                    dto.setCreateTime(new Date());
                    moveAccountNotifyFacade.saveAccountNotifyRule(dto);
                }

            }
        } catch (Exception e) {
            LOGGER.error("商户通知规则更新异常，ex={}", e);
        }
        return resMsg;
    }

    /**
     * 构建参数
     * @param dto
     * @param type
     * @param systemAddress
     * @param email
     * @param phone
     * @param smsNightReceivedStatus
     * @return
     */
    private AccountNotifyRuleReqDTO buildNotifyRuleParam(AccountNotifyRuleReqDTO dto, String type, String systemAddress,
                                                         String email, String phone, String smsNightReceivedStatus) {
        if (NotifyTypeEnum.SYSTEM.name().equals(type)) {
            dto.setNotifyUrl(systemAddress);
        } else if (NotifyTypeEnum.EMAIL.name().equals(type)) {
            dto.setNotifyUrl(email);
        } else {
            dto.setSmsNightReceivedStatus(SmsNightReceivedStatusEnum.valueOf(smsNightReceivedStatus));
            dto.setNotifyUrl(phone);
        }

        return dto;
    }



}
