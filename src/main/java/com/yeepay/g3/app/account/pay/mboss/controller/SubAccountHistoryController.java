package com.yeepay.g3.app.account.pay.mboss.controller;

import com.alibaba.fastjson.JSON;
import com.yeepay.g3.app.account.pay.mboss.dto.AccountHistoryQueryParam;
import com.yeepay.g3.app.account.pay.mboss.dto.AccountHistoryQueryResult;
import com.yeepay.g3.app.account.pay.mboss.service.impl.AccountInfoService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.MerchantRemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.SubAccountHistoryDownloadService;
import com.yeepay.g3.app.account.pay.mboss.utils.CheckParamUtils;
import com.yeepay.g3.app.account.pay.mboss.utils.Costants;
import com.yeepay.g3.app.account.pay.mboss.utils.UniformConfigUtils;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.mp.dto.AccountHistoryQueryResp;
import com.yeepay.g3.app.mp.service.AccountBalanceQueryService;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.account.manage.enums.AccountGtypeEnum;
import com.yeepay.g3.facade.account.manage.union.enums.AmountDirectionEnum;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.MerchantRespDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationReqDTO;
import com.yeepay.g3.facade.merchant_platform.dto.cuscenter.GroupBusinessRelationRespDTO;
import com.yeepay.g3.facade.merchant_platform.enumtype.BusinessType;
import com.yeepay.g3.facade.merchant_platform.facade.GroupBusinessRelationFacade;
import com.yeepay.g3.facade.merchant_platform.facade.MerchantFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 下级账户历史
 * <AUTHOR>
 * @date 2020-04-13 10:59
 */
@Controller
@RequestMapping("/subAccountHistory")
public class SubAccountHistoryController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubAccountHistoryController.class);

    /**
     * 客户中心接口，查询上下级关系
     */
    private GroupBusinessRelationFacade groupBusinessRelationFacade = RemoteServiceFactory.getService(GroupBusinessRelationFacade.class);

    /**
     * 客户中心接口
     */
    private MerchantFacade merchantFacade = RemoteServiceFactory.getService(MerchantFacade.class);

    @Autowired
    private AccountBalanceQueryService accountBalanceQueryService;


    @Autowired
    private MerchantRemoteService merchantRemoteService;


    /**
     * @Description: 查询账务历史页面
     * <AUTHOR>
     * @date 2020-04-14 12:18
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequiresPermissions(Costants.SUBMERCHANG_ACCOUNT_HISTORY)
    @RequestMapping
    @ResponseBody
    public ModelAndView forwardTradeHisQuery(@RequestParam(value = "subMerchantNo", required = false) String subMerchantNo) {
        ModelAndView mav = new ModelAndView();
        String subMerchantName = "";
        List<GroupBusinessRelationDTO> groupBusinessRelationDTOS = getGroupBusinessRelationReqDTO();
        mav.addObject("subGroup", groupBusinessRelationDTOS);
        //从查询账户过来的校验一下父子关系
        if(!CheckUtils.isEmpty(subMerchantNo)) {
            boolean flag = false;
            for(int i = 0; i < groupBusinessRelationDTOS.size(); i++) {
                if(subMerchantNo.equals(groupBusinessRelationDTOS.get(i).getCustomerNumber())){
                    flag = true;
                    subMerchantName = groupBusinessRelationDTOS.get(i).getFullname();
                    break;
                }
            }
            if(!flag) {
                throw new RuntimeException("请输入正确的子商户编号");
            }
        }
        mav.setViewName("subAccount/queryAccountHis");
        mav.addObject("subMerchantNo", null == subMerchantNo ? "": subMerchantNo);
        mav.addObject("subMerchantName", subMerchantName);
        return mav;

    }



    @RequestMapping(value = "/queryList")
    @ResponseBody
    public ResponseMessage queryList(
            AccountHistoryQueryParam accountHistoryQueryParam,
            @RequestParam(value = "pageSize", defaultValue = Costants.PAGE_SIZE_DEFAULT_VAL) int pageSize,
            @RequestParam(value = "pageNo", defaultValue = Costants.PAGE_NO_DEFAULT_VAL) int pageNo) {
        ResponseMessage resMsg = new ResponseMessage("success");
        LOGGER.info("查询子商户账务历史入参{}",JSON.toJSONString(accountHistoryQueryParam));
        AmountDirectionEnum directionEnum = null;
        try {
            if(!CheckUtils.isEmpty(accountHistoryQueryParam.getDirection())) {
                directionEnum = AmountDirectionEnum.valueOf(accountHistoryQueryParam.getDirection());
            }
            CheckParamUtils.checkQueryHistoryParam(accountHistoryQueryParam);
            if(CheckUtils.isEmpty(accountHistoryQueryParam.getSubMerchantNo())) {
                throw new RuntimeException("子商户编号是必填项");
            }
        } catch (Throwable e) {
            LOGGER.error("查询子商户账务参数异常", e);
            // 直接把异常信息返回
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
            return resMsg;
        }

        AccountHistoryQueryResp accountHistoryQueryResp = null;
        try {
            accountHistoryQueryResp = accountBalanceQueryService.accountHistoryQuery(Costants.REQUESTOR, accountHistoryQueryParam.getAccountType(), accountHistoryQueryParam.getSubMerchantNo(),
                    AccountGtypeEnum.G2_ACCOUNT, accountHistoryQueryParam.getCreateStartDate(),
                    accountHistoryQueryParam.getCreateEndDate(),pageNo, pageSize, directionEnum, accountHistoryQueryParam.getBizType());
            LOGGER.info("查询子商户账务历史返回的信息为 {}", JSON.toJSONString(accountHistoryQueryResp));

        } catch (Throwable e) {
            LOGGER.error("查询子商户账务历史异常",e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("查询子商户账务历史异常");
            return resMsg;
        }
        if (accountHistoryQueryResp != null && accountHistoryQueryResp.getHistoryMap() != null) {
            resMsg.put("totalCount", accountHistoryQueryResp.getTotalCount());
            List<AccountHistoryQueryResult> accountHistoryQueryResults = dealQueryHistoryResp(accountHistoryQueryResp, accountHistoryQueryParam);
            resMsg.put("dataList", accountHistoryQueryResults);
            resMsg.put("totalPageCount", accountHistoryQueryResp.getPageCount());
            resMsg.put("debitAmount", accountHistoryQueryResp.getDebitAmount());
            resMsg.put("creditAmount", accountHistoryQueryResp.getCreditAmount());
        }
        resMsg.put("pageNo", pageNo);
        resMsg.put("pageSize", pageSize);
        return resMsg;
    }

    /**
     * @Description: 子商户账户历史下载
     * <AUTHOR>
     * @date 2020-04-15 16:47
     * @param param:
     * @param request:
     * @param response:
     * @return void
     */
    @RequestMapping(value = "/download")
    public void downloadRecord(AccountHistoryQueryParam param, HttpServletRequest request, HttpServletResponse response) throws Exception{
        try{
            CheckParamUtils.checkDownloadParam(param);
            String subMerchantNo = param.getSubMerchantNo();
            String merchantName = getSignName(subMerchantNo);
            param.setCustomerNumber(subMerchantNo);
            StringBuilder desc = new StringBuilder();
            desc.append("子商户账务历史查询,").append(param.getCreateStartDateStr()).append(" 00：00：00").append("至").append(param.getCreateEndDateStr()).append(" 23:59:59").append("数据");
            new SubAccountHistoryDownloadService(getCurrentUser(),param,merchantName,desc.toString(),"子商户账务历史查询-",accountBalanceQueryService).download(request,response);
        }catch (Throwable ex){
            LOGGER.error("downloadRecord-error",ex);
            response.getWriter().write("<script type='text/javascript'>parent.mpAlert('"+ex.getMessage()+"')</script>");
        }
    }

    /**
     * @Description: 查询下级机构
     * <AUTHOR>
     * @date 2020-04-13 11:32
     * @return java.lang.Object
     */
    private List<GroupBusinessRelationDTO> getGroupBusinessRelationReqDTO() {
        GroupBusinessRelationReqDTO dtoP = new GroupBusinessRelationReqDTO();
        dtoP.setBusinessType(BusinessType.GROUP_BUSINESS);
        dtoP.setParentNo(getCurrentCustomerNumber());
        dtoP.setSystem(Costants.SYSTEM_CODE);
        dtoP.setUid(UniformConfigUtils.getSystemUidForReqCusCenter());
        dtoP.setReqTime(new Date());
        dtoP.setCharSet("UTF-8");
        GroupBusinessRelationRespDTO dto;
        try {
            dto = groupBusinessRelationFacade.queryChildrenMerchant(dtoP);
            LOGGER.info("下级账户信息查询下级关系返回参数{}",JSON.toJSONString(dto));
        }catch (Throwable e) {
            LOGGER.error("查询下级机构异常 ", e);
            throw new RuntimeException("查询下级机构异常");
        }
        if (dto == null || dto.getGroupBusinessRelationDTOList().isEmpty()) {
            throw new RuntimeException("查询下级机构异常");
        }
        return dto.getGroupBusinessRelationDTOList();
    }

    /**
     * 获取商户名称
     */
    private String getSignName(String merchantNo) {
        return merchantRemoteService.getMerchantName(merchantNo);
    }


    private List<AccountHistoryQueryResult> dealQueryHistoryResp(AccountHistoryQueryResp accountHistoryQueryResp, AccountHistoryQueryParam accountHistoryQueryParam) {
        List<AccountHistoryQueryResult> resultList = new ArrayList<AccountHistoryQueryResult>();
        String merchantName = getSignName(accountHistoryQueryParam.getSubMerchantNo());
        if (!CheckUtils.isEmpty(accountHistoryQueryResp) && !CheckUtils.isEmpty(accountHistoryQueryResp.getHistoryMap())) {
            List<Map<String, Object>> dataList = accountHistoryQueryResp.getHistoryMap();
            for (Map<String, Object> map : dataList) {
                AccountHistoryQueryResult result = new AccountHistoryQueryResult();

                result.setisMigrate(getCurrentUser().isMigrateMerchant()); //true:网银迁移用户，false：大算用户
                result.setSummary(map.get("SUMMARY") == null ? "" : map.get("SUMMARY").toString());

                String historyType = map.get("HISTORYTYPE") + "";
                result.setBiztype(historyType);
                result.setHistorytype(translateHistoryType(historyType));
                String createDate = map.get("CREATEDATE") + "";
                createDate = createDate.substring(0, createDate.lastIndexOf(".") > 0 ? createDate.lastIndexOf(".") : createDate.length());
                result.setCreatedateonly(createDate);
                result.setDesc(map.get("DESCRIPTION") == null ? "" : map.get("DESCRIPTION") + "");
                BigDecimal amount = new BigDecimal(map.get("REALAMOUNT").toString()).setScale(2, BigDecimal.ROUND_DOWN);
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    result.setDirection(AmountDirectionEnum.INCREMENT.toString());
                    result.setIncome(amount.abs());
                    result.setDirectionType("收入");
                    result.setAmount(amount.abs().toString());
                } else {
                    result.setDirection(AmountDirectionEnum.DECREASE.toString());
                    result.setOutcome(amount);
                    result.setDirectionType("支出");
                    result.setAmount(amount.toString());
                }
                result.setRequestId(map.get("REQUESTID") == null ? "" : map.get("REQUESTID") + ""); // 航旅定制 商户订单号
                // 手续费
                BigDecimal fee = new BigDecimal(map.get("FEE").toString()).setScale(2, BigDecimal.ROUND_DOWN);
                result.setFee(fee);
                result.setExtinfo("");
                result.setPostbalance(new BigDecimal(map.get("POSTBALANCE").toString()).setScale(2, BigDecimal.ROUND_DOWN));
                String trxdate = map.get("TRXDATE") + "";
                trxdate = trxdate.substring(0, trxdate.lastIndexOf(".") > 0 ? trxdate.lastIndexOf(".") : trxdate.length());
                result.setTrxdate(trxdate);
                //增加流水号字段
                result.setTrxid(map.get("trxid") == null ? "0" : map.get("trxid") + "");
                //增加系统字段
                result.setSys(System.currentTimeMillis() + "");
                //增加是否可结算状态
                result.setSettleable(map.get("settleable") + "");
                //增加是否已结算状态
                result.setSettleflag(map.get("settleflag") + "");
                result.setMerchantNo(accountHistoryQueryParam.getSubMerchantNo());
                result.setMerchantName(merchantName);
                resultList.add(result);
            }
        }
        return resultList;
    }

    /**
     * 翻译账户历史类型
     */
    private String translateHistoryType(String histroyType) {
        Map<String, String> type2Name = UniformConfigUtils.getHistoryType();
        String historyTypeStr = type2Name.get(histroyType);
        return CheckUtils.isEmpty(historyTypeStr) ? histroyType : historyTypeStr;
    }
}
