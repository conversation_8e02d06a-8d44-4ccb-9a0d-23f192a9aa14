package com.yeepay.g3.app.account.pay.mboss.controller.app;

import com.yeepay.g3.app.account.pay.mboss.controller.app.dto.model.AccountTotalBalanceModel;
import com.yeepay.g3.app.account.pay.mboss.dto.AccountQueryResult;
import com.yeepay.g3.app.account.pay.mboss.dto.BaseRespDTO;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.AccountManageInfoService;
import com.yeepay.g3.app.account.pay.mboss.utils.Costants;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BankAccountInfo;
import com.yeepay.g3.facade.unionaccount.manage.dto.response.BankGroupListResp;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.NumberFormat;

/**
 * 老板管账app 企业账户 提现
 *
 * @author: Mr.yin
 * @date: 2024/7/24  15:11
 */
@Controller
@Api(tags = "app-企业账户-账户查询")
@RequestMapping("/app/account")
public class AccountController extends BaseController {

    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Resource
    private AccountManageInfoService accountManageInfoService;

    @Resource
    private RemoteService remoteService;

    private static final String RMB_LOGO = "¥";
    private static final String ZERO_BALANCE = "0.00";

    /**
     * 提供给app展示 账户总览
     *银行账户+易宝账户
     * @return
     */
    @RequestMapping(value = "/totalBalance", method = RequestMethod.GET)
    @ApiOperation("app查询账户总余额")
    @ResponseBody
    /*这个权限交由APP的菜单功能ID配置了，控制了入口实现了*/
//    @RequiresPermissions(Costants.APP_BALANCE_SHOW)
    public BaseRespDTO<AccountTotalBalanceModel> totalBalance() {
        String merchantNo = getCurrentUser().getCustomerNumber();
        LOGGER.info("[app查询总余额] 商户={}", merchantNo);
        try {
            BigDecimal totalBalance = BigDecimal.ZERO;
            AccountQueryResult result = accountManageInfoService.accountQuery(merchantNo);
            BigDecimal totalBalanceAccount = result.getTotalBalance().setScale(2, BigDecimal.ROUND_HALF_UP);
            BankGroupListResp respDTO = remoteService.queryBankAccountList(merchantNo);
            if (CollectionUtils.isNotEmpty(respDTO.getBankAccountInfos())) {
                if (CollectionUtils.isNotEmpty(respDTO.getBankAccountInfos())) {
                    BigDecimal reduce = respDTO.getBankAccountInfos().stream().map(BankAccountInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalBalance = totalBalance.add(reduce);
                }
            }
            if (totalBalanceAccount != null) {
                totalBalance = totalBalance.add(totalBalanceAccount);
            }
            AccountTotalBalanceModel model = new AccountTotalBalanceModel();
            String balance = ZERO_BALANCE;
            if (totalBalance.compareTo(BigDecimal.ZERO) > 0) {
                NumberFormat numberFormat = NumberFormat.getInstance();
                numberFormat.setMaximumFractionDigits(2);
                numberFormat.setMinimumFractionDigits(2);
                balance = numberFormat.format(totalBalance);
            }

            LOGGER.info("[app查询总余额] 商户={} 返回总余额={}", merchantNo, balance);
            model.setTips(RMB_LOGO + balance);
            return BaseRespDTO.success(model);
        } catch (AccountPayException e) {
            LOGGER.info("app查询账户总余额 业务异常，currentCustomerNumber=" + merchantNo, e);
            return BaseRespDTO.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("app查询账户总余额 系统异常，currentCustomerNumber=" + merchantNo, e);
            return BaseRespDTO.fail(e.getMessage());
        }
    }

    public static void main(String[] args) {
        NumberFormat format = NumberFormat.getInstance();
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setMinimumFractionDigits(2);
        numberFormat.setGroupingUsed(true);
        String balance = numberFormat.format(new BigDecimal("*********"));
//        format.format(new BigDecimal("2345678.80"));
        System.out.println(balance);
    }


}
