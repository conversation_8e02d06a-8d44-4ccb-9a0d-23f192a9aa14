package com.yeepay.g3.app.account.pay.mboss.controller.middleground;

import com.google.common.collect.Lists;
import com.yeepay.g3.app.account.pay.mboss.dto.BankPayConfirmReqDTO;
import com.yeepay.g3.app.account.pay.mboss.dto.RechargeBankPayOrderInfo;
import com.yeepay.g3.app.account.pay.mboss.dto.RechargeBankPayOrderQueryParam;
import com.yeepay.g3.app.account.pay.mboss.enumtype.BankAccountBankCodeEnum;
import com.yeepay.g3.app.account.pay.mboss.enumtype.OrderStatusEnum;
import com.yeepay.g3.app.account.pay.mboss.exception.AccountPayException;
import com.yeepay.g3.app.account.pay.mboss.remote.RemoteService;
import com.yeepay.g3.app.account.pay.mboss.service.impl.BusinessCheckRemoteService;
import com.yeepay.g3.app.account.pay.mboss.utils.*;
import com.yeepay.g3.app.mp.controller.BaseController;
import com.yeepay.g3.app.newframe.response.ResponseMessage;
import com.yeepay.g3.facade.mp.dto.TradePasswordValidateDTO;
import com.yeepay.g3.facade.mp.enumtype.security.AuthenticationStatusEnum;
import com.yeepay.g3.facade.mp.shiro.ShiroUser;
import com.yeepay.g3.facade.unionaccount.recharge.dto.request.BankPayRequestDTO;
import com.yeepay.g3.facade.unionaccount.recharge.dto.request.RechargeRequestDTO;
import com.yeepay.g3.facade.unionaccount.recharge.dto.response.RechargeRespDTO;
import com.yeepay.g3.facade.unionaccount.recharge.enumtype.AccountTypeEnum;
import com.yeepay.g3.facade.unionaccount.recharge.enumtype.FeeTypeEnum;
import com.yeepay.g3.facade.unionaccount.recharge.enumtype.PayTypeEnum;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * description:
 *
 * <AUTHOR>
 * @since 2023/10/18 16:44
 */
@Controller
@RequestMapping("/rechargeBankPay")
@Api(tags = "充值银行扣款")
public class RechargeBankPayController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(RechargeBankPayController.class);

    @Resource
    private RemoteService remoteService;
    @Resource
    private BusinessCheckRemoteService businessCheckRemoteService;
    @Resource
    private QueryService bankPayRechargeService;

    /**
     * 订单管理
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequiresPermissions("***********")
    @RequestMapping("/orderManage")
    @ApiOperation(hidden = true, value="订单管理")
    public ModelAndView openProcess(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("bankAccount/orderManage");
        mv.addObject("UIWebRootUrl", WebPropertiesHolder.getUIWebRootUrl());
        LOGGER.info("订单管理页面菜单,{}", request.getAttribute("tabMenu"));
        return mv;
    }

    @RequestMapping(value = "/bankPayConfirm", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation("充值银行扣款确认接口")
    public ResponseMessage bankPayConfirm(@RequestBody BankPayConfirmReqDTO bankPayConfirmReqDTO) {
        LOGGER.info("[充值银行扣款]确认请求参数={}", bankPayConfirmReqDTO);
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            bankPayConfirmReqDTO.validateParam();
            ValidateUtils.judgeBigDecimal(bankPayConfirmReqDTO.getAmount());
            bankPayConfirmReqDTO.replaceBankCode();
            BigDecimal amount = new BigDecimal(bankPayConfirmReqDTO.getAmount());
            String currentCustomerNumber = getCurrentCustomerNumber();
            ShiroUser currentUser = getCurrentUser();
            // 校验密码
            validatePassword(bankPayConfirmReqDTO, currentCustomerNumber, currentUser);
            // 充值请求参数
            RechargeRequestDTO rechargeRequestDTO = assembleRequestDTO(bankPayConfirmReqDTO, amount, currentCustomerNumber, currentUser);
            RechargeRespDTO rechargeRespDTO = remoteService.initiateRecharge(rechargeRequestDTO);
            if ("UA00000".equals(rechargeRespDTO.getReturnCode())) {
                resMsg.put("orderNo", rechargeRespDTO.getOrderNo());
            } else {
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance(rechargeRespDTO.getReturnMsg());
            }
        } catch (YeepayBizException e) {
            LOGGER.error("充值银行扣款确认业务异常, cause by={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("充值银行扣款确认系统异常, cause by=", e);
            resMsg.setErrCode("9999");
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统调用异常，请稍后再试");
        }
        return resMsg;
    }

    /**
     * @Description: 查询银行扣款充值订单列表
     */
    @RequestMapping(value = "/queryBankPayOrderList", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询银行扣款充值订单列表")
    public ResponseMessage queryOrderList(@RequestParam(value = "orderNo",required = false) String orderNo,
                                          @RequestParam(value = "requestNo",required = false) String requestNo,
                                          @RequestParam(value = "status",required = false) String status,
                                          @RequestParam(value = "createStartDate",required = false) String createStartDate,
                                          @RequestParam(value = "createEndDate",required = false) String createEndDate,
                                          @RequestParam(value = "bankCode",required = false) String bankCode,
                                          @RequestParam(value = "pageNo",required = false) Integer pageNo,
                                          @RequestParam(value = "pageSize",required = false) Integer pageSize
                                        ) {
        ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
        try {
            RechargeBankPayOrderQueryParam rechargeBankPayOrderQueryParam = new RechargeBankPayOrderQueryParam();
            rechargeBankPayOrderQueryParam.setOrderNo(orderNo);
            rechargeBankPayOrderQueryParam.setRequestNo(requestNo);
            rechargeBankPayOrderQueryParam.setStatus(status);
            rechargeBankPayOrderQueryParam.setCreateStartDate(createStartDate);
            rechargeBankPayOrderQueryParam.setCreateEndDate(createEndDate);
            rechargeBankPayOrderQueryParam.setBankCode(bankCode);
            rechargeBankPayOrderQueryParam.setPageNo(pageNo);
            rechargeBankPayOrderQueryParam.setPageSize(pageSize);
            LOGGER.info("查询银行扣款充值订单列表, 请求参数{}", JSONUtils.toJsonString(rechargeBankPayOrderQueryParam));
            String currentCustomerNumber = getCurrentCustomerNumber();
            LOGGER.info("查询银行扣款充值订单列表,当前登录商编={}", currentCustomerNumber);
            if (rechargeBankPayOrderQueryParam.isEmptyCheck()) {
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("查询参数缺失，请核实");
            }
            if (!StringUtils.isEmpty(rechargeBankPayOrderQueryParam.getBankCode()) && !BankAccountBankCodeEnum.containBankCode(rechargeBankPayOrderQueryParam.getBankCode())) {
                throw AccountPayException.BOSS_BIZ_EXCEPTION.newInstance("查询参数银行编码不正确，请核实");
            }
            rechargeBankPayOrderQueryParam.replaceBankCode();
            String useStatus = rechargeBankPayOrderQueryParam.getStatus();
            if (OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.name().equals(useStatus)) {
                useStatus = String.join(",", OrderStatusEnum.PAY_SUCCESS.name(), OrderStatusEnum.ACCOUNTING.name(), OrderStatusEnum.ACCOUNTING_EXCEPTION.name());
            } else if (OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(useStatus)) {
                useStatus = OrderStatusEnum.ACCOUNTING_FAIL.name();
            }
            rechargeBankPayOrderQueryParam.setStatus(useStatus);
            this.queryBankPayOrderList(rechargeBankPayOrderQueryParam, resMsg);
            LOGGER.info("查询银行扣款充值订单列表,resMsg={}", JSONUtils.toJsonString(resMsg));
        } catch (YeepayBizException e) {
            LOGGER.error("查询银行扣款充值订单列表业务异常, cause by={}", e.getMessage());
            resMsg.setErrCode(e.getDefineCode());
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("查询银行扣款充值订单列表异常,e=", e);
            resMsg.setStatus(ResponseMessage.Status.ERROR);
            resMsg.setErrMsg("系统调用异常，请稍后再试");
        }
        return resMsg;
    }

    /**
     * 查询充值列表
     * @return
     */
    private void queryBankPayOrderList(RechargeBankPayOrderQueryParam rechargeBankPayOrderQueryParam, ResponseMessage resMsg) {
        Map<String, Object> queryMap = new HashMap<>(16);
        queryMap.put("customerNumber", getCurrentCustomerNumber());
        queryMap.put("orderNo", rechargeBankPayOrderQueryParam.getOrderNo());
        queryMap.put("requestNo", rechargeBankPayOrderQueryParam.getRequestNo());
        queryMap.put("status", rechargeBankPayOrderQueryParam.getStatus());
        queryMap.put("createStartDate", rechargeBankPayOrderQueryParam.getCreateStartDate());
        queryMap.put("createEndDate", rechargeBankPayOrderQueryParam.getCreateEndDate());
        List<String> bankCodeList = Lists.newArrayList();
        if(Objects.equals(rechargeBankPayOrderQueryParam.getBankCode(), BankAccountBankCodeEnum.XWB.name())){
            bankCodeList.add(BankAccountBankCodeEnum.XWB_Z.name());
        }
        if(!StringUtils.isEmpty(rechargeBankPayOrderQueryParam.getBankCode())){
            bankCodeList.add(rechargeBankPayOrderQueryParam.getBankCode());
        }
        queryMap.put("bankCode", bankCodeList);
        Integer pageNo = rechargeBankPayOrderQueryParam.getPageNo();
        Integer pageSize = rechargeBankPayOrderQueryParam.getPageSize();
        Integer startIndex = (pageNo - 1) * pageSize + 1;
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(startIndex);
        queryParam.setMaxSize(pageSize);
        queryParam.setDoSum(true);
        queryParam.setParams(queryMap);
        QueryResult queryResult = bankPayRechargeService.query("queryBankPayOrderList", queryParam);
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) queryResult.getData();
        List<RechargeBankPayOrderInfo> rechargeBankPayOrderInfos = Lists.newArrayList();
        for (Map<String, Object> resultMap : dataList) {
            RechargeBankPayOrderInfo rechargeBankPayOrderInfo = assembleOrderInfos(resultMap);
            rechargeBankPayOrderInfos.add(rechargeBankPayOrderInfo);
        }
        resMsg.put("dataList", rechargeBankPayOrderInfos);
        resMsg.put("pageNo", rechargeBankPayOrderQueryParam.getPageNo());
        resMsg.put("pageSize", rechargeBankPayOrderQueryParam.getPageSize());
        resMsg.put("totalCount", queryResult.getTotalCount());
    }

    /**
     * 组装列表响应
     * @param resultMap
     * @return
     */
    private RechargeBankPayOrderInfo assembleOrderInfos(Map<String, Object> resultMap) {
        LOGGER.info("resultMap={}", JSONUtils.toJsonString(resultMap));
        RechargeBankPayOrderInfo rechargeBankPayOrderInfo = new RechargeBankPayOrderInfo();
        Date createTime = (Date) resultMap.get("create_time");
        if (createTime != null) {
            String createTimeStr = DateUtil.getDateString(createTime, DateUtil.DATE_TIME_FORMATTER);
            rechargeBankPayOrderInfo.setCreateTime(createTimeStr);
        }
        rechargeBankPayOrderInfo.setRequestNo((String) resultMap.get("request_no"));
        rechargeBankPayOrderInfo.setOrderNo((String) resultMap.get("order_no"));
        String bankId = (String) resultMap.get("bank_id");
        rechargeBankPayOrderInfo.setBankName(BankAccountBankCodeEnum.getDescriptionByBankCode(bankId));
        BigDecimal applyAmount = (BigDecimal) resultMap.get("apply_amount");
        if (applyAmount != null) {
            rechargeBankPayOrderInfo.setApplyAmount(applyAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        }
        BigDecimal fee = (BigDecimal) resultMap.get("fee");
        String feeUndertakerMerchantNo = (String) resultMap.get("fee_undertaker_merchant_no");
        String merchantNo = (String) resultMap.get("merchant_no");
        // 判断是不是自己承担
        boolean feeUndertakerOneself = (StringUtils.isEmpty(feeUndertakerMerchantNo) ? StringUtils.isEmpty(merchantNo) : feeUndertakerMerchantNo.equals(merchantNo));
        if (fee != null && feeUndertakerOneself) {
            rechargeBankPayOrderInfo.setFee(fee.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        } else {
            rechargeBankPayOrderInfo.setFee("无");
        }
        String orderStatus = (String) resultMap.get("status");
        rechargeBankPayOrderInfo.setStatus(OrderStatusEnum.valueOf(orderStatus).getDesc());
        if (OrderStatusEnum.PAY_SUCCESS.name().equals(orderStatus) || OrderStatusEnum.ACCOUNTING.name().equals(orderStatus) || OrderStatusEnum.ACCOUNTING_EXCEPTION.name().equals(orderStatus)) {
            rechargeBankPayOrderInfo.setStatus(OrderStatusEnum.ACCOUNTIN_PAY_SUCCESS.getDesc());
        }else if(OrderStatusEnum.ACCOUNTING_FAIL.name().equals(orderStatus)){
            rechargeBankPayOrderInfo.setStatus(OrderStatusEnum.ACCOUNTING_EXCEPTION.getDesc());
        }
        Date finishTime = (Date) resultMap.get("finish_time");
        if (finishTime != null) {
            String finishTimeStr = DateUtil.getDateString(finishTime, DateUtil.DATE_TIME_FORMATTER);
            rechargeBankPayOrderInfo.setFinishTime(finishTimeStr);
        }
        rechargeBankPayOrderInfo.setReturnMsg((String) resultMap.get("return_msg"));
        return rechargeBankPayOrderInfo;
    }

    private RechargeRequestDTO assembleRequestDTO(BankPayConfirmReqDTO bankPayConfirmReqDTO, BigDecimal amount, String currentCustomerNumber, ShiroUser currentUser) {
        RechargeRequestDTO rechargeRequestDTO = new RechargeRequestDTO();
        Long requestNo = SnowflakeIdFactory.generateId();
        rechargeRequestDTO.setRequestNo("BZCZ" + requestNo);
        rechargeRequestDTO.setRechargeAmount(amount);
        rechargeRequestDTO.setAccountType(AccountTypeEnum.FUND_ACCOUNT);
        rechargeRequestDTO.setPayType(PayTypeEnum.BANK_PAY);
        rechargeRequestDTO.setOperator(currentUser.getLoginName());
        rechargeRequestDTO.setClientIp(NetUtils.getIpAddress());
        String marketProduct = businessCheckRemoteService.queryMarketProduct(currentCustomerNumber);
        rechargeRequestDTO.setMarketProductCode(marketProduct);
        rechargeRequestDTO.setInitiateMerchantNo(currentCustomerNumber);
        rechargeRequestDTO.setParentMerchantNo(currentCustomerNumber);
        rechargeRequestDTO.setMerchantNo(currentCustomerNumber);

        //内外扣手续费类型
        if (!StringUtils.isEmpty(bankPayConfirmReqDTO.getFeeType())) {
            FeeTypeEnum feeType = FeeTypeEnum.getFeeTypeEnumByName(bankPayConfirmReqDTO.getFeeType());
            PreCheck.checkArgument(Objects.nonNull(feeType), "手续费类型不合法");
            rechargeRequestDTO.setFeeType(feeType);
        }

        BankPayRequestDTO bankPayRequestDTO = new BankPayRequestDTO();
        bankPayRequestDTO.setBankCode(bankPayConfirmReqDTO.getBankCode());
        bankPayRequestDTO.setBankAccountNo(bankPayConfirmReqDTO.getBankAccountNo());
        rechargeRequestDTO.setBankPayRequestDTO(bankPayRequestDTO);

        return rechargeRequestDTO;
    }

    private void validatePassword(BankPayConfirmReqDTO bankPayConfirmReqDTO, String currentCustomerNumber, ShiroUser currentUser) {
        String loginName = currentUser.getLoginName();
        // 交易密码需要解密
        String userTradePassword = BACRsaUtil.privateDecrypt(bankPayConfirmReqDTO.getPassword(), ConfigUtils.getPrivateKey());
        LOGGER.info("当前商户merchantNo={},loginName={}", currentCustomerNumber, loginName);
        TradePasswordValidateDTO tradePasswordValidateDTO = remoteService.queryTradePasswordValidateResult(loginName, userTradePassword);
        if (tradePasswordValidateDTO.getIsTradeValidate() != null && tradePasswordValidateDTO.getIsTradeValidate()) {
            LOGGER.info("loginName={},交易密码验证通过", loginName);
            return;
        } else {
            // 判断是否有密码
            if (tradePasswordValidateDTO.getExistTradeValidate() != null && !tradePasswordValidateDTO.getExistTradeValidate()) {
                throw AccountPayException.TRADE_PASSWORD_NOT_EXIST.newInstance("交易密码未设置");
            }
            // 判断是否被冻结
            if (AuthenticationStatusEnum.FORBID == tradePasswordValidateDTO.getAuthenticationStatusEnum()) {
                long minute = tradePasswordValidateDTO.getFrezonSecond() / 60;
                long second = tradePasswordValidateDTO.getFrezonSecond() % 60;
                long showMinute = second > 0 ? minute + 1 : minute;
                throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("交易密码错误，请于" + (showMinute == 0 ? 1 : showMinute) + "分钟后重试");
            }
            throw AccountPayException.CHECK_USER_PASSWORD_ERROR.newInstance("交易密码错误");
        }
    }

}
