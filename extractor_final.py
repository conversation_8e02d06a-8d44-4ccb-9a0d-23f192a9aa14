#!/usr/bin/env python3
"""
Java 代码上下文提取器 - 通用版
从 diff 中提取完整的代码上下文，纯粹的上下文组装工具
不进行代码分析，只负责收集和组织相关代码

支持功能:
- 指定仓库根目录读取源文件
- 支持从文件读取或直接传入 diff 内容
- 提取被修改方法的完整定义
- 查找调用关系（上游调用者和下游被调用者）
- 提取相关实体类和 DTO
- 支持多层架构代码分析
"""

import re
import subprocess
import os
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CodeContext:
    """代码上下文信息"""
    file_path: str
    class_name: str
    method_name: str
    code_snippet: str
    context_type: str
    relevance_reason: str
    start_line: int
    end_line: int
    layer: str

class JavaContextExtractor:
    def __init__(self, debug=False, repo_root=None):
        self.debug = debug
        self.repo_root = os.path.abspath(repo_root or '.')  # 转换为绝对路径
        self.contexts: List[CodeContext] = []
        self.file_cache: Dict[str, List[str]] = {}

        # 验证仓库根目录
        self._validate_repo_root()

    def _validate_repo_root(self):
        """验证仓库根目录的有效性"""
        if not os.path.exists(self.repo_root):
            raise ValueError(f"仓库根目录不存在: {self.repo_root}")

        if not os.path.isdir(self.repo_root):
            raise ValueError(f"仓库根目录不是一个目录: {self.repo_root}")

        self.debug_print(f"使用仓库根目录: {self.repo_root}")

    def debug_print(self, message: str):
        """调试输出"""
        if self.debug:
            print(f"[DEBUG] {message}")

    def get_cached_file_content(self, file_path: str) -> List[str]:
        """获取缓存的文件内容"""
        if file_path not in self.file_cache:
            try:
                # 如果文件路径不是绝对路径，则相对于仓库根目录
                if not os.path.isabs(file_path):
                    full_path = os.path.join(self.repo_root, file_path)
                else:
                    full_path = file_path

                with open(full_path, 'r', encoding='utf-8') as f:
                    self.file_cache[file_path] = f.readlines()
            except Exception as e:
                self.debug_print(f"读取文件失败 {file_path}: {e}")
                return []
        return self.file_cache[file_path]

    def determine_layer(self, file_path: str, class_name: str) -> str:
        """确定代码层级"""
        if 'controller' in file_path.lower() or 'Controller' in class_name:
            return 'controller'
        elif 'service' in file_path.lower() or 'Service' in class_name:
            return 'service'
        elif 'facade' in file_path.lower() or 'Facade' in class_name:
            return 'facade'
        elif 'dao' in file_path.lower() or 'Dao' in class_name or 'Repository' in class_name:
            return 'dao'
        elif 'entity' in file_path.lower() or 'Entity' in class_name or 'dto' in file_path.lower():
            return 'entity'
        else:
            return 'util'

    def extract_complete_method(self, file_path: str, method_name: str, start_hint_line: int = 0) -> Optional[CodeContext]:
        """提取完整的方法定义"""
        self.debug_print(f"提取完整方法: {file_path} -> {method_name}")

        # 如果文件路径不是绝对路径，则相对于仓库根目录
        if not os.path.isabs(file_path):
            full_path = os.path.join(self.repo_root, file_path)
        else:
            full_path = file_path

        if not os.path.exists(full_path):
            self.debug_print(f"文件不存在: {full_path}")
            return None

        lines = self.get_cached_file_content(file_path)
        if not lines:
            return None

        class_name = os.path.basename(file_path).replace('.java', '')

        # 查找方法定义 - 支持多行方法签名
        method_start = None

        for i, line in enumerate(lines):
            if method_name in line:
                # 检查当前行或前几行是否有访问修饰符
                found_modifier = False
                for j in range(max(0, i-3), i+1):
                    if j < len(lines) and ('public' in lines[j] or 'private' in lines[j] or 'protected' in lines[j]):
                        found_modifier = True
                        method_start = j
                        break

                if found_modifier:
                    self.debug_print(f"找到方法定义: {method_name} (第 {method_start+1} 行)")
                    break

        if method_start is None:
            self.debug_print(f"未找到方法定义: {method_name}")
            return None

        # 向前查找注释和注解
        comment_start = method_start
        for i in range(method_start - 1, max(-1, method_start - 20), -1):
            line = lines[i].strip()
            if line.startswith('/**') or line.startswith('//') or line.startswith('*') or line.startswith('@'):
                comment_start = i
            elif line and not line.startswith('*'):
                break

        # 向后查找方法结束
        brace_count = 0
        method_end = method_start
        found_opening_brace = False

        for i in range(method_start, len(lines)):
            line = lines[i]

            open_braces = line.count('{')
            close_braces = line.count('}')

            if open_braces > 0:
                found_opening_brace = True

            brace_count += open_braces - close_braces

            if found_opening_brace and brace_count == 0:
                method_end = i
                break

        # 提取方法代码
        method_lines = []
        for i in range(comment_start, method_end + 1):
            method_lines.append(f"{i+1:4d}: {lines[i].rstrip()}")

        layer = self.determine_layer(file_path, class_name)

        return CodeContext(
            file_path=file_path,
            class_name=class_name,
            method_name=method_name,
            code_snippet='\n'.join(method_lines),
            context_type='modified',
            relevance_reason=f"包含代码变更的方法",
            start_line=comment_start + 1,
            end_line=method_end + 1,
            layer=layer
        )

    def find_method_callers(self, method_name: str, class_name: str = None) -> List[Tuple[str, int, str]]:
        """查找方法的调用者"""
        self.debug_print(f"查找方法调用者: {method_name}")

        callers = []

        # 构建搜索模式
        patterns = [
            f"{method_name}\\s*\\(",
            f"\\.{method_name}\\s*\\("
        ]

        if class_name:
            patterns.append(f"{class_name}\\.{method_name}\\s*\\(")

        try:
            for pattern in patterns:
                cmd = ['git', 'grep', '-n', '-E', pattern, '--', '**/*.java']
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.repo_root)

                if result.returncode == 0:
                    for line in result.stdout.strip().split('\n'):
                        if ':' in line:
                            parts = line.split(':', 2)
                            if len(parts) >= 3:
                                file_path = parts[0]
                                line_num = int(parts[1])
                                code_line = parts[2]
                                callers.append((file_path, line_num, code_line))

        except Exception as e:
            self.debug_print(f"查找调用者失败: {e}")

        self.debug_print(f"找到 {len(callers)} 个调用者")
        return callers

    def parse_diff_content(self, diff_content: str) -> Tuple[str, str, int]:
        """解析 diff 内容，返回文件路径、类名和变更行号"""
        self.debug_print("开始解析 diff 内容")

        lines = diff_content.strip().split('\n')
        current_file = None
        current_class = None
        change_line = 0

        for line in lines:
            line = line.strip()

            # 解析标准 Git diff 格式
            if line.startswith('diff --git'):
                match = re.search(r'b/(.+\.java)', line)
                if match:
                    current_file = match.group(1)
                    current_class = os.path.basename(current_file).replace('.java', '')
                    self.debug_print(f"找到变更文件: {current_file}, 类名: {current_class}")
                continue

            # 解析自定义格式: 文件{路径}的变更内容为: @@ ...
            if line.startswith('文件') and '的变更内容为:' in line:
                # 提取文件路径
                match = re.search(r'文件(.+?)的变更内容为:', line)
                if match:
                    current_file = match.group(1)
                    current_class = os.path.basename(current_file).replace('.java', '')
                    self.debug_print(f"找到变更文件: {current_file}, 类名: {current_class}")

                # 提取变更行号
                hunk_match = re.search(r'@@\s*-\d+,?\d*\s*\+(\d+),?\d*\s*@@', line)
                if hunk_match:
                    change_line = int(hunk_match.group(1))
                    self.debug_print(f"找到变更起始行号: {change_line}")

                # 如果找到了文件和行号，返回第一个匹配的结果
                if current_file and change_line > 0:
                    return current_file, current_class, change_line
                continue

            # 解析标准 hunk 头部
            if line.startswith('@@'):
                match = re.search(r'@@\s*-\d+,?\d*\s*\+(\d+),?\d*\s*@@', line)
                if match:
                    change_line = int(match.group(1))
                    self.debug_print(f"找到变更起始行号: {change_line}")
                continue

        return current_file, current_class, change_line

    def find_method_at_line(self, file_path: str, target_line: int) -> Optional[str]:
        """查找指定行所在的方法名"""
        lines = self.get_cached_file_content(file_path)
        if not lines:
            return None

        # 从目标行向上查找方法定义
        for i in range(min(target_line - 1, len(lines) - 1), max(-1, target_line - 100), -1):
            line = lines[i].strip()

            # 查找方法定义模式 - 支持多行方法签名
            method_pattern = r'(public|private|protected).*?(\w+)\s*\([^)]*\)\s*\{'
            match = re.search(method_pattern, line)
            if match:
                return match.group(2)

            # 检查是否是多行方法签名的开始
            if ('public' in line or 'private' in line or 'protected' in line) and '(' in line:
                # 向前查找几行，寻找方法名
                for j in range(i, min(i + 5, len(lines))):
                    if j < len(lines):
                        combined_line = ' '.join(lines[k].strip() for k in range(i, j + 1))
                        method_pattern = r'(public|private|protected).*?(\w+)\s*\([^)]*\)'
                        match = re.search(method_pattern, combined_line)
                        if match:
                            return match.group(2)

        return None

    def parse_all_files_from_diff(self, diff_content: str) -> List[Tuple[str, str, int]]:
        """解析 diff 中的所有文件变更"""
        self.debug_print("解析 diff 中的所有文件变更")

        files = []
        lines = diff_content.strip().split('\n')

        for line in lines:
            line = line.strip()

            # 解析自定义格式: 文件{路径}的变更内容为: @@ ...
            if line.startswith('文件') and '的变更内容为:' in line:
                # 提取文件路径
                match = re.search(r'文件(.+?)的变更内容为:', line)
                if match:
                    file_path = match.group(1)
                    class_name = os.path.basename(file_path).replace('.java', '')

                    # 提取变更行号
                    hunk_match = re.search(r'@@\s*-\d+,?\d*\s*\+(\d+),?\d*\s*@@', line)
                    change_line = int(hunk_match.group(1)) if hunk_match else 1

                    files.append((file_path, class_name, change_line))
                    self.debug_print(f"找到变更文件: {file_path}, 类名: {class_name}, 行号: {change_line}")

        return files

    def extract_all_contexts(self, diff_content: str) -> str:
        """提取所有相关的代码上下文"""
        self.debug_print("开始提取完整的代码上下文")

        # 1. 解析 diff 中的所有文件
        changed_files = self.parse_all_files_from_diff(diff_content)

        if not changed_files:
            return self.format_no_context_result(diff_content, "无法解析 diff 文件路径")

        # 2. 处理每个变更的文件
        for file_path, class_name, change_line in changed_files:
            self.debug_print(f"处理文件: {file_path}")

            # 检查文件是否存在
            full_path = os.path.join(self.repo_root, file_path) if not os.path.isabs(file_path) else file_path
            if not os.path.exists(full_path):
                self.debug_print(f"文件不存在，跳过: {full_path}")
                continue

            # 找到被修改的方法
            modified_method = self.find_method_at_line(file_path, change_line)
            if not modified_method:
                self.debug_print(f"无法找到第 {change_line} 行所在的方法，跳过")
                continue

            self.debug_print(f"找到被修改的方法: {modified_method}")

            # 提取被修改方法的完整定义
            modified_context = self.extract_complete_method(file_path, modified_method)
            if modified_context:
                modified_context.relevance_reason = f"包含代码变更的方法 (第 {change_line} 行附近)"
                self.contexts.append(modified_context)

                # 查找调用该方法的上层代码 (Controller/Service)
                self.debug_print(f"查找 {modified_method} 的上层调用者...")
                callers = self.find_method_callers(modified_method, class_name)

                for caller_file, caller_line, _ in callers[:3]:  # 限制数量
                    caller_method = self.find_method_at_line(caller_file, caller_line)
                    if caller_method:
                        caller_context = self.extract_complete_method(caller_file, caller_method)
                        if caller_context:
                            caller_context.context_type = 'caller'
                            caller_context.relevance_reason = f"调用了被修改的方法 {modified_method}()"
                            self.contexts.append(caller_context)

        # 3. 格式化输出
        if not self.contexts:
            return self.format_no_context_result(diff_content, "所有变更文件都不存在或无法找到相关方法")

        return self.format_contexts(diff_content)

    def format_contexts(self, original_diff: str) -> str:
        """格式化上下文输出"""
        output = []

        # 原始 diff
        output.append("# 原始 Diff")
        output.append("")
        output.append("```diff")
        output.append(original_diff)
        output.append("```")
        output.append("")

        if not self.contexts:
            return self.format_no_context_result(original_diff, "未找到相关代码上下文")

        # 详细代码上下文
        output.append("# 📝 详细代码上下文")
        output.append("")

        # 按层级分组
        layers = {
            'controller': [],
            'service': [],
            'facade': [],
            'dao': [],
            'entity': [],
            'util': []
        }

        for ctx in self.contexts:
            layers[ctx.layer].append(ctx)

        layer_names = {
            'controller': '🎮 Controller 层',
            'service': '⚙️ Service 层',
            'facade': '🔌 Facade 层',
            'dao': '💾 DAO 层',
            'entity': '📦 Entity/DTO 层',
            'util': '🛠️ Util 层'
        }

        section_count = 1
        for layer_key, layer_contexts in layers.items():
            if layer_contexts:
                output.append(f"## {layer_names[layer_key]}")
                output.append("")

                for ctx in layer_contexts:
                    output.append(f"### {section_count}. {ctx.class_name}.{ctx.method_name}()")
                    output.append("")
                    output.append(f"**文件路径**: `{ctx.file_path}`")
                    output.append(f"**相关性**: {ctx.relevance_reason}")
                    output.append(f"**代码位置**: 第 {ctx.start_line}-{ctx.end_line} 行")
                    output.append("")

                    output.append("```java")
                    output.append(ctx.code_snippet)
                    output.append("```")

                    output.append("")
                    section_count += 1

        return '\n'.join(output)

    def format_no_context_result(self, original_diff: str, reason: str) -> str:
        """格式化无上下文结果"""
        output = []

        output.append("# 原始 Diff")
        output.append("")
        output.append("```diff")
        output.append(original_diff)
        output.append("```")
        output.append("")

        output.append("## ⚠️ 未找到相关上下文")
        output.append("")
        output.append(f"**原因**: {reason}")
        output.append("")

        return '\n'.join(output)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Java Final Context Extractor')

    # 创建互斥组：要么指定文件，要么直接提供 diff 内容
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--file', help='Diff file path')
    input_group.add_argument('--diff', help='Diff content as string')

    parser.add_argument('--output', help='Output file path')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    parser.add_argument('--repo-root', help='Repository root directory (default: current directory)', default='.')

    args = parser.parse_args()

    # 获取 diff 内容
    diff_content = None

    if args.file:
        # 从文件读取 diff 内容
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                diff_content = f.read()
        except FileNotFoundError:
            print(f"❌ 错误: 文件 {args.file} 不存在")
            return
        except Exception as e:
            print(f"❌ 读取文件错误: {e}")
            return
    elif args.diff:
        # 直接使用提供的 diff 内容
        diff_content = args.diff
    else:
        print("❌ 错误: 必须指定 --file 或 --diff 参数")
        return

    if not diff_content or not diff_content.strip():
        print("❌ 错误: diff 内容为空")
        return

    # 执行上下文提取
    extractor = JavaContextExtractor(debug=args.debug, repo_root=args.repo_root)
    context = extractor.extract_all_contexts(diff_content)

    # 输出结果
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(context)
            print(f"✅ 代码上下文已保存到 {args.output}")

        except Exception as e:
            print(f"❌ 保存文件错误: {e}")
    else:
        print(context)

if __name__ == "__main__":
    main()