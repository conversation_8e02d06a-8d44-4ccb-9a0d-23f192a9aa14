#!/usr/bin/env node
"""
Java Code Context Extractor - Generic Version
Extracts complete code context from a diff, acting as a pure context assembly tool.
It does not perform code analysis, only collects and organizes related code.

Supported features:
- Read source files from a specified repository root.
- Support reading from a file or directly passing diff content.
- Extract the complete definition of modified methods.
- Find call relationships (upstream callers and downstream callees).
- Extract related entity classes and DTOs.
- Support multi-layer architecture code analysis.
"""

import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

interface CodeContext {
    filePath: string;
    className: string;
    methodName: string;
    codeSnippet: string;
    contextType: string;
    relevanceReason: string;
    startLine: number;
    endLine: number;
    layer: string;
}

class JavaContextExtractor {
    private debug: boolean;
    private repoRoot: string;
    private contexts: CodeContext[] = [];
    private fileCache: { [key: string]: string[] } = {};

    constructor(debug = false, repoRoot: string | null = null) {
        this.debug = debug;
        this.repoRoot = path.resolve(repoRoot || '.');
        this.validateRepoRoot();
    }

    private validateRepoRoot() {
        if (!fs.existsSync(this.repoRoot)) {
            throw new Error(`Repository root does not exist: ${this.repoRoot}`);
        }
        if (!fs.statSync(this.repoRoot).isDirectory()) {
            throw new Error(`Repository root is not a directory: ${this.repoRoot}`);
        }
        this.debugPrint(`Using repository root: ${this.repoRoot}`);
    }

    private debugPrint(message: string) {
        if (this.debug) {
            console.log(`[DEBUG] ${message}`);
        }
    }

    private getCachedFileContent(filePath: string): string[] {
        if (!this.fileCache[filePath]) {
            try {
                const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.repoRoot, filePath);
                this.fileCache[filePath] = fs.readFileSync(fullPath, 'utf-8').split('\n');
            } catch (e: any) {
                this.debugPrint(`Failed to read file ${filePath}: ${e.message}`);
                return [];
            }
        }
        return this.fileCache[filePath];
    }

    private determineLayer(filePath: string, className: string): string {
        const lowerFilePath = filePath.toLowerCase();
        if (lowerFilePath.includes('controller') || className.includes('Controller')) {
            return 'controller';
        } else if (lowerFilePath.includes('service') || className.includes('Service')) {
            return 'service';
        } else if (lowerFilePath.includes('facade') || className.includes('Facade')) {
            return 'facade';
        } else if (lowerFilePath.includes('dao') || className.includes('Dao') || className.includes('Repository')) {
            return 'dao';
        } else if (lowerFilePath.includes('entity') || className.includes('Entity') || lowerFilePath.includes('dto')) {
            return 'entity';
        } else {
            return 'util';
        }
    }

    private extractCompleteMethod(filePath: string, methodName: string, startHintLine = 0): CodeContext | null {
        this.debugPrint(`Extracting complete method: ${filePath} -> ${methodName}`);
        const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.repoRoot, filePath);

        if (!fs.existsSync(fullPath)) {
            this.debugPrint(`File does not exist: ${fullPath}`);
            return null;
        }

        const lines = this.getCachedFileContent(filePath);
        if (!lines.length) {
            return null;
        }

        const className = path.basename(filePath, '.java');
        let methodStart: number | null = null;

        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(methodName)) {
                let foundModifier = false;
                for (let j = Math.max(0, i - 3); j <= i; j++) {
                    if (j < lines.length && (lines[j].includes('public') || lines[j].includes('private') || lines[j].includes('protected'))) {
                        foundModifier = true;
                        methodStart = j;
                        break;
                    }
                }
                if (foundModifier) {
                    this.debugPrint(`Found method definition: ${methodName} (line ${methodStart + 1})`);
                    break;
                }
            }
        }

        if (methodStart === null) {
            this.debugPrint(`Method definition not found: ${methodName}`);
            return null;
        }

        let commentStart = methodStart;
        for (let i = methodStart - 1; i >= Math.max(-1, methodStart - 20); i--) {
            const line = lines[i].trim();
            if (line.startsWith('/**') || line.startsWith('//') || line.startsWith('*') || line.startsWith('@')) {
                commentStart = i;
            } else if (line && !line.startsWith('*')) {
                break;
            }
        }

        let braceCount = 0;
        let methodEnd = methodStart;
        let foundOpeningBrace = false;

        for (let i = methodStart; i < lines.length; i++) {
            const line = lines[i];
            const openBraces = (line.match(/\{/g) || []).length;
            const closeBraces = (line.match(/\}/g) || []).length;

            if (openBraces > 0) {
                foundOpeningBrace = true;
            }

            braceCount += openBraces - closeBraces;

            if (foundOpeningBrace && braceCount === 0) {
                methodEnd = i;
                break;
            }
        }

        const methodLines = [];
        for (let i = commentStart; i <= methodEnd; i++) {
            methodLines.push(`${(i + 1).toString().padStart(4, ' ')}: ${lines[i].trimEnd()}`);
        }

        const layer = this.determineLayer(filePath, className);

        return {
            filePath,
            className,
            methodName,
            codeSnippet: methodLines.join('\n'),
            contextType: 'modified',
            relevanceReason: 'Method containing code changes',
            startLine: commentStart + 1,
            endLine: methodEnd + 1,
            layer,
        };
    }

    private async findMethodCallers(methodName: string, className?: string): Promise<[string, number, string][]> {
        this.debugPrint(`Finding method callers: ${methodName}`);
        const callers: [string, number, string][] = [];
        const patterns = [
            `${methodName}\\s*\\(`, 
            `\\.${methodName}\\s*\\(`, 
        ];

        if (className) {
            patterns.push(`${className}\\.${methodName}\\s*\\(`);
        }

        for (const pattern of patterns) {
            const cmd = `git grep -n -E "${pattern}" -- '**/*.java'`;
            try {
                const { stdout } = await new Promise<{ stdout: string; stderr: string }>((resolve, reject) => {
                    exec(cmd, { cwd: this.repoRoot }, (error, stdout, stderr) => {
                        if (error && error.code !== 1) { // git grep returns 1 if no matches are found
                            reject(error);
                        } else {
                            resolve({ stdout, stderr });
                        }
                    });
                });

                if (stdout) {
                    for (const line of stdout.trim().split('\n')) {
                        if (line.includes(':')) {
                            const parts = line.split(':', 3);
                            if (parts.length >= 3) {
                                const filePath = parts[0];
                                const lineNum = parseInt(parts[1], 10);
                                const codeLine = parts[2];
                                callers.push([filePath, lineNum, codeLine]);
                            }
                        }
                    }
                }
            } catch (e: any) {
                this.debugPrint(`Failed to find callers: ${e.message}`);
            }
        }

        this.debugPrint(`Found ${callers.length} callers`);
        return callers;
    }

    private parseDiffContent(diffContent: string): [string | null, string | null, number] {
        this.debugPrint("Parsing diff content");
        const lines = diffContent.trim().split('\n');
        let currentFile: string | null = null;
        let currentClass: string | null = null;
        let changeLine = 0;

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine.startsWith('diff --git')) {
                const match = /b\/(.+\.java)/.exec(trimmedLine);
                if (match) {
                    currentFile = match[1];
                    currentClass = path.basename(currentFile, '.java');
                    this.debugPrint(`Found changed file: ${currentFile}, class: ${currentClass}`);
                }
                continue;
            }

            if (trimmedLine.startsWith('文件') && trimmedLine.includes('的变更内容为:')) {
                const fileMatch = /文件(.+?)的变更内容为:/.exec(trimmedLine);
                if (fileMatch) {
                    currentFile = fileMatch[1];
                    currentClass = path.basename(currentFile, '.java');
                    this.debugPrint(`Found changed file: ${currentFile}, class: ${currentClass}`);
                }

                const hunkMatch = /@@\s*-\d+,?\d*\s*\+(\d+),?\d*\s*@@/.exec(trimmedLine);
                if (hunkMatch) {
                    changeLine = parseInt(hunkMatch[1], 10);
                    this.debugPrint(`Found change start line: ${changeLine}`);
                }

                if (currentFile && changeLine > 0) {
                    return [currentFile, currentClass, changeLine];
                }
                continue;
            }

            if (trimmedLine.startsWith('@@')) {
                const match = /@@\s*-\d+,?\d*\s*\+(\d+),?\d*\s*@@/.exec(trimmedLine);
                if (match) {
                    changeLine = parseInt(match[1], 10);
                    this.debugPrint(`Found change start line: ${changeLine}`);
                }
                continue;
            }
        }
        return [currentFile, currentClass, changeLine];
    }

    private findMethodAtLine(filePath: string, targetLine: number): string | null {
        const lines = this.getCachedFileContent(filePath);
        if (!lines.length) {
            return null;
        }

        for (let i = Math.min(targetLine - 1, lines.length - 1); i >= Math.max(-1, targetLine - 100); i--) {
            const line = lines[i].trim();
            const methodPattern = /(public|private|protected).*?(\w+)\s*\([^)]*\)\s*\{/;
            const match = methodPattern.exec(line);
            if (match) {
                return match[2];
            }

            if ((line.includes('public') || line.includes('private') || line.includes('protected')) && line.includes('(')) {
                for (let j = i; j < Math.min(i + 5, lines.length); j++) {
                    if (j < lines.length) {
                        const combinedLine = lines.slice(i, j + 1).map(l => l.trim()).join(' ');
                        const multiLinePattern = /(public|private|protected).*?(\w+)\s*\([^)]*\)/;
                        const multiLineMatch = multiLinePattern.exec(combinedLine);
                        if (multiLineMatch) {
                            return multiLineMatch[2];
                        }
                    }
                }
            }
        }
        return null;
    }

    private parseAllFilesFromDiff(diffContent: string): [string, string, number][] {
        this.debugPrint("Parsing all file changes from diff");
        const files: [string, string, number][] = [];
        const lines = diffContent.trim().split('\n');

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine.startsWith('文件') && trimmedLine.includes('的变更内容为:')) {
                const fileMatch = /文件(.+?)的变更内容为:/.exec(trimmedLine);
                if (fileMatch) {
                    const filePath = fileMatch[1];
                    const className = path.basename(filePath, '.java');
                    const hunkMatch = /@@\s*-\d+,?\d*\s*\+(\d+),?\d*\s*@@/.exec(trimmedLine);
                    const changeLine = hunkMatch ? parseInt(hunkMatch[1], 10) : 1;
                    files.push([filePath, className, changeLine]);
                    this.debugPrint(`Found changed file: ${filePath}, class: ${className}, line: ${changeLine}`);
                }
            }
        }
        return files;
    }

    public async extractAllContexts(diffContent: string): Promise<string> {
        this.debugPrint("Starting to extract complete code context");
        const changedFiles = this.parseAllFilesFromDiff(diffContent);

        if (!changedFiles.length) {
            return this.formatNoContextResult(diffContent, "Could not parse diff file path");
        }

        for (const [filePath, className, changeLine] of changedFiles) {
            this.debugPrint(`Processing file: ${filePath}`);
            const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.repoRoot, filePath);
            if (!fs.existsSync(fullPath)) {
                this.debugPrint(`File does not exist, skipping: ${fullPath}`);
                continue;
            }

            const modifiedMethod = this.findMethodAtLine(filePath, changeLine);
            if (!modifiedMethod) {
                this.debugPrint(`Could not find method at line ${changeLine}, skipping`);
                continue;
            }

            this.debugPrint(`Found modified method: ${modifiedMethod}`);
            const modifiedContext = this.extractCompleteMethod(filePath, modifiedMethod);
            if (modifiedContext) {
                modifiedContext.relevanceReason = `Method containing code changes (near line ${changeLine})`;
                this.contexts.push(modifiedContext);

                this.debugPrint(`Finding upstream callers for ${modifiedMethod}...`);
                const callers = await this.findMethodCallers(modifiedMethod, className);

                for (const [callerFile, callerLine] of callers.slice(0, 3)) {
                    const callerMethod = this.findMethodAtLine(callerFile, callerLine);
                    if (callerMethod) {
                        const callerContext = this.extractCompleteMethod(callerFile, callerMethod);
                        if (callerContext) {
                            callerContext.contextType = 'caller';
                            callerContext.relevanceReason = `Calls the modified method ${modifiedMethod}()`;
                            this.contexts.push(callerContext);
                        }
                    }
                }
            }
        }

        if (!this.contexts.length) {
            return this.formatNoContextResult(diffContent, "All changed files do not exist or no related methods could be found");
        }

        return this.formatContexts(diffContent);
    }

    private formatContexts(originalDiff: string): string {
        let output: string[] = [];
        output.push("# Original Diff");
        output.push("");
        output.push("```diff");
        output.push(originalDiff);
        output.push("```");
        output.push("");

        if (!this.contexts.length) {
            return this.formatNoContextResult(originalDiff, "No related code context found");
        }

        output.push("# 📝 Detailed Code Context");
        output.push("");

        const layers: { [key: string]: CodeContext[] } = {
            controller: [],
            service: [],
            facade: [],
            dao: [],
            entity: [],
            util: [],
        };

        for (const ctx of this.contexts) {
            layers[ctx.layer].push(ctx);
        }

        const layerNames: { [key: string]: string } = {
            controller: '🎮 Controller Layer',
            service: '⚙️ Service Layer',
            facade: '🔌 Facade Layer',
            dao: '💾 DAO Layer',
            entity: '📦 Entity/DTO Layer',
            util: '🛠️ Util Layer',
        };

        let sectionCount = 1;
        for (const layerKey in layers) {
            const layerContexts = layers[layerKey];
            if (layerContexts.length > 0) {
                output.push(`## ${layerNames[layerKey]}`);
                output.push("");

                for (const ctx of layerContexts) {
                    output.push(`### ${sectionCount}. ${ctx.className}.${ctx.methodName}()`);
                    output.push("");
                    output.push(`**File Path**: `${ctx.filePath}``);
                    output.push(`**Relevance**: ${ctx.relevanceReason}`);
                    output.push(`**Code Location**: Lines ${ctx.startLine}-${ctx.endLine}`);
                    output.push("");
                    output.push("```java");
                    output.push(ctx.codeSnippet);
                    output.push("```");
                    output.push("");
                    sectionCount++;
                }
            }
        }

        return output.join('\n');
    }

    private formatNoContextResult(originalDiff: string, reason: string): string {
        let output: string[] = [];
        output.push("# Original Diff");
        output.push("");
        output.push("```diff");
        output.push(originalDiff);
        output.push("```");
        output.push("");
        output.push("## ⚠️ No Related Context Found");
        output.push("");
        output.push(`**Reason**: ${reason}`);
        output.push("");
        return output.join('\n');
    }
}

async function main() {
    const argv = await yargs(hideBin(process.argv))
        .option('file', {
            type: 'string',
            description: 'Diff file path',
        })
        .option('diff', {
            type: 'string',
            description: 'Diff content as string',
        })
        .option('output', {
            type: 'string',
            description: 'Output file path',
        })
        .option('debug', {
            type: 'boolean',
            description: 'Enable debug output',
            default: false,
        })
        .option('repo-root', {
            type: 'string',
            description: 'Repository root directory (default: current directory)',
            default: '.', 
        })
        .check((argv) => {
            if (!argv.file && !argv.diff) {
                throw new Error('Either --file or --diff must be provided');
            }
            return true;
        })
        .parse();

    let diffContent: string | null = null;

    if (argv.file) {
        try {
            diffContent = fs.readFileSync(argv.file, 'utf-8');
        } catch (e: any) {
            console.error(`❌ Error: File ${argv.file} not found`);
            return;
        }
    } else if (argv.diff) {
        diffContent = argv.diff;
    }

    if (!diffContent || !diffContent.trim()) {
        console.error("❌ Error: diff content is empty");
        return;
    }

    const extractor = new JavaContextExtractor(argv.debug, argv['repo-root']);
    const context = await extractor.extractAllContexts(diffContent);

    if (argv.output) {
        try {
            fs.writeFileSync(argv.output, context, 'utf-8');
            console.log(`✅ Code context saved to ${argv.output}`);
        } catch (e: any) {
            console.error(`❌ Error saving file: ${e.message}`);
        }
    } else {
        console.log(context);
    }
}

if (require.main === module) {
    main();
}
