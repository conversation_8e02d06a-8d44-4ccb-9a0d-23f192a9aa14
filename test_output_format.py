#!/usr/bin/env python3
"""
测试去掉"代码变更上下文"部分后的输出格式
"""

import subprocess
import tempfile

def test_output_format():
    """测试输出格式"""
    print("🧪 测试去掉'代码变更上下文'部分后的输出格式")
    print("=" * 60)
    
    # 示例 diff 内容
    diff_content = """diff --git a/src/main/java/com/example/service/UserService.java b/src/main/java/com/example/service/UserService.java
index 1234567..abcdefg 100644
--- a/src/main/java/com/example/service/UserService.java
+++ b/src/main/java/com/example/service/UserService.java
@@ -15,6 +15,8 @@ public class UserService {
     public User updateUser(Long userId, UserDto userDto) {
         // 验证用户存在
         User existingUser = userDao.findById(userId);
+        // 新增：记录更新日志
+        logger.info("更新用户信息: userId={}", userId);
         
         if (existingUser == null) {
             throw new UserNotFoundException("用户不存在: " + userId);
"""
    
    print("\n1. 使用 --diff 参数测试输出格式:")
    try:
        cmd = [
            'python3', 'extractor_final.py',
            '--diff', diff_content
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 执行成功")
            
            output_lines = result.stdout.split('\n')
            
            # 检查是否包含不应该存在的内容
            has_context_section = any("代码变更上下文" in line for line in output_lines)
            has_change_location = any("变更位置" in line for line in output_lines)
            has_file_path = any("文件路径" in line for line in output_lines)
            has_code_level = any("代码层级" in line for line in output_lines)
            has_upstream = any("调用此方法的上游代码" in line for line in output_lines)
            has_downstream = any("此方法调用的下游代码" in line for line in output_lines)
            
            # 检查应该存在的内容
            has_original_diff = any("原始 Diff" in line for line in output_lines)
            has_detailed_context = any("详细代码上下文" in line for line in output_lines)
            
            print(f"\n   📊 输出内容检查:")
            print(f"   - 包含'代码变更上下文': {'❌' if not has_context_section else '⚠️'} {has_context_section}")
            print(f"   - 包含'变更位置': {'❌' if not has_change_location else '⚠️'} {has_change_location}")
            print(f"   - 包含'文件路径': {'❌' if not has_file_path else '⚠️'} {has_file_path}")
            print(f"   - 包含'代码层级': {'❌' if not has_code_level else '⚠️'} {has_code_level}")
            print(f"   - 包含'上游代码': {'❌' if not has_upstream else '⚠️'} {has_upstream}")
            print(f"   - 包含'下游代码': {'❌' if not has_downstream else '⚠️'} {has_downstream}")
            print(f"   - 包含'原始 Diff': {'✅' if has_original_diff else '❌'} {has_original_diff}")
            print(f"   - 包含'详细代码上下文': {'✅' if has_detailed_context else '❌'} {has_detailed_context}")
            
            # 显示输出结构
            print(f"\n   📄 输出结构 (总共 {len(output_lines)} 行):")
            for i, line in enumerate(output_lines[:20], 1):
                if line.strip():
                    if line.startswith('#'):
                        print(f"   {i:2d}: {line}")
                    elif line.startswith('**') or line.startswith('##'):
                        print(f"   {i:2d}: {line}")
            
            if len(output_lines) > 20:
                print("       ...")
            
            # 验证结果
            removed_sections = not (has_context_section or has_change_location or 
                                  has_file_path or has_code_level or 
                                  has_upstream or has_downstream)
            
            if removed_sections and has_original_diff and has_detailed_context:
                print(f"\n   ✅ 输出格式修改成功!")
                print(f"   - 已去掉'代码变更上下文'相关部分")
                print(f"   - 保留了'原始 Diff'和'详细代码上下文'部分")
            else:
                print(f"\n   ⚠️  输出格式可能存在问题")
        else:
            print(f"   ❌ 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
    
    except subprocess.TimeoutExpired:
        print("   ⏰ 命令执行超时")
    except Exception as e:
        print(f"   ❌ 执行错误: {e}")

def test_file_vs_diff_output():
    """测试文件方式和参数方式的输出一致性"""
    print("\n🔄 测试文件方式和参数方式的输出一致性")
    print("=" * 60)
    
    diff_content = """diff --git a/src/main/java/TestClass.java b/src/main/java/TestClass.java
index 1234567..abcdefg 100644
--- a/src/main/java/TestClass.java
+++ b/src/main/java/TestClass.java
@@ -10,6 +10,7 @@ public class TestClass {
     public void testMethod() {
         System.out.println("Hello");
+        System.out.println("World");
     }
 }
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.diff', delete=False) as f:
        f.write(diff_content)
        temp_file = f.name
    
    try:
        print("\n1. 使用 --file 参数:")
        cmd_file = ['python3', 'extractor_final.py', '--file', temp_file]
        result_file = subprocess.run(cmd_file, capture_output=True, text=True, timeout=30)
        
        print("\n2. 使用 --diff 参数:")
        cmd_diff = ['python3', 'extractor_final.py', '--diff', diff_content]
        result_diff = subprocess.run(cmd_diff, capture_output=True, text=True, timeout=30)
        
        if result_file.returncode == 0 and result_diff.returncode == 0:
            # 检查两种方式的输出结构是否一致
            file_lines = [line for line in result_file.stdout.split('\n') if line.strip()]
            diff_lines = [line for line in result_diff.stdout.split('\n') if line.strip()]
            
            print(f"   📊 输出对比:")
            print(f"   - 文件方式行数: {len(file_lines)}")
            print(f"   - 参数方式行数: {len(diff_lines)}")
            
            # 检查关键标题是否一致
            file_headers = [line for line in file_lines if line.startswith('#')]
            diff_headers = [line for line in diff_lines if line.startswith('#')]
            
            print(f"   - 文件方式标题数: {len(file_headers)}")
            print(f"   - 参数方式标题数: {len(diff_headers)}")
            
            if file_headers == diff_headers:
                print(f"   ✅ 两种方式的输出结构一致")
                print(f"   📋 输出标题:")
                for header in file_headers:
                    print(f"      - {header}")
            else:
                print(f"   ⚠️  两种方式的输出结构存在差异")
                print(f"   文件方式标题: {file_headers}")
                print(f"   参数方式标题: {diff_headers}")
        else:
            print(f"   ❌ 执行失败 - 文件方式: {result_file.returncode}, 参数方式: {result_diff.returncode}")
    
    except Exception as e:
        print(f"   ❌ 对比测试错误: {e}")
    finally:
        # 清理临时文件
        import os
        try:
            os.unlink(temp_file)
        except:
            pass

def test_no_context_output():
    """测试无上下文时的输出"""
    print("\n📭 测试无上下文时的输出")
    print("=" * 60)
    
    # 使用一个不存在的文件的 diff
    diff_content = """diff --git a/nonexistent/file.java b/nonexistent/file.java
index 1234567..abcdefg 100644
--- a/nonexistent/file.java
+++ b/nonexistent/file.java
@@ -1,3 +1,4 @@
 public class Test {
+    // new comment
 }
"""
    
    try:
        cmd = ['python3', 'extractor_final.py', '--diff', diff_content]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 执行成功")
            
            output_lines = result.stdout.split('\n')
            
            # 检查是否包含"未找到相关上下文"
            has_no_context = any("未找到相关上下文" in line for line in output_lines)
            has_original_diff = any("原始 Diff" in line for line in output_lines)
            
            print(f"   📊 无上下文输出检查:")
            print(f"   - 包含'原始 Diff': {'✅' if has_original_diff else '❌'} {has_original_diff}")
            print(f"   - 包含'未找到相关上下文': {'✅' if has_no_context else '❌'} {has_no_context}")
            
            if has_original_diff and has_no_context:
                print(f"   ✅ 无上下文输出格式正确")
            else:
                print(f"   ⚠️  无上下文输出格式可能存在问题")
        else:
            print(f"   ❌ 执行失败 (返回码: {result.returncode})")
    
    except Exception as e:
        print(f"   ❌ 测试错误: {e}")

def main():
    """主函数"""
    print("🚀 测试去掉'代码变更上下文'部分后的输出格式")
    print("=" * 80)
    
    # 检查 extractor_final.py 是否存在
    import os
    if not os.path.exists('extractor_final.py'):
        print("❌ 错误: 找不到 extractor_final.py 文件")
        return
    
    try:
        test_output_format()
        test_file_vs_diff_output()
        test_no_context_output()
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成!")
        print("\n📋 修改总结:")
        print("- ✅ 已去掉'📋 代码变更上下文'标题")
        print("- ✅ 已去掉变更位置、文件路径、代码层级信息")
        print("- ✅ 已去掉上游和下游调用关系统计")
        print("- ✅ 保留了'原始 Diff'和'详细代码上下文'部分")
        print("- ✅ 输出更加简洁，专注于代码内容")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
