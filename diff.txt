文件cbp-member-facade/src/main/java/com/fxyeepay/cbp/member/merchant/dto/MerchantBizProfileDTO.java的变更内容为: @@ -24,6 +24,30 @@ public class MerchantBizProfileDTO implements Serializable {\n \n     private List<String> priorityReportBank;\n \n+    /**\n+     * 全球企业账户支付  代扣支付币种默认配置\n+     *  格式\n+     *  [\n+     *      {\n+     *          \"currency\":\"CNY\",\n+     *          \"isUse\":true\n+     *      },\n+     *      {\n+     *          \"currency\":\"USD\",\n+     *          \"isUse\":false\n+     *      }\n+     *  ]\n+     *\n+     */\n+    private String apWithholdPayCurrency;\n+\n+    public String getApWithholdPayCurrency() {\n+        return apWithholdPayCurrency;\n+    }\n+\n+    public void setApWithholdPayCurrency(String apWithholdPayCurrency) {\n+        this.apWithholdPayCurrency = apWithholdPayCurrency;\n+    }\n \n     public String getMemberId() {\n         return memberId;\n 文件cbp-member-web/src/main/java/com/fxyeepay/cbp/member/controller/merchant/accountpayment/vo/WithholdPayCurrencyVO.java的变更内容为: @@ -0,0 +1,42 @@\n+/*\n+ * Copyright: Copyright (c)2011\n+ * Company: 易宝支付(YeePay)\n+ */\n+package com.fxyeepay.cbp.member.controller.merchant.accountpayment.vo;\n+\n+import java.io.Serializable;\n+\n+/**\n+ * title: <br>\n+ * description: 描述<br>\n+ * Copyright: Copyright (c)2014<br>\n+ * Company: 易宝支付(YeePay)<br>\n+ *\n+ * <AUTHOR> * @version 1.0.0\n+ * @since 2025/7/8 15:29\n+ */\n+public class WithholdPayCurrencyVO implements Serializable {\n+\n+    private static final long serialVersionUID = -1L;\n+\n+    private String currency;\n+\n+    private boolean isUse;\n+\n+    public String getCurrency() {\n+        return currency;\n+    }\n+\n+    public void setCurrency(String currency) {\n+        this.currency = currency;\n+    }\n+\n+    public boolean getIsUse() {\n+        return isUse;\n+    }\n+\n+    public void setIsUse(boolean isUse) {\n+        this.isUse = isUse;\n+    }\n+}\n 文件cbp-member-web/src/main/java/com/fxyeepay/cbp/member/controller/merchant/accountpayment/AccountPaymentConfigController.java的变更内容为: @@ -0,0 +1,80 @@\n+package com.fxyeepay.cbp.member.controller.merchant.accountpayment;\n+\n+\n+import com.fxyeepay.cbp.dynaform.facade.DynaMemberProfileFacade;\n+import com.fxyeepay.cbp.framework.commons.controller.FrameBaseController;\n+import com.fxyeepay.cbp.framework.member.Constant;\n+import com.fxyeepay.cbp.framework.shiro.AuthHelper;\n+import com.fxyeepay.cbp.member.controller.merchant.accountpayment.vo.WithholdPayCurrencyVO;\n+import com.fxyeepay.cbp.member.merchant.dto.MerchantBizProfileDTO;\n+import com.fxyeepay.cbp.member.merchant.dto.MerchantDTO;\n+import com.fxyeepay.commons.configuration.ConfigurationUtils;\n+import com.fxyeepay.commons.remoting.annotation.stereotype.UseService;\n+import com.fxyeepay.commons.runtime.springmvc.ResponseMessage;\n+import com.fxyeepay.commons.utils.StringUtils;\n+import com.fxyeepay.commons.utils.json.JSONObject;\n+import com.fxyeepay.commons.utils.json.JSONUtils;\n+import com.fxyeepay.framework.dynaform.bizopen.MemberProfileFacadeProxy;\n+import org.slf4j.Logger;\n+import org.slf4j.LoggerFactory;\n+import org.springframework.beans.factory.annotation.Autowired;\n+import org.springframework.stereotype.Controller;\n+import org.springframework.web.bind.annotation.RequestBody;\n+import org.springframework.web.bind.annotation.RequestMapping;\n+import org.springframework.web.bind.annotation.ResponseBody;\n+\n+import java.util.List;\n+\n+@RequestMapping(\"merchant/accountPayment/config/\")\n+@Controller\n+public class AccountPaymentConfigController extends FrameBaseController {\n+    private final static Logger logger = LoggerFactory.getLogger(AccountPaymentConfigController.class);\n+\n+\n+    @Autowired\n+    private MemberProfileFacadeProxy memberProfileFacadeProxy;\n+\n+    @UseService\n+    DynaMemberProfileFacade dynaMemberProfileFacade;\n+\n+\n+    @RequestMapping(\"getWithholdPayCurrency\")\n+    @ResponseBody\n+    public ResponseMessage getWithholdPayCurrency() {\n+        ResponseMessage responseMessage = new ResponseMessage();\n+        MerchantDTO merchant = (MerchantDTO) AuthHelper.getSession().getAttribute(Constant.MEMBER_MERCHANT_DTO);\n+        MerchantBizProfileDTO merchantBizProfileDTO = memberProfileFacadeProxy.findMemberProfile(\"CBP\", merchant.getId(), MerchantBizProfileDTO.BIZ_NAME, MerchantBizProfileDTO.class);\n+        if (null == merchantBizProfileDTO || StringUtils.isEmpty(merchantBizProfileDTO.getApWithholdPayCurrency())) {\n+            String configCurrency = ConfigurationUtils.getConfiguration().getString(\"cbp.transfer.account.payment.withhold.pay.currency\", \"[{\\\"currency\\\":\\\"CNY\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"USD\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"CNH\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"HKD\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"AUD\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"CAD\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"EUR\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"GBP\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"JPY\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"SGD\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"THB\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"IDR\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"VND\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"PHP\\\",\\\"isUse\\\":false},{\\\"currency\\\":\\\"NGN\\\",\\\"isUse\\\":false}]\");\n+            List<WithholdPayCurrencyVO> withholdPayCurrencyVOS = JSONUtils.jsonToList(configCurrency, WithholdPayCurrencyVO.class);\n+            return responseMessage.put(\"withholdPayCurrencyVOS\", withholdPayCurrencyVOS);\n+        }\n+        return responseMessage.put(\"withholdPayCurrencyVOS\", JSONUtils.jsonToList(merchantBizProfileDTO.getApWithholdPayCurrency(), WithholdPayCurrencyVO.class));\n+    }\n+\n+    @RequestMapping(\"updateWithholdPayCurrency\")\n+    @ResponseBody\n+    public ResponseMessage update(@RequestBody List<WithholdPayCurrencyVO> withholdPayCurrency) {\n+        ResponseMessage responseMessage = new ResponseMessage();\n+\n+        try {\n+            logger.info(\"请求参数 {}\", JSONUtils.toJsonString(withholdPayCurrency));\n+            MerchantDTO merchant = (MerchantDTO) AuthHelper.getSession().getAttribute(Constant.MEMBER_MERCHANT_DTO);\n+            JSONObject merchantBizProfileDTO = dynaMemberProfileFacade.findProfileByMerchantId(\"CBP\", merchant.getId(), MerchantBizProfileDTO.BIZ_NAME);\n+            if (null == merchantBizProfileDTO) {\n+                merchantBizProfileDTO = new JSONObject();\n+                merchantBizProfileDTO.put(\"bizName\", \"CBP_MERCHANT_BIZ_CONFIG\");\n+                merchantBizProfileDTO.put(\"merchantId\", merchant.getId());\n+                merchantBizProfileDTO.put(\"memberId\", merchant.getMemberId());\n+                merchantBizProfileDTO.put(\"state\",\"OPEN\");\n+            }\n+            merchantBizProfileDTO.put(\"apWithholdPayCurrency\", JSONUtils.toJsonString(withholdPayCurrency));\n+            dynaMemberProfileFacade.save(\"CBP\", merchantBizProfileDTO.getString(\"_id\"), merchantBizProfileDTO);\n+            return responseMessage.success(\"处理成功\");\n+        } catch (Throwable e) {\n+            logger.error(\"处理失败\", e);\n+            return responseMessage.error(\"处理失败\");\n+        }\n+    }\n+\n+}\n 文件cbp-member-web/src/main/resources/cbp-member-web-message-en_US.xml的变更内容为: @@ -1949,6 +1949,165 @@\n                             </map>\n                         </entry>\n \n+                        <entry key=\"ALLCurrency\">\n+                            <map>\n+                                <entry key= \"CNY\" value=\"CNY\"/>\n+                                <entry key= \"CNH\" value=\"CNH\"/>\n+                                <entry key= \"USD\" value=\"USD\"/>\n+                                <entry key= \"EUR\" value=\"EUR\"/>\n+                                <entry key= \"JPY\" value=\"JPY\"/>\n+                                <entry key= \"GBP\" value=\"GBP\"/>\n+                                <entry key= \"HKD\" value=\"HKD\"/>\n+                                <entry key= \"AUD\" value=\"AUD\"/>\n+                                <entry key= \"CAD\" value=\"CAD\"/>\n+                                <entry key= \"CHF\" value=\"CHF\"/>\n+                                <entry key= \"DKK\" value=\"DKK\"/>\n+                                <entry key= \"NOK\" value=\"NOK\"/>\n+                                <entry key= \"SGD\" value=\"SGD\"/>\n+                                <entry key= \"SEK\" value=\"SEK\"/>\n+                                <entry key= \"MOP\" value=\"MOP\"/>\n+                                <entry key= \"TWD\" value=\"TWD\"/>\n+                                <entry key= \"THB\" value=\"THB\"/>\n+                                <entry key= \"KRW\" value=\"KRW\"/>\n+                                <entry key= \"NZD\" value=\"NZD\"/>\n+                                <entry key= \"AED\" value=\"AED\"/>\n+                                <entry key= \"ALL\" value=\"ALL\"/>\n+                                <entry key= \"ANG\" value=\"ANG\"/>\n+                                <entry key= \"ARS\" value=\"ARS\"/>\n+                                <entry key= \"ATS\" value=\"ATS\"/>\n+                                <entry key= \"AWG\" value=\"AWG\"/>\n+                                <entry key= \"BBD\" value=\"BBD\"/>\n+                                <entry key= \"BDT\" value=\"BDT\"/>\n+                                <entry key= \"BGN\" value=\"BGN\"/>\n+                                <entry key= \"BHD\" value=\"BHD\"/>\n+                                <entry key= \"BIF\" value=\"BIF\"/>\n+                                <entry key= \"BMD\" value=\"BMD\"/>\n+                                <entry key= \"BND\" value=\"BND\"/>\n+                                <entry key= \"BOB\" value=\"BOB\"/>\n+                                <entry key= \"BRL\" value=\"BRL\"/>\n+                                <entry key= \"BSD\" value=\"BSD\"/>\n+                                <entry key= \"BTN\" value=\"BTN\"/>\n+                                <entry key= \"BWP\" value=\"BWP\"/>\n+                                <entry key= \"BYR\" value=\"BYR\"/>\n+                                <entry key= \"BZD\" value=\"BZD\"/>\n+                                <entry key= \"CLP\" value=\"CLP\"/>\n+                                <entry key= \"COP\" value=\"COP\"/>\n+                                <entry key= \"CRC\" value=\"CRC\"/>\n+                                <entry key= \"CUP\" value=\"CUP\"/>\n+                                <entry key= \"CVE\" value=\"CVE\"/>\n+                                <entry key= \"CZK\" value=\"CZK\"/>\n+                                <entry key= \"DJF\" value=\"DJF\"/>\n+                                <entry key= \"DOP\" value=\"DOP\"/>\n+                                <entry key= \"DZD\" value=\"DZD\"/>\n+                                <entry key= \"ECS\" value=\"ECS\"/>\n+                                <entry key= \"EGP\" value=\"EGP\"/>\n+                                <entry key= \"ETB\" value=\"ETB\"/>\n+                                <entry key= \"ESP\" value=\"ESP\"/>\n+                                <entry key= \"FIM\" value=\"FIM\"/>\n+                                <entry key= \"FJD\" value=\"FJD\"/>\n+                                <entry key= \"FKP\" value=\"FKP\"/>\n+                                <entry key= \"GIP\" value=\"GIP\"/>\n+                                <entry key= \"GMD\" value=\"GMD\"/>\n+                                <entry key= \"GNF\" value=\"GNF\"/>\n+                                <entry key= \"GTQ\" value=\"GTQ\"/>\n+                                <entry key= \"GYD\" value=\"GYD\"/>\n+                                <entry key= \"HNL\" value=\"HNL\"/>\n+                                <entry key= \"HRK\" value=\"HRK\"/>\n+                                <entry key= \"HTG\" value=\"HTG\"/>\n+                                <entry key= \"HUF\" value=\"HUF\"/>\n+                                <entry key= \"IDR\" value=\"IDR\"/>\n+                                <entry key= \"ILS\" value=\"ILS\"/>\n+                                <entry key= \"INR\" value=\"INR\"/>\n+                                <entry key= \"IQD\" value=\"IQD\"/>\n+                                <entry key= \"IRR\" value=\"IRR\"/>\n+                                <entry key= \"ISK\" value=\"ISK\"/>\n+                                <entry key= \"JMD\" value=\"JMD\"/>\n+                                <entry key= \"JOD\" value=\"JOD\"/>\n+                                <entry key= \"KES\" value=\"KES\"/>\n+                                <entry key= \"KHR\" value=\"KHR\"/>\n+                                <entry key= \"KMF\" value=\"KMF\"/>\n+                                <entry key= \"KPW\" value=\"KPW\"/>\n+                                <entry key= \"KWD\" value=\"KWD\"/>\n+                                <entry key= \"KZT\" value=\"KZT\"/>\n+                                <entry key= \"LAK\" value=\"LAK\"/>\n+                                <entry key= \"LBP\" value=\"LBP\"/>\n+                                <entry key= \"LKR\" value=\"LKR\"/>\n+                                <entry key= \"LRD\" value=\"LRD\"/>\n+                                <entry key= \"LSL\" value=\"LSL\"/>\n+                                <entry key= \"LTL\" value=\"LTL\"/>\n+                                <entry key= \"LVL\" value=\"LVL\"/>\n+                                <entry key= \"LYD\" value=\"LYD\"/>\n+                                <entry key= \"MAD\" value=\"MAD\"/>\n+                                <entry key= \"MDL\" value=\"MDL\"/>\n+                                <entry key= \"MKD\" value=\"MKD\"/>\n+                                <entry key= \"MMK\" value=\"MMK\"/>\n+                                <entry key= \"MNT\" value=\"MNT\"/>\n+                                <entry key= \"MRO\" value=\"MRO\"/>\n+                                <entry key= \"MUR\" value=\"MUR\"/>\n+                                <entry key= \"MVR\" value=\"MVR\"/>\n+                                <entry key= \"MWK\" value=\"MWK\"/>\n+                                <entry key= \"MXN\" value=\"MXN\"/>\n+                                <entry key= \"NAD\" value=\"NAD\"/>\n+                                <entry key= \"NGN\" value=\"NGN\"/>\n+                                <entry key= \"NIO\" value=\"NIO\"/>\n+                                <entry key= \"NPR\" value=\"NPR\"/>\n+                                <entry key= \"OMR\" value=\"OMR\"/>\n+                                <entry key= \"PAB\" value=\"PAB\"/>\n+                                <entry key= \"PEN\" value=\"PEN\"/>\n+                                <entry key= \"PGK\" value=\"PGK\"/>\n+                                <entry key= \"PHP\" value=\"PHP\"/>\n+                                <entry key= \"PKR\" value=\"PKR\"/>\n+                                <entry key= \"PLN\" value=\"PLN\"/>\n+                                <entry key= \"PYG\" value=\"PYG\"/>\n+                                <entry key= \"QAR\" value=\"QAR\"/>\n+                                <entry key= \"RON\" value=\"RON\"/>\n+                                <entry key= \"RUB\" value=\"RUB\"/>\n+                                <entry key= \"RWF\" value=\"RWF\"/>\n+                                <entry key= \"SAR\" value=\"SAR\"/>\n+                                <entry key= \"SBD\" value=\"SBD\"/>\n+                                <entry key= \"SCR\" value=\"SCR\"/>\n+                                <entry key= \"SHP\" value=\"SHP\"/>\n+                                <entry key= \"SLL\" value=\"SLL\"/>\n+                                <entry key= \"SOS\" value=\"SOS\"/>\n+                                <entry key= \"STD\" value=\"STD\"/>\n+                                <entry key= \"SUR\" value=\"SUR\"/>\n+                                <entry key= \"SVC\" value=\"SVC\"/>\n+                                <entry key= \"SYP\" value=\"SYP\"/>\n+                                <entry key= \"SZL\" value=\"SZL\"/>\n+                                <entry key= \"TND\" value=\"TND\"/>\n+                                <entry key= \"TOP\" value=\"TOP\"/>\n+                                <entry key= \"TRY\" value=\"TRY\"/>\n+                                <entry key= \"TTD\" value=\"TTD\"/>\n+                                <entry key= \"TZS\" value=\"TZS\"/>\n+                                <entry key= \"UAH\" value=\"UAH\"/>\n+                                <entry key= \"UYU\" value=\"UYU\"/>\n+                                <entry key= \"VND\" value=\"VND\"/>\n+                                <entry key= \"VUV\" value=\"VUV\"/>\n+                                <entry key= \"WST\" value=\"WST\"/>\n+                                <entry key= \"XAF\" value=\"XAF\"/>\n+                                <entry key= \"XAG\" value=\"XAG\"/>\n+                                <entry key= \"XAU\" value=\"XAU\"/>\n+                                <entry key= \"XCD\" value=\"XCD\"/>\n+                                <entry key= \"XCP\" value=\"XCP\"/>\n+                                <entry key= \"XOF\" value=\"XOF\"/>\n+                                <entry key= \"XPD\" value=\"XPD\"/>\n+                                <entry key= \"XPF\" value=\"XPF\"/>\n+                                <entry key= \"XPT\" value=\"XPT\"/>\n+                                <entry key= \"YER\" value=\"YER\"/>\n+                                <entry key= \"ZAR\" value=\"ZAR\"/>\n+                                <entry key= \"ZMK\" value=\"ZMK\"/>\n+                                <entry key= \"ZRZ\" value=\"ZRZ\"/>\n+                                <entry key= \"ZWD\" value=\"ZWD\"/>\n+                                <entry key= \"DEM\" value=\"DEM\"/>\n+                                <entry key= \"FRF\" value=\"FRF\"/>\n+                                <entry key= \"NLG\" value=\"NLG\"/>\n+                                <entry key= \"BEF\" value=\"BEF\"/>\n+                                <entry key= \"ITL\" value=\"ITL\"/>\n+                                <entry key= \"MYR\" value=\"MYR\"/>\n+                                <entry key= \"GHS\" value=\"GHS\"/>\n+                            </map>\n+                        </entry>\n+\n                     </map>\n                 </entry>\n \n 文件cbp-member-web/src/main/resources/cbp-member-web-message-zh_CN.xml的变更内容为: @@ -6231,10 +6231,167 @@\n                                 <entry key=\"DISABLE\" value=\"失效\"/>\n                             </map>\n                         </entry>\n+\n+                        <entry key=\"ALLCurrency\">\n+                            <map>\n+                                <entry key= \"CNY\" value=  \"人民币\" />\n+                                <entry key= \"CNH\" value=  \"离岸人民币\" />\n+                                <entry key= \"USD\" value=  \"美元\" />\n+                                <entry key= \"EUR\" value=  \"欧元\" />\n+                                <entry key= \"JPY\" value=  \"日元\" />\n+                                <entry key= \"GBP\" value=  \"英镑\" />\n+                                <entry key= \"HKD\" value=  \"港币\" />\n+                                <entry key= \"AUD\" value=  \"澳元\" />\n+                                <entry key= \"CAD\" value=  \"加元\" />\n+                                <entry key= \"CHF\" value=  \"瑞士法郎\" />\n+                                <entry key= \"DKK\" value=  \"丹麦克朗\" />\n+                                <entry key= \"NOK\" value=  \"挪威克朗\" />\n+                                <entry key= \"SGD\" value=  \"新加坡元\" />\n+                                <entry key= \"SEK\" value=  \"瑞典克朗\" />\n+                                <entry key= \"MOP\" value=  \"澳门元\" />\n+                                <entry key= \"TWD\" value=  \"新台币\" />\n+                                <entry key= \"THB\" value=  \"泰铢\" />\n+                                <entry key= \"KRW\" value=  \"韩元\" />\n+                                <entry key= \"NZD\" value=  \"新西兰元\" />\n+                                <entry key= \"AED\" value=  \"阿联酋迪拉姆\" />\n+                                <entry key= \"ALL\" value=  \"阿尔巴尼亚列克\" />\n+                                <entry key= \"ANG\" value=  \"列斯荷兰盾\" />\n+                                <entry key= \"ARS\" value=  \"阿根廷比索\" />\n+                                <entry key= \"ATS\" value=  \"奥地利先令\" />\n+                                <entry key= \"AWG\" value=  \"阿鲁巴岛弗罗林\" />\n+                                <entry key= \"BBD\" value=  \"巴巴多斯元\" />\n+                                <entry key= \"BDT\" value=  \"孟加拉塔卡\" />\n+                                <entry key= \"BGN\" value=  \"保加利亚列瓦\" />\n+                                <entry key= \"BHD\" value=  \"巴林第纳尔\" />\n+                                <entry key= \"BIF\" value=  \"布隆迪法郎\" />\n+                                <entry key= \"BMD\" value=  \"百慕大元\" />\n+                                <entry key= \"BND\" value=  \"文莱元\" />\n+                                <entry key= \"BOB\" value=  \"玻利维亚诺\" />\n+                                <entry key= \"BRL\" value=  \"巴西里亚伊\" />\n+                                <entry key= \"BSD\" value=  \"巴哈马元\" />\n+                                <entry key= \"BTN\" value=  \"不丹卢比\" />\n+                                <entry key= \"BWP\" value=  \"博茨瓦纳普拉\" />\n+                                <entry key= \"BYR\" value=  \"白俄罗斯卢布\" />\n+                                <entry key= \"BZD\" value=  \"伯利兹元\" />\n+                                <entry key= \"CLP\" value=  \"智利比索\" />\n+                                <entry key= \"COP\" value=  \"哥伦比亚比索\" />\n+                                <entry key= \"CRC\" value=  \"哥斯达黎加科朗\" />\n+                                <entry key= \"CUP\" value=  \"古巴比索\" />\n+                                <entry key= \"CVE\" value=  \"佛得角埃斯库多\" />\n+                                <entry key= \"CZK\" value=  \"捷克克朗\" />\n+                                <entry key= \"DJF\" value=  \"吉布提法郎\" />\n+                                <entry key= \"DOP\" value=  \"多米尼加比索\" />\n+                                <entry key= \"DZD\" value=  \"阿尔及利亚第纳尔\" />\n+                                <entry key= \"ECS\" value=  \"苏克雷\" />\n+                                <entry key= \"EGP\" value=  \"埃及镑\" />\n+                                <entry key= \"ETB\" value=  \"埃塞俄比亚比尔\" />\n+                                <entry key= \"ESP\" value=  \"比塞塔\" />\n+                                <entry key= \"FIM\" value=  \"芬兰马克\" />\n+                                <entry key= \"FJD\" value=  \"斐济元\" />\n+                                <entry key= \"FKP\" value=  \"福克兰群岛镑\" />\n+                                <entry key= \"GIP\" value=  \"直布罗陀镑\" />\n+                                <entry key= \"GMD\" value=  \"冈比亚达拉西\" />\n+                                <entry key= \"GNF\" value=  \"几内亚法郎\" />\n+                                <entry key= \"GTQ\" value=  \"危地马拉格查尔\" />\n+                                <entry key= \"GYD\" value=  \"圭亚那元\" />\n+                                <entry key= \"HNL\" value=  \"洪都拉斯伦皮拉\" />\n+                                <entry key= \"HRK\" value=  \"克罗地亚库纳\" />\n+                                <entry key= \"HTG\" value=  \"海地古德\" />\n+                                <entry key= \"HUF\" value=  \"匈牙利福林\" />\n+                                <entry key= \"IDR\" value=  \"印度尼西亚卢比盾\" />\n+                                <entry key= \"ILS\" value=  \"以色列镑\" />\n+                                <entry key= \"INR\" value=  \"印度卢比\" />\n+                                <entry key= \"IQD\" value=  \"伊拉克第纳尔\" />\n+                                <entry key= \"IRR\" value=  \"伊朗里亚尔\" />\n+                                <entry key= \"ISK\" value=  \"冰岛克朗\" />\n+                                <entry key= \"JMD\" value=  \"牙买加元\" />\n+                                <entry key= \"JOD\" value=  \"约旦第纳尔\" />\n+                                <entry key= \"KES\" value=  \"肯尼亚先令\" />\n+                                <entry key= \"KHR\" value=  \"柬埔寨利尔斯\" />\n+                                <entry key= \"KMF\" value=  \"科摩罗法郎\" />\n+                                <entry key= \"KPW\" value=  \"朝鲜圆\" />\n+                                <entry key= \"KWD\" value=  \"科威特第纳尔\" />\n+                                <entry key= \"KZT\" value=  \"哈萨克斯坦腾格\" />\n+                                <entry key= \"LAK\" value=  \"老挝基普\" />\n+                                <entry key= \"LBP\" value=  \"黎巴嫩镑\" />\n+                                <entry key= \"LKR\" value=  \"斯里兰卡卢比\" />\n+                                <entry key= \"LRD\" value=  \"利比里亚元\" />\n+                                <entry key= \"LSL\" value=  \"莱索托洛提\" />\n+                                <entry key= \"LTL\" value=  \"立陶宛里塔斯\" />\n+                                <entry key= \"LVL\" value=  \"拉脱维亚拉图\" />\n+                                <entry key= \"LYD\" value=  \"利比亚第纳尔\" />\n+                                <entry key= \"MAD\" value=  \"摩洛哥道拉姆\" />\n+                                <entry key= \"MDL\" value=  \"摩尔多瓦列伊\" />\n+                                <entry key= \"MKD\" value=  \"马其顿第纳尔\" />\n+                                <entry key= \"MMK\" value=  \"缅甸元\" />\n+                                <entry key= \"MNT\" value=  \"蒙古图格里克\" />\n+                                <entry key= \"MRO\" value=  \"毛里塔尼亚乌吉亚\" />\n+                                <entry key= \"MUR\" value=  \"毛里求斯卢比\" />\n+                                <entry key= \"MVR\" value=  \"马尔代夫卢非亚\" />\n+                                <entry key= \"MWK\" value=  \"马拉维克瓦查\" />\n+                                <entry key= \"MXN\" value=  \"墨西哥比索\" />\n+                                <entry key= \"NAD\" value=  \"纳米比亚元\" />\n+                                <entry key= \"NGN\" value=  \"尼日利亚奈拉\" />\n+                                <entry key= \"NIO\" value=  \"尼加拉瓜科多巴\" />\n+                                <entry key= \"NPR\" value=  \"尼泊尔卢比\" />\n+                                <entry key= \"OMR\" value=  \"阿曼里亚尔\" />\n+                                <entry key= \"PAB\" value=  \"巴拿马巴尔博亚\" />\n+                                <entry key= \"PEN\" value=  \"秘鲁索尔\" />\n+                                <entry key= \"PGK\" value=  \"巴布亚新几内亚基那\" />\n+                                <entry key= \"PHP\" value=  \"菲律宾比索\" />\n+                                <entry key= \"PKR\" value=  \"巴基斯坦卢比\" />\n+                                <entry key= \"PLN\" value=  \"波兰兹罗提\" />\n+                                <entry key= \"PYG\" value=  \"巴拉圭瓜拉尼\" />\n+                                <entry key= \"QAR\" value=  \"卡塔尔利尔\" />\n+                                <entry key= \"RON\" value=  \"罗马尼亚新列伊\" />\n+                                <entry key= \"RUB\" value=  \"俄罗斯卢布\" />\n+                                <entry key= \"RWF\" value=  \"卢旺达法郎\" />\n+                                <entry key= \"SAR\" value=  \"沙特阿拉伯里亚尔\" />\n+                                <entry key= \"SBD\" value=  \"所罗门群岛元\" />\n+                                <entry key= \"SCR\" value=  \"塞舌尔法郎\" />\n+                                <entry key= \"SHP\" value=  \"圣赫勒拿群岛磅\" />\n+                                <entry key= \"SLL\" value=  \"塞拉利昂利昂\" />\n+                                <entry key= \"SOS\" value=  \"索马里先令\" />\n+                                <entry key= \"STD\" value=  \"圣多美多布拉\" />\n+                                <entry key= \"SUR\" value=  \"卢布\" />\n+                                <entry key= \"SVC\" value=  \"萨尔瓦多科朗\" />\n+                                <entry key= \"SYP\" value=  \"叙利亚镑\" />\n+                                <entry key= \"SZL\" value=  \"斯威士兰里兰吉尼\" />\n+                                <entry key= \"TND\" value=  \"突尼斯第纳尔\" />\n+                                <entry key= \"TOP\" value=  \"汤加潘加\" />\n+                                <entry key= \"TRY\" value=  \"土耳其新里拉\" />\n+                                <entry key= \"TTD\" value=  \"特立尼达和多巴哥元\" />\n+                                <entry key= \"TZS\" value=  \"坦桑尼亚先令\" />\n+                                <entry key= \"UAH\" value=  \"乌克兰赫夫米\" />\n+                                <entry key= \"UYU\" value=  \"乌拉圭新比索\" />\n+                                <entry key= \"VND\" value=  \"越南盾\" />\n+                                <entry key= \"VUV\" value=  \"瓦努阿图瓦图\" />\n+                                <entry key= \"WST\" value=  \"萨摩亚塔拉\" />\n+                                <entry key= \"XAF\" value=  \"刚果中非共同体法郎\" />\n+                                <entry key= \"XAG\" value=  \"银价盎司\" />\n+                                <entry key= \"XAU\" value=  \"金价盎司\" />\n+                                <entry key= \"XCD\" value=  \"格林纳达东加勒比元\" />\n+                                <entry key= \"XCP\" value=  \"铜价盎司\" />\n+                                <entry key= \"XOF\" value=  \"多哥非洲共同体法郎\" />\n+                                <entry key= \"XPD\" value=  \"钯价盎司\" />\n+                                <entry key= \"XPF\" value=  \"太平洋法郎\" />\n+                                <entry key= \"XPT\" value=  \"铂价盎司\" />\n+                                <entry key= \"YER\" value=  \"也门里亚尔\" />\n+                                <entry key= \"ZAR\" value=  \"南非兰特\" />\n+                                <entry key= \"ZMK\" value=  \"赞比亚克瓦查\" />\n+                                <entry key= \"ZRZ\" value=  \"扎伊尔\" />\n+                                <entry key= \"ZWD\" value=  \"津巴布韦元\" />\n+                                <entry key= \"DEM\" value=  \"德国马克\" />\n+                                <entry key= \"FRF\" value=  \"法国法郎\" />\n+                                <entry key= \"NLG\" value=  \"荷兰盾\" />\n+                                <entry key= \"BEF\" value=  \"比利时法郎\" />\n+                                <entry key= \"ITL\" value=  \"意大利里拉\" />\n+                                <entry key= \"MYR\" value=  \"马来西亚林吉特\" />\n+                                <entry key= \"GHS\" value=  \"加纳塞地\" />\n+                            </map>\n+                        </entry>\n                     </map>\n                 </entry>\n-\n-\n             </map>\n         </property>\n     </bean>\n
