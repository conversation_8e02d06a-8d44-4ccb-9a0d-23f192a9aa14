#!/usr/bin/env python3
"""
Java 代码上下文提取器 - 通用版
从 diff 中提取完整的代码上下文，纯粹的上下文组装工具
不进行代码分析，只负责收集和组织相关代码

支持功能:
- 指定仓库根目录读取源文件
- 支持从文件读取或直接传入 diff 内容
- 提取被修改方法的完整定义
- 查找调用关系（上游调用者和下游被调用者）
- 提取相关实体类和 DTO
- 支持多层架构代码分析
"""

import re
import subprocess
import os
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CodeContext:
    """代码上下文信息"""
# 📝 详细代码上下文

## 🎮 Controller 层

### 1. WithdrawAppController.queryWithDrawArrivalTime()

**文件路径**: `src/main/java/com/yeepay/g3/app/account/pay/mboss/controller/app/WithdrawAppController.java`
**相关性**: 调用了被修改的方法 queryAccountInfos()
**代码位置**: 第 133-191 行

```java
133:     /**
 134:      * 1.不同的账户类型 规则上定义的可支持的到账时间不同，例：手续费账户，只支持次日到账
 135:      * 2.一些账户类型提现不需要校验产品，或者校验的不是一般的产品 ENTERPRISE_WITHDRAW_STANDARD 例：手续费不校验，不计费，营销账户提现是这个：MARKET_WITHDRAW
 136:      *
 137:      * @return
 138:      */
 139:     @RequestMapping(value = "/arrive-time", method = RequestMethod.GET)
 140:     @ApiOperation(value = "查询可支持的提现账户 及 到账时效")
 141:     @ResponseBody
 142: //    @RequiresPermissions(Costants.CONFIRM_WITHDRAW_RULE)
 143:     public BaseRespDTO<List<AccountWithdrawArrivalTypeModel>> queryWithDrawArrivalTime() {
 144:         String currentCustomerNumber = getCurrentCustomerNumber();
 145:         LOGGER.info("[/arrive-time 查询可支持的提现账户到账时效] 商户={}", currentCustomerNumber);
 146:         List<AccountWithdrawArrivalTypeModel> list = Lists.newArrayList();
 147:         try {
 148:             /*首先校验权限*/
 149:             if (!ShiroSecurityHelper.hasPermission(Logical.AND, Costants.CONFIRM_WITHDRAW_RULE)) {
 150:                 LOGGER.info("[/arrive-time] 当前操作员无提现发起权限 operator={} ,商户={}",getCurrentUser().getLoginName(), currentCustomerNumber);
 151:                 throw AccountPayException.NO_PERMISSION_ERROR.newInstance("暂无权限,请联系管理员或咨询客服申请权限");
 152:             }
 153:             /*1.校验一下资金账户存不存在?*/
 154:             List<AccountDTO> accountList = businessCheckRemoteService.queryAccountInfos(currentCustomerNumber);
 155:             Map<String, String> allowAccountTypeAndFirstProductCode = ConfigUtils.getAppWithdrawAllowAccountTypeMap();
 156:             List<AccountDTO> allowAccountType = accountList.stream().filter(e -> allowAccountTypeAndFirstProductCode.containsKey(e.getAccountType())).collect(Collectors.toList());
 157:             if (CollectionUtils.isEmpty(allowAccountType)) {
 158:                 LOGGER.info("[查询可支持的提现账户到账时效] 不存在支持的账户类型 商编为={}，result={}", currentCustomerNumber, JSONUtils.toJsonString(list));
 159:                 return BaseRespDTO.success(list);
 160:             }
 161:             /*2.为各个账户类型找到账产品开通的时间*/
 162:             Map<String, List<DicCodeDTO>> arriveTypeMap = Maps.newHashMap();
 163:             for (AccountDTO accountDTO : allowAccountType) {
 164:                 /*2.1确认好是不是这个一级*/
 165:                 String firstProduct = allowAccountTypeAndFirstProductCode.get(accountDTO.getAccountType());
 166:                 /*2.2 获取一下一级下的产品开通列表*/
 167:                 List<DicCodeDTO> arriveTypeList = arriveTypeMap.get(firstProduct);
 168:                 if (/*没查过就查一下*/null == arriveTypeList) {
 169:                     arriveTypeList = merchantProductRemoteService.queryWithdrawArriveTypeMerchant(currentCustomerNumber, firstProduct);
 170:                     /*如果为空底下是空list*/
 171:                     arriveTypeMap.put(firstProduct, arriveTypeList);
 172:                 }
 173:                 /*2.3存在对应的产品开通，则加进去*/
 174:                 if (!CollectionUtils.isEmpty(arriveTypeList)) {
 175:                     AccountWithdrawArrivalTypeModel arrivalTypeModel = new AccountWithdrawArrivalTypeModel();
 176:                     arrivalTypeModel.setAccountType(accountDTO.getAccountType());
 177:                     arrivalTypeModel.setAccountTypeDesc(EnumHelper.getAccountTypeDesc(accountDTO.getAccountType()));
 178:                     arrivalTypeModel.setArriveTypeModelList(arriveTypeList);
 179:                     list.add(arrivalTypeModel);
 180:                 }
 181:             }
 182:             LOGGER.info("[查询可支持的提现账户到账时效] 商编为={}，result={}", currentCustomerNumber, JSONUtils.toJsonString(list));
 183:             return BaseRespDTO.success(list);
 184:         } catch (YeepayBizException e) {
 185:             LOGGER.warn("[查询可支持的提现账户到账时效] 业务异常，商编=" + currentCustomerNumber + ",异常为={}", e);
 186:             return BaseRespDTO.fail(e.getDefineCode(), e.getMessage());
 187:         } catch (Exception e) {
 188:             LOGGER.error("[查询可支持的提现账户到账时效] 异常，商编=" + currentCustomerNumber + ",异常为={}", e);
 189:             return BaseRespDTO.fail(e.getMessage());
 190:         }
 191:     }
```

### 2. CrossTransferController.balance()

**文件路径**: `src/main/java/com/yeepay/g3/app/account/pay/mboss/controller/middleground/CrossTransferController.java`
**相关性**: 调用了被修改的方法 queryAccountInfos()
**代码位置**: 第 85-121 行

```java
85:     /**
  86:      * 查询汇款账户余额
  87:      *
  88:      * @param request
  89:      * @return
  90:      */
  91:     @RequestMapping(value = "/balance", method = RequestMethod.GET)
  92:     @ResponseBody
  93:     public ResponseMessage balance(HttpServletRequest request) {
  94:         ResponseMessage resMsg = new ResponseMessage(ResponseMessage.Status.SUCCESS);
  95:         //当前商编
  96:         String currentCustomerNumber = getCurrentCustomerNumber();
  97:         LOGGER.info("[跨境汇款] [账户余额查询] 请求参数 currentCustomerNumber = {}", currentCustomerNumber);
  98:         //查询资金账户余额
  99:         BigDecimal balance = new BigDecimal(BigInteger.ZERO);
 100:         AccountInfoRespDTO queryAccountResponseDto = businessCheckRemoteService.accountStatusAndBalance(currentCustomerNumber, AccountTypeEnum.FUND_ACCOUNT);
 101:         BigDecimal queryBalance = queryAccountResponseDto.getBalance();
 102:         if (queryBalance != null) {
 103:             balance = queryBalance;
 104:         }
 105:         resMsg.put("balance", balance);
 106:         List<CrossAccountInfoDTO> crossAccountList = new ArrayList<>();
 107:         List<AccountDTO> accountList = businessCheckRemoteService.queryAccountInfos(currentCustomerNumber);
 108:         if (CollectionUtils.isNotEmpty(accountList)) {
 109:             for (AccountDTO account : accountList) {
 110:                 if (AccountTypeEnum.CNH.name().equals(account.getAccountType())) {
 111:                     CrossAccountInfoDTO crossAccount = new CrossAccountInfoDTO();
 112:                     crossAccount.setAccountType(account.getAccountType());
 113:                     crossAccount.setBalance(account.getBalance().setScale(2).toPlainString());
 114:                     crossAccountList.add(crossAccount);
 115:                 }
 116:             }
 117:         }
 118:         resMsg.put("accountInfoList", crossAccountList);
 119:         LOGGER.info("[跨境汇款] [账户余额查询] 响应参数 currentCustomerNumber = {} , resMsg = {}", currentCustomerNumber, JSONUtils.toJsonString(resMsg));
 120:         return resMsg;
 121:     }
```

### 3. WithdrawController.buildAccountView()

**文件路径**: `src/main/java/com/yeepay/g3/app/account/pay/mboss/controller/middleground/WithdrawController.java`
**相关性**: 调用了被修改的方法 queryAccountInfos()
**代码位置**: 第 418-433 行

```java
418:     private void buildAccountView(List<AccountInfo> accountInfoList, ModelAndView mav) {
 419:         List<Map<String,Object>> list = new ArrayList<>();
 420:         if(!CollectionUtils.isEmpty(accountInfoList)){
 421:             accountInfoList.stream().forEach(e->{
 422:                 Map<String,Object> map = new HashMap<>();
 423:                 map.put("accountType",e.getAccountType());
 424:                 map.put("balance",e.getBalance());
 425:                 map.put("accountTypeName",AccountTypeEnum.valueOf(e.getAccountType()).getDesc());
 426:                 map.put("serviceType",e.getServiceType());
 427:                 Collections.sort(e.getDicCodeDTOS());
 428:                 map.put("arriveType",JSONUtils.toJsonString(e.getDicCodeDTOS()));
 429:                 list.add(map);
 430:             });
 431:         }
 432:         mav.addObject("accountTypeList",list);
 433:     }
```

### 4. WithdrawController.withdrawInit()

**文件路径**: `src/main/java/com/yeepay/g3/app/account/pay/mboss/controller/middleground/WithdrawController.java`
**相关性**: 调用了被修改的方法 queryAccountInfos()
**代码位置**: 第 2182-2208 行

```java
2182:     /**
2183:      * 提现页面初始化
2184:      * @param request
2185:      * @return
2186:      */
2187:     @RequestMapping(value = "/order/init", method = RequestMethod.GET)
2188:     @ResponseBody
2189:     public BaseRespDTO withdrawInit(HttpServletRequest request) {
2190:         String currentCustomerNumber = getCurrentCustomerNumber();
2191:         //提现账户基本信息配置
2192:         Map<String, AccountBasicParam> basicConfig = WithdrawProductBasicConfigUtils.getAccountBasicConfig();
2193:         Map<String, Object> map = new HashMap<>();
2194:         try {
2195:             map = buildBindCardListView(map, currentCustomerNumber);
2196:             List<AccountDTO> accountList = businessCheckRemoteService.queryAccountInfos(getCurrentCustomerNumber());
2197:             List<AccountInfo> accountInfoList = sortAccountInfo(basicConfig, accountList);
2198:             accountInfoList = getWithdrawProductView(basicConfig, accountInfoList);
2199:             map.put("hasWithdrawProduct", !accountInfoList.isEmpty());
2200:             buildAccountView(accountInfoList, map);
2201:             map = buildModelView(map, request);
2202:             LOGGER.info("提现页面初始化，商编={}，返回为={}", currentCustomerNumber, JSONUtils.toJsonString(map));
2203:             return BaseRespDTO.success(map);
2204:         } catch (Exception e) {
2205:             LOGGER.error("提现页面初始化异常，商编=" + currentCustomerNumber + ",异常为={}", e);
2206:             return BaseRespDTO.fail(e.getMessage());
2207:         }
2208:     }
```

## ⚙️ Service 层

### 5. AccountManageInfoServiceImpl.queryAccountInfos()

**文件路径**: `src/main/java/com/yeepay/g3/app/account/pay/mboss/service/impl/AccountManageInfoServiceImpl.java`
**相关性**: 包含代码变更的方法 (第 279 行附近)
**代码位置**: 第 233-261 行

```java
233:     /**
 234:      * 查询下级商户所有的账户
 235:      * @param merchantNo
 236:      * @return
 237:      */
 238:     @Override
 239:     public List<MerchantAccountInfoEntity> queryAccountInfos(String merchantNo,String signName) {
 240:         List<MerchantAccountInfoEntity> resp = new ArrayList<>();
 241:         try{
 242:             List<QueryAccountResponseDto>  queryAccountResponseDtoList =  accountManageFacade.queryOuterAccountByCustomerNo(merchantNo);
 243:             LOGGER.info("查询账户信息，去请求accounting-sys的批量查询返回为: {}", JSONUtils.toJsonString(queryAccountResponseDtoList));
 244:             if(CollectionUtils.isNotEmpty(queryAccountResponseDtoList)){
 245:                 queryAccountResponseDtoList.forEach(queryAccountResponseDto->{
 246:                     MerchantAccountInfoEntity merchantAccountInfoEntity = new MerchantAccountInfoEntity();
 247:                     merchantAccountInfoEntity.setMerchantNo(merchantNo);
 248:                     merchantAccountInfoEntity.setSignName(signName);
 249:                     merchantAccountInfoEntity.setAccountType(queryAccountResponseDto.getAccountType());
 250:                     merchantAccountInfoEntity.setBalance(queryAccountResponseDto.getBalance());
 251:                     merchantAccountInfoEntity.setStatus(queryAccountResponseDto.getAccountStatus());
 252:                     merchantAccountInfoEntity.setCreateDate(queryAccountResponseDto.getCreateDate());
 253:                     resp.add(merchantAccountInfoEntity);
 254:                 });
 255:             }
 256:             return resp;
 257:         }catch (Exception e){
 258:             LOGGER.info("查询下级商户信息列表，发生了异常: " + "参数为" + merchantNo +"异常为" , e);
 259:             throw UnionAccountException.SYSTEM_ERROR.newInstance("查询账户信息异常");
 260:         }
 261:     }
```

### 6. AccountManageInfoServiceImpl.info()

**文件路径**: `src/main/java/com/yeepay/g3/app/account/pay/mboss/service/impl/AccountManageInfoServiceImpl.java`
**相关性**: 被修改的方法 queryAccountInfos() 调用了此方法
**代码位置**: 第 159-196 行

```java
159:     /**
 160:      * 商户的查询接口是分页的，递归
 161:      *
 162:      * @param req
 163:      * @param page
 164:      * @return
 165:      */
 166:     public List<MerchantAccountInfoEntity> getAllMerchantNo(List<MerchantAccountInfoEntity> merchantTotal, QueryMerchantNosByParentNoForPageReqDTO req, Integer page) {
 167:         try {
 168:             LOGGER.info("查询下级商户信息列表，去请求客户中心的参数为: {}", JSONUtils.toJsonString(req));
 169:             req.setPage(page);
 170:             QueryMerchantNosByParentNoForPageRespDTO respDTO = customerMerchantInfoFacade.queryMerchantNoByParentNoForPage(req);
 171:             LOGGER.info("查询下级商户信息列表，去请求客户中心的返回为: {}", JSONUtils.toJsonString(respDTO));
 172:             if (respDTO != null && "0000".equals(respDTO.getRetCode())) {
 173:                 List<MerchantNosByParentNoDTO> merchantNosByParentNoDTOList = respDTO.getQueryMerchantNosByParentNoDTOList();
 174:                 if (CollectionUtils.isNotEmpty(merchantNosByParentNoDTOList)) {
 175:                     respDTO.getQueryMerchantNosByParentNoDTOList().forEach(merchantNosByParentNoDTO -> {
 176:                         MerchantAccountInfoEntity merchantAccountInfoEntity = new MerchantAccountInfoEntity();
 177:                         merchantAccountInfoEntity.setMerchantNo(merchantNosByParentNoDTO.getMerchantNo());
 178:                         merchantAccountInfoEntity.setSignName(merchantNosByParentNoDTO.getName());
 179:                         merchantAccountInfoEntity.setShortName(merchantNosByParentNoDTO.getShortName());
 180:                         merchantTotal.add(merchantAccountInfoEntity);
 181:                     });
 182:                     Integer limit = respDTO.getCount() / 500 + 1;
 183:                     if (page >= limit) {
 184:                         return merchantTotal;
 185:                     } else {
 186:                         page = page + 1;
 187:                         getAllMerchantNo(merchantTotal, req, page);
 188:                     }
 189:                 }
 190:             }
 191:             return merchantTotal;
 192:         } catch (Exception e) {
 193:             LOGGER.info("查询下级商户信息列表，发生了异常: " + "参数为" + JSONUtils.toJsonString(req) + "异常为", e);
 194:             throw UnionAccountException.SYSTEM_ERROR.newInstance("查询商户信息异常");
 195:         }
 196:     }
```

### 7. AccountManageInfoServiceImpl.queryOuterAccountByCustomerNo()

**文件路径**: `src/main/java/com/yeepay/g3/app/account/pay/mboss/service/impl/AccountManageInfoServiceImpl.java`
**相关性**: 被修改的方法 queryAccountInfos() 调用了此方法
**代码位置**: 第 233-261 行

```java
 233:     /**
 234:      * 查询下级商户所有的账户
 235:      * @param merchantNo
 236:      * @return
 237:      */
 238:     @Override
 239:     public List<MerchantAccountInfoEntity> queryAccountInfos(String merchantNo,String signName) {
 240:         List<MerchantAccountInfoEntity> resp = new ArrayList<>();
 241:         try{
 242:             List<QueryAccountResponseDto>  queryAccountResponseDtoList =  accountManageFacade.queryOuterAccountByCustomerNo(merchantNo);
 243:             LOGGER.info("查询账户信息，去请求accounting-sys的批量查询返回为: {}", JSONUtils.toJsonString(queryAccountResponseDtoList));
 244:             if(CollectionUtils.isNotEmpty(queryAccountResponseDtoList)){
 245:                 queryAccountResponseDtoList.forEach(queryAccountResponseDto->{
 246:                     MerchantAccountInfoEntity merchantAccountInfoEntity = new MerchantAccountInfoEntity();
 247:                     merchantAccountInfoEntity.setMerchantNo(merchantNo);
 248:                     merchantAccountInfoEntity.setSignName(signName);
 249:                     merchantAccountInfoEntity.setAccountType(queryAccountResponseDto.getAccountType());
 250:                     merchantAccountInfoEntity.setBalance(queryAccountResponseDto.getBalance());
 251:                     merchantAccountInfoEntity.setStatus(queryAccountResponseDto.getAccountStatus());
 252:                     merchantAccountInfoEntity.setCreateDate(queryAccountResponseDto.getCreateDate());
 253:                     resp.add(merchantAccountInfoEntity);
 254:                 });
 255:             }
 256:             return resp;
 257:         }catch (Exception e){
 258:             LOGGER.info("查询下级商户信息列表，发生了异常: " + "参数为" + merchantNo +"异常为" , e);
 259:             throw UnionAccountException.SYSTEM_ERROR.newInstance("查询账户信息异常");
 260:         }
 261:     }
  # 5. 查找该方法调用的下层代码 (Facade/DAO)
        self.debug_print("查找下层被调用者...")
        callees = self.find_method_callees(file_path, modified_method)

        for callee_method in callees[:5]:  # 限制数量
            callee_context = self.extract_complete_method(file_path, callee_method)
            if callee_context:
                callee_context.context_type = 'callee'
                callee_context.relevance_reason = f"被修改的方法 {modified_method}() 调用了此方法"
                self.contexts.append(callee_context)

        # 6. 查找相关的 Facade 方法
        facade_methods = self.extract_facade_methods_from_diff(diff_content)
        for facade_method in facade_methods[:3]:  # 限制数量
            facade_context = self.extract_facade_method(facade_method)
            if facade_context:
                self.contexts.append(facade_context)

        # 7. 提取相关实体类
        self.extract_related_entities(diff_content)

        # 8. 格式化输出
        return self.format_contexts(diff_content)
    
    def find_method_at_line(self, file_path: str, target_line: int) -> Optional[str]:
        """查找指定行所在的方法名"""
        lines = self.get_cached_file_content(file_path)
        if not lines:
            return None
        
        # 从目标行向上查找方法定义
        for i in range(min(target_line - 1, len(lines) - 1), max(-1, target_line - 100), -1):
            line = lines[i].strip()
            
            method_pattern = r'(public|private|protected).*?(\w+)\s*\([^)]*\)\s*\{'
            match = re.search(method_pattern, line)
            if match:
                return match.group(2)
        
        return None

    def find_method_callees(self, file_path: str, method_name: str) -> List[str]:
        """查找方法内部调用的其他方法"""
        self.debug_print(f"查找方法 {method_name} 调用的其他方法")

        lines = self.get_cached_file_content(file_path)
        if not lines:
            return []

        # 找到方法的范围
        method_start = None
        method_end = None

        for i, line in enumerate(lines):
            if method_name in line and ('public' in line or 'private' in line or 'protected' in line):
                method_pattern = rf'(public|private|protected).*?{re.escape(method_name)}\s*\([^)]*\)'
                if re.search(method_pattern, line):
                    method_start = i
                    break

        if method_start is None:
            return []

        # 找到方法结束
        brace_count = 0
        found_opening_brace = False

        for i in range(method_start, len(lines)):
            line = lines[i]

            open_braces = line.count('{')
            close_braces = line.count('}')

            if open_braces > 0:
                found_opening_brace = True

            brace_count += open_braces - close_braces

            if found_opening_brace and brace_count == 0:
                method_end = i
                break

        if method_end is None:
            method_end = len(lines) - 1

        # 提取方法内的调用
        callees = set()
        for i in range(method_start, method_end + 1):
            line = lines[i]

            # 查找方法调用模式
            method_calls = re.findall(r'(\w+)\s*\([^)]*\)', line)
            for call in method_calls:
                if call not in ['if', 'for', 'while', 'switch', 'catch', 'try', 'new', 'return']:
                    callees.add(call)

        self.debug_print(f"找到 {len(callees)} 个被调用方法")
        return list(callees)

    def extract_facade_methods_from_diff(self, diff_content: str) -> List[str]:
        """从 diff 中提取 Facade 方法调用"""
        facade_methods = set()

        for line in diff_content.split('\n'):
            # 查找 Facade 方法调用模式
            facade_calls = re.findall(r'(\w+Facade|\w+Service)\.(\w+)\s*\(', line)
            for facade_class, method_name in facade_calls:
                facade_methods.add(method_name)

        self.debug_print(f"从 diff 中找到 {len(facade_methods)} 个 Facade 方法")
        return list(facade_methods)

    def extract_related_entities(self, diff_content: str):
        """提取相关的实体类和 DTO"""
        # 从 diff 中提取实体类名
        entity_names = set()

        for line in diff_content.split('\n'):
            # 查找实体类引用
            entities = re.findall(r'(\w*Entity|\w*Dto|\w*VO)\b', line)
            entity_names.update(entities)

        self.debug_print(f"找到相关实体: {entity_names}")

        # 查找实体类定义
        for entity_name in list(entity_names)[:3]:  # 限制数量
            if entity_name and len(entity_name) > 3:
                try:
                    cmd = ['find', '.', '-name', f"{entity_name}.java", '-type', 'f']
                    result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.repo_root)

                    if result.returncode == 0 and result.stdout.strip():
                        entity_file = result.stdout.strip().split('\n')[0]
                        entity_context = self.extract_entity_definition(entity_file, entity_name)
                        if entity_context:
                            self.contexts.append(entity_context)

                except Exception as e:
                    self.debug_print(f"查找实体类失败 {entity_name}: {e}")

    def extract_facade_method(self, method_name: str) -> Optional[CodeContext]:
        """提取 Facade 层方法"""
        self.debug_print(f"查找 Facade 方法: {method_name}")

        try:
            # 查找包含该方法的 Facade 文件
            cmd = ['git', 'grep', '-l', '-E', f'{method_name}\\s*\\(', '--', '**/*Facade*.java']
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.repo_root)

            if result.returncode == 0:
                facade_files = result.stdout.strip().split('\n')
                for facade_file in facade_files[:3]:  # 限制数量
                    if facade_file:
                        facade_context = self.extract_complete_method(facade_file, method_name)
                        if facade_context:
                            facade_context.context_type = 'callee'
                            facade_context.relevance_reason = f"被修改方法调用的 Facade 方法"
                            return facade_context

        except Exception as e:
            self.debug_print(f"查找 Facade 方法失败: {e}")

        return None

    def extract_entity_definition(self, file_path: str, entity_name: str) -> Optional[CodeContext]:
        """提取实体类定义"""
        lines = self.get_cached_file_content(file_path)
        if not lines:
            return None
        
        # 提取类定义和主要字段（前80行）
        entity_lines = []
        for i in range(min(80, len(lines))):
            entity_lines.append(f"{i+1:4d}: {lines[i].rstrip()}")
        
        return CodeContext(
            file_path=file_path,
            class_name=entity_name,
            method_name="class_definition",
            code_snippet='\n'.join(entity_lines),
            context_type='related',
            relevance_reason=f"代码变更中引用的实体类",
            start_line=1,
            end_line=min(80, len(lines)),
            layer='entity'
        )

    def format_contexts(self, original_diff: str) -> str:
        """格式化上下文输出"""
        output = []

        # 原始 diff
        output.append("# 原始 Diff")
        output.append("")
        output.append("```diff")
        output.append(original_diff)
        output.append("```")
        output.append("")

        if not self.contexts:
            return self.format_no_context_result(original_diff, "未找到相关代码上下文")

        # 详细代码上下文
        output.append("# 📝 详细代码上下文")
        output.append("")

        # 按层级分组
        layers = {
            'controller': [],
            'service': [],
            'facade': [],
            'dao': [],
            'entity': [],
            'util': []
        }

        for ctx in self.contexts:
            layers[ctx.layer].append(ctx)

        layer_names = {
            'controller': '🎮 Controller 层',
            'service': '⚙️ Service 层',
            'facade': '🔌 Facade 层',
            'dao': '💾 DAO 层',
            'entity': '📦 Entity/DTO 层',
            'util': '🛠️ Util 层'
        }

        section_count = 1
        for layer_key, layer_contexts in layers.items():
            if layer_contexts:
                output.append(f"## {layer_names[layer_key]}")
                output.append("")

                for ctx in layer_contexts:
                    output.append(f"### {section_count}. {ctx.class_name}.{ctx.method_name}()")
                    output.append("")
                    output.append(f"**文件路径**: `{ctx.file_path}`")
                    output.append(f"**相关性**: {ctx.relevance_reason}")
                    output.append(f"**代码位置**: 第 {ctx.start_line}-{ctx.end_line} 行")
                    output.append("")

                    output.append("```java")
                    output.append(ctx.code_snippet)
                    output.append("```")

                    output.append("")
                    section_count += 1



        return '\n'.join(output)

    def format_no_context_result(self, original_diff: str, reason: str) -> str:
        """格式化无上下文结果"""
        output = []

        output.append("# 原始 Diff")
        output.append("")
        output.append("```diff")
        output.append(original_diff)
        output.append("```")
        output.append("")

        output.append("## ⚠️ 未找到相关上下文")
        output.append("")
        output.append(f"**原因**: {reason}")
        output.append("")

        return '\n'.join(output)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Java Final Context Extractor')

    # 创建互斥组：要么指定文件，要么直接提供 diff 内容
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--file', help='Diff file path')
    input_group.add_argument('--diff', help='Diff content as string')

    parser.add_argument('--output', help='Output file path')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    parser.add_argument('--repo-root', help='Repository root directory (default: current directory)', default='.')

    args = parser.parse_args()

    # 获取 diff 内容
    diff_content = None

    if args.file:
        # 从文件读取 diff 内容
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                diff_content = f.read()
        except FileNotFoundError:
            print(f"❌ 错误: 文件 {args.file} 不存在")
            return
        except Exception as e:
            print(f"❌ 读取文件错误: {e}")
            return
    elif args.diff:
        # 直接使用提供的 diff 内容
        diff_content = args.diff
    else:
        print("❌ 错误: 必须指定 --file 或 --diff 参数")
        return

    if not diff_content or not diff_content.strip():
        print("❌ 错误: diff 内容为空")
        return

    # 执行上下文提取
    extractor = JavaContextExtractor(debug=args.debug, repo_root=args.repo_root)
    context = extractor.extract_all_contexts(diff_content)

    # 输出结果
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(context)
            print(f"✅ 代码上下文已保存到 {args.output}")

        except Exception as e:
            print(f"❌ 保存文件错误: {e}")
    else:
        print(context)

if __name__ == "__main__":
    main()
