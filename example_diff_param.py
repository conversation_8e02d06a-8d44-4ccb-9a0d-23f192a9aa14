#!/usr/bin/env python3
"""
演示 extractor_final.py 的 --diff 参数功能
"""

import subprocess
import sys

def demo_basic_diff_usage():
    """演示基本的 --diff 参数用法"""
    print("🚀 演示基本的 --diff 参数用法")
    print("=" * 50)
    
    # 示例 diff 内容
    diff_content = """diff --git a/src/main/java/com/example/controller/UserController.java b/src/main/java/com/example/controller/UserController.java
index 1234567..abcdefg 100644
--- a/src/main/java/com/example/controller/UserController.java
+++ b/src/main/java/com/example/controller/UserController.java
@@ -25,6 +25,8 @@ public class UserController {
     @PostMapping("/users/{id}")
     public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody UserDto userDto) {
         try {
+            // 新增：参数验证
+            validateUserDto(userDto);
             User updatedUser = userService.updateUser(id, userDto);
             return ResponseEntity.ok(updatedUser);
         } catch (UserNotFoundException e) {
"""
    
    print("\n📝 示例 diff 内容:")
    print("```diff")
    print(diff_content[:200] + "...")
    print("```")
    
    print("\n🔧 执行命令:")
    cmd = [
        'python3', 'extractor_final.py',
        '--diff', diff_content,
        '--debug'
    ]
    print(f"python3 extractor_final.py --diff '<diff_content>' --debug")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("\n✅ 执行成功!")
            print("\n📄 输出结果 (前15行):")
            lines = result.stdout.split('\n')[:15]
            for i, line in enumerate(lines, 1):
                if line.strip():
                    print(f"{i:2d}: {line}")
            
            if len(result.stdout.split('\n')) > 15:
                print("    ...")
                print(f"    (总共 {len(result.stdout.split('\n'))} 行)")
        else:
            print(f"\n❌ 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
    
    except subprocess.TimeoutExpired:
        print("\n⏰ 命令执行超时")
    except Exception as e:
        print(f"\n❌ 执行错误: {e}")

def demo_programmatic_usage():
    """演示编程方式使用"""
    print("\n💻 演示编程方式使用")
    print("=" * 50)
    
    try:
        from extractor_final import JavaContextExtractor
        
        # 示例 diff 内容
        diff_content = """diff --git a/src/main/java/service/PaymentService.java b/src/main/java/service/PaymentService.java
index 1234567..abcdefg 100644
--- a/src/main/java/service/PaymentService.java
+++ b/src/main/java/service/PaymentService.java
@@ -30,6 +30,9 @@ public class PaymentService {
     public PaymentResult processPayment(PaymentRequest request) {
         // 验证支付请求
         validatePaymentRequest(request);
+        
+        // 新增：风险检查
+        riskCheckService.checkRisk(request);
         
         // 处理支付
         return paymentProcessor.process(request);
"""
        
        print("\n📝 创建提取器实例:")
        extractor = JavaContextExtractor(debug=True, repo_root='.')
        print(f"✅ 仓库根目录: {extractor.repo_root}")
        
        print("\n🔍 提取代码上下文:")
        context = extractor.extract_all_contexts(diff_content)
        
        print("✅ 提取完成!")
        print(f"📊 输出长度: {len(context)} 字符")
        
        # 显示部分结果
        lines = context.split('\n')[:10]
        print("\n📄 结果预览 (前10行):")
        for i, line in enumerate(lines, 1):
            print(f"{i:2d}: {line}")
        
        if len(context.split('\n')) > 10:
            print("    ...")
    
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def demo_output_to_file():
    """演示输出到文件"""
    print("\n📁 演示输出到文件")
    print("=" * 50)
    
    diff_content = """diff --git a/src/main/java/util/StringUtils.java b/src/main/java/util/StringUtils.java
index 1234567..abcdefg 100644
--- a/src/main/java/util/StringUtils.java
+++ b/src/main/java/util/StringUtils.java
@@ -15,6 +15,8 @@ public class StringUtils {
     public static boolean isEmpty(String str) {
         return str == null || str.trim().isEmpty();
     }
+    
+    // 新增：检查字符串是否为空白
+    public static boolean isBlank(String str) {
+        return str == null || str.trim().length() == 0;
+    }
 }
"""
    
    output_file = "diff_analysis_result.md"
    
    print(f"\n🔧 执行命令 (输出到 {output_file}):")
    cmd = [
        'python3', 'extractor_final.py',
        '--diff', diff_content,
        '--output', output_file,
        '--repo-root', '.'
    ]
    print(f"python3 extractor_final.py --diff '<diff_content>' --output {output_file}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"\n✅ 执行成功! 结果已保存到 {output_file}")
            
            # 检查文件是否创建
            import os
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"📄 文件大小: {file_size} 字节")
                
                # 显示文件前几行
                with open(output_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:8]
                    print(f"\n📖 文件内容预览 (前8行):")
                    for i, line in enumerate(lines, 1):
                        print(f"{i:2d}: {line.rstrip()}")
                
                # 清理文件
                os.unlink(output_file)
                print(f"\n🗑️  已清理临时文件 {output_file}")
            else:
                print(f"❌ 文件 {output_file} 未创建")
        else:
            print(f"\n❌ 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
    
    except Exception as e:
        print(f"\n❌ 执行错误: {e}")

def demo_complex_diff():
    """演示复杂 diff 处理"""
    print("\n🔧 演示复杂 diff 处理")
    print("=" * 50)
    
    # 包含多个文件的复杂 diff
    complex_diff = """diff --git a/src/main/java/com/example/service/OrderService.java b/src/main/java/com/example/service/OrderService.java
index 1234567..abcdefg 100644
--- a/src/main/java/com/example/service/OrderService.java
+++ b/src/main/java/com/example/service/OrderService.java
@@ -45,6 +45,10 @@ public class OrderService {
     public Order createOrder(OrderDto orderDto) {
         // 验证订单数据
         validateOrder(orderDto);
+        
+        // 新增：库存检查
+        if (!inventoryService.checkStock(orderDto.getItems())) {
+            throw new InsufficientStockException("库存不足");
+        }
         
         // 创建订单
         Order order = new Order();
@@ -60,6 +64,8 @@ public class OrderService {
         order.setStatus(OrderStatus.PENDING);
         
         // 保存订单
+        // 新增：发送订单创建事件
+        eventPublisher.publishEvent(new OrderCreatedEvent(order));
         return orderRepository.save(order);
     }
 
diff --git a/src/main/java/com/example/dto/OrderDto.java b/src/main/java/com/example/dto/OrderDto.java
index 2345678..bcdefgh 100644
--- a/src/main/java/com/example/dto/OrderDto.java
+++ b/src/main/java/com/example/dto/OrderDto.java
@@ -10,6 +10,9 @@ public class OrderDto {
     private Long customerId;
     private List<OrderItemDto> items;
     private BigDecimal totalAmount;
+    
+    // 新增：优惠券ID
+    private Long couponId;
     
     // getters and setters...
 }
"""
    
    print("\n📝 复杂 diff 内容特点:")
    print("- 包含多个文件的修改")
    print("- 包含业务逻辑变更")
    print("- 包含数据结构变更")
    
    print(f"\n🔧 执行分析:")
    cmd = [
        'python3', 'extractor_final.py',
        '--diff', complex_diff,
        '--debug'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 复杂 diff 分析成功!")
            
            # 统计输出信息
            output_lines = result.stdout.split('\n')
            debug_lines = [line for line in output_lines if '[DEBUG]' in line]
            
            print(f"📊 分析统计:")
            print(f"   - 总输出行数: {len(output_lines)}")
            print(f"   - 调试信息行数: {len(debug_lines)}")
            
            # 显示关键调试信息
            print(f"\n🔍 关键调试信息:")
            for line in debug_lines[:5]:
                if '找到变更文件' in line or '找到变更起始行号' in line:
                    print(f"   {line}")
        else:
            print(f"❌ 分析失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
    
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def main():
    """主函数"""
    print("🎉 extractor_final.py --diff 参数功能演示")
    print("=" * 60)
    
    # 检查 extractor_final.py 是否存在
    import os
    if not os.path.exists('extractor_final.py'):
        print("❌ 错误: 找不到 extractor_final.py 文件")
        print("请确保在正确的目录中运行此脚本")
        return
    
    try:
        demo_basic_diff_usage()
        demo_programmatic_usage()
        demo_output_to_file()
        demo_complex_diff()
        
        print("\n" + "=" * 60)
        print("✅ 所有演示完成!")
        print("\n📚 新功能总结:")
        print("- ✅ 支持通过 --diff 参数直接传入 diff 内容")
        print("- ✅ 与 --file 参数互斥，确保输入唯一性")
        print("- ✅ 支持空内容验证和错误处理")
        print("- ✅ 完全兼容现有的所有功能")
        print("- ✅ 支持编程方式和命令行方式使用")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")

if __name__ == "__main__":
    main()
