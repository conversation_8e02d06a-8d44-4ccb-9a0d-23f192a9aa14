#!/usr/bin/env python3
"""
测试 extractor_final.py 的 diff 参数功能
"""

import os
import subprocess
import tempfile

def test_diff_parameter():
    """测试 --diff 参数功能"""
    print("🧪 测试 --diff 参数功能...")
    
    # 示例 diff 内容
    diff_content = """diff --git a/src/main/java/com/example/service/UserService.java b/src/main/java/com/example/service/UserService.java
index 1234567..abcdefg 100644
--- a/src/main/java/com/example/service/UserService.java
+++ b/src/main/java/com/example/service/UserService.java
@@ -15,6 +15,8 @@ public class UserService {
     public User updateUser(Long userId, UserDto userDto) {
         // 验证用户存在
         User existingUser = userDao.findById(userId);
+        // 新增：记录更新日志
+        logger.info("更新用户信息: userId={}", userId);
         
         if (existingUser == null) {
             throw new UserNotFoundException("用户不存在: " + userId);
"""
    
    print("\n1. 测试通过 --diff 参数传入内容:")
    try:
        cmd = [
            'python3', 'extractor_final.py',
            '--diff', diff_content,
            '--debug'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ 执行成功")
            # 显示部分输出
            lines = result.stdout.split('\n')[:8]
            for line in lines:
                if line.strip():
                    print(f"   📄 {line}")
            if len(result.stdout.split('\n')) > 8:
                print("   📄 ...")
        else:
            print(f"   ❌ 执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
    
    except subprocess.TimeoutExpired:
        print("   ⏰ 命令执行超时")
    except Exception as e:
        print(f"   ❌ 执行错误: {e}")

def test_file_vs_diff_parameter():
    """测试文件参数 vs diff 参数"""
    print("\n🔄 测试文件参数 vs diff 参数对比...")
    
    diff_content = """diff --git a/src/main/java/TestClass.java b/src/main/java/TestClass.java
index 1234567..abcdefg 100644
--- a/src/main/java/TestClass.java
+++ b/src/main/java/TestClass.java
@@ -10,6 +10,7 @@ public class TestClass {
     public void testMethod() {
         System.out.println("Hello");
+        System.out.println("World");
     }
 }
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.diff', delete=False) as f:
        f.write(diff_content)
        temp_file = f.name
    
    try:
        print("\n1. 使用 --file 参数:")
        cmd_file = [
            'python3', 'extractor_final.py',
            '--file', temp_file,
            '--debug'
        ]
        
        result_file = subprocess.run(cmd_file, capture_output=True, text=True, timeout=30)
        
        print("\n2. 使用 --diff 参数:")
        cmd_diff = [
            'python3', 'extractor_final.py',
            '--diff', diff_content,
            '--debug'
        ]
        
        result_diff = subprocess.run(cmd_diff, capture_output=True, text=True, timeout=30)
        
        # 比较结果
        if result_file.returncode == 0 and result_diff.returncode == 0:
            print("   ✅ 两种方式都执行成功")
            
            # 比较输出长度
            file_lines = len(result_file.stdout.split('\n'))
            diff_lines = len(result_diff.stdout.split('\n'))
            
            print(f"   📊 文件方式输出行数: {file_lines}")
            print(f"   📊 参数方式输出行数: {diff_lines}")
            
            if abs(file_lines - diff_lines) <= 2:  # 允许小差异
                print("   ✅ 输出结果基本一致")
            else:
                print("   ⚠️  输出结果存在差异")
        else:
            print(f"   ❌ 执行失败 - 文件方式: {result_file.returncode}, 参数方式: {result_diff.returncode}")
    
    except Exception as e:
        print(f"   ❌ 对比测试错误: {e}")
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file)
        except:
            pass

def test_mutually_exclusive_args():
    """测试互斥参数"""
    print("\n🚫 测试互斥参数...")
    
    diff_content = "test diff content"
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.diff', delete=False) as f:
        f.write(diff_content)
        temp_file = f.name
    
    try:
        print("\n1. 测试同时使用 --file 和 --diff (应该失败):")
        cmd = [
            'python3', 'extractor_final.py',
            '--file', temp_file,
            '--diff', diff_content
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("   ✅ 正确拒绝了互斥参数")
            if "not allowed" in result.stderr or "mutually exclusive" in result.stderr:
                print("   📝 错误信息正确")
        else:
            print("   ❌ 应该失败但没有失败")
        
        print("\n2. 测试不提供任何输入参数 (应该失败):")
        cmd = ['python3', 'extractor_final.py']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("   ✅ 正确要求必需参数")
            if "required" in result.stderr:
                print("   📝 错误信息正确")
        else:
            print("   ❌ 应该失败但没有失败")
    
    except Exception as e:
        print(f"   ❌ 互斥参数测试错误: {e}")
    finally:
        try:
            os.unlink(temp_file)
        except:
            pass

def test_empty_diff_content():
    """测试空 diff 内容"""
    print("\n📭 测试空 diff 内容...")
    
    print("\n1. 测试空字符串:")
    try:
        cmd = [
            'python3', 'extractor_final.py',
            '--diff', ''
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

        if result.returncode != 0:
            print("   ✅ 正确拒绝了空内容")
            if "diff 内容为空" in result.stdout or "必须指定" in result.stdout:
                print("   📝 错误信息正确")
        else:
            print("   ❌ 应该失败但没有失败")
            print(f"   输出: {result.stdout}")

    except Exception as e:
        print(f"   ❌ 空内容测试错误: {e}")
    
    print("\n2. 测试只有空白字符:")
    try:
        cmd = [
            'python3', 'extractor_final.py',
            '--diff', '   \n\t  \n  '
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("   ✅ 正确拒绝了空白内容")
            if "diff 内容为空" in result.stdout:
                print("   📝 错误信息正确")
        else:
            print("   ❌ 应该失败但没有失败")
            print(f"   输出: {result.stdout}")
    
    except Exception as e:
        print(f"   ❌ 空白内容测试错误: {e}")

def test_help_message():
    """测试帮助信息"""
    print("\n❓ 测试帮助信息...")
    
    try:
        cmd = ['python3', 'extractor_final.py', '--help']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("   ✅ 帮助信息显示成功")
            
            # 检查是否包含新参数的说明
            if '--diff' in result.stdout:
                print("   ✅ 包含 --diff 参数说明")
            else:
                print("   ❌ 缺少 --diff 参数说明")
            
            if 'mutually exclusive' in result.stdout or '--file' in result.stdout:
                print("   ✅ 包含参数选择说明")
            else:
                print("   ⚠️  可能缺少参数选择说明")
        else:
            print(f"   ❌ 帮助信息显示失败: {result.returncode}")
    
    except Exception as e:
        print(f"   ❌ 帮助信息测试错误: {e}")

def main():
    """主函数"""
    print("🚀 开始测试 extractor_final.py 的 diff 参数功能")
    print("=" * 60)
    
    # 检查 extractor_final.py 是否存在
    if not os.path.exists('extractor_final.py'):
        print("❌ 错误: 找不到 extractor_final.py 文件")
        print("请确保在正确的目录中运行此脚本")
        return
    
    try:
        test_diff_parameter()
        test_file_vs_diff_parameter()
        test_mutually_exclusive_args()
        test_empty_diff_content()
        test_help_message()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
